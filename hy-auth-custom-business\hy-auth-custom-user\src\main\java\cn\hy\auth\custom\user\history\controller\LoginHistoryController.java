package cn.hy.auth.custom.user.history.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.log.annotation.ConfigLog;
import cn.hy.auth.custom.common.log.enums.ModuleNameEnum;
import cn.hy.auth.custom.common.log.enums.OperationTypeEnum;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述: 认证信息查询历史登录信息接口
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/16
 */
@AllArgsConstructor
@RestController
@RequestMapping("/history")
@Slf4j
public class LoginHistoryController {

    private final UserCacheService userCacheService;

    private final ObjectMapper objetMapper;
    /**
     * 查询上一次成功登录信息
     *
     * @return 返回最后登录信息
     */
    @GetMapping("/login/last")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "查询上一次成功登录信息", operationType = OperationTypeEnum.LIST,isSave = false)
    public UserLoginInfoDTO getLastSuccessLoginInfo() {
        return userCacheService.getUserLoginInfoByTokenId(TokenUtil.getTokenID());
    }
    @GetMapping("/object")
    public Map<String,Object> testObjectMapper() throws JsonProcessingException {
        Map<String,Object> map = new HashMap<>();
        map.put("id",123456789021L);
        map.put("date",new Date());
        map.put("localDateTime",  LocalDateTimeUtil.of(new Date()));
        log.info("{}",objetMapper.writeValueAsString(map));
        return map;
    }
}

{"groups": [{"name": "hy.security.request-type", "type": "cn.hy.auth.custom.common.properties.RequestTypeProperties", "sourceType": "cn.hy.auth.custom.common.properties.RequestTypeProperties"}], "properties": [{"name": "hy.security.request-type.authenticate", "type": "java.util.Set<java.lang.String>", "description": "认证相关URL格式，从Header的Authorization中的token提取租户、应用编码", "sourceType": "cn.hy.auth.custom.common.properties.RequestTypeProperties"}, {"name": "hy.security.request-type.login", "type": "java.util.Set<java.lang.String>", "description": "登录的URL格式，从参数中提取租户、应用编码", "sourceType": "cn.hy.auth.custom.common.properties.RequestTypeProperties"}, {"name": "hy.security.request-type.non-business", "type": "java.util.Set<java.lang.String>", "description": "需要忽略的 URL 格式", "sourceType": "cn.hy.auth.custom.common.properties.RequestTypeProperties"}], "hints": []}
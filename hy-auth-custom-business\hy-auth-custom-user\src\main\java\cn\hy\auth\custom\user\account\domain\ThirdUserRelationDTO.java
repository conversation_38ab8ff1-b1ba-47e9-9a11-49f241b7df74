package cn.hy.auth.custom.user.account.domain;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;


/**
 * 类描述:第三方用户账号
 *
 * <AUTHOR>
 * @date ：创建于 2022/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "第三方用户账号")
public class ThirdUserRelationDTO {

    private Long id;
    private String providerId;
    private String providerUserId;
    private String userAccountName;
    private Date createTime;
    private Long createUserId;
    private Long lastUpdateUserId;
    private Date lastUpdateTime;
}

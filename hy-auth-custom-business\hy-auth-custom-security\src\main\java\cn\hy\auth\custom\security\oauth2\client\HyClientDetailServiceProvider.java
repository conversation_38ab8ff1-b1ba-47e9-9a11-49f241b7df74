package cn.hy.auth.custom.security.oauth2.client;

import cn.hy.auth.common.security.oauth2.client.ClientDetailsServiceProvider;
import cn.hy.auth.custom.security.oauth2.client.dao.OauthClientDetailsMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.CacheManager;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/18 18:16
 **/
@Service
@Slf4j
public class HyClientDetailServiceProvider implements ClientDetailsServiceProvider {
    private final OauthClientDetailsMapper oauthClientDetailsMapper;
    private final ObjectMapper objectMapper;

    private final CacheManager cacheManager;
    private ClientDetailsService clientDetailsService;
    public HyClientDetailServiceProvider( OauthClientDetailsMapper oauthClientDetailsMapper,
                                          ObjectMapper objectMapper,
                                          @Qualifier("hyAuthSystemConfigCacheManager")CacheManager cacheManager) {
        this.oauthClientDetailsMapper = oauthClientDetailsMapper;
        this.objectMapper = objectMapper;
        this.cacheManager = cacheManager;
    }

    @Override
    public ClientDetailsService getClientDetailsService() {
        log.debug("JDBC cache ClientDetailsService");
        if (clientDetailsService == null){
            clientDetailsService = new OauthClientDetailsServiceImpl(oauthClientDetailsMapper, objectMapper,cacheManager);
        }
        return clientDetailsService;
    }

    public void clearCache(){
        Collection<String> cacheNames = cacheManager.getCacheNames();
        if (CollectionUtils.isNotEmpty(cacheNames) ){
            for (String cacheName : cacheNames) {
                if (cacheName.contains(OauthClientDetailsServiceImpl.ROOT_CACHE_NAME)){
                    Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
                }
            }
        }

    }

    public void clearCache(String lessCode,String appCode){
        StringJoiner stringJoiner = new StringJoiner("_");
        String lessCodeAppCode = stringJoiner.add(lessCode).add(appCode).toString();
        Collection<String> cacheNames = cacheManager.getCacheNames();
        if (CollectionUtils.isNotEmpty(cacheNames) ){
            for (String cacheName : cacheNames) {
                if (cacheName.contains(OauthClientDetailsServiceImpl.ROOT_CACHE_NAME) && cacheName.contains(lessCodeAppCode)){
                    log.debug("清空了缓存数据。{}",cacheName);
                    Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
                }
            }
        }

    }

    public void clearCache(String lessCode){
        Collection<String> cacheNames = cacheManager.getCacheNames();
        if (CollectionUtils.isNotEmpty(cacheNames) ){
            for (String cacheName : cacheNames) {
                if (cacheName.contains(OauthClientDetailsServiceImpl.ROOT_CACHE_NAME) && cacheName.contains(lessCode)){
                    log.info("清空了缓存数据。{}",cacheName);
                    Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
                }
            }
        }

    }
}

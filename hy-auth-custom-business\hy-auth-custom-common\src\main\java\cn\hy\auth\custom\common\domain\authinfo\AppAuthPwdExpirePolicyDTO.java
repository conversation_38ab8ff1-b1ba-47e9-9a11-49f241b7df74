package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 密码过期前提醒策略
 *
 * <AUTHOR>
 * @date 2020-12-03 16:27
 **/
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class AppAuthPwdExpirePolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -1294693337223380472L;

    /**
     * 密码最后修改时间对应字段编码
     * 默认是null
     */
    private String pwdLastChangeTime;
    /**
     * 过期前多少天提醒的阈值，范围0~20
     */
    private String expireRemindThreshold;

    /**
     * 解析密码最后修改时间
     *
     * @param data 数据集
     * @return 密码最后修改时间戳
     */
    public Long parseLastChangeTime(Map<String, Object> data) {
        return parseTimestamp(data, pwdLastChangeTime);
    }
}

package cn.hy.saas.boot.log;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import cn.hy.auth.boot.AuthApplication;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.LoginUtil;

import java.util.Optional;

/**
 * 日志增强，打印应用上下文信息
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2022/9/14 14:49
 */

public class TracingInfoConverter extends ClassicConverter {

    
    //hy, hyinvoice, bis_api_1612657733612482561, admin, 948b3322-3c18-490d-b189-32535cc08165]
    private String prefix = ",,,,";

    @Override
    public String convert(ILoggingEvent event) {
        StringBuilder sb = new StringBuilder();
        if (!AuthApplication.AUTH_IS_UP){
            sb.append(prefix);
            sb.append("Auth is starting");
          return  sb.toString() ;
        }
        Optional<Object> requestId= AuthContext.getContext().get("_requestId_");
        if (requestId.isPresent()) {
            sb.append(AuthContext.getContext().getLesseeCode()).append(",");
            sb.append(AuthContext.getContext().getAppCode()).append(",");
            Optional<Object> uri = AuthContext.getContext().get("_uri_");
            uri.ifPresent(sb::append);
            sb.append(",");
            String loginName = AuthContext.getContext().get("_login_name_").map(Object::toString).orElse(null);
            sb.append(loginName).append(",");
            sb.append(requestId.get());
        } else {
            sb.append(prefix);
            sb.append("Not Auth Request!");
        }

        return sb.toString();

    }


}

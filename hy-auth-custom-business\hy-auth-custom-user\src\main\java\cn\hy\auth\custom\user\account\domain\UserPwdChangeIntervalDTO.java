package cn.hy.auth.custom.user.account.domain;

import cn.hy.auth.custom.user.account.enums.UsmPwdChangeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: userPwdChangeIntervalDTO
 * @description: 用户密码变更时间间隔
 * @date 2024/1/17
 */
@Data
public class UserPwdChangeIntervalDTO implements Serializable {

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 账号名
     */
    private String userAccountName;

    /**
     * 用户有效开始时间
     */
    private Date userValidStartTime;

    /**
     * 用户有效截止时间
     */
    private Date userValidEndTime;

    /**
     * 密码最近修改时间
     */
    private Date lastPwdChangeTime;

    /**
     * 密码更换时间间隔
     * {@link UsmPwdChangeEnum}
     */
    private String pwdChangeInterval;

    /**
     * 密码更换时间间隔显示值
     */
    private String pwdChangeIntervalName;

    /**
     * 用户账号主键
     */
    private BigDecimal userAccountId;

    /**
     * 密码更新状态：0未修改 1已修改 2当天已修改（判断当前时间是不是跟“密码最近修改时间”同一天，如果是则无需提示）
     */
    private String pwdChangeStatus;

    /**
     * 密码更新状态显示值
     */
    private String pwdChangeStatusName;

    /**
     * 密码下次修改时间
     */
    private Date nextPwdChangeTime;

}

package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.user.account.domain.UserPwdForceModifyResult;
import cn.hy.auth.custom.user.account.enums.UsmAppParamEnum;
import cn.hy.auth.custom.user.account.service.PwdExpirationAndForceModifyService;
import cn.hy.auth.custom.user.account.service.PwdStatusService;
import cn.hy.auth.custom.user.account.service.UserSecurityManageService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户密码状态：1、默认密码强制修改，2、密码过期，强制修改
 *
 * <AUTHOR>
 * @date 2022-06-13 16:52
 **/
@Component
@Slf4j
public class PwdExpirationAndForceModifyProviderer extends AbstractPwdPolicyProvider {

    private final PwdExpirationAndForceModifyService pwdExpirationAndForceModifyService;
    private final PwdStatusService pwdStatusService;
    private final UserSecurityManageService userSecurityManageService;
    protected PwdExpirationAndForceModifyProviderer(PwdExpirationAndForceModifyService pwdExpirationAndForceModifyService, UserDetailsService userDetailsService, TokenStore tokenStore, TokenServicesProperties tokenServicesProperties, ApplicationContext applicationContext, ClientDetailsService clientDetailsService, PasswordEncoder passwordEncoder, PwdExpirationAndForceModifyService pwdExpirationAndForceModifyService1, PwdStatusService pwdStatusService, UserSecurityManageService userSecurityManageService) {
        super(pwdExpirationAndForceModifyService, userDetailsService, tokenStore, tokenServicesProperties, applicationContext, clientDetailsService, passwordEncoder);
        this.pwdExpirationAndForceModifyService = pwdExpirationAndForceModifyService1;
        this.pwdStatusService = pwdStatusService;
        this.userSecurityManageService = userSecurityManageService;
    }


    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return 只能return null,否则会截断后续的处理
     */
    @Override
    protected Authentication doAuthenticate(Authentication authentication) {
        String userName = (String)authentication.getPrincipal();
        if (StringUtils.isBlank(userName)) {
            log.error("用户名参数为空。");
            throw new BadCredentialsException(LocaleUtil.getMessage("PwdExpirationAndForceModifyProviderer.result.msg1", null));
        }
        Map<String, Object> params = (Map<String, Object>) authentication.getDetails();
        String ignorePwdStatus = MapUtils.getString(params, LoginParamterConts.IGNORE_PWD_STATUS, StringUtils.EMPTY);
        if (Objects.equals(ignorePwdStatus,"1")){
            log.info("检查用户账号密码状态-->ignorePwdStatus='1',直接认为用户【{}】的密码状态正常。", userName);
            return null;
        }

        HyUserDetails account = getUserDetail(userName);
        if (Objects.isNull(account)) {
            log.error("账号密码检查-->根据用户名【" + userName + "】无法获取用户信息。");
            throw new BadCredentialsException(LocaleUtil.getMessage("PwdExpirationAndForceModifyProviderer.result.msg2", null));
        }

        if (pwdStatusService.handleInitPwd(account)) {
            //抛出错误前，取token返回用于修改密码
            log.info("默认密码检查-->验证用户【{}】的密码为默认密码，请立即修改！", userName);
            UserPwdForceModifyResult result = new UserPwdForceModifyResult(AuthErrorCodeEnum.A0245.code(), AuthErrorCodeEnum.A0245.msg(),getAccessToken(authentication));
            throw new AuthBusinessException(AuthErrorCodeEnum.A0245.code(), JSON.toJSONString(result));
        }

        if (pwdExpirationAndForceModifyService.isExpiration(account)){
            //抛出错误前，取token返回用于修改密码
            log.info("密码过期检查-->验证用户【{}】的密码已过期，请修改！", userName);
            UserPwdForceModifyResult result = new UserPwdForceModifyResult(AuthErrorCodeEnum.A0242.code(), AuthErrorCodeEnum.A0242.msg(),getAccessToken(authentication));
            throw new AuthBusinessException(AuthErrorCodeEnum.A0242.code(), JSON.toJSONString(result));
        }

        if (
                isPwdChangeInterval(account)
        ) {
            log.info("更换密码时间间隔检查-->验证用户【{}】的密码已到达指定修改时间，请修改！", userName);
            UserPwdForceModifyResult result = new UserPwdForceModifyResult(AuthErrorCodeEnum.A0247.code(), AuthErrorCodeEnum.A0247.msg(), getAccessToken(authentication));
            throw new AuthBusinessException(AuthErrorCodeEnum.A0247.code(), JSON.toJSONString(result));
        }

        String ip = IpUtils.getIpAddress();
        if (
                isLoginBindIp(account, ip)
        ) {
            log.info("绑定用户登录ip检查-->验证用户【{}】的登录IP【{}】与绑定的IP不一致，请检查！", userName, ip);
            UserPwdForceModifyResult result = new UserPwdForceModifyResult(AuthErrorCodeEnum.A0248.code(), AuthErrorCodeEnum.A0248.msg(), getAccessToken(authentication));
            throw new AuthBusinessException(AuthErrorCodeEnum.A0248.code(), JSON.toJSONString(result));
        }

        log.info("检查用户账号密码状态-->用户【{}】的密码状态正常。", userName);

        return null;
    }


    private boolean isPwdChangeInterval(HyUserDetails account) {
        try {
            return userSecurityManageService.existsEnableParamCfg(UsmAppParamEnum.PWD_CHANGE_INTERVAL.getCode())
                    && !userSecurityManageService.existsIgnoreWhiteList(account)
                    && userSecurityManageService.changeTimeReached(account);
        } catch (Exception e) {
            log.warn("密码更换时间间隔校验异常：{}", e.getMessage(), e);
        }
        return false;
    }

    private boolean isLoginBindIp(HyUserDetails account, String ip) {
        try {
            return userSecurityManageService.existsEnableParamCfg(UsmAppParamEnum.LOGIN_BIND_IP.getCode())
                    && userSecurityManageService.ipRestrict(account, ip);
        } catch (Exception e) {
            log.warn("绑定登陆ip校验异常：{}", e.getMessage(), e);
        }
        return false;
    }


    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    @Override
    protected int order() {
        return 20;
    }

    /**
     * 判断是否启用策略
     *
     * @param authentication .
     * @return .
     */
    @Override
    protected boolean isSupport(Authentication authentication) {
       /* AppAuthPwdUpdatePolicyDTO policy
                = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdUpdatePolicy();
        return ObjectUtil.isNotNull(policy) && policy.getEnable();*/
        List<LoginTypeEnum> supports = Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD,
                LoginTypeEnum.OAUTH2_PASSWORD);

        return supports.contains(AuthContext.getContext().loginState().getLoginType());
    }
}

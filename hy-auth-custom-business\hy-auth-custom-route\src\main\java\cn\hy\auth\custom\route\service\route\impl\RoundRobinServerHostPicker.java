package cn.hy.auth.custom.route.service.route.impl;

import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.route.service.route.ServerHostPicker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类描述：轮询的返回server的ip
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/24 14:08
 **/
@Service
public class RoundRobinServerHostPicker implements ServerHostPicker {
    @Autowired
    private RouteProperties routeProperties;
    private Map<String, Integer> lastRobinIndexMap = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Object> parallelLockMap = new ConcurrentHashMap<>();


    @Override
    public String getServerHost(String tokenAreaIdentify) {
        String serverHosts = routeProperties.getServerHosts().get(tokenAreaIdentify);
        if (StringUtils.isEmpty(serverHosts)) {
            return null;
        }
        String[] hosts = serverHosts.split(",");
        int getCurrentIndex = getCurrentIndex(tokenAreaIdentify, hosts.length);
        return hosts[getCurrentIndex];
    }

    private int getCurrentIndex(String tokenAreaIdentify, int hostsLength) {
        synchronized (getLoadingLock(tokenAreaIdentify)){
            Integer index = lastRobinIndexMap.computeIfAbsent(tokenAreaIdentify, k -> 0);
            Integer nextIndex = (index+1)%hostsLength;
            lastRobinIndexMap.put(tokenAreaIdentify,nextIndex);
            return index;
        }
    }

    private Object getLoadingLock(String tokenAreaIdentify) {
        if (tokenAreaIdentify == null) {
            return this;
        }
        return parallelLockMap.computeIfAbsent(tokenAreaIdentify, t -> new Object());

    }
}

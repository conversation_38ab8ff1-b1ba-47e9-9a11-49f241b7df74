package cn.hy.auth.custom.security.verifycode.listener;

import cn.hy.auth.custom.security.oauth2.event.PwdErrorEvent;
import cn.hy.auth.custom.security.verifycode.LoginFailedCounter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: PwdErrorEventListener
 * @time: 2022-04-16 14:41
 * @desc: 密码错误事件监听
 **/
@Slf4j
@Component
public class PwdErrorEventListener implements ApplicationListener<PwdErrorEvent> {

    @Autowired
    private LoginFailedCounter loginFailedCounter;

    @Override
    public void onApplicationEvent(PwdErrorEvent pwdErrorEvent) {
        String loginName = String.valueOf(pwdErrorEvent.getSource());
        log.debug("当前登录用户：{}", loginName);
        //获取用户密码输入错误事件，记录失败次数
        loginFailedCounter.addFailed(loginName);
    }
}

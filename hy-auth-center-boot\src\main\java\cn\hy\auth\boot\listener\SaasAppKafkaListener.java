package cn.hy.auth.boot.listener;

import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;
import cn.hy.auth.custom.security.oauth2.client.HyClientDetailServiceProvider;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 类描述：监听saas安装应用的主题，用于刷新更新缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2024/4/23 15:49
 **/
@Service
@Slf4j
public class SaasAppKafkaListener {
    private final MetaDataCacheService metaDataCacheService;
    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;
    private final AppAuthStrategyManager appAuthStrategyManager;
    public SaasAppKafkaListener(MetaDataCacheService metaDataCacheService, HyClientDetailServiceProvider hyClientDetailServiceProvider, AppAuthStrategyManager appAuthStrategyManager) {
        this.metaDataCacheService = metaDataCacheService;
        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;
        this.appAuthStrategyManager = appAuthStrategyManager;
    }

    @KafkaListener(topics = "${hy.saas.cluster-cache.topic:saas-sys-apps}",
            groupId = "#{authKafkaGroupIdUtil.buildSaasGroupId()}"
    )
    public void processAsyncTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional<Object> message = Optional.ofNullable(record.value());
        try {
            if (message.isPresent()) {
                String msg = (String)message.get();
                log.debug("消费了： Topic:{},Message:{}",record.topic(),msg);
                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);
                try {
                    // 清除各种缓存
                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());
                    if ("appmanage".equals(eventMsg.getAppCode())){
                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());
                    }else {
                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());
                    }
                    appAuthStrategyManager.clearCache(eventMsg.getLessCode());
                } catch (Exception e) {
                    log.warn("处理Kafka的应用安装消息{}_{},报错，可以忽略不处理。{}",eventMsg.getLessCode(),eventMsg.getAppCode(),e.getMessage());
                }
            }
        }finally {
            ack.acknowledge();
        }
    }
}

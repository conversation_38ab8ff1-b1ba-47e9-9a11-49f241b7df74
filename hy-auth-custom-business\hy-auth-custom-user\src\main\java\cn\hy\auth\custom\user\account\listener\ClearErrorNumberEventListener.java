package cn.hy.auth.custom.user.account.listener;

import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.common.security.core.authentication.validate.event.ClearErrorNumberEvent;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.mapper.DynamicUserAccountMapper;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@AllArgsConstructor
@Component
public class ClearErrorNumberEventListener implements ApplicationListener<ClearErrorNumberEvent> {

    @Autowired
    private List<ValidateErrorService> validateErrorServiceList;

    @Autowired
    private DynamicUserAccountMapper dynamicUserAccountMapper;

    @Autowired
    private final UserAccountService userAccountService;

    @Override
    public void onApplicationEvent(ClearErrorNumberEvent event) {
        String lesseeCode = event.getLesseeCode();
        String appCode = event.getAppCode();
        if ("paas".equals(lesseeCode) && "sys".equals(appCode)) {
            return;
        }
        String mobile = event.getMobile();
        String mobileCipherText = Objects.toString(event.getMobileCipherText(), "");
        String accountName = event.getAccountName();
        boolean needToCipher = StringUtils.isNotBlank(mobileCipherText);
        UserLoginAccountDTO userInfo = dynamicUserAccountMapper.getUserByUserNameOrPhoneLimitOne(accountName, needToCipher ? mobileCipherText : mobile);
        try {
            if (userInfo != null) {
                // 记录用户在线离线状态,先判断表是否存在
                if (userAccountService.checkOnLineStatusTableIsExist(lesseeCode, appCode)) {
                    userAccountService.insertOrUpdateUserOnlineStataus(Arrays.asList(userInfo.getUserAccountName()), UserOnLineStatus.ONLINE);
                }
                for (ValidateErrorService validateErrorService : validateErrorServiceList) {
                    validateErrorService.clearErrorTime(StringUtils.isNotBlank(mobile) ? mobile : Objects.toString(userInfo.getMobile(), ""),
                            userInfo.getUserAccountName(),
                            needToCipher ? mobileCipherText : "",
                            userInfo.getId());
                }
            }
        } catch (Exception e) {
            log.error("参数异常");
        }
    }
}

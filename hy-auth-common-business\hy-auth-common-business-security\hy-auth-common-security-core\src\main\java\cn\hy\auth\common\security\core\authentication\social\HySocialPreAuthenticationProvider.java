package cn.hy.auth.common.security.core.authentication.social;

import cn.hutool.core.lang.Assert;
import cn.hy.auth.common.security.core.authentication.common.ThirdLoginBeanFactory;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.enums.LoginType;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import cn.hy.auth.common.security.core.authentication.social.service.IThirdLoginAccount;
import cn.hy.auth.common.security.core.authentication.social.service.SocialUsersConnectionService;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * 社交登录认证提供者(预先校验处理)
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Data
@Slf4j
@Component
public class HySocialPreAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService myUserDetailsService;
    protected boolean hideUserNotFoundExceptions = false;
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    @Autowired
    private List<HyConnectionFactory> connectionFactories;
    @Autowired
    private SocialUsersConnectionService socialUsersConnectionService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        //这个authentication就是HySocialAuthenticationToken
        HySocialAuthenticationToken authenticationToken = (HySocialAuthenticationToken) authentication;
        String code = (String) authenticationToken.getPrincipal();
        String lesseeCode = authenticationToken.getLesseeCode();
        String appCode = authenticationToken.getAppCode();
        String loginType = authenticationToken.getLoginType();
        Map<String, Object> details = (Map<String, Object>) authenticationToken.getDetails();
        IThirdLoginAccount loginBean = ThirdLoginBeanFactory.getLoginBean(LoginType.getByCode(loginType));
        try {
            HyConnectionFactory hyConnectionFactory = getHyConnectionFactory(authenticationToken.getProviderId());
            ProviderUserInfo providerUserInfo = null;
            //自建应用appid和appSecrte等信息，就保存在auth_config中，未配置则使用全平台默认配置即可
            ProviderInfo providerInfo = hyConnectionFactory.getServiceProvider().getProviderInfoService().getProviderInfo(lesseeCode, appCode);
            // 1、获取自建应用的access_token
            ProviderAppAccessToken providerAppAccessToken = hyConnectionFactory.getServiceProvider().getOauth2Operations().getProviderAppAccessToken(lesseeCode, appCode, providerInfo, details);
            // 2. 获取第三方用户信息
            Assert.notNull(loginBean,LocaleUtil.getMessage("HySocialPreAuthenticationProvider.assert.msg1", null) + loginType + "!");
            if (loginType != null && loginType.equals(LoginType.THIRD_LINK.getCode())) {
                providerUserInfo = hyConnectionFactory.getServiceProvider().getSocialUserInfoApi().getProviderUserInfo(code, providerAppAccessToken, providerInfo);
            }else{
                providerUserInfo = hyConnectionFactory.getServiceProvider().getSocialUserInfoApi().getProviderUserInfo(lesseeCode, appCode, code, providerAppAccessToken, providerInfo,details);
            }
            //3. 获取登录账号信息
            String userAccount = loginBean.getUserAccount(code, lesseeCode, appCode, providerUserInfo, authenticationToken, providerAppAccessToken);
            if(StringUtils.isEmpty(userAccount)){
                throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HySocialPreAuthenticationProvider.result.msg1", null));
            }
            authenticationToken.setPrincipal(userAccount);
            return authenticationToken;
        } catch (UsernameNotFoundException ex) {
            if (hideUserNotFoundExceptions) {
                throw new BadCredentialsException(messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.badCredentials",
                        "Bad credentials"));
            } else {
                throw ex;
            }
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }
    }

    private HyConnectionFactory getHyConnectionFactory(String providerId) {
        if (CollectionUtils.isEmpty(connectionFactories)) {
            throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HySocialPreAuthenticationProvider.result.msg2", null));
        }
        HyConnectionFactory targetConnectionFactory = null;
        for (HyConnectionFactory connectionFactory : connectionFactories) {
            if (providerId.equals(connectionFactory.getProviderId())) {
                targetConnectionFactory = connectionFactory;
                break;
            }
        }
        if (targetConnectionFactory == null) {
            log.error("providerId:{},没有找到对应的ConnectionFactory", providerId);
            throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HySocialPreAuthenticationProvider.result.msg3", null));
        }
        return targetConnectionFactory;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        //该SmsCodeAuthenticationProvider智支持SmsCodeAuthenticationToken的token认证
        return HySocialAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public void setMyUserDetailsService(UserDetailsService myUserDetailsService) {
        this.myUserDetailsService = myUserDetailsService;
    }

    public void setHideUserNotFoundExceptions(boolean hideUserNotFoundExceptions) {
        this.hideUserNotFoundExceptions = hideUserNotFoundExceptions;
    }

    public List<HyConnectionFactory> getConnectionFactories() {
        return connectionFactories;
    }

    public void setConnectionFactories(List<HyConnectionFactory> connectionFactories) {
        this.connectionFactories = connectionFactories;
    }
}

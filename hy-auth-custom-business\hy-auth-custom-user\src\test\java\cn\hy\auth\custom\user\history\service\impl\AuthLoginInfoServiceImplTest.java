package cn.hy.auth.custom.user.history.service.impl;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.history.dao.UserLoginInfoMapper;
import cn.hy.auth.custom.user.history.domain.UserLoginInfoDO;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 类描述: 查询历史登录信息测试类
 *
 * <AUTHOR>
 * @date ：创建于 2021/2/1
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {AuthLoginInfoServiceImpl.class})
public class AuthLoginInfoServiceImplTest {

    @Autowired
    private AuthLoginInfoService authLoginInfoService;

    @MockBean
    private UserLoginInfoMapper userLoginInfoMapper;

    @MockBean
    private UserAccountService userAccountService;

    @Test
    public void insert() {
        UserLoginInfoDTO record = UserLoginInfoDTO.builder().id(1L).build();
        authLoginInfoService.insert(record);
    }

    @Test
    public void selectByPrimaryKey() {
        UserLoginInfoDTO record = UserLoginInfoDTO.builder().id(1L).build();
        authLoginInfoService.selectByPrimaryKey(record.getId());
        Mockito.verify(userLoginInfoMapper, Mockito.times(1)).selectByPrimaryKey(record.getId());
    }

    @Test
    public void getLastSuccessLoginInfo() {
        String clientId = "test";
        String userName = "admin";
       // Mockito.when(userAccountService.getUserIdByUserName(userName)).thenReturn(1L);

        List<UserLoginInfoDO> array = new ArrayList<>();
        array.add(UserLoginInfoDO.builder().id(2L).createTime(new Date()).build());
        array.add(UserLoginInfoDO.builder().id(3L).createTime(new Date()).build());
        Mockito.when(userLoginInfoMapper.getLastTwoSuccessLoginInfo(clientId, String.valueOf(1L))).thenReturn(array);
        //authLoginInfoService.getLastSuccessLoginInfo(clientId, userName);
    }

    @Test
    public void getMaxSequence() {
        authLoginInfoService.getMaxSequence();
        Mockito.verify(userLoginInfoMapper, Mockito.times(1)).getMaxSequence();
    }
}
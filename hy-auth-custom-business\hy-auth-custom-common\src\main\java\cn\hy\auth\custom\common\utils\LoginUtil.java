package cn.hy.auth.custom.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

import java.util.Map;

/**
 * 类描述: 用户登录信息获取类
 *
 * <AUTHOR>
 * @date :创建于 2020/11/18
 */
@Slf4j
public class LoginUtil {

    private LoginUtil() {

    }

    /**
     * 登录账号
     */
    private static final String USER_NAME = "username";

    /**
     * 客户端标识
     */
    private static final String CLIENT_ID = "client_id";

    /**
     * 获取当前登录用户登录名
     *
     * @return 返回当前登录名称
     */
    public static String getLoginName() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUserName = getUserNameAfterLogin(authentication);

            if (StringUtils.isNotBlank(currentUserName)) {
                return currentUserName;
            }
            return getUserNameByLogging(authentication);
        } catch (Exception e) {
            log.error("获取当前登录名失败：[{}]", e.getMessage(), e);
        }
        return StringUtils.EMPTY;
    }
    /**
     * 获取当前登录用户主键
     *
     * @return 返回当前登录用户主键
     */
    public static Long getLoginUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
           return getUserIdAfterLogin(authentication);
        } catch (Exception e) {
            log.error("获取当前登录用户id：[{}]", e.getMessage(), e);
        }
        return null;
    }

    private static Long getUserIdAfterLogin(Authentication authentication) {
        if (authentication instanceof OAuth2Authentication) {
            Authentication userAuthentication = ((OAuth2Authentication) authentication).getUserAuthentication();
            if (ObjectUtil.isNotNull(userAuthentication)) {
                Object principal = userAuthentication.getPrincipal();
                if (principal instanceof HyUserDetails) {
                   HyUserDetails hyUserDetails = (HyUserDetails) principal;
                    log.debug("登录用户名：{}", hyUserDetails.getUserId());
                    return hyUserDetails.getUserId();
                }
            }
        }
      return null;
    }

    /**
     * 登录之后从认证信息中获取客户端标识
     *
     * @return 返回客户端标识
     */
    public static String getClientId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof OAuth2Authentication) {
            Authentication userAuthentication = ((OAuth2Authentication) authentication).getUserAuthentication();
            if (ObjectUtil.isNotNull(userAuthentication)) {
                Object detail = userAuthentication.getDetails();
                if (detail instanceof Map) {
                    String clientId = MapUtils.getString((Map) detail, CLIENT_ID);
                    log.debug("客户端标识client：{}", clientId);
                    return clientId;
                }
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 登录之后从认证信息中获取
     *
     * @param authentication 认证对象
     * @return 返回登录名
     */
    private static String getUserNameAfterLogin(Authentication authentication) {
        if (authentication instanceof OAuth2Authentication) {
            Authentication userAuthentication = ((OAuth2Authentication) authentication).getUserAuthentication();
            String currentUserName=StringUtils.EMPTY;
            if (ObjectUtil.isNotNull(userAuthentication)) {
                Object detail = userAuthentication.getDetails();
                if (detail instanceof Map) {
                     currentUserName = MapUtils.getString((Map) detail, USER_NAME);
                    log.debug("登录用户名：{}", currentUserName);
                }
                if (StringUtils.isBlank(currentUserName)){
                    Object principal = userAuthentication.getPrincipal();
                    if (principal instanceof HyUserDetails) {
                        HyUserDetails hyUserDetails = (HyUserDetails) principal;
                        log.debug("登录用户名：{}", hyUserDetails.getUserId());
                        currentUserName = hyUserDetails.getUsername();
                    }
                }
                return currentUserName;
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 登录中从认证信息中获取
     *
     * @param authentication 认证对象
     * @return 返回登录名
     */
    private static String getUserNameByLogging(Authentication authentication) {
        if (authentication != null && !(authentication instanceof AnonymousAuthenticationToken)) {
            Object detail = authentication.getDetails();
            if (detail instanceof Map) {
                String currentUserName = MapUtils.getString((Map) detail, USER_NAME);
                log.debug("登录用户名：{}", currentUserName);
                return currentUserName;
            }
        }
        return StringUtils.EMPTY;
    }

}

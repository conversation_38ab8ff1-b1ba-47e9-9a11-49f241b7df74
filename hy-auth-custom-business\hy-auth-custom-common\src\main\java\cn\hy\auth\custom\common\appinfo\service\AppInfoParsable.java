package cn.hy.auth.custom.common.appinfo.service;

import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import org.apache.commons.lang3.tuple.ImmutablePair;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求参数解析器
 * 用于解析不同请求中的租户、应用编码信息
 *
 * <AUTHOR>
 * @date 2020-12-13 09:50
 **/
public interface AppInfoParsable {

    /**
     * 解析得到租户、应用编码信息已经请求分类
     *
     * @param request 请求信息
     * @return 第一个属性：租户、应用编码；第二个属性：请求类型
     */
    ImmutablePair<AppInfoDTO, RequestTypeEnum> parse(HttpServletRequest request);

    /**
     * 解析请求类型信息
     *
     * @param request 请求信息
     * @return 请求分类
     */
    RequestTypeEnum parseType(HttpServletRequest request);
}

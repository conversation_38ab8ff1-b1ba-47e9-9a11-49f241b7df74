package cn.hy.auth.custom.multi.authinfo.service;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthLoginFieldDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.multi.authinfo.dao.AppAuthStrategyDao;
import cn.hy.auth.custom.multi.event.AppAuthInfoLoadEvent;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.IbatisException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020-11-30 16:18
 **/
@Service
@Slf4j
public class AppAuthStrategyManagerImpl implements AppAuthStrategyManager {
    private static final String CACHE_NAME = "auth_info";
    private static final String CACHE_KEY = "rule";

    /**
     * 缓存管理类
     */
    private final CacheManager cacheManager;
    private final AppAuthStrategyDao appAuthInfoDao;
    private final ApplicationContext applicationContext;
    private final ConcurrentHashMap<String, Object> parallelLockMap = new ConcurrentHashMap<>();

    public AppAuthStrategyManagerImpl(@Qualifier("hyAuthSystemConfigCacheManager") CacheManager cacheManager, AppAuthStrategyDao appAuthInfoDao, ApplicationContext applicationContext) {
        this.cacheManager = cacheManager;
        this.appAuthInfoDao = appAuthInfoDao;
        this.applicationContext = applicationContext;
    }

    /**
     * 获取应用对应的认证规则对象
     *
     * @return 认证规则对象
     */
    @Override
    public AppAuthStrategyDTO get() {
        if (StringUtils.isBlank(AuthContext.getContext().getLesseeCode()) ||
                StringUtils.isBlank(AuthContext.getContext().getAppCode())) {
            log.info("租户编码或应用编码为空，跳过加载应用的认证规则数据逻辑");
            return new AppAuthStrategyDTO();
        }

        AppAuthStrategyDTO info = getFromCache();
        if (Objects.nonNull(info)) {
            return info;
        }

        StringBuilder errorMsg = new StringBuilder();
        String key = AuthContext.getContext().getLesseeCode() + "_" + AuthContext.getContext().getAppCode();
        synchronized (getLock(key)) {
            info = getFromCache();
            if (info == null) {
                info = getFromDb(errorMsg);
                log.debug("从数据库加载的初始化认证策略数据：{}", info);
                if (Objects.nonNull(info)) {
                    addToCache(info);
                }
            }
            removeLock(key);
        }
        if (Objects.nonNull(info)) {
            return info;
        }
        // fix QX202404090015,不显示数据库方面的信息
        String msg = LocaleUtil.getMessage("AppAuthStrategyManagerImpl.other.msg2", "应用编码错误，无法加载应用的认证配置信息,租户编码：【") + AuthContext.getContext().getLesseeCode() + LocaleUtil.getMessage("AppAuthStrategyManagerImpl.other.msg3", "】，应用编码：【") +
                AuthContext.getContext().getAppCode() + "】。";
        //打印异常
        log.error(msg + errorMsg);
        throw new AuthBusinessException(AuthErrorCodeEnum.B0301.code(), msg);
    }

    /**
     * 从数据库加载认证规则数据
     *
     * @return 认证规则对象
     */
    private AppAuthStrategyDTO getFromDb(StringBuilder errorMsg) {
        String json = null;
        try {
            String lesseeCode = AuthContext.getContext().getLesseeCode();
            String appCode = AuthContext.getContext().getAppCode();
            log.debug("租户编码【{}】,应用编码【{}】从数据库加载认证规则。", lesseeCode, appCode);
            json = appAuthInfoDao.getAppAuthStrategyJson();
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            String msg = createMsg(e);
            errorMsg.append(msg);
        }
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            AppAuthStrategyDTO authInfoDTO = JSON.parseObject(json, AppAuthStrategyDTO.class);
            removeErrorLoginRuleField(authInfoDTO);
            // 覆盖不使用配置里的租户号和应用号。解决复制应用时会没有修改到这个地方，导致登录报错。
            authInfoDTO.setLesseeAccessName( AuthContext.getContext().getLesseeCode());
            authInfoDTO.setAppAccessName( AuthContext.getContext().getAppCode());
            applicationContext.publishEvent(new AppAuthInfoLoadEvent(authInfoDTO));
            return authInfoDTO;
        } catch (Exception e) {
            String lesseeCode = AuthContext.getContext().getLesseeCode();
            String appCode = AuthContext.getContext().getAppCode();
            log.warn("租户编码【{}】,应用编码【{}】对应认证规则解析失败。", lesseeCode, appCode, e);
            return null;
        }
    }

    private void removeErrorLoginRuleField(AppAuthStrategyDTO authInfoDTO) {
        if (!CollectionUtils.isEmpty(authInfoDTO.getLoginRule().getLoginField())) {
            List<AppAuthLoginFieldDTO> loginField = new ArrayList<>();
            for (int i = 0; i < authInfoDTO.getLoginRule().getLoginField().size(); i++) {
                String field = authInfoDTO.getLoginRule().getLoginField().get(i).getFiled();
                try {
                    appAuthInfoDao.checkFieldInUserAccount(field);
                    loginField.add(authInfoDTO.getLoginRule().getLoginField().get(i));
                } catch (Exception e) {
                    String lesseeCode = AuthContext.getContext().getLesseeCode();
                    String appCode = AuthContext.getContext().getAppCode();
                    log.warn("租户编码【{}】,应用编码【{}】,在user_account表不存在登录字段【{}】,不启用该登录字段. {}", lesseeCode, appCode, field, e.getMessage());
                }
            }
            authInfoDTO.getLoginRule().setLoginField(loginField);
        }
    }

    private String createMsg(Exception e) {
        // fix QX202404090015
        if (e instanceof IbatisException || e instanceof SQLException || e instanceof DataAccessException) {
            return "数据库层错误.";
        }
        return e.getMessage();
    }

    /**
     * 从缓存加载认证规则对象
     *
     * @return 如果不存在于缓存，则返回null
     */
    private AppAuthStrategyDTO getFromCache() {
        Cache cache = getCache(AuthContext.getContext().getLesseeCode(), AuthContext.getContext().getAppCode());
        Optional<AppAuthStrategyDTO> opData = Optional.ofNullable(cache).map(c -> c.get(CACHE_KEY, AppAuthStrategyDTO.class));
        return opData.orElse(null);
    }

    /**
     * 清空缓存信息
     *
     * @return .
     */
    @Override
    public boolean removeFromCache(String lesseeCode, String appCode) {
        Cache cache = getCache(lesseeCode, appCode);
        if (cache != null) {
            log.info("清空了缓存信息,cache {}", cache.getName());
            cache.clear();
            return true;
        }
        return false;
    }

    @Override
    public void clearCache(String lessCode) {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cacheNames)) {
            for (String cacheName : cacheNames) {
                if (cacheName.contains(CACHE_NAME) && cacheName.contains(lessCode)) {
                    log.info("清空了缓存数据。{}", cacheName);
                    Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
                }
            }
        }
    }

    /**
     * 将认证规则对象增加到缓存中
     *
     * @param infoDTO 认证规则对象
     */
    private void addToCache(AppAuthStrategyDTO infoDTO) {
        Optional.ofNullable(getCache(AuthContext.getContext().getLesseeCode(), AuthContext.getContext().getAppCode())).ifPresent(cache -> cache.put(CACHE_KEY, infoDTO));
    }

    private Cache getCache(String lesseeCode, String appCode) {
        //租户编码
        //String lesseeCode = AuthContext.getContext().getLesseeCode();
        //应用编码
        //String appCode = AuthContext.getContext().getAppCode();
        StringJoiner stringJoiner = new StringJoiner("_");
        stringJoiner.add(lesseeCode).add(appCode);
        String cacheName = stringJoiner + "_" + CACHE_NAME;
        log.debug("AppAuthStrategyManagerImpl cacheName: {}", cacheName);
        Boolean autoAttachCachePrefix = CachePrefixUtil.isAutoAttachCcahePrefix();
        try {
            CachePrefixUtil.setAutoAttachCachePrefix(false);
            return cacheManager.getCache(cacheName);
        } finally {
            // 恢复原样
            CachePrefixUtil.setAutoAttachCachePrefix(autoAttachCachePrefix);
        }
    }

    private Object getLock(String identify) {
        // 线程安全
        return parallelLockMap.computeIfAbsent(identify, t -> new Object());

    }

    private void removeLock(String identify) {
        // 线程安全
        parallelLockMap.remove(identify);
    }

}

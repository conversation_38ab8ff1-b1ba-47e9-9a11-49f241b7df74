package cn.hy.auth.common.business.oauth2.starter;

import cn.hy.auth.common.security.oauth2.token.store.HyInMemoryTokenStore;
import cn.hy.auth.common.security.oauth2.token.store.HyRedisLocalCacheTokenStore;
import cn.hy.auth.common.security.oauth2.token.store.HyRedisTokenStore;
import cn.hy.auth.common.security.oauth2.token.store.JdbcWithMemoryTokenStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.DefaultAuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.InMemoryTokenStore;
import org.springframework.security.oauth2.provider.token.store.JdbcTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.sql.DataSource;

/**
 * 类描述: token内存切换
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/23
 */
@Slf4j
@Configuration
public class TokenAutoConfiguration {
    /**
     * -1 表示不启用
     */
    @Value("${auth.token.store.removingDelta:30000}")
    private int removingDelta = 30 * 1000;
    /**
     * 创建TokenStore bean 内存模式
     *
     * @return 返回TokenStore
     */
    @Bean
    @ConditionalOnMissingBean(TokenStore.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "jvm",
            matchIfMissing = true
    )
    public TokenStore jvmTokenStore(AuthenticationKeyGenerator authenticationKeyGenerator) {
        if (this.removingDelta > 0){
            log.debug("使用内存方式缓存token信息,启用HyInMemoryTokenStore实例对象。removingDelta：【{}】ms",this.removingDelta);
            HyInMemoryTokenStore tokenStore = new HyInMemoryTokenStore();
            tokenStore.setRemovingDelta(removingDelta);
            tokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
            return tokenStore;
        } else {
            log.debug("使用内存方式缓存token信息,启用InMemoryTokenStore实例对象。removingDelta：【{}】ms",this.removingDelta);
            InMemoryTokenStore tokenStore = new InMemoryTokenStore();
            tokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
            return tokenStore;
        }
    }

    /**
     * 创建TokenStore bean 数据库模式
     *
     * @return 返回TokenStore
     */
    @Bean
    @ConditionalOnMissingBean(TokenStore.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "jdbc"
    )
    public TokenStore jdbcTokenStore(DataSource dataSource, AuthenticationKeyGenerator authenticationKeyGenerator) {
        log.debug("使用数据库方式缓存token信息");
        JdbcTokenStore jdbcTokenStore = new JdbcTokenStore(dataSource);
        jdbcTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
        return jdbcTokenStore;
    }

    /**
     * 创建TokenStore bean redis模式
     *
     * @param redisConnectionFactory redisConnectionFactory
     * @return 返回TokenStore
     */
    @Bean
    @ConditionalOnMissingBean(TokenStore.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "redis"
    )
    public TokenStore redisTokenStore(RedisConnectionFactory redisConnectionFactory, AuthenticationKeyGenerator authenticationKeyGenerator) {
        if (this.removingDelta > 0){
            log.debug("使用redis缓存token信息,启用HyRedisTokenStore实例对象。removingDelta：【{}】ms",this.removingDelta);
            HyRedisTokenStore redisTokenStore = new HyRedisTokenStore(redisConnectionFactory);
            redisTokenStore.setRemovingDelta(removingDelta);
            redisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
            return redisTokenStore;
        } else {
            log.debug("使用redis方式缓存token信息,启用RedisTokenStore实例对象。removingDelta：【{}】ms",this.removingDelta);
            RedisTokenStore redisTokenStore = new RedisTokenStore(redisConnectionFactory);
            redisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
            return redisTokenStore;
        }
    }


    /**
     * 创建TokenStore bean redis和cache2级缓存模式
     *
     * @param redisConnectionFactory redisConnectionFactory
     * @return 返回TokenStore
     */
    @Bean
    @ConditionalOnMissingBean(TokenStore.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "redisLocalCache"
    )
    public TokenStore redisLoaclCacheTokenStore(RedisConnectionFactory redisConnectionFactory, AuthenticationKeyGenerator authenticationKeyGenerator) {
        log.debug("使用redisLocalCache 2级缓存token信息,启用HyRedisMemoryTokenStore实例对象。removingDelta：【{}】ms",this.removingDelta);
        HyRedisLocalCacheTokenStore redisTokenStore = new HyRedisLocalCacheTokenStore(redisConnectionFactory);
        redisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
        redisTokenStore.setRemovingDelta(removingDelta);
        return redisTokenStore;
    }



    /**
     * 创建TokenStore bean redis模式  数据库+内存
     *
     * @param dataSource dataSource
     * @return 返回TokenStore
     */
    @Bean
    @ConditionalOnMissingBean(TokenStore.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "cacheJdbc"
    )
    public TokenStore cacheJdbcTokenStore(DataSource dataSource, AuthenticationKeyGenerator authenticationKeyGenerator) {
        log.debug("使用数据库+内存方式缓存token信息");
        JdbcWithMemoryTokenStore jdbcWithMemoryTokenStore = new JdbcWithMemoryTokenStore(dataSource);
        jdbcWithMemoryTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
        if (this.removingDelta > 0){
            log.debug("使用数据库+内存方式 HyInMemoryTokenStore 缓存token信息。removingDelta：【{}】ms",this.removingDelta);
            HyInMemoryTokenStore tokenStore = new HyInMemoryTokenStore();
            tokenStore.setRemovingDelta(removingDelta);
            jdbcWithMemoryTokenStore.setInMemoryTokenStore(tokenStore);
        }
        return jdbcWithMemoryTokenStore;
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationKeyGenerator defaultAuthenticationKeyGenerator() {
        return new DefaultAuthenticationKeyGenerator();
    }
}

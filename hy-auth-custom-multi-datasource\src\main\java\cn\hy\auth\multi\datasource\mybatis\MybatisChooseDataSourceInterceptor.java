package cn.hy.auth.multi.datasource.mybatis;

import cn.hy.dataengine.context.DataEngineContextProxy;
import cn.hy.dataengine.metadata.RedirectBlacklist;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.common.MetaDataEngineContext;
import cn.hy.paas.multi.datasource.SqlParserUtil;
import cn.hy.paas.multi.datasource.config.MultiDataSourceConfig;
import cn.hy.paas.multi.datasource.service.DataSourceChooser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 类描述: 配置平台表名重定向
 *
 * <AUTHOR>
 * @date ：创建于 2022/5/31
 */
@Intercepts({@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
@Component
@Slf4j
@ConditionalOnBean(MultiDataSourceConfig.class)
public class MybatisChooseDataSourceInterceptor implements Interceptor {
    private final RedirectBlacklist redirectBlacklist;
    private final TableRelocateUtil tableRelocateUtil;
    private final ApplicationContext applicationContext;
    public MybatisChooseDataSourceInterceptor(RedirectBlacklist redirectBlacklist, TableRelocateUtil tableRelocateUtil, ApplicationContext applicationContext) {
        this.redirectBlacklist = redirectBlacklist;
        this.tableRelocateUtil = tableRelocateUtil;
        this.applicationContext = applicationContext;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        chooseDataSource(invocation);
        return invocation.proceed();
    }

    private void chooseDataSource(Invocation invocation){
        DataSourceChooser customDataSourceChooser = applicationContext.getBean(DataSourceChooser.class);
        if (!customDataSourceChooser.isUseDataSource()){
            return;
        }
        Object[] args = invocation.getArgs();
        //获取MappedStatement对象
        MappedStatement ms = (MappedStatement) args[0];
        //获取传入sql语句的参数对象
        Object parameterObject = args[1];
        BoundSql boundSql = ms.getBoundSql(parameterObject);
        //获取到拥有占位符的sql语句
        String sql = boundSql.getSql();
        List<String> tableNames = SqlParserUtil.getAllTableNamesBySql(sql);
        // 为元数据的jpa选择数据源
        log.debug("为元数据的jpa选择数据源");
        customDataSourceChooser.chooseDataSourceByLessCodeAndAppCode(MetaDataEngineContext.getContext().getLesseeCode(),MetaDataEngineContext.getContext().getAppCode());
        tableNames = tableNames.stream().map(t ->{
            if (this.redirectBlacklist == null || this.redirectBlacklist.isNeedRedirect(t)) {
                return this.tableRelocateUtil.tableNameRedirect(t);
            }
            return t;
        }).collect(Collectors.toList());
        log.debug("为mybatis选择数据源");
        customDataSourceChooser.chooseDataSourceByTableName(tableNames,MetaDataEngineContext.getContext().getLesseeCode(),MetaDataEngineContext.getContext().getAppCode());
    }



    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        //留空
    }


}

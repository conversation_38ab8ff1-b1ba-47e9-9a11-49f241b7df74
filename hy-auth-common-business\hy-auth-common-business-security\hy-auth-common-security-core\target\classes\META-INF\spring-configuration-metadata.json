{"groups": [{"name": "hy.security", "type": "cn.hy.auth.common.security.core.properties.SecurityProperties", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.web.ignore", "type": "cn.hy.auth.common.security.core.properties.WebIgnoreProperties", "sourceType": "cn.hy.auth.common.security.core.properties.WebIgnoreProperties"}], "properties": [{"name": "hy.security.form-auth", "type": "cn.hy.auth.common.security.core.properties.FormAuthProperties", "description": "账号密码的表单登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.hy-face-login-properties", "type": "cn.hy.auth.common.security.core.properties.HyFaceLoginProperties", "description": "hy 人脸登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.hy-fky-properties", "type": "cn.hy.auth.common.security.core.properties.HyExternalProperties", "description": "hy 第三方绑定登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.hy-social-properties", "type": "cn.hy.auth.common.security.core.properties.HySocialProperties", "description": "hy 第三方绑定登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.re-authentication-properties", "type": "cn.hy.auth.common.security.core.properties.ReAuthenticationProperties", "description": "二次认证", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.response", "type": "cn.hy.auth.common.security.core.properties.SecurityResponseFormatProperties", "description": "类描述：spring security 类型登录后响应的格式配置", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.sms-auth", "type": "cn.hy.auth.common.security.core.properties.SmsCodeProperties", "description": "手机验证码的登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.social", "type": "cn.hy.auth.common.security.core.properties.SocialProperties", "description": "社交的登录", "sourceType": "cn.hy.auth.common.security.core.properties.SecurityProperties"}, {"name": "hy.security.web.ignore.pattern", "type": "java.util.List<java.lang.String>", "description": "需要忽略的 URL 格式，不考虑请求方法", "sourceType": "cn.hy.auth.common.security.core.properties.WebIgnoreProperties"}], "hints": []}
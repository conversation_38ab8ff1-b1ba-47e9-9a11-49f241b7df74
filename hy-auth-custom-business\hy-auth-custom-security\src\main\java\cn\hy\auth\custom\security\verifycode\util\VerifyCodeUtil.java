package cn.hy.auth.custom.security.verifycode.util;

import cn.hy.auth.custom.common.context.AuthContext;

import java.util.Objects;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: VerifyCodeUtil
 * @time: 2023-01-12 16:31
 * @desc: 验证码工具类
 **/
public class VerifyCodeUtil {

    /**
     * 获取完整的登录用户键：租户编号_应用编号_登录账号
     * @param loginName 登录账号
     * @return 结果
     */
    public static String getFullLoginNameKey(String loginName) {
        AuthContext context = AuthContext.getContext();
        if (Objects.nonNull(context)) {
            return context.getLesseeCode() + "_" + context.getAppCode() + "_" + loginName;
        }
        return loginName;
    }
}

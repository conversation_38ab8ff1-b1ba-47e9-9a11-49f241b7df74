package cn.hy.auth.custom.route.service.route.impl;

import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.route.service.route.SimpleHostRoutingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.http.*;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.commons.httpclient.ApacheHttpClientConnectionManagerFactory;
import org.springframework.cloud.commons.httpclient.ApacheHttpClientFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 新版的实现，解决在K8S环境转发到2.x认证中心问题
 * <AUTHOR>
 * @date 2024-10-15
 */
@Slf4j
@Component
@ConditionalOnProperty(
        name = "hy.security.oauth2.route.type",
        havingValue = "new",
        matchIfMissing = true
)
public class SimpleHostRoutingServiceNewImpl implements SimpleHostRoutingService {
    private final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1,
            new BasicThreadFactory.Builder().namingPattern("SimpleHostRoutingService.connectionManagerTimer-%d").daemon(true).build());


    private boolean sslHostnameValidationEnabled = false;


    private ProxyRequestHelper helper;
    private RouteProperties.HostConnectionProperties hostProperties;


    private ApacheHttpClientConnectionManagerFactory connectionManagerFactory;

    private ApacheHttpClientFactory httpClientFactory;

    private HttpClientConnectionManager connectionManager;

    private CloseableHttpClient httpClient;

    @Value("${hy.security.oauth2.route.getRequest: /ims-idms/auth/oauth/IUserInfo,/ims-idms/usersession/getUserInfoByCode}")
    private Set<String> getRequestUrl;



    public SimpleHostRoutingServiceNewImpl(ProxyRequestHelper helper, RouteProperties routeProperties,
                                           ApacheHttpClientConnectionManagerFactory connectionManagerFactory,
                                           ApacheHttpClientFactory httpClientFactory) {
        this.helper = helper;
        this.hostProperties = routeProperties.getHostConnection();
        this.connectionManagerFactory = connectionManagerFactory;
        this.httpClientFactory = httpClientFactory;
        checkServletVersion();
    }


    @PostConstruct
    private void initialize() {
        this.connectionManager = newConnectionManager();
        this.httpClient = newClient();
        executorService.scheduleAtFixedRate(() -> {
            if (SimpleHostRoutingServiceNewImpl.this.connectionManager == null) {
                return;
            }
            SimpleHostRoutingServiceNewImpl.this.connectionManager
                    .closeExpiredConnections();
        }, 30000, 5000, TimeUnit.MILLISECONDS);
    }

    @PreDestroy
    public void stop() {
        this.executorService.shutdownNow();
    }


    @Override
    public HttpResponse run(HttpServletRequest request, String hostUrl, String uri) throws Exception {
        MultiValueMap<String, String> headers = this.helper.buildRequestHeaders(request);
        // 解决在 K8s 环境下转发到2.x认证中心问题，转发地址会使用 HOST 头，所以移除 HOST
        headers.remove(HttpHeaders.HOST);

        MultiValueMap<String, String> queryParams = this.helper.buildRequestQueryParams(request);
        String verb= getVerb(request);
        verb= isMatch(getRequestUrl,uri,verb);
        Map<String, String[]> parameterMap = request.getParameterMap();

        CloseableHttpResponse response = forward(this.httpClient, verb, hostUrl, uri, request, headers, queryParams, parameterMap);
        response.removeHeaders(HttpHeaders.TRANSFER_ENCODING);
        return response;
    }

    private String isMatch(Set<String> getRequestUrl, String uri,String vert) {
        for(String requestUrl: getRequestUrl){
            if(uri.contains(requestUrl)){
                 return HttpGet.METHOD_NAME;
            }
        }
        return vert;
    }


    protected void checkServletVersion() {
        // To support Servlet API 3.1 we need to check if getContentLengthLong exists
        // Spring 5 minimum support is 3.0, so this stays
        try {
            HttpServletRequest.class.getMethod("getContentLengthLong");
        } catch (NoSuchMethodException e) {
            //
        }
    }


    protected HttpClientConnectionManager newConnectionManager() {
        return connectionManagerFactory.newConnectionManager(
                !this.sslHostnameValidationEnabled,
                this.hostProperties.getMaxTotalConnections(),
                this.hostProperties.getMaxPerRouteConnections(),
                this.hostProperties.getTimeToLive(), this.hostProperties.getTimeUnit(),
                null);
    }

    protected CloseableHttpClient newClient() {
        final RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(
                        this.hostProperties.getConnectionRequestTimeoutMillis())
                .setSocketTimeout(this.hostProperties.getSocketTimeoutMillis())
                .setConnectionRequestTimeout(this.hostProperties.getConnectionRequestTimeoutMillis())
                .setConnectTimeout(this.hostProperties.getConnectTimeoutMillis())
                .setCookieSpec(CookieSpecs.IGNORE_COOKIES).build();
        return httpClientFactory.createBuilder().setDefaultRequestConfig(requestConfig)
                .setConnectionManager(this.connectionManager).disableRedirectHandling()
                .build();
    }

    /**
     *  .
     * @param httpclient .
     * @param verb 请求方式
     * @param hostUrl host地址
     * @param uri 完整地址
     * @param request .
     * @param headers 头
     * @param queryparams ?a=b&c=d 的参数
     * @param parameterMap form表单的参数
     * @return .
     * @throws Exception .
     */
    private CloseableHttpResponse forward(CloseableHttpClient httpclient, String verb,
                                          String hostUrl, String uri, HttpServletRequest request, MultiValueMap<String, String> headers,
                                          MultiValueMap<String, String> queryparams,Map<String, String[]> parameterMap)
            throws Exception {

        HttpHost httpHost = getHttpHost(hostUrl);

        HttpRequest httpRequest = buildHttpRequest(verb, uri,  headers,parameterMap,request);

        logRequestInfo(verb, uri,  headers, queryparams,parameterMap);
        return forwardRequest(httpclient, httpHost, httpRequest);

    }

    private void logRequestInfo(String verb, String uri,MultiValueMap<String, String> headers,
                                MultiValueMap<String, String> queryParams, Map<String, String[]> parameterMap) {
        log.info("method = 【{}】,url = 【{}】,queryParams = 【{}】,parameterMap = 【{}】,headers = 【{}】",
                verb, uri, queryParams,parameterMap, headers);

    }

    protected HttpRequest buildHttpRequest(String verb, String uri,
                                           MultiValueMap<String, String> headers,
                                           Map<String, String[]> parameterMap,
                                           HttpServletRequest request) throws UnsupportedEncodingException  {
        HttpRequest httpRequest;
        String encodedQueryString = getEncodedQueryString(request);

        String uriWithQueryString = uri + (uri.contains("?") ? "&":"?")+encodedQueryString;

        switch (verb.toUpperCase()) {
            case "POST":
                HttpPost httpPost = new HttpPost(uriWithQueryString);
                httpRequest = httpPost;
                httpPost.setEntity(createHttpEntity(parameterMap));
                break;
            case "PUT":
                HttpPut httpPut = new HttpPut(uriWithQueryString);
                httpRequest = httpPut;
                httpPut.setEntity(createHttpEntity(parameterMap));
                break;
            case "PATCH":
                HttpPatch httpPatch = new HttpPatch(uriWithQueryString);
                httpRequest = httpPatch;
                httpPatch.setEntity(createHttpEntity(parameterMap));
                break;
            case "DELETE":
                BasicHttpEntityEnclosingRequest entityRequest = new BasicHttpEntityEnclosingRequest(
                        verb, uriWithQueryString);
                httpRequest = entityRequest;
                entityRequest.setEntity(createHttpEntity(parameterMap));
                break;
            default:
                httpRequest = new BasicHttpRequest(verb, uriWithQueryString);
                log.debug(uriWithQueryString);
        }

        httpRequest.setHeaders(convertHeaders(headers));
        return httpRequest;
    }


    private HttpEntity createHttpEntity( Map<String, String[]> parameterMap) throws UnsupportedEncodingException {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        parameterMap.forEach((k,v)->{if (v.length>0){nameValuePairs.add(new BasicNameValuePair(k,v[0]));}});

        return new UrlEncodedFormEntity(nameValuePairs);

    }
    private String getEncodedQueryString(HttpServletRequest request) {
        String query = request.getQueryString();
        return (query != null) ? query : "";
    }


    private Header[] convertHeaders(MultiValueMap<String, String> headers) {
        List<Header> list = new ArrayList<>();
        for (String name : headers.keySet()) {
            for (String value : headers.get(name)) {
                list.add(new BasicHeader(name, value));
            }
        }
        return list.toArray(new BasicHeader[0]);
    }

    private CloseableHttpResponse forwardRequest(CloseableHttpClient httpclient,
                                                 HttpHost httpHost, HttpRequest httpRequest) throws IOException {

        return httpclient.execute(httpHost, httpRequest);
    }

    private HttpHost getHttpHost(String hostUri) {
        return HttpHost.create(hostUri);
    }


    private String getVerb(HttpServletRequest request) {
        String sMethod = request.getMethod();
        return sMethod.toUpperCase();
    }


}

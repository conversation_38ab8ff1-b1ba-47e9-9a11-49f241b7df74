package cn.hy.auth.custom.user.utils;

import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.common.utils.StrUtil;
import cn.hy.auth.custom.user.account.service.impl.UserAccountServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 密码加密工具类
 *
 * <AUTHOR>
 * @date 2020/12/18 17:41
 **/
@Slf4j
public class PasswordAesUtil {
    private PasswordAesUtil() {

    }

    /**
     * 解析密码，解析后仍然是加密密码。
     *
     * @param passwordWithName 现在密码加密格式是：encode（密码+用户名）。
     * @param loginName        用户名
     * @return 剥离用户名的密码加密字符串
     */
    private static String extractPwd(String passwordWithName, String loginName) {
        return removeUserName(loginName, passwordWithName, EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION);
    }

    /**
     * 解析密码，解析后仍然是加密密码。
     * 如果是用户名+密码的加密方式，则解密后，提取密码串，再加密返回
     * 如果是密码密文，则直接放回
     * 如果是密码明文，也直接放回
     *
     * @param srcPassWord 源密码字符串
     * @param loginName   用户名
     * @return 剥离用户名的密码加密字符串
     */
    public static String extractPwd(String srcPassWord, String loginName, EncryptTypeEnum encryptType) {
        if (EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION.equals(encryptType)) {
            //帐号名明文+密码明文加密
            return removeUserName(loginName, srcPassWord, encryptType);
        } else if (EncryptTypeEnum.PASSWORD_ENCRYPTION.equals(encryptType)) {
            //密码明文加密
            return srcPassWord;
        } else {
            //密码明文
            return AesUtil.encrypt(srcPassWord, AesUtil.DEFAULT_KEY);
        }
    }


    /**
     * 从加密的密码字符串中，移除开头的用户名，再返回加密字符串
     *
     * @param userName    用户名
     * @param password    密文
     * @param encryptType 密文的加密类型
     * @return 移除用户名后内容的加密字符串
     */
    private static String removeUserName(String userName, String password, EncryptTypeEnum encryptType) {
        if (!EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION.equals(encryptType)) {
            return password;
        }
        String passwordParam = AesUtil.decrypt(password, AesUtil.DEFAULT_KEY);
        if (!StrUtil.startsWithIgnoreCase(passwordParam, userName)) {
            //没有使用账号+密码，抛出异常
            throw new AuthBusinessException(AuthErrorCodeEnum.A0125.code(), AuthErrorCodeEnum.A0125.msg());
        }
        passwordParam = StrUtil.removeWithStart(passwordParam, userName);
        if (StringUtils.isBlank(passwordParam)) {
            return null;
        }
        // 修复QX202404170055,以账号名开头的密码会报错的问题 账号：liuzhihan 密码：liuzhihan123
//        password = AesUtil.encryptByDefaultKey(passwordParam);
//        String passwordparam = AesUtil.decrypt(password, AesUtil.DEFAULT_KEY);
//        passwordparam = StrUtil.removeWithStart(passwordparam, userName);
//        if (StringUtils.isBlank(passwordparam)) {
//            return null;
//        }
        password = AesUtil.encryptByDefaultKey(passwordParam);
        return password;
    }


    /**
     * 密码加密
     *
     * @param pwd 密码明文
     * @return 密码密文
     */
    public static String encryptPwd(String pwd) {
        return AesUtil.encryptByDefaultKey(pwd);
    }

    /**
     * 解密密码
     *
     * @param pwd         密码参数，可能是明文，也可能是密文，根据encryptType参数判断
     * @param userName    用户名
     * @param encryptType 加密方式
     * @return 密码明文
     */
    public static String decryptPwd(String pwd, String userName, EncryptTypeEnum encryptType) {
        switch (encryptType) {
            case LOGIN_NAME_PASSWORD_ENCRYPTION:
                return AesUtil.decryptByDefaultKey(extractPwd(pwd, userName));
            case PASSWORD_ENCRYPTION:
                return AesUtil.decryptByDefaultKey(pwd);
            default:
                return pwd;
        }
    }

    /**
     * 密码比对
     *
     * @param referPassword  密码密文（对照字符串）
     * @param verifyPassword 待校验密文
     * @param userName       该参数不为空，意味待校验密文的加密类型为：用户名+密码的加密方式
     * @return 比对结果
     */
    private static Boolean matches(String referPassword, String verifyPassword, String userName) {
        if (StringUtils.isBlank(referPassword) && StringUtils.isBlank(verifyPassword)) {
            return true;
        }

        String targetPwd = verifyPassword;
        if (StringUtils.isNotBlank(userName)) {
            targetPwd = extractPwd(verifyPassword, userName);
        }
        if (targetPwd == null || StringUtils.isBlank(targetPwd)) {
            return false;
        }

        return targetPwd.equals(referPassword);
    }

    /**
     * 密码比对
     *
     * @param referPassword  密码密文（对照字符串）
     * @param verifyPassword 待校验明文
     * @param userName       该参数不为空，意味待校验密文的加密类型为：用户名+密码的加密方式
     * @param encryptType    密文的加密类型
     * @return 比对结果
     */
    public static Boolean matches(String referPassword, String verifyPassword, String userName,
                                  EncryptTypeEnum encryptType) {
        try {
            switch (encryptType) {
                case NOT_ENCRYPTION:
                    return matches(referPassword, encryptPwd(verifyPassword), null);
                case LOGIN_NAME_PASSWORD_ENCRYPTION:
                    //用户名+密码密文
                    return matches(referPassword, verifyPassword, userName);
                case PASSWORD_ENCRYPTION:
                    return matches(referPassword, verifyPassword, null);
                default:
                    return false;
            }
        } catch (Exception e) {
            //对调用方隐藏密码匹配异常细节
            log.error(e.getMessage(),e);
            return false;
        }
    }
}

package cn.hy.auth.custom.security.log.service;

import cn.hy.auth.custom.common.log.domain.LogDTO;
import cn.hy.auth.custom.common.log.event.LogEvent;
import cn.hy.auth.custom.common.script.ScriptEngine;
import cn.hy.auth.custom.security.log.dao.AuthSysLogHandleMapper;
import cn.hy.auth.custom.security.log.dao.AuthSysLogMapper;
import cn.hy.auth.custom.security.log.domain.AuthSysLog;
import cn.hy.auth.custom.security.log.domain.AuthSysLogHandle;
import cn.hy.dataengine.context.DataEngineContextProxy;
import cn.hy.id.IdWorker;
import cn.hy.metadata.engine.common.MetaDataEngineContext;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/2 11:45
 **/
@Service
public class LogServiceImpl implements LogService {
    private static final Logger log = LoggerFactory.getLogger("LogServiceImpl");
    private final AuthSysLogMapper authSysLogMapper;
    private final AuthSysLogHandleMapper authSysLogHandleMapper;
    private final IdWorker idWorker;

    private final ScriptEngine scriptEngine;
    private final NamedParameterJdbcTemplate jdbcTemplate;
    private final Cache<String, List<AuthSysLogHandle>> cache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(20, TimeUnit.MINUTES)
            .recordStats().build();

    @Value("${hy.security.log.saasUrl:}")
    private String saasUrl;
    @Value("${spring.cloud.consul.host}")
    private String defaultSaasIp;
    private static String DEFAULT_SAAS_URL;

    public LogServiceImpl(AuthSysLogMapper authSysLogMapper, AuthSysLogHandleMapper authSysLogHandleMapper, IdWorker idWorker,
                          ScriptEngine scriptEngine, NamedParameterJdbcTemplate jdbcTemplate) {
        this.authSysLogMapper = authSysLogMapper;
        this.authSysLogHandleMapper = authSysLogHandleMapper;
        this.idWorker = idWorker;
        this.scriptEngine = scriptEngine;
        this.jdbcTemplate = jdbcTemplate;
        initDefaultSaasUrl();
    }


    @Override
    public void saveLogDto(LogEvent logEvent) {
        setLesseeCodeAndAppCode(logEvent);
        try {
            Boolean isSaveToTable = logEvent.getIsSaveToTable();
            fillFieldToLogDto(logEvent);
            boolean customLogHandleSuccess = processCustomLogHandle(logEvent);
            if (isSaveToTable && !customLogHandleSuccess) {
                LogDTO logDTO = logEvent.getLogWithBlobDTO();
                // 通过查询元数据引擎获得表前缀（有可能是超级表）
                AuthSysLog authSysLog = AuthSysLog.toAuthSysLog(logDTO);
                authSysLogMapper.insert(authSysLog);
            }
        } finally {
            removeLesseeCodeAndAppCode();
        }
    }

    @Override
    public void clearCache() {
        cache.invalidateAll();
    }

    private void setLesseeCodeAndAppCode(LogEvent logEvent) {
        String lesseeCode = logEvent.getLogWithBlobDTO().getLesseeCode();
        String appCode = logEvent.getLogWithBlobDTO().getAppCode();
        MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCode).putAppCodeToAttachments(appCode);
        DataEngineContextProxy.getContext().setLesseeCodeCopy(lesseeCode).setAppCodeCopy(appCode);
    }

    private void removeLesseeCodeAndAppCode() {
        MetaDataEngineContext.getContext().clearLesseeCodeAndAppCodeInAttachments();
        DataEngineContextProxy.getContext().clearLesseeCodeCopy().clearAppCodeCopy();
    }

    private boolean processCustomLogHandle(LogEvent logEvent) {
        boolean success = false;
        LogDTO logDTO = logEvent.getLogWithBlobDTO();
        List<AuthSysLogHandle> authSysLogHandles = getAuthSysLogHandles(logDTO);
        if (!CollectionUtils.isEmpty(authSysLogHandles)) {
            for (AuthSysLogHandle authSysLogHandle : authSysLogHandles) {
                if (!StringUtils.isBlank(authSysLogHandle.getScript())) {
                    try {
                        log.debug("开始执行id为 {} 的日志处理脚本", authSysLogHandle.getId());
                        Map<String, Object> params = new HashMap<>();
                        params.put("request", null);
                        params.put("jdbcTemplate", jdbcTemplate);
                        params.put("logDto", logEvent.getLogWithBlobDTO());
                        params.put("token", logEvent.getToken());
                        params.put("idWorker", idWorker);
                        params.put("isSaveToTable", logEvent.getIsSaveToTable());
                        String script = authSysLogHandle.getScript();
                        // 替换 SAAS_URL 变量
                        script = script.replaceAll("\\{\\{saas_url}}", StringUtils.isBlank(saasUrl) ? DEFAULT_SAAS_URL : saasUrl);
                        success = (boolean) scriptEngine.run(script, params);
                        log.debug("结束执行id为 {} 的日志处理脚本", authSysLogHandle.getId());
                    } catch (Exception e) {
                        log.error("执行id为{}的日志处理脚本失败。", authSysLogHandle.getId(), e);
                    }
                }
            }
        }
        return success;
    }

    private void fillFieldToLogDto(LogEvent logEvent) {
        LogDTO logDTO = logEvent.getLogWithBlobDTO();
        if (logDTO.getId() == null) {
            logDTO.setId(idWorker.nextId());
        }
        logDTO.setCreateTime(new Date());
        logDTO.setLastUpdateTime(new Date());
        logDTO.setLastUpdateUserId(logDTO.getCreateUserId());
        logDTO.setCreateUserId(1L);
        logDTO.setLastUpdateUserId(1L);
    }

    private List<AuthSysLogHandle> getAuthSysLogHandles(LogDTO logDTO) {
        String key = logDTO.getLesseeCode() + logDTO.getAppCode() + logDTO.getUrl();
        return cache.get(key, k -> {
                    List<AuthSysLogHandle> authSysLogHandles = authSysLogHandleMapper.selectByUri(logDTO.getUrl());
                    if (CollectionUtils.isEmpty(authSysLogHandles)) {
                        authSysLogHandles.addAll(authSysLogHandleMapper.selectDefaultAuthLogHandle());
                    }
                    return authSysLogHandles;
                }
        );
    }

    private void initDefaultSaasUrl() {
        DEFAULT_SAAS_URL = "http://" + defaultSaasIp + ":7090";
    }
}

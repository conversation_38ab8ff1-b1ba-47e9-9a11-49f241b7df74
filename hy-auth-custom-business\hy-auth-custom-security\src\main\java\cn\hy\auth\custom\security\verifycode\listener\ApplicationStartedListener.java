package cn.hy.auth.custom.security.verifycode.listener;

import cn.hy.auth.custom.security.verifycode.VerifyBgImgInitFromLocal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: ApplicationStartedListener
 * @time: 2022-04-18 20:54
 * @desc: 初始化加载验证码
 **/
@Component
public class ApplicationStartedListener implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    private VerifyBgImgInitFromLocal verifyBgImgInitFromLocal;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        //初始加载验证码图片
        verifyBgImgInitFromLocal.init();
    }
}

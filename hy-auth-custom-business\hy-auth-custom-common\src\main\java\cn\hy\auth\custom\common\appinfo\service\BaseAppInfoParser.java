package cn.hy.auth.custom.common.appinfo.service;

import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.properties.RequestTypeProperties;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 请求参数解析器基类
 *
 * <AUTHOR>
 * @date 2020-12-13 10:18
 **/
public abstract class BaseAppInfoParser implements AppInfoParsable, Comparable<BaseAppInfoParser> {
    protected static final String GRANT_TYPE_PARAM = LoginParamterConts.GRANT_TYPE;
    /**
     * uri路径分隔符
     */
    protected static final String PATH_SEPARATOR = "/";
    /**
     * uri最少分片数
     */
    protected static final int MIN_URI_PART = 3;

    /**
     * URL匹配验证工具类
     */
    protected AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 排序值，越小越靠前
     *
     * @return 排序值
     */
    protected abstract int order();

    /**
     * 支持的uri模板列表
     *
     * @return 支持的uri模板列表
     */
    @NonNull
    protected abstract List<String> uriPatterns();

    @Autowired
    protected RequestTypeProperties requestTypeProperties;

    /**
     * 是否支持该处理
     *
     * @param request 请求信息
     * @return 当前实例是否支持对该请求处理
     */
    protected boolean supports(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        return uriPatterns().stream().anyMatch(pattern -> pathMatcher.match(pattern, request.getRequestURI()));
    }

    /**
     * 排序处理
     *
     * @param o .
     * @return .
     */
    @Override
    public int compareTo(BaseAppInfoParser o) {

        return this.order() - o.order();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof BaseAppInfoParser)) {
            return false;
        }
        BaseAppInfoParser that = (BaseAppInfoParser) o;
        return Objects.equals(requestTypeProperties, that.requestTypeProperties);
    }

    @Override
    public int hashCode() {
        return Objects.hash(requestTypeProperties);
    }

    protected String obtainParameter(HttpServletRequest request, String parameter) {
        String value = request.getParameter(parameter);
        return Optional.ofNullable(value).orElse(StringUtils.EMPTY);
    }
}

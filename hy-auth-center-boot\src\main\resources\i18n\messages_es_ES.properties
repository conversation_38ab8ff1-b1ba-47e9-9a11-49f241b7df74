AccountStatusProviderer.result.msg1=Nombre de usuario o contrase\u00F1a incorrecta
AccountStatusProviderer.result.msg2=\u3011No se puede obtener informaci\u00F3n del usuario\u3002
AccountStatusProviderer.result.msg3=La cuenta ha sido bloqueada
AccountStatusProviderer.result.msg4=La cuenta ha sido desactivada
AccountStatusProviderer.result.msg5=La cuenta ha expirado
AccountStatusProviderer.result.msg6=Las credenciales de la cuenta han expirado
ExternalAuthServiceImpl.result.msg1=No se puede encontrar la direcci\u00F3n de solicitud externa\uFF1A
ExternalAuthServiceImpl.result.msg2=Obtenci\u00F3n de solicitudes de terceros token Fracaso\uFF0CPar\u00E1metros\uFF1A
ExternalAuthServiceImpl.result.msg3=Resultados de retorno\uFF1A\u3010
ExternalAuthServiceImpl.result.msg4=Llamar a consultas de interfaz de terceros TOKEN Fracaso!
ExternalAuthServiceImpl.result.msg5=No se puede encontrar el procesamiento de la informaci\u00F3n del usuario bean
ExternalAuthServiceImpl.result.msg6=Falta un tercero en la tabla de configuraci\u00F3n de autenticaci\u00F3n\u3010
ExternalAuthServiceImpl.result.msg7=\u3011Informaci\u00F3n de configuraci\u00F3n\uFF0CNo se admite iniciar sesi\u00F3n de esta manera!
ExternalAuthServiceImpl.result.msg8=Falta informaci\u00F3n de configuraci\u00F3n de terceros en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CNo se admite iniciar sesi\u00F3n de esta manera!
ExternalAuthServiceImpl.result.msg9=La informaci\u00F3n de configuraci\u00F3n de autenticaci\u00F3n de la aplicaci\u00F3n no existe!
ExternalAuthServiceImpl.result.msg10=Fall\u00F3 la consulta de la informaci\u00F3n de la aplicaci\u00F3n de terceros\uFF1A
HySocialPreAuthenticationProvider.result.msg1=Fall\u00F3 la obtenci\u00F3n de la informaci\u00F3n de la cuenta de inicio de sesi\u00F3n del usuario de terceros\uFF01
HySocialPreAuthenticationProvider.result.msg2=Error de ejecuci\u00F3n del sistema\u3002connectionFactories is null
HySocialPreAuthenticationProvider.result.msg3=Error de ejecuci\u00F3n del sistema\u3002No se encontr\u00F3 ninguna coincidencia ConnectionFactory
OkHttpTools.result.msg1=Tipo de cuerpo de par\u00E1metro de solicitud no reconocido
MiniProgramProviderTokenImpl.result.msg1=Consulta de aplicaciones de terceros token Fracaso, Retorno de la interfaz\uFF1A
AuthSdkValidUtil.result.msg1=Errores internos\uFF1A%s
OpenIdAuthenticationProvider.result.msg1=No se puede obtener informaci\u00F3n del usuario
AuthContextFilter.result.msg1=Fall\u00F3 el an\u00E1lisis de la informaci\u00F3n de la aplicaci\u00F3n del inquilino\u3002
AuthContextFilter.result.msg2=El Estado de la aplicaci\u00F3n est\u00E1 desactivado\uFF0CNo se puede iniciar sesi\u00F3n para acceder\u3002C\u00F3digo del inquilino:
AuthContextFilter.result.msg3=; Codificaci\u00F3n de aplicaciones:
AuthContextFilter.result.msg4=La aplicaci\u00F3n ha sido desinstalada\uFF0CNo se puede iniciar sesi\u00F3n para acceder\u3002C\u00F3digo del inquilino:
AuthApiServeiceDubboImpl.result.msg1=No hay apoyo por el momento dubbo Modo\uFF0CPor favor, pase http Modo\u8BF7\u6C42 OnlineUserIds
OpenApiConnectionServiceImpl.result.msg1=Inquilino\uFF1A
OpenApiConnectionServiceImpl.result.msg2=,Aplicaci\u00F3n\uFF1A
OpenApiConnectionServiceImpl.result.msg3=,La tabla de metadatos no existe\uFF1A
OpenApiConnectionServiceImpl.result.msg4=Fall\u00F3 la autenticaci\u00F3n facial,Causa\uFF1A
OpenApiConnectionServiceImpl.result.msg5=A0401, El personal\u3010
OpenApiConnectionServiceImpl.result.msg6=\u3011Ha sido desactivado\uFF0CPor favor, active a la gente para intentar iniciar sesi\u00F3n de nuevo\uFF01
WechatProviderTokenImpl.result.msg1=Consulta de aplicaciones de terceros token Fracaso, Retorno de la interfaz\uFF1A
DingTalkProviderTokenImpl.result.msg1=Consulta de aplicaciones de terceros token Fracaso, Retorno de la interfaz\uFF1A
UserAccountDubboService.result.msg1=No 3.x Efectivo token.
OauthLogoutController.result.msg1=AccesotokenFracaso
DingTalkProviderInfoImpl.result.msg1=Falta informaci\u00F3n de configuraci\u00F3n de microaplicaci\u00F3n de clavos en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
DingTalkProviderInfoImpl.result.msg2=Falta informaci\u00F3n de configuraci\u00F3n de microaplicaci\u00F3n en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
DingTalkProviderInfoImpl.result.msg3=La informaci\u00F3n de configuraci\u00F3n de autenticaci\u00F3n de la aplicaci\u00F3n no existe!
DingTalkProviderInfoImpl.result.msg4=Fall\u00F3 la consulta de la informaci\u00F3n de la aplicaci\u00F3n de terceros\uFF1A
WechatUserProviderImpl.result.msg1=Fall\u00F3 la consulta de la informaci\u00F3n del usuario de terceros, Retorno de la interfaz\uFF1A
AuthenticateAppInfoParser.result.msg1=Falta[Authorization]Informaci\u00F3n
HyFacePreAuthenticationProvider.result.msg1=Personal actual\u3010
HyFacePreAuthenticationProvider.result.msg2=\u3011Los datos de informaci\u00F3n del personal no existen\uFF0CVerifique si hay supertablas no convertidas o falta datos b\u00E1sicos\uFF01
HyFacePreAuthenticationProvider.result.msg3=\u3011Los datos de informaci\u00F3n de asociaci\u00F3n de la cuenta no existen\uFF0CPor favor, verifique\uFF01
HyFacePreAuthenticationProvider.result.msg4=Fall\u00F3 la autenticaci\u00F3n facial\uFF0CPor favor, verifique la informaci\u00F3n del retrato.
HyFacePreAuthenticationProvider.result.msg5=Error de ejecuci\u00F3n del sistema\u3002No se encontr\u00F3 ninguna coincidencia\u3010HyFacePartyTypeEnum\u3011
MiniProgramProviderInfoImpl.result.msg1=Falta informaci\u00F3n de configuraci\u00F3n de aplicaci\u00F3n de Wechat en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
MiniProgramProviderInfoImpl.result.msg2=Falta informaci\u00F3n de configuraci\u00F3n de microaplicaci\u00F3n en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
MiniProgramProviderInfoImpl.result.msg3=La informaci\u00F3n de configuraci\u00F3n de autenticaci\u00F3n de la aplicaci\u00F3n no existe!
MiniProgramProviderInfoImpl.result.msg4=Fall\u00F3 la consulta de la informaci\u00F3n de la aplicaci\u00F3n de terceros\uFF1A
DingTalkUserProviderImpl.result.msg1=Fall\u00F3 la consulta de la informaci\u00F3n del usuario de terceros, Retorno de la interfaz\uFF1A
DingTalkUserProviderImpl.result.msg2=Fall\u00F3 la consulta de la informaci\u00F3n del usuario de clavos, Retorno de la interfaz\uFF1A
UserAccountLockAuthenticationProviderer.result.msg1=Nombre de usuario o contrase\u00F1a incorrecta
UserAccountLockAuthenticationProviderer.result.msg2=Ha superado el l\u00EDmite en el n\u00FAmero de errores de entrada, la cuenta est\u00E1 bloqueada y no se puede iniciar sesi\u00F3n, por favor vuelva a intentarlo m\u00E1s tarde o P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg3=Ha superado el l\u00EDmite en el n\u00FAmero de errores de entrada, la cuenta ha sido bloqueada permanentemente y no se puede iniciar sesi\u00F3n, P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg4=El n\u00FAmero de errores de entrada que ha introducido ha superado el l\u00EDmite y la cuenta ha sido bloqueada.
UserAccountLockAuthenticationProviderer.result.msg5=No se puede iniciar sesi\u00F3n en minutos, vuelva a intentarlo m\u00E1s tarde o P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg6=El n\u00FAmero de errores de entrada ha superado el l\u00EDmite, la cuenta est\u00E1 bloqueada y no se puede iniciar sesi\u00F3n este d\u00EDa, por favor vuelva a intentarlo al d\u00EDa siguiente o P\u00F3ngase en contacto con el Administrador.
PwdExpirationAndForceModifyServiceImpl.result.msg1=Contrase\u00F1a incorrecta obliga a actualizar el modo de pol\u00EDtica,Por favor, compruebe la configuraci\u00F3n
PwdExpirationAndForceModifyServiceImpl.result.msg2=Error de conversi\u00F3n de tiempo,Cadena de tiempo\uFF1A%s
PwdExpirationAndForceModifyServiceImpl.result.msg3=Error de datos, input No se puede null
PwdExpirationAndForceModifyServiceImpl.result.msg4=Unidad de intervalo de tiempo incorrecto %s\uFF0CPor favor, compruebe
AbstractPwdPolicyProvider.result.msg1=Entrada del usuario clientId Incorrecto:
AbstractPwdPolicyProvider.result.msg2=Entrada del usuario clientSecret Incorrecto:
MiniProgramUserProviderImpl.result.msg1=AccesoopenidError
MiniProgramUserProviderImpl.result.msg2=Fall\u00F3 la consulta de la informaci\u00F3n del usuario de terceros\uFF1A
MiniProgramUserProviderImpl.result.msg3=Fall\u00F3 la consulta de la informaci\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil de un usuario de terceros, Retorno de la interfaz\uFF1A
MiniProgramUserProviderImpl.result.msg4=Fall\u00F3 la consulta de la informaci\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil del usuario\uFF1A
LinkLoginServiceImpl.result.msg1=Datos b\u00E1sicos del personal\u3010
LinkLoginServiceImpl.result.msg2=\u3011Informaci\u00F3n de cuenta no vinculada\uFF0CPor favor, vincule primero\uFF01
LinkLoginServiceImpl.result.msg3=Error de ejecuci\u00F3n del sistema\u3002providerId:
LinkLoginServiceImpl.result.msg4=,Encontrar
LinkLoginServiceImpl.result.msg5=Registro de datos!
WechatProviderInfoImpl.result.msg1=Falta informaci\u00F3n de configuraci\u00F3n de aplicaciones de Wechat corporativo en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
WechatProviderInfoImpl.result.msg2=Falta informaci\u00F3n de configuraci\u00F3n de microaplicaci\u00F3n en la tabla de configuraci\u00F3n de autenticaci\u00F3n\uFF0CPor favor, vaya a complementar!
WechatProviderInfoImpl.result.msg3=La informaci\u00F3n de configuraci\u00F3n de autenticaci\u00F3n de la aplicaci\u00F3n no existe!
WechatProviderInfoImpl.result.msg4=Fall\u00F3 la consulta de la informaci\u00F3n de la aplicaci\u00F3n de terceros\uFF1A
SmsCodeAuthenticationProvider.result.msg1=Nombre de usuario o contrase\u00F1a incorrecta
SmsCodeAuthenticationProvider.result.msg2=Nombre de usuario o contrase\u00F1a incorrecta
PwdExpirationAndForceModifyProviderer.result.msg1=Falta informaci\u00F3n del usuario de inicio de sesi\u00F3n
PwdExpirationAndForceModifyProviderer.result.msg2=Nombre de usuario o contrase\u00F1a incorrecta
HyAuthenticationSuccessHandler.result.msg1=Entrada del usuario clientId Incorrecto:
HyAuthenticationSuccessHandler.result.msg2=Entrada del usuario clientSecret Incorrecto:
SmsCodeMapServiceImpl.result.msg1=El n\u00FAmero de tel\u00E9fono m\u00F3vil no coincide con el C\u00F3digo de verificaci\u00F3n
SmsCodeMapServiceImpl.result.msg2=El C\u00F3digo de verificaci\u00F3n ha expirado
SmsCodeMapServiceImpl.result.msg3=%s No puede estar vac\u00EDo\uFF0CPor favor, introduzca el n\u00FAmero de tel\u00E9fono m\u00F3vil
SmsCodeMapServiceImpl.result.msg4=%s No puede estar vac\u00EDo\uFF0CPor favor, introduzca el C\u00F3digo de verificaci\u00F3n
AppLoginServiceImpl.result.msg1=Error de ejecuci\u00F3n del sistema\u3002providerId:
AppLoginServiceImpl.result.msg2=,Encontrar
AppLoginServiceImpl.result.msg3=Registro de datos!
FkyUserInfoAdapter.result.msg1=No se puede encontrar la direcci\u00F3n de solicitud externa\uFF1A
FkyUserInfoAdapter.result.msg2=Fall\u00F3 la llamada a la interfaz de terceros para consultar la informaci\u00F3n del usuario!
FkyUserInfoAdapter.result.msg3=Fall\u00F3 la consulta de terceros para consultar la informaci\u00F3n del usuario\uFF0CCausa\uFF1A\u3010
OkHttpUtils.result.msg1=Fall\u00F3 el env\u00EDo de la solicitud:
IamAuthServiceImpl.result.msg1=Obtener informaci\u00F3n de usuarios de terceros\uFF0CPar\u00E1metros\uFF1A
IamAuthServiceImpl.result.msg2=Resultados de retorno\uFF1A\u3010
IamAuthServiceImpl.result.msg3=Fall\u00F3 la obtenci\u00F3n de informaci\u00F3n de usuarios de terceros!
PwdAuthenticationProviderer.result.msg1=Nombre de usuario o contrase\u00F1a incorrecta
PwdAuthenticationProviderer.result.msg2=Ha ingresado la contrase\u00F1a incorrecta
PwdAuthenticationProviderer.result.msg3=Veces, tambi\u00E9n se puede introducir
PwdAuthenticationProviderer.result.msg4=Veces. La cuenta ser\u00E1 bloqueada despu\u00E9s de que el n\u00FAmero de errores exceda el l\u00EDmite.
WechatOAuth2Template.result.msg1=Acceso access token Fracaso, errcode:
LoginStateInitFilter.assertUtil.msg1=Par\u00E1metros
LoginStateInitFilter.assertUtil.msg2=No puede estar vac\u00EDo
LoginStateInitFilter.assertUtil.msg3=Par\u00E1metros[
LoginStateInitFilter.assertUtil.msg4=]Ilegalidad\u3002
AuthContextFilter.assertUtil.msg1=Fall\u00F3 el an\u00E1lisis de la informaci\u00F3n de la aplicaci\u00F3n del inquilino\u3002
AuthContextFilter.assertUtil.msg2=Falta el par\u00E1metro de codificaci\u00F3n del inquilino\u3002
AuthContextFilter.assertUtil.msg3=Falta el par\u00E1metro de Codificaci\u00F3n de la aplicaci\u00F3n\u3002
AuthContextFilter.assertUtil.msg4=La informaci\u00F3n de la aplicaci\u00F3n no est\u00E1 registrada\uFF0CNo se puede iniciar sesi\u00F3n para acceder\u3002C\u00F3digo del inquilino:
AuthContextFilter.assertUtil.msg5=; Codificaci\u00F3n de aplicaciones:
AuthApiServiceImpl.assertUtil.msg1=Fall\u00F3 la obtenci\u00F3n de la direcci\u00F3n del Centro de certificaci\u00F3n,La informaci\u00F3n de la direcci\u00F3n est\u00E1 vac\u00EDa
AuthApiServiceImpl.assertUtil.msg2=Fall\u00F3 al obtener la direcci\u00F3n de usuario actual del Centro de autenticaci\u00F3n,La informaci\u00F3n de la direcci\u00F3n est\u00E1 vac\u00EDa
AuthApiServiceImpl.assertUtil.msg3=Fall\u00F3 al obtener la direcci\u00F3n de la informaci\u00F3n de inicio de sesi\u00F3n final del Centro de autenticaci\u00F3n,La informaci\u00F3n de la direcci\u00F3n est\u00E1 vac\u00EDa
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg1=El modo de pol\u00EDtica de actualizaci\u00F3n obligatoria de la contrase\u00F1a est\u00E1 vac\u00EDo
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg2=Pol\u00EDtica de actualizaci\u00F3n obligatoria de contrase\u00F1as\uFF1Auser Modo\uFF0CLa informaci\u00F3n de configuraci\u00F3n est\u00E1 vac\u00EDa
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg3=Pol\u00EDtica de actualizaci\u00F3n obligatoria de contrase\u00F1as\uFF1Aapp Modo\uFF0CLa informaci\u00F3n de configuraci\u00F3n est\u00E1 vac\u00EDa
UserAccountServiceImpl.assertUtil.msg1=Nombre de usuario o contrase\u00F1a incorrecta
UserAccountServiceImpl.assertUtil.msg2=Clave principal:[%d],Los datos de la tabla de cuentas de consulta no existen
UserAccountServiceImpl.assertUtil.msg3=Cuenta de usuario\u3010userAccountName\u3011No puede estar vac\u00EDo
UserAccountServiceImpl.assertUtil.msg4=Tipo de terceros\u3010providerId\u3011No puede estar vac\u00EDo
UserAccountServiceImpl.assertUtil.msg5=Usuarios de tercerosID\u3010providerUserId\u3011No puede estar vac\u00EDo
UserAccountServiceImpl.assertUtil.msg6=La cuenta actual no tiene personal vinculado\uFF0CPor favor, vaya primero a la persona vinculada a los datos b\u00E1sicos.\uFF01
UserAccountServiceImpl.assertUtil.msg7=La cuenta\u3010
UserAccountServiceImpl.assertUtil.msg8=\u3011No existe\uFF01
UserAccountServiceImpl.assertUtil.msg9=Usuarios actuales de terceros ID Se ha aplicado la vinculaci\u00F3n de otras cuentas\uFF0CPor favor, desate primero.\uFF01
UserAccountServiceImpl.assertUtil.msg10=La cuenta de aplicaci\u00F3n actual ha sido vinculada por otros usuarios de terceros\uFF0CPor favor, elija otra cuenta\uFF01
UserAccountServiceImpl.assertUtil.msg11=La contrase\u00F1a es 6-18 N\u00FAmero\u3001Composici\u00F3n de letras o caracteres especiales
RequestIdentifyServiceImpl.assertUtil.msg1=Sistemas externos no reconocibles\uFF1A
AuthRelationUtil.assertUtil.msg1=consul La Direcci\u00F3n est\u00E1 vac\u00EDa
AuthRelationUtil.assertUtil.msg2=consul El puerto est\u00E1 vac\u00EDo
PwdAuthenticationProviderer.assertUtil.msg1=Par\u00E1metros
PwdAuthenticationProviderer.assertUtil.msg2=No puede estar vac\u00EDo
PwdAuthenticationProviderer.assertUtil.msg3=Tipos de cifrado no compatibles\uFF1A\u3010
HySocialPreAuthenticationProvider.assert.msg1=M\u00E9todo de inicio de sesi\u00F3n no admitido:
HyFaceAuthenticationFilter.assert.msg1=Par\u00E1metros de la foto facial[facePic]No puede estar vac\u00EDo\uFF1A
HyFaceAuthenticationFilter.assert.msg2=Par\u00E1metros de codificaci\u00F3n del inquilino[lessCode]No puede estar vac\u00EDo\uFF1A
HyFaceAuthenticationFilter.assert.msg3=Aplicar par\u00E1metros de Codificaci\u00F3n[appCode]No puede estar vac\u00EDo\uFF1A
HyFaceAuthenticationFilter.assert.msg4=Par\u00E1metros del tipo de base de datos facial[loginType]No puede estar vac\u00EDo\uFF1A
InitDeploy.assert.msg1=ymlNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg2=MYSQL_HOSTNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg3=MYSQL_PORTNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg4=DATABASE_NAMENo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg5=DATABASE_USERNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg6=DATABASE_PWDNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg7=consul hostNo puede estar vac\u00EDo\uFF01
InitDeploy.assert.msg8=consul portNo puede estar vac\u00EDo\uFF01
OpenApiConnectionServiceImpl.assert.msg1=La direcci\u00F3n del servicio facial est\u00E1 vac\u00EDa\uFF0CPor favor, compruebe consul Configuraci\u00F3n\uFF1Athird.proxy.faceServer
HyPasswordEncoder.assert.msg1=La contrase\u00F1a de cifrado no est\u00E1 permitida en blanco
HyPasswordEncoder.assert.msg2=La contrase\u00F1a encriptada no est\u00E1 permitida en blanco
AppInfoStatusController.controllerMap.msg1=Falta el par\u00E1metro de solicitud: El n\u00FAmero de inquilino no puede estar vac\u00EDo.
AppInfoStatusController.controllerMap.msg2=Falta el par\u00E1metro de solicitud: El n\u00FAmero de aplicaci\u00F3n no puede estar vac\u00EDo.
AppInfoStatusController.controllerMap.msg3=Error en el par\u00E1metro de solicitud del usuario: status Solo puede ser 0\u30011 O 2, 0\uFF1AParar\uFF0C1\uFF1AFuncionamiento\uFF0C2\uFF1ADesinstalado
AppInfoStatusController.controllerMap.msg4=La solicitud fue procesada con \u00E9xito
AppInfoStatusController.controllerMap.msg5=Error en la ejecuci\u00F3n del sistema
MetaDataCacheController.controllerMap.msg1=La solicitud fue procesada con \u00E9xito
OauthLogoutController.controllerMap.msg1=\u00E9xito de salida
ReturnCode.enum.msg1=La solicitud fue procesada con \u00E9xito
AuthErrorCodeEnum.SUCCESS=La solicitud fue procesada con \u00E9xito
AuthErrorCodeEnum.A0101=Falta el par\u00E1metro de solicitud
AuthErrorCodeEnum.A0102=Falta informaci\u00F3n sobre la aplicaci\u00F3n del inquilino
AuthErrorCodeEnum.A0103=Solicitud ilegal
AuthErrorCodeEnum.A0342=access_token No existe
AuthErrorCodeEnum.A0343=refresh_token No existe
AuthErrorCodeEnum.A0311=El inicio de sesi\u00F3n del usuario ha expirado
AuthErrorCodeEnum.A0120=La contrase\u00F1a antigua no es correcta
AuthErrorCodeEnum.A0123=La nueva contrase\u00F1a no puede ser la misma que la antigua
AuthErrorCodeEnum.A0124=No se permite modificar la contrase\u00F1a de la cuenta no actual
AuthErrorCodeEnum.A0125=M\u00E9todo de cifrado nombre de cuenta claro+Verificaci\u00F3n de cifrado de texto plano de contrase\u00F1a anormal
AuthErrorCodeEnum.A0201=La cuenta del usuario no existe
AuthErrorCodeEnum.A0202=La actualizaci\u00F3n de la informaci\u00F3n del usuario debe especificar la clave principal del usuario
AuthErrorCodeEnum.A0203=Cuenta de usuario anormal
AuthErrorCodeEnum.A0204=Nombre de usuario o contrase\u00F1a incorrecta
AuthErrorCodeEnum.A0205=Se necesita verificar el C\u00F3digo
AuthErrorCodeEnum.A0300=Permisos de acceso anormales
AuthErrorCodeEnum.A0242=La contrase\u00F1a ha expirado\uFF0CPor favor, modifique
AuthErrorCodeEnum.A0243=Saltar a la p\u00E1gina modificada para obtener token Fracaso
AuthErrorCodeEnum.A0245=Su contrase\u00F1a es la contrase\u00F1a predeterminada\uFF0CPor favor, modifique de inmediato.
AuthErrorCodeEnum.A0246=Fall\u00F3 el inicio de sesi\u00F3n con un solo clic\uFF0CPor favor, inicie sesi\u00F3n en la cuenta vinculada
AuthErrorCodeEnum.A0422=La Direcci\u00F3n no est\u00E1 en el servicio
AuthErrorCodeEnum.A0400=Error en el par\u00E1metro de solicitud del usuario
AuthErrorCodeEnum.A0427=Solicitud JSON An\u00E1lisis fallido
AuthErrorCodeEnum.B0300=Recursos anormales del sistema
AuthErrorCodeEnum.B0301=Fall\u00F3 la carga de las reglas de autenticaci\u00F3n de la aplicaci\u00F3n
AuthErrorCodeEnum.C0319=Los datos no existen
AuthErrorCodeEnum.C0001=Se produjo un error al llamar al servicio de terceros
AuthErrorCodeEnum.B0001=Error en la ejecuci\u00F3n del sistema
AppAuthStrategyManagerImpl.other.msg1=La tabla No existe:
AppAuthStrategyManagerImpl.other.msg2=Error de Codificaci\u00F3n de la aplicaci\u00F3n, no se puede cargar la informaci\u00F3n de configuraci\u00F3n de autenticaci\u00F3n de la aplicaci\u00F3n, C\u00F3digo del inquilino\uFF1A\u3010
AppAuthStrategyManagerImpl.other.msg3=\u3011\uFF0CCodificaci\u00F3n de aplicaciones\uFF1A\u3010
ReturnCode.SUCCESS=La solicitud fue procesada con \u00E9xito
ReturnCode.SUCCESS_SAAS=La solicitud fue procesada con \u00E9xito
ReturnCode.A0200=Cuenta de usuario o contrase\u00F1a incorrecta
ReturnCode.A0202=Error en el C\u00F3digo de verificaci\u00F3n
ReturnCode.A0204=Se necesita verificar el C\u00F3digo
ReturnCode.A0300=Permisos de acceso anormales
ReturnCode.A0301=Acceso no autorizado
ReturnCode.A0311=La autorizaci\u00F3n ha expirado
ReturnCode.A0201=La cuenta del usuario no existe
ReturnCode.A0203=La cuenta ha expirado
ReturnCode.A0400=Error en el par\u00E1metro de solicitud del usuario
ReturnCode.A0420=El valor del par\u00E1metro de solicitud est\u00E1 fuera del rango permitido
ReturnCode.A0422=url La Direcci\u00F3n no est\u00E1 en el servicio
ReturnCode.A0430=El contenido de entrada del usuario es ilegal
ReturnCode.A0427=Solicitud JSON An\u00E1lisis fallido
ReturnCode.A0600=Recursos anormales del usuario
ReturnCode.C0300=Error en el Servicio de base de datos
ReturnCode.C0311=La tabla No existe
ReturnCode.C0312=La columna no existe
ReturnCode.C0313=La tabla tiene datos
ReturnCode.C0314=La columna tiene datos
ReturnCode.C0315=Las tablas existentes son citadas por otras tablas
ReturnCode.C0316=Las columnas existentes son citadas por otras tablas
ReturnCode.C0317=Los campos del sistema no permiten la eliminaci\u00F3n
ReturnCode.C0318=Las tablas del sistema no permiten la eliminaci\u00F3n
ReturnCode.C0319=Los datos no existen
ReturnCode.C0320=Los datos ya existen
ReturnCode.C0321=Descargar archivos\uFF0CLa ruta completa del archivo no est\u00E1 permitida en blanco\u3002
ReturnCode.C0322=Descargar archivos\uFF0CEl nombre del archivo no puede estar vac\u00EDo\u3002
ReturnCode.C0323=Descargar archivos\uFF0CEl archivo no existe\u3002
ReturnCode.C0324=Fall\u00F3 la creaci\u00F3n del cat\u00E1logo\u3002
ReturnCode.C0325=Formato de archivo ilegal\u3002
ReturnCode.C0326=Fall\u00F3 la carga del archivo\u3002
ReturnCode.C0327=Fall\u00F3 la importaci\u00F3n de metadatos de la tabla\u3002
ReturnCode.C0328=La plantilla de importaci\u00F3n y exportaci\u00F3n no existe\u3002
ReturnCode.C0329=Modelado de Negocios API No existe\u3002
ReturnCode.C0340=Existe una asociaci\u00F3n
ReturnCode.C0330=El campo de negocio de la tabla est\u00E1 vac\u00EDo\uFF0CNo crear plantilla\u3002
ReturnCode.C0331=Fall\u00F3 la construcci\u00F3n de la informaci\u00F3n de la celda importada y exportada\u3002
ReturnCode.C0332=Importar plantilla de exportaci\u00F3nSheetEl nombre no permite la repetici\u00F3n\u3002
ReturnCode.C0333=El superreloj no viene de la conversi\u00F3n\uFF0CNo se puede retroceder
ReturnCode.C0001=Se produjo un error al llamar al servicio de terceros
ReturnCode.B0001=Error en la ejecuci\u00F3n del sistema
DynamicUserAccountMapper.result.msg1=Seg\u00FAn la informaci\u00F3n del usuario \u3010%s\u3011 solo se debe consultar un registro como m\u00E1ximo, pero ahora se consulta a \u3010%s\u3011.


AuthErrorCodeEnum.A0247=La contrase\u00F1a ha llegado a la hora de modificaci\u00F3n especificada\uFF0CPor favor, modifique
AuthErrorCodeEnum.A0248=Su inicio de sesi\u00F3nIPVinculado IP Inconsistencias\uFF0CPor favor, compruebe
SmsCodeMapServiceImpl.result.msg5=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SmsCodeMapServiceImpl.result.msg6=N\u00FAmero de tel\u00E9fono m\u00F3vil o c\u00F3digo de verificaci\u00F3n incorrecto
SmsCodeMapServiceImpl.result.msg7=C\u00F3digo de verificaci\u00F3n no v\u00E1lido
SmsCodeMapServiceImpl.result.msg8=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SmsCodeMapServiceImpl.result.msg9=Error en la entrada del C\u00F3digo de verificaci\u00F3n\uFF0CSe ha equivocado %S Veces\uFF0CVuelve a equivocarte %s Veces\u540E\u5C06\u9501\u5B9A\u8D26\u53F7\u3002
SmsCodeMapServiceImpl.result.msg10=C\u00F3digo de verificaci\u00F3n 1 V\u00E1lido en minutos\uFF0CNo env\u00EDe repetidamente
SmsController.result.msg1=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SmsController.result.msg2=El sistema actual no tiene este n\u00FAmero de tel\u00E9fono m\u00F3vil\uFF0CPor favor, vuelva a ingresar
SmsCodeLogPrintSender.result.msg1=1 El n\u00FAmero de mensajes de texto enviados en horas ha alcanzado el l\u00EDmite m\u00E1ximo
SmsCodeLogPrintSender.result.msg2=Fall\u00F3 el env\u00EDo de SMS\uFF0CPor favor, compruebe la configuraci\u00F3n del Servicio de SMS
SmsCodeLogPrintSender.result.msg3=\u3010Justicia de Guangxi\u3011C\u00F3digo de verificaci\u00F3n\uFF1A%s\uFF0CEst\u00E1 haciendo el inicio de sesi\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil.\uFF08Si no lo hubiera operado yo mismo\uFF0CPor favor, elimine este mensaje de texto\uFF09
SmsCodeLogPrintSender.result.msg4=\u3010Justicia de Guangxi\u3011C\u00F3digo de verificaci\u00F3n\uFF1A%s\uFF0CEst\u00E1 recuperando su contrase\u00F1a a trav\u00E9s de su n\u00FAmero de tel\u00E9fono m\u00F3vil.\uFF08Si no lo hubiera operado yo mismo\uFF0CPor favor, elimine este mensaje de texto\uFF09
SmsCodeLogPrintSender.result.msg5=\u3010n\u00FAmero de tel\u00E9fono m\u00F3vil para iniciar sesi\u00F3n\u3011 C\u00F3digo de verificaci\u00F3n: %s, est\u00E1 realizando el inicio de sesi\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil (si no opera por s\u00ED mismo, elimine este mensaje de texto)
SmsCodeLogPrintSender.result.msg6=\u3010verificaci\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil\u3011 C\u00F3digo de verificaci\u00F3n: %s, est\u00E1 recuperando la contrase\u00F1a a trav\u00E9s del n\u00FAmero de tel\u00E9fono m\u00F3vil (si no opera por s\u00ED mismo, elimine este mensaje de texto)
SmsServiceImpl.resule.msg1=N\u00FAmero de tel\u00E9fono m\u00F3vil o c\u00F3digo de verificaci\u00F3n incorrecto
SmsServiceImpl.resule.msg2=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SmsServiceImpl.resule.msg3=C\u00F3digo de verificaci\u00F3n no v\u00E1lido
SmsServiceImpl.resule.msg4=Error en la entrada del C\u00F3digo de verificaci\u00F3n\uFF0CSe ha equivocado %s Veces\uFF0CVuelve a equivocarte %s Veces\u540E\u9501\u5B9A\u8D26\u53F7
SmsServiceImpl.resule.msg5=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SmsServiceImpl.resule.msg6=C\u00F3digo de verificaci\u00F3n1V\u00E1lido en minutos\uFF0CNo env\u00EDe repetidamente
SmsServiceImpl.resule.msg7=Mensaje de texto enviado con \u00E9xito\uFF01

EmailServiceImpl.doCheck.msg1=La direcci\u00F3n de correo electr\u00F3nico o el c\u00F3digo de verificaci\u00F3n son incorrectos
EmailServiceImpl.sendToThirdService.msg1=El c\u00F3digo de verificaci\u00F3n es v\u00E1lido durante 5 minutos, no lo env\u00EDe repetidamente
EmailCodeSenderImpl.send.msg1=Se ha alcanzado el n\u00FAmero m\u00E1ximo de correos electr\u00F3nicos enviados en una hora
EmailServiceImpl.sendToThirdService.msg2=\u00A1El correo electr\u00F3nico se envi\u00F3 con \u00E9xito!

SecurityQuestionServiceImpl.result.msg1=Nombre de usuario o contrase\u00F1a incorrecta
SecurityQuestionServiceImpl.result.msg2=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
SecurityQuestionServiceImpl.result.msg3=Verificaci\u00F3n exitosa
SecurityQuestionServiceImpl.result.msg4=Error al introducir la respuesta de la garant\u00EDa secreta\uFF0CSe ha equivocado %s Veces\uFF0CVuelve a equivocarte %s Veces\u540E\u9501\u5B9A\u8D26\u53F7
SecurityQuestionServiceImpl.result.msg5=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
UserAccountController.result.msg1=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
UserAccountController.result.msg2=El sistema actual no tiene este n\u00FAmero de tel\u00E9fono m\u00F3vil\uFF0CPor favor, vuelva a ingresar
UserAccountController.result.msg3=Tipo de contrase\u00F1a de recuperaci\u00F3n inexistente
UserAccountController.result.msg4=Introduzca el n\u00FAmero de tel\u00E9fono correcto
UserAccountController.result.msg5=Tipo de contrase\u00F1a de recuperaci\u00F3n inexistente
UserAccountController.result.msg6=La cuenta est\u00E1 bloqueada\uFF0CP\u00F3ngase en contacto con el Administrador para procesarlo.
UserAccountController.result.msg7=Nombre de usuario o contrase\u00F1a incorrecta
UserAccountController.result.msg8=El sistema actual no tiene este n\u00FAmero de tel\u00E9fono m\u00F3vil\uFF0CPor favor, vuelva a ingresar
UserAccountController.result.msg9=Anomal\u00EDa de entrada
UserAccountController.result.msg10=La nueva contrase\u00F1a no coincide con la contrase\u00F1a de confirmaci\u00F3n
UserAccountController.result.msg11=La sesi\u00F3n ha expirado\uFF0CPor favor, vuelva a operar
UserAccountController.result.msg12=Modificaci\u00F3n exitosa
UserAccountController.result.msg13=Introduzca una direcci\u00F3n de correo electr\u00F3nico v\u00E1lida
UserAccountController.result.msg14=El sistema actual no tiene esta direcci\u00F3n de correo electr\u00F3nico, vuelva a ingresarla

UserAccountLockType.LOGIN_ERROR_TIME=Ha superado el l\u00EDmite en el n\u00FAmero de errores de entrada
UserAccountLockType.MOBILE_SMS=Se olvida de la contrase\u00F1a, el n\u00FAmero de tel\u00E9fono m\u00F3vil se recupera incorrectamente demasiadas veces
UserAccountLockType.SECURITY_QUESTION=Olvidar el m\u00E9todo de recuperaci\u00F3n de la garant\u00EDa secreta de la contrase\u00F1a demasiadas veces
UserAccountLockType.MOBILE_LOGIN=Demasiados errores en el C\u00F3digo de verificaci\u00F3n de inicio de sesi\u00F3n del n\u00FAmero de tel\u00E9fono m\u00F3vil

UserAccountLockAuthenticationProviderer.result.msg7=Ha superado el l\u00EDmite en el n\u00FAmero de errores de entrada, la cuenta est\u00E1 bloqueada y no se puede iniciar sesi\u00F3n, por favor vuelva a intentarlo m\u00E1s tarde o P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg8=La cuenta est\u00E1 bloqueada permanentemente y no se puede iniciar sesi\u00F3n. P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg9=La cuenta ha sido bloqueada,
UserAccountLockAuthenticationProviderer.result.msg10=No se puede iniciar sesi\u00F3n en minutos, vuelva a intentarlo m\u00E1s tarde o P\u00F3ngase en contacto con el Administrador
UserAccountLockAuthenticationProviderer.result.msg11=La cuenta est\u00E1 bloqueada y no se puede iniciar sesi\u00F3n este d\u00EDa. por favor, vuelva a intentarlo o P\u00F3ngase en contacto con el Administrador al d\u00EDa siguiente.
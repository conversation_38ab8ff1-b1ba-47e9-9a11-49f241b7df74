package cn.hy.auth.custom.user.cache.service.impl;

import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class ForgetPasswordRedisManage implements ForgetPasswordCacheManage {

    private final RedisTemplate<String, Object> redisTemplate;

    public ForgetPasswordRedisManage(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.java());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(RedisSerializer.java());
        template.afterPropertiesSet();
        this.redisTemplate = template;
    }

    @Override
    public void put(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void put(String key, Object value, Date expireDate) {
        redisTemplate.opsForValue().set(key, value);
        redisTemplate.expireAt(key, expireDate);
    }

    @Override
    public void put(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value);
        redisTemplate.expire(key, timeout, unit);
    }

    @Override
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public boolean invalidate(String key) {
        redisTemplate.opsForValue().getOperations().delete(key);
        return true;
    }
}

package cn.hy.auth.custom.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Locale;

/**
 * hy-saas-cn.hy.saas.commons.utils
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/4/22 10:14
 */
@Slf4j
@Component("LocaleUtil2")
public class LocaleUtil implements ApplicationContextAware {

    private static MessageSource messageSource;

    @Value("${spring.messages.country:zh}")
    private String countryConfig;
    @Value("${spring.messages.variant:CN}")
    private String variantConfig;
    private static String country;
    private static String variant;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        LocaleUtil.messageSource = applicationContext.getBean(MessageSource.class);
        LocaleUtil.country = countryConfig;
        LocaleUtil.variant = variantConfig;
    }


    /**
     * 获取国际化message
     *
     * @param code           code
     * @param args           占位参数
     * @param defaultMessage 默认值
     * @return 国际化文本
     */
    public static String getMessage(String code, @Nullable Object[] args, @Nullable String defaultMessage) {
        return messageSource.getMessage(code, args, defaultMessage, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化message
     *
     * @param code           code
     * @param args           占位参数
     * @param defaultMessage 默认值
     * @param locale         地区
     * @return 国际化文本
     */
    public static String getMessage(String code, @Nullable Object[] args, @Nullable String defaultMessage, Locale locale) {
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取国际化message
     *
     * @param code           code
     * @param defaultMessage 默认值
     * @return 国际化文本
     */
    public static String getMessage(String code, @Nullable String defaultMessage) {
        return messageSource.getMessage(code, null, defaultMessage, new Locale(country, variant));
    }

}

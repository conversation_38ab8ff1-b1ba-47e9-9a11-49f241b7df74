package cn.hy.auth.boot;

import cn.hy.paas.multi.datasource.bean.DbConnection;
import cn.hy.paas.multi.datasource.bean.MultiDataSourceProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * 平台已启动
 *
 * <AUTHOR>
 * @date 2020-06-17 13:14
 */
@Component
@Slf4j
@Order(value = 1)
public class InitAuth implements CommandLineRunner {

    private final ApplicationContext applicationContext;

    @Autowired
    public InitAuth(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }


    @Override
    public void run(String... args) throws Exception {
        DataSourceProperties dataSourceProperties = applicationContext.getBean(DataSourceProperties.class);
        log.info("auth 平台启动成功!数据库连接信息：{},username = {}",dataSourceProperties.getUrl(),dataSourceProperties.getUsername());
        MultiDataSourceProperties multiDataSourceProperties = applicationContext.getBean(MultiDataSourceProperties.class);
        if (multiDataSourceProperties.getEnable() && !CollectionUtils.isEmpty(multiDataSourceProperties.getConnection())) {
            for (DbConnection dbConnection : multiDataSourceProperties.getConnection()) {
                log.info("启用多数据源信息: {}", dbConnection.toString());
            }
        }
    }


}

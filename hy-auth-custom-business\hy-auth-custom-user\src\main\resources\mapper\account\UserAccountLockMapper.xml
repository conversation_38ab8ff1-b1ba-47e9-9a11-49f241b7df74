<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.user.account.dao.UserAccountLockDao">
  <resultMap id="BaseResultMap" type="cn.hy.auth.custom.user.account.domain.UserAccountLock">
    <id column="id" jdbcType="DECIMAL" property="id" />
    <result column="create_user_id" jdbcType="DECIMAL" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="data_version" jdbcType="VARCHAR" property="dataVersion" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="lock_end_time" jdbcType="TIMESTAMP" property="lockEndTime" />
    <result column="client_type" jdbcType="VARCHAR" property="clientType" />
    <result column="last_update_user_id" jdbcType="DECIMAL" property="lastUpdateUserId" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="sequence" jdbcType="BIGINT" property="sequence" />
    <result column="user_id" jdbcType="DECIMAL" property="userId" />
    <result column="lock_start_time" jdbcType="TIMESTAMP" property="lockStartTime" />
    <result column="last_update_user_name" jdbcType="VARCHAR" property="lastUpdateUserName" />
    <result column="lock_record_type" jdbcType="VARCHAR" property="lockRecodeType" />
  </resultMap>
  <delete id="deleteByUid" >
    delete from user_account_lock
    where user_id = #{uid}
  </delete>
  <insert id="insert" parameterType="cn.hy.auth.custom.user.account.domain.UserAccountLock">
    insert into user_account_lock (id, create_user_id, create_user_name, 
      create_time, data_version, ip, 
      lock_end_time, client_type, last_update_user_id, 
      mac, last_update_time, sequence, 
      user_id, lock_start_time, last_update_user_name, lock_record_type
      )
    values (#{id,jdbcType=DECIMAL}, #{createUserId,jdbcType=DECIMAL}, #{createUserName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{dataVersion,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, 
      #{lockEndTime,jdbcType=TIMESTAMP}, #{clientType,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=DECIMAL}, 
      #{mac,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{sequence,jdbcType=BIGINT}, 
      #{userId,jdbcType=DECIMAL}, #{lockStartTime,jdbcType=TIMESTAMP}, #{lastUpdateUserName,jdbcType=VARCHAR},
      #{lockRecodeType,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.hy.auth.custom.user.account.domain.UserAccountLock">
    update user_account_lock
    set create_user_id = #{createUserId,jdbcType=DECIMAL},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      data_version = #{dataVersion,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      lock_end_time = #{lockEndTime,jdbcType=TIMESTAMP},
      client_type = #{clientType,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=DECIMAL},
      mac = #{mac,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      sequence = #{sequence,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=DECIMAL},
      lock_start_time = #{lockStartTime,jdbcType=TIMESTAMP},
      last_update_user_name = #{lastUpdateUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByUid"  resultMap="BaseResultMap">
    select id, create_user_id, create_user_name, create_time, data_version, ip, lock_end_time, 
    client_type, last_update_user_id, mac, last_update_time, sequence, user_id, lock_start_time, 
    last_update_user_name, lock_record_type
    from user_account_lock
    where user_id = #{uid} order by id limit 1
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, create_user_id, create_user_name, create_time, data_version, ip, lock_end_time, 
    client_type, last_update_user_id, mac, last_update_time, sequence, user_id, lock_start_time, 
    last_update_user_name
    from user_account_lock
  </select>
</mapper>
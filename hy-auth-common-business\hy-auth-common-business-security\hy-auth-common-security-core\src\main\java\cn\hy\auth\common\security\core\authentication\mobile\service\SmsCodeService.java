package cn.hy.auth.common.security.core.authentication.mobile.service;

import cn.hy.auth.common.security.core.authentication.mobile.ValidateCode;
import cn.hy.auth.common.security.core.authentication.mobile.exception.ValidateCodeException;
import org.springframework.lang.Nullable;
import org.springframework.web.context.request.ServletWebRequest;

import java.time.LocalDateTime;

/**
 * 手机验证码service组件
 *
 * <AUTHOR>
 * @date 2020/11/24  17:15
 */
public interface SmsCodeService {

    /**
     * 校验手机验证码是否匹配
     *
     * @param mobile          手机号
     * @param codeInRequest   验证码
     * @param mobileParameter 手机号参数名称
     * @param codeParameter   验证码参数名称
     * @param request         .
     * @throws ValidateCodeException .
     */
    void validateCode(@Nullable String mobile, String mobileCipherText, @Nullable String codeInRequest, String mobileParameter, String codeParameter, ServletWebRequest request);

    /**
     * 保存验证码
     *
     * @param mobile     手机号
     * @param code       验证码
     * @param expireTime 有效期截止时间
     */
    String saveCode(String mobile, String code, LocalDateTime expireTime);

    /**
     * 获取验证码(支持同一个手机同时存在多个验证码)
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 验证码
     */
    ValidateCode getCode(String mobile, String code);


    /**
     * 删除验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     */
    void removeCode(String mobile, String code);
}
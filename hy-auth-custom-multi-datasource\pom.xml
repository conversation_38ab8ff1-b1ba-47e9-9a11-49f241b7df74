<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-authentication-center</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-custom-multi-datasource</artifactId>

    <name>hy-auth-custom-multi-datasource</name>
    <url>http://www.example.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hy.paas</groupId>
            <artifactId>hy-paas-multi-datasource</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
            <exclusion>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
            </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hy.dataengine.extend</groupId>
            <artifactId>hy-data-engine-metadata-extend</artifactId>
            <version>1.1.9-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-common</artifactId>
            <version>1.0.0-i18n-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- <dependency>
             <groupId>cn.hy</groupId>
             <artifactId>hy-paas-multi-datasource</artifactId>
             <version>1.0.0</version>
             <scope>compile</scope>
             <exclusions>
                 <exclusion>
                     <groupId>cn.hy.dataengine</groupId>
                     <artifactId>hy-data-engine-core</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>cn.hy.dataengine.extend</groupId>
                     <artifactId>hy-data-engine-metadata-extend</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>cn.hy.metadata</groupId>
                     <artifactId>hy-metadata-engine-core</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>org.springframework.boot</groupId>
                     <artifactId>spring-boot-starter-web</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>org.springframework.boot</groupId>
                     <artifactId>spring-boot-starter-validation</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>cn.hy</groupId>
                     <artifactId>hy-id-generator-starter</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>org.jetbrains</groupId>
                     <artifactId>annotations</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>org.mapstruct</groupId>
                     <artifactId>mapstruct</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>cn.hy</groupId>
                     <artifactId>hy-logging-generic-starter</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>org.zeroturnaround</groupId>
                     <artifactId>zt-zip</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>io.springfox</groupId>
                     <artifactId>springfox-swagger2</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>io.swagger</groupId>
                     <artifactId>swagger-annotations</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>io.swagger</groupId>
                     <artifactId>swagger-models</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>com.github.xiaoymin</groupId>
                     <artifactId>swagger-bootstrap-ui</artifactId>
                 </exclusion>
             </exclusions>
             </dependency>-->


    </dependencies>

    <build>

    </build>
</project>

package cn.hy.auth.custom.security.third.wechat.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import lombok.Setter;

import java.util.Date;

/**
 * 企业微信用户信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
public class WechatAccessToken implements ProviderAppAccessToken {

    private String accessToken;
    private int errCode;
    private String errorMsg;
    private Date expiresTime;
    private int expiresIn;

    @Override
    public int getErrCode() {
        return errCode;
    }

    @Override
    public String getErrMsg() {
        return errorMsg;
    }

    @Override
    public String getAccessToken() {
        return accessToken;
    }

    @Override
    public Date getExpiresTime() {
        return expiresTime;
    }

    @Override
    public int getExpiresIn() {
        return expiresIn;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/hy-auth-center-boot/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-center-boot/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-center-boot/src/main/resources/config" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/hy-auth-common-security-oauth2-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/hy-auth-common-security-oauth2-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/hy-auth-common-security-session-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/hy-auth-common-security-session-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-session/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-session/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-tool/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/hy-auth-common-business-tool/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-common-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-multi-app/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-route/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-route/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-user/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-custom-user/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-sdk-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-sdk-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/hy-auth-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-dubbo/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-dubbo/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-multi-datasource/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-custom-multi-datasource/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hy-auth-test/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>
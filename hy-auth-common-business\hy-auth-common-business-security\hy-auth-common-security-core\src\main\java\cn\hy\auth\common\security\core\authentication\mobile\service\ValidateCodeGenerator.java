package cn.hy.auth.common.security.core.authentication.mobile.service;

import cn.hy.auth.common.security.core.authentication.mobile.ValidateCode;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020-11-24 10:56
 */
public interface ValidateCodeGenerator {
    /**
     * 生成验证码
     *
     * @param request  .
     * @param response .
     * @return .
     */
    ValidateCode createCode(HttpServletRequest request, HttpServletResponse response);
}

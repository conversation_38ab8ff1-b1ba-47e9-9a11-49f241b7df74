package cn.hy.auth.boot.listener;

import cn.hy.auth.common.security.oauth2.event.TokenRefreshedSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * 类描述: 刷新token事件
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/26
 */
@Component
@Slf4j
@ConditionalOnProperty(
        prefix = "auth.token.store",
        name = "type",
        havingValue = "redisLocalCache"
)
public class TokenStatusChangedEventListener implements ApplicationListener<ApplicationEvent> {
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final String topic;

    public TokenStatusChangedEventListener(KafkaTemplate<String, Object> kafkaTemplate,
                                           @Value("${hy.saas.token.status.topic:hy-auth-sys-token-status}")
                                           String topic) {
        this.kafkaTemplate = kafkaTemplate;
        this.topic = topic;
    }

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof TokenRevokedEvent || event instanceof TokenRefreshedSuccessEvent){
            JSONObject jsonObject = new JSONObject();
            if (event instanceof TokenRevokedEvent){
                TokenRevokedEvent tokenRevokedEvent = (TokenRevokedEvent) event;
                jsonObject.put("token",tokenRevokedEvent.getTokenValue());
                jsonObject.put("refreshToken",tokenRevokedEvent.getOAuth2AccessToken().getRefreshToken().getValue());
                jsonObject.put("type","revoked");
            } else{
                TokenRefreshedSuccessEvent tokenRefreshedSuccessEvent = (TokenRefreshedSuccessEvent) event;
                jsonObject.put("token",tokenRefreshedSuccessEvent.getoAuth2AccessToken().getValue());
                jsonObject.put("refreshToken",tokenRefreshedSuccessEvent.getRefreshTokenValue());
                jsonObject.put("type","refreshToken");
            }
            kafkaTemplate.send(topic,JSON.toJSONString(jsonObject)).addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                @Override
                public void onFailure(Throwable ex) {
                    log.warn("发送token状态变化消息失败,{},{}",JSON.toJSONString(jsonObject),ex.getMessage());
                }

                @Override
                public void onSuccess(SendResult<String, Object> result) {

                }
            });
        }

    }
}

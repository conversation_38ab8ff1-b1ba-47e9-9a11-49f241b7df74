package cn.hy.auth.custom.security.reauthentication.domain;

import lombok.Data;

import java.util.Date;

/**
 * usbkey认证
 *
 * <AUTHOR>
 * @date 2024/6/14 10:03
 */
@Data
public class AuthUsbkeyDO {

    private Long id;

    /**
     * 人员编码
     */
    private String hrCode;

    /**
     * 随机数
     */
    private String randomNumber;

    /**
     * 公钥信息
     */
    private String publicKey;

    /**
     * usbkey序列号
     */
    private String sn;

    private String dataVersion;

    private Long createUserId;

    private Date createTime;

    private String createUserName;

    private Long lastUpdateUserId;

    private Date lastUpdateTime;

    private String lastUpdateUserName;

    private Long sequence;
}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;45f267b8-9727-45ff-900d-427d58f84c33&quot;,&quot;conversations&quot;:{&quot;45f267b8-9727-45ff-900d-427d58f84c33&quot;:{&quot;id&quot;:&quot;45f267b8-9727-45ff-900d-427d58f84c33&quot;,&quot;createdAtIso&quot;:&quot;2025-06-09T09:49:43.637Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-24T04:16:46.726Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;ab23e719-31ec-4ee7-b8e5-f5f1885e8342&quot;,&quot;uuid&quot;:&quot;34dcd91b-009f-4bbf-bcda-0556c58dfa7b&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1749462583637,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f844a4bb-129b-4888-a673-c55e83433f2d&quot;,&quot;request_message&quot;:&quot;解析这个项目怎么实现二级缓存的，怎么保证缓存一致性的&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;解析这个项目怎么实现二级缓存的，怎么保证缓存一致性的&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C54WHTMKYvg3mUz83Kce8d&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮您分析这个项目的二级缓存实现和缓存一致性保证机制。首先让我了解一下项目的整体结构和缓存相关的代码。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;解析这个项目怎么实现二级缓存的，怎么保证缓存一致性的&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:50:10.351Z&quot;,&quot;response_text&quot;:&quot;我来帮您分析这个项目的二级缓存实现和缓存一致性保证机制。首先让我了解一下项目的整体结构和缓存相关的代码。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;955a401b-3f18-45d6-9ce7-5f3eea2a3c8f&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QZAdgXLKnW7ZFJBQb7Aowp&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u627e\\u9879\\u76ee\\u4e2d\\u6240\\u6709\\u4e0e\\u7f13\\u5b58\\u76f8\\u5173\\u7684\\u4ee3\\u7801\\uff0c\\u5305\\u62ec\\uff1a1. \\u7f13\\u5b58\\u914d\\u7f6e\\u7c7b\\u548c\\u6ce8\\u89e3 2. \\u7f13\\u5b58\\u7ba1\\u7406\\u5668\\u548c\\u7f13\\u5b58\\u5b9e\\u73b0 3. \\u4e8c\\u7ea7\\u7f13\\u5b58\\u7684\\u5177\\u4f53\\u5b9e\\u73b0 4. \\u7f13\\u5b58\\u4e00\\u81f4\\u6027\\u4fdd\\u8bc1\\u673a\\u5236 5. Redis\\u7f13\\u5b58\\u76f8\\u5173\\u914d\\u7f6e\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我搜索项目中与缓存相关的代码，特别是二级缓存的实现：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./configs\n./configs\\hy-authentication-center.init.yml\n./configs\\hy-authentication-center.run.yml\n./docker-compose-auth-center.yml\n./hy-auth-center-boot\n./hy-auth-center-boot\\Dockerfile\n./hy-auth-center-boot\\Dockerfile_backup\n./hy-auth-center-boot\\pom.xml\n./hy-auth-center-boot\\src\n./hy-auth-center-boot\\target\n./hy-auth-common-business\n./hy-auth-common-business-starter\n./hy-auth-common-business-starter\\hy-auth-common-security-oauth2-starter\n./hy-auth-common-business-starter\\hy-auth-common-security-session-starter\n./hy-auth-common-business-starter\\pom.xml\n./hy-auth-common-business\\hy-auth-common-business-security\n./hy-auth-common-business\\hy-auth-common-business-tool\n./hy-auth-common-business\\pom.xml\n./hy-auth-custom-business\n./hy-auth-custom-business-starter\n./hy-auth-custom-business-starter\\pom.xml\n./hy-auth-custom-business-starter\\src\n./hy-auth-custom-business-starter\\target\n./hy-auth-custom-business\\hy-auth-custom-common\n./hy-auth-custom-business\\hy-auth-custom-multi-app\n./hy-auth-custom-business\\hy-auth-custom-route\n./hy-auth-custom-business\\hy-auth-custom-security\n./hy-auth-custom-business\\hy-auth-custom-user\n./hy-auth-custom-business\\hy-auth-sdk\n./hy-auth-custom-business\\hy-auth-sdk-starter\n./hy-auth-custom-business\\pom.xml\n./hy-auth-custom-dubbo\n./hy-auth-custom-dubbo\\target\n./hy-auth-custom-multi-datasource\n./hy-auth-custom-multi-datasource\\pom.xml\n./hy-auth-custom-multi-datasource\\src\n./hy-auth-custom-multi-datasource\\target\n./hy-auth-test\n./hy-auth-test\\pom.xml\n./license\n./license\\license\n./logs\n./logs\\debug.log.2024-07-04-14.0\n./logs\\debug.log.2024-07-04-15.0\n./logs\\debug.log.2024-07-04-16.0\n./logs\\debug.log.2024-07-04-17.0\n./logs\\debug.log.2024-07-04-18.0\n./logs\\debug.log.2024-07-04-19.0\n./logs\\debug.log.2024-07-09-15.0\n./logs\\debug.log.2024-07-09-16.0\n./logs\\debug.log.2024-07-17-12.0\n./logs\\debug.log.2024-07-17-14.0\n./logs\\debug.log.2024-07-17-19.0\n./logs\\debug.log.2024-07-17-20.0\n./logs\\debug.log.none.none.2025-04-15-10.0\n./logs\\debug.log.none.none.2025-04-15-11.0\n./logs\\error.log.2024-07-04-14.0\n./logs\\error.log.2024-07-04-15.0\n./logs\\error.log.2024-07-04-17.0\n./logs\\error.log.2024-07-04-19.0\n./logs\\error.log.2024-07-09-15.0\n./logs\\error.log.2024-07-17-12.0\n./logs\\error.log.2024-07-17-14.0\n./logs\\error.log.2024-07-17-19.0\n./logs\\error.log.2024-07-17-20.0\n./logs\\error.log.none.none.2025-04-15-10.0\n./logs\\error.log.none.none.2025-04-15-11.0\n./logs\\info.log.2024-07-04-14.0\n./logs\\info.log.2024-07-04-15.0\n./logs\\info.log.2024-07-04-16.0\n./logs\\info.log.2024-07-04-17.0\n./logs\\info.log.2024-07-04-18.0\n./logs\\info.log.2024-07-04-19.0\n./logs\\info.log.2024-07-09-15.0\n./logs\\info.log.2024-07-09-16.0\n./logs\\info.log.2024-07-17-12.0\n./logs\\info.log.2024-07-17-14.0\n./logs\\info.log.2024-07-17-19.0\n./logs\\info.log.2024-07-17-20.0\n./logs\\info.log.none.none.2025-04-15-10.0\n./logs\\info.log.none.none.2025-04-15-11.0\n./logs\\trace.log.2024-07-04-14.0\n./logs\\trace.log.2024-07-04-15.0\n./logs\\trace.log.2024-07-04-19.0\n./logs\\trace.log.2024-07-09-15.0\n./logs\\trace.log.2024-07-17-12.0\n./logs\\trace.log.2024-07-17-14.0\n./logs\\trace.log.2024-07-17-19.0\n./logs\\trace.log.2024-07-17-20.0\n./logs\\trace.log.none.none.2025-04-15-10.0\n./logs\\trace.log.none.none.2025-04-15-11.0\n./logs\\warn.log.2024-07-04-14.0\n./logs\\warn.log.2024-07-04-15.0\n./logs\\warn.log.2024-07-04-17.0\n./logs\\warn.log.2024-07-04-18.0\n./logs\\warn.log.2024-07-04-19.0\n./logs\\warn.log.2024-07-09-15.0\n./logs\\warn.log.2024-07-17-12.0\n./logs\\warn.log.2024-07-17-14.0\n./logs\\warn.log.2024-07-17-19.0\n./logs\\warn.log.2024-07-17-20.0\n./logs\\warn.log.none.none.2025-04-15-10.0\n./logs\\warn.log.none.none.2025-04-15-11.0\n./pom.xml\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C54WHTMKYvg3mUz83Kce8d&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:50:18.163Z&quot;,&quot;response_text&quot;:&quot;现在让我搜索项目中与缓存相关的代码，特别是二级缓存的实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0d7a84bb-8877-49ef-a1c6-9d1f97fb73d1&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01R4hET83HKHcmaJxc5CpfAv&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u641c\\u7d22\\u5305\\u542b@Cacheable\\u3001@CacheEvict\\u3001@CachePut\\u3001CacheManager\\u3001RedisTemplate\\u3001\\u4e8c\\u7ea7\\u7f13\\u5b58\\u3001\\u7f13\\u5b58\\u914d\\u7f6e\\u7684Java\\u7c7b\\u548c\\u65b9\\u6cd5\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;让我更具体地搜索缓存相关的实现：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QZAdgXLKnW7ZFJBQb7Aowp&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:51:26.309Z&quot;,&quot;response_text&quot;:&quot;让我更具体地搜索缓存相关的实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ce93fcac-1f72-4d87-969a-8f1066de3ee2&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01NVZBMipzzauRpTtFvjg4tD&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u627eLesseeCaffeineCacheManager\\u7c7b\\u7684\\u5b9e\\u73b0\\uff0c\\u4ee5\\u53ca\\u7f13\\u5b58\\u524d\\u7f00\\u5de5\\u5177\\u7c7bCachePrefixUtil\\uff0c\\u8fd8\\u6709\\u7528\\u6237\\u7f13\\u5b58\\u670d\\u52a1UserCacheService\\u7684\\u5b8c\\u6574\\u5b9e\\u73b0\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看更多关于缓存管理器和缓存一致性的实现：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceTest.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述: redis缓存测试类\n    20\t *\n    21\t * <AUTHOR>    22\t * @date ：创建于 2020/12/3\n    23\t */\n    24\t@RunWith(SpringJUnit4ClassRunner.class)\n    25\t@ContextConfiguration(classes = {RedisCacheServiceImpl.class})\n    26\tpublic class RedisCacheServiceTest {\n    27\t\n    28\t    @Resource(name = \&quot;redisCacheServiceImpl\&quot;)\n    29\t    private NativeDiffCacheService nativeDiffCacheService;\n    30\t\n    31\t    @MockBean\n    32\t    private RedisService redisService;\n    33\t\n    34\t    @Test\n    35\t    public void handle() {\n    36\t        when(redisService.getValuesByPrefix(any())).thenReturn(Collections.singletonList(1L));\n    37\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().rootCacheName(\&quot;hy_auth\&quot;).build();\n    38\t        nativeDiffCacheService.handle(nativeCacheDTO);\n    39\t    }\n    40\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     4\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     5\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     6\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     7\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     8\timport com.github.benmanes.caffeine.cache.Cache;\n     9\timport com.google.common.collect.Sets;\n    10\timport org.apache.commons.lang3.StringUtils;\n    11\timport org.springframework.stereotype.Service;\n    12\t\n    13\timport java.util.Set;\n    14\timport java.util.concurrent.ConcurrentMap;\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/test/java/cn/hy/auth/custom/multi/utils/TestCacheConfiguration.java\n...\n    84\t\n    85\t    /**\n    86\t     * 因为按key模糊获取数据的时候没有提供对应的方法，所有如果是redis的时候暴露处理方便使用\n    87\t     *\n    88\t     * @param factory factory\n    89\t     * @return RedisTemplate\n    90\t     */\n    91\t    @Bean\n    92\t    @ConditionalOnMissingBean(RedisTemplate.class)\n    93\t    @ConditionalOnProperty(\n    94\t            prefix = \&quot;auth.cache\&quot;,\n    95\t            name = \&quot;type\&quot;,\n    96\t            havingValue = \&quot;redis\&quot;\n    97\t    )\n    98\t    public RedisTemplate redisTemplate(RedisConnectionFactory factory) {\n    99\t        // 创建一个模板类\n   100\t        RedisTemplate&lt;String, Object&gt; template = new RedisTemplate&lt;&gt;();\n   101\t        // 将刚才的redis连接工厂设置到模板类中\n   102\t        template.setConnectionFactory(factory);\n   103\t\n   104\t        // 设置redis的String/Value的默认序列化方式\n   105\t        template.setKeySerializer(RedisSerializer.string());\n   106\t        template.setValueSerializer(RedisSerializer.java());\n   107\t        template.setHashKeySerializer(RedisSerializer.string());\n   108\t        template.setHashValueSerializer(RedisSerializer.java());\n   109\t        template.afterPropertiesSet();\n   110\t        return template;\n   111\t    }\n   112\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/annotation/NativeCacheHandle.java\n     1\tpackage cn.hy.auth.custom.user.cache.annotation;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     4\t\n     5\timport java.lang.annotation.*;\n     6\t\n     7\t/**\n     8\t * 类描述: 缓存类型枚举类\n     9\t *\n    10\t * <AUTHOR>    11\t * @date ：创建于 2020/11/30\n    12\t */\n    13\t@Documented\n    14\t@Inherited\n    15\t@Target(ElementType.TYPE)\n    16\t@Retention(RetentionPolicy.RUNTIME)\n    17\tpublic @interface NativeCacheHandle {\n    18\t\n    19\t    NativeCacheEnum value();\n    20\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\n   569\t    /**\n   570\t     * 缓存用户信息\n   571\t     *\n   572\t     * @param tokenId     tokenId\n   573\t     * @param userAccount 用户账号信息\n   574\t     * @return 返回用户账号信息\n   575\t     */\n   576\t    private UserAccountDTO putUserToCache(String tokenId, UserAccountDTO userAccount) {\n   577\t        //获取生成账号缓存标识\n   578\t        String userCacheKey = getUserCacheKey(userAccount.getId());\n   579\t        // 租户应用信息\n   580\t        setLesseeAppCodeToUserAccount(userAccount);\n   581\t        // 缓存用户信息\n   582\t        try {\n   583\t\n   584\t            getRootCache().put(userCacheKey, objectMapper.writeValueAsString(userAccount));\n   585\t\n   586\t            log.debug(\&quot;缓存用户信息：用户主键:[{}]-缓存标识:[{}]\&quot;, userAccount.getId(), userCacheKey);\n   587\t        } catch (JsonProcessingException e) {\n   588\t            log.warn(\&quot;json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}\&quot;,userAccount,e.getMessage());\n   589\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordRedisManage.java\n...\n    30\t\n    31\t    @Override\n    32\t    public void put(String key, Object value, Date expireDate) {\n    33\t        redisTemplate.opsForValue().set(key, value);\n    34\t        redisTemplate.expireAt(key, expireDate);\n    35\t    }\n    36\t\n    37\t    @Override\n    38\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    39\t        redisTemplate.opsForValue().set(key, value);\n    40\t        redisTemplate.expire(key, timeout, unit);\n    41\t    }\n    42\t\n    43\t    @Override\n    44\t    public Object get(String key) {\n    45\t        return redisTemplate.opsForValue().get(key);\n    46\t    }\n    47\t\n    48\t    @Override\n    49\t    public boolean invalidate(String key) {\n    50\t        redisTemplate.opsForValue().getOperations().delete(key);\n    51\t        return true;\n    52\t    }\n    53\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/environment/service/OauthSysMServiceImpl.java\n...\n    90\t                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);\n    91\t                DataEngineContextProxy.getContext().setLesseeCode(lesseeCodeBefore);\n    92\t                DataEngineContextProxy.getContext().setAppCode(appCodeBefore);\n    93\t            }\n    94\t        }\n    95\t        return cachedUuid;\n    96\t    }\n    97\t\n    98\t    @Override\n    99\t    public String clearCache() {\n   100\t        // 打印缓存信息\n   101\t        if (log.isInfoEnabled()){\n   102\t            log.info(\&quot;Cache stats: {}\&quot;, cache.stats());\n   103\t        }\n   104\t        // 清空缓存\n   105\t        cache.invalidateAll();\n   106\t        return \&quot;Cache cleared\&quot;;\n   107\t    }\n   108\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/enums/NativeCacheEnum.java\n...\n     7\t\n     8\t/**\n     9\t * 类描述: 缓存类型枚举\n    10\t *\n    11\t * <AUTHOR>    12\t * @date ：创建于 2020/11/30\n    13\t */\n    14\t@Getter\n    15\t@AllArgsConstructor\n    16\tpublic enum NativeCacheEnum {\n    17\t\n    18\t    /**\n    19\t     * caffeine缓存\n    20\t     */\n    21\t    CAFFEINE(CaffeineCache.class, \&quot;caffeine\&quot;),\n    22\t    /**\n    23\t     * redis缓存\n    24\t     */\n    25\t    REDIS(RedisCache.class, \&quot;redis\&quot;);\n    26\t\n    27\t    /**\n    28\t     * class类\n    29...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01R4hET83HKHcmaJxc5CpfAv&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:51:42.860Z&quot;,&quot;response_text&quot;:&quot;现在让我查看更多关于缓存管理器和缓存一致性的实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;13314a64-d5cc-4ff2-b597-a6a4b28f4e8b&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01PeMrGfAa9hGMJzKbDBuwf8&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u627e\\u7f13\\u5b58\\u6e05\\u7406\\u76f8\\u5173\\u7684\\u4ee3\\u7801\\uff0c\\u5305\\u62ecKafka\\u6d88\\u606f\\u76d1\\u542c\\u5668\\u3001\\u7f13\\u5b58\\u5931\\u6548\\u673a\\u5236\\u3001\\u5206\\u5e03\\u5f0f\\u7f13\\u5b58\\u540c\\u6b65\\u3001\\u4ee5\\u53caMetaDataCacheService\\u7b49\\u7f13\\u5b58\\u7ba1\\u7406\\u670d\\u52a1\\u7684\\u5b9e\\u73b0\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看缓存一致性相关的实现，特别是Kafka监听器和缓存清理机制：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java\n     1\tpackage cn.hy.auth.custom.multi.cache;\n     2\t\n     3\timport cn.hutool.core.util.ObjectUtil;\n     4\timport cn.hy.auth.custom.common.utils.CachePrefixUtil;\n     5\timport org.springframework.cache.Cache;\n     6\timport org.springframework.cache.caffeine.CaffeineCacheManager;\n     7\t\n     8\timport java.util.Collection;\n     9\timport java.util.stream.Collectors;\n    10\t\n    11\t/**\n    12\t * 类描述: 继承Caffeine缓存，重新获取cahce方法适应与多租户多应用\n    13\t *\n    14\t * <AUTHOR>    15\t * @date ：创建于 2020/12/1\n    16\t */\n    17\tpublic class LesseeCaffeineCacheManager extends CaffeineCacheManager {\n    18\t\n    19\t    @Override\n    20\t    public void setCacheNames(Collection&lt;String&gt; cacheNames) {\n    21\t        if (ObjectUtil.isNull(cacheNames)) {\n    22\t            super.setCacheNames(null);\n    23\t            return;\n    24\t        }\n    25\t        String prefix = CachePrefixUtil.get();\n    26\t        Collection&lt;String&gt; newCacheNames = cacheNames.stream().filter(ObjectUtil::isNotNull).map(name -&gt; prefix + name).collect(Collectors.toSet());\n    27\t        super.setCacheNames(newCacheNames);\n    28\t    }\n    29\t\n    30\t    @Override\n    31\t    public Cache getCache(String name) {\n    32\t        String prefix = CachePrefixUtil.get();\n    33\t        return super.getCache(prefix + name);\n    34\t    }\n    35\t\n    36\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     4\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     5\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     6\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     7\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     8\timport com.github.benmanes.caffeine.cache.Cache;\n     9\timport com.google.common.collect.Sets;\n    10\timport org.apache.commons.lang3.StringUtils;\n    11\timport org.springframework.stereotype.Service;\n    12\t\n    13\timport java.util.Set;\n    14\timport java.util.concurrent.ConcurrentMap;\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n     1\tpackage cn.hy.auth.custom.business.starter;\n     2\t\n     3\timport cn.hy.auth.custom.common.utils.CachePrefixUtil;\n     4\timport cn.hy.auth.custom.multi.cache.LesseeCaffeineCacheManager;\n     5\timport com.github.benmanes.caffeine.cache.Caffeine;\n     6\timport lombok.extern.slf4j.Slf4j;\n     7\timport org.springframework.beans.factory.annotation.Value;\n     8\timport org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;\n     9\timport org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;\n    10\timport org.springframework.cache.CacheManager;\n    11\timport org.springframework.cache.caffeine.CaffeineCacheManager;\n    12\timport org.springframework.context.annotation.Bean;\n    13\timport org.springframework.context.annotation.Configuration;\n    14\timport org.springframework.data.redis.cache.RedisCacheConfiguration;\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/test/java/cn/hy/auth/custom/multi/utils/TestCacheConfiguration.java\n...\n    47\t    public CacheManager cacheManagerWithCaffeine() {\n    48\t        log.debug(\&quot;Test Caffeine cache 配置\&quot;);\n    49\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    50\t        Caffeine caffeine = Caffeine.newBuilder()\n    51\t                //cache的初始容量值\n    52\t                .initialCapacity(100)\n    53\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    54\t                .maximumSize(5000)\n    55\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    56\t        cacheManager.setCaffeine(caffeine);\n    57\t        //不允许设置空值\n    58\t        cacheManager.setAllowNullValues(false);\n    59\t        return cacheManager;\n    60\t    }\n    61\t\n    62\t    @Bean\n    63\t    @ConditionalOnMissingBean(CacheManager.class)\n    64\t    @ConditionalOnProperty(\n    65\t            prefix = \&quot;auth.cache\&quot;,\n    66\t            name = \&quot;type\&quot;,\n    67\t            havingValue = \&quot;redis\&quot;\n    68\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-common/src/main/java/cn/hy/auth/custom/common/utils/CachePrefixUtil.java\n     1\tpackage cn.hy.auth.custom.common.utils;\n     2\t\n     3\timport cn.hutool.core.util.ObjectUtil;\n     4\timport cn.hy.auth.custom.common.context.AuthContext;\n     5\timport cn.hy.auth.custom.common.domain.LoginStateDTO;\n     6\timport cn.hy.auth.custom.common.enums.ClientTypeEnum;\n     7\timport org.apache.commons.lang3.StringUtils;\n     8\t\n     9\timport java.util.StringJoiner;\n    10\timport java.util.function.Supplier;\n    11\t\n    12\t/**\n    13\t * 类描述: 获取缓存前缀\n    14\t *\n    15\t * <AUTHOR>    16\t * @date ：创建于 2020/12/1\n    17\t */\n    18\tpublic class CachePrefixUtil {\n    19\t    private static final ThreadLocal&lt;Boolean&gt; LOCAL = ThreadLocal.withInitial(() -&gt; Boolean.TRUE);\n    20\t    private CachePrefixUtil() {\n    21\t\n    22\t    }\n    23\t\n    24\t    public static Boolean isAutoAttachCcahePrefix(){\n    25\t        return LOCAL.get();\n    26\t    }\n    27\t    public static void setAutoAttachCachePrefix(Boolean b){\n    28\t         LOCAL.set(b);\n    29\t    }\n    30\t    public static void resetAutoAttachCachePrefix(){\n    31\t        LOCAL.remove();\n    32\t    }\n    33\t    /**\n    34\t     * 返回缓存前缀，格式:租户编码_应用编码_客户端标识\n    35\t     *\n    36\t     * @return 返回前缀\n    37\t     */\n    38\t    public static String get() {\n    39\t        // 不需要自动添加缓存前缀\n    40\t        if (!Boolean.TRUE.equals(isAutoAttachCcahePrefix())){\n    41\t            return \&quot;\&quot;;\n    42\t        }\n    43\t        //租户编码\n    44\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    45\t        //应用编码\n    46\t        String appCode = AuthContext.getContext().getAppCode();\n    47\t        if (StringUtils.isBlank(lesseeCode) &amp;&amp; StringUtils.isBlank(appCode)){\n    48\t            return \&quot;\&quot;;\n    49\t        }\n    50\t        LoginStateDTO loginStateDTO = AuthContext.getContext().loginState();\n    51\t\n    52\t        StringJoiner stringJoiner = new StringJoiner(\&quot;_\&quot;);\n    53\t        stringJoiner.add(lesseeCode).add(appCode);\n    54\t        if (ObjectUtil.isNotNull(loginStateDTO)) {\n    55\t            ClientTypeEnum clientType = loginStateDTO.getClientType();\n    56\t            if (ObjectUtil.isNotNull(clientType)) {\n    57\t                stringJoiner.add(String.valueOf(clientType.getCode()));\n    58\t            }\n    59\t        }\n    60\t\n    61\t        return stringJoiner.toString() + \&quot;_\&quot;;\n    62\t    }\n    63\t\n    64\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n     1\tpackage cn.hy.auth.custom.user.cache.service;\n     2\t\n     3\timport cn.hy.auth.custom.common.domain.UserAccountDTO;\n     4\timport cn.hy.auth.custom.common.domain.UserLoginInfoDTO;\n     5\t\n     6\timport javax.validation.constraints.NotBlank;\n     7\timport java.util.Set;\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     * @return 返回用户缓存信息\n    30\t     */\n    31\t    //UserAccountDTO getCacheByUserId(@NotNull Long userId);\n    32\t\n    33\t    /**\n    34\t     * 按登录账号查询用户信息，没有找到则从数据库中获取并放入缓存\n    35\t     *\n    36\t     * @param loginName 登录账号\n    37\t     * @return 返回用户缓存信息\n    38\t     */\n    39\t    UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName);\n    40\t\n    41\t    /**\n    42\t     * 按登录账号查询用户主键，没有找到则从数据库中获取并放入缓存\n    43\t     *\n    44\t     * @param loginName 登录账号\n    45\t     * @return 返回用户主键\n    46\t     */\n    47\t    //Long getUserIdByLoginName(@NotBlank String loginName);\n    48\t\n    49\t    /**\n    50\t     * 按用户主键移除缓存信息\n    51\t     *\n    52\t     * @param userId 用户主键\n    53\t     */\n    54\t    void removeCacheByUserId(@NotBlank Long userId);\n    55\t\n    56\t    /**\n    57\t     * 按登录账号移除缓存信息\n    58\t     *\n    59\t     * @param loginName 登录账号\n    60\t     */\n    61\t    void removeCacheByLoginName(@NotBlank String loginName);\n    62\t\n    63\t    /**\n    64\t     * 查询在线用户\n    65\t     *\n    66\t     * @return 返回在线用户的主键信息\n    67\t     */\n    68\t    Set&lt;Long&gt; getOnlineUserId();\n    69\t\n    70\t    /**\n    71\t     * 按tokenId查询用户登录数据\n    72\t     *\n    73\t     * @param tokenId ..\n    74\t     * @return 返回登录信息\n    75\t     */\n    76\t    UserLoginInfoDTO getUserLoginInfoByTokenId(@NotBlank String tokenId);\n    77\t\n    78\t\n    79\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n    44\t\n    45\t/**\n    46\t * 类描述: 用户缓存信息实现类\n    47\t *\n    48\t * <AUTHOR>    49\t * @date ：创建于 2020/11/27\n    50\t */\n    51\t@Slf4j\n    52\t@Service\n    53\tpublic class UserCacheServiceImpl implements UserCacheService, UserCacheTokenService {\n    54\t    private final TokenStore tokenStore;\n    55\t    private final ObjectMapper objectMapper;\n    56\t    /**\n    57\t     * 缓存实现类，该处已实现租户应用间的隔离\n    58\t     */\n    59\t    private final CacheManager cacheManager;\n    60\t\n    61\t    /**\n    62\t     * 用户账号信息实现类\n    63\t     */\n    64\t    private final UserAccountService userAccountService;\n    65\t\n    66\t    /**\n    67\t     * 缓存在线人员不同缓存的处理实现\n    68\t     */\n    69\t    private final NativeDiffCacheContext nativeDiffCacheContext;\n    70\t\n    71\t    /**\n    72\t     * 获取租户应用实现\n    73\t     */\n    74\t    private final AppInfoParseProxy appInfoParseProxy;\n    75\t\n    76\t    /**\n    77\t     * 用户登录信息\n    78\t     */\n    79\t    private final AuthLoginInfoService authLoginInfoService;\n    80\t\n    81\t    /**\n    82\t     * 跟主键标识\n    83\t     */\n    84\t    private static final String ROOT_CACHE_NAME = \&quot;hy_auth\&quot;;\n    85\t    /**\n    86\t     * 登录账号\n    87\t     */\n    88\t    private static final String USER_NAME = \&quot;username\&quot;;\n...\n   134\t        if (ObjectUtil.isNull(userAccountDTO)) {\n   135\t            String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n   136\t            synchronized (getLock(lockIdentify)){\n   137\t                try {\n   138\t                    userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n   139\t                    if (userAccountDTO == null){\n   140\t                        // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n   141\t                        userAccountDTO = loadUserAccountByLoginNameAndCache();\n   142\t                    }\n   143\t                    if (userAccountDTO == null) {\n   144\t                        userAccountDTO = loadUserAccountByTokenStore();\n   145\t                    }\n   146\t                }finally {\n   147\t                    removeLock(lockIdentify);\n   148\t                }\n   149\t            }\n   150\t        }\n   151\t        return userAccountDTO;\n   152\t    }\n...\n   229\t\n   230\t    @Override\n   231\t    public Set&lt;Long&gt; getOnlineUserId() {\n   232\t        Cache cache = cacheManager.getCache(ROOT_CACHE_NAME);\n   233\t        Object nativeCache = cache.getNativeCache();\n   234\t        if (ObjectUtil.isNull(nativeCache)) {\n   235\t            return Collections.emptySet();\n   236\t        }\n   237\t        NativeCacheEnum nativeCacheEnum = NativeCacheEnum.classOf(cache);\n   238\t        if (ObjectUtil.isNull(nativeCacheEnum)) {\n   239\t            log.debug(\&quot;没有找到对应的缓存类型[{}]\&quot;, cache.getName());\n   240\t            return Collections.emptySet();\n   241\t        }\n   242\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(nativeCache).rootCacheName(CachePrefixUtil.get() + ROOT_CACHE_NAME).build();\n   243\t        return nativeDiffCacheContext.getNativeCacheHandleMap(nativeCacheEnum.getType()).handle(nativeCacheDTO);\n   244\t    }\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/client/OauthClientDetailsServiceImpl.java\n...\n    84\t\n    85\t    /**\n    86\t     * 获取根部缓存信息\n    87\t     *\n    88\t     * @return 返回缓存对象\n    89\t     */\n    90\t    private Cache getRootCache() {\n    91\t        //租户编码\n    92\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    93\t        //应用编码\n    94\t        S...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01NVZBMipzzauRpTtFvjg4tD&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:51:55.337Z&quot;,&quot;response_text&quot;:&quot;现在让我查看缓存一致性相关的实现，特别是Kafka监听器和缓存清理机制：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;322b195a-3f46-4ab0-92a9-6c1db78eac07&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01K8FCeusfd96mLbnvo2Et9t&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/account/service/impl/UserAccountLockServiceImpl.java\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看具体的缓存注解使用和缓存策略：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenConfigChangedKafkaListener.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：监听更新token配置信息，用于刷新更新缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2024/6/13 15:49\n    22\t **/\n    23\t@Service\n    24\t@Slf4j\n    25\tpublic class TokenConfigChangedKafkaListener {\n    26\t    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;\n    27\t    public TokenConfigChangedKafkaListener(HyClientDetailServiceProvider hyClientDetailServiceProvider) {\n    28\t        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;\n    29\t    }\n    30\t\n    31\t    @KafkaListener(topics = \&quot;${hy.saas.tokenConfig.cluster-cache.topic:saas-app-token-config}\&quot;,\n    32\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    33\t    )\n    34\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    35\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    36\t        try {\n    37\t            if (message.isPresent()) {\n    38\t                String msg = (String)message.get();\n    39\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    40\t                JSONObject jsonObject = JSON.parseObject(msg);\n    41\t                String lessCode = (String) jsonObject.get(\&quot;lessCode\&quot;);\n    42\t                String appCode = (String) jsonObject.get(\&quot;appCode\&quot;);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    if (ObjectUtil.isEmpty(appCode)|| \&quot;appmanage\&quot;.equals(appCode)){\n    46\t                        hyClientDetailServiceProvider.clearCache(lessCode);\n    47\t                    } else {\n    48\t                        hyClientDetailServiceProvider.clearCache(lessCode,appCode);\n    49\t                    }\n    50\t                } catch (Exception e) {\n    51\t                    log.warn(\&quot;处理Kafka的token 配置變化消息{}_{},报错，可以忽略不处理。{}\&quot;,lessCode,appCode,e.getMessage());\n    52\t                }\n    53\t            }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenStatusChangedKafkaListener.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述：监听更新token配置信息，用于刷新更新缓存\n    18\t *\n    19\t * <AUTHOR> by fuxinrong\n    20\t * @date 2024/6/13 15:49\n    21\t **/\n    22\t@Slf4j\n    23\t@Service\n    24\t@ConditionalOnProperty(\n    25\t        prefix = \&quot;auth.token.store\&quot;,\n    26\t        name = \&quot;type\&quot;,\n    27\t        havingValue = \&quot;redisLocalCache\&quot;\n    28\t)\n    29\tpublic class TokenStatusChangedKafkaListener {\n    30\t    private final HyRedisLocalCacheTokenStore hyRedisLocalCacheTokenStore;\n    31\t    public TokenStatusChangedKafkaListener(TokenStore hyRedisLocalCacheTokenStore) {\n    32\t        this.hyRedisLocalCacheTokenStore = (HyRedisLocalCacheTokenStore) hyRedisLocalCacheTokenStore;\n    33\t    }\n    34\t\n    35\t    @KafkaListener(topics = \&quot;${hy.saas.token.status.topic:hy-auth-sys-token-status}\&quot;,\n    36\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    37\t    )\n    38\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    39\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    40\t        try {\n    41\t            if (message.isPresent()) {\n    42\t                String msg = (String)message.get();\n    43\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    44\t                JSONObject jsonObject = JSON.parseObject(msg);\n    45\t                String type = (String) jsonObject.get(\&quot;type\&quot;);\n    46\t                String token = (String) jsonObject.get(\&quot;token\&quot;);\n    47\t                String refreshToken = (String) jsonObject.get(\&quot;refreshToken\&quot;);\n    48\t                try {\n    49\t                    if (\&quot;revoked\&quot;.equals(type)){\n    50\t                        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);\n    51\t                    } else if (\&quot;refreshToken\&quot;.equals(type)){\n    52\t                        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);\n    53\t                    }\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/AuthAppStatusKafkaListener.java\n...\n    14\t\n    15\t/**\n    16\t * <AUTHOR>    17\t * @title: AuthAppStatusKafkaListener\n    18\t * @description: 监听应用状态变更情况，清除缓存\n    19\t * @date 2024/7/1\n    20\t */\n    21\t@Service\n    22\t@Slf4j\n    23\tpublic class AuthAppStatusKafkaListener {\n    24\t\n    25\t    @Value(\&quot;${hy.saas.cluster-cache.app-status-topic:saas-app-status}\&quot;)\n    26\t    private String appStatusTopic;\n    27\t\n    28\t    private final AppInvolvedTableServiceImpl AppInvolvedTableServiceImpl;\n    29\t\n    30\t    public AuthAppStatusKafkaListener(cn.hy.auth.custom.multi.systable.AppInvolvedTableServiceImpl appInvolvedTableServiceImpl) {\n    31\t        AppInvolvedTableServiceImpl = appInvolvedTableServiceImpl;\n    32\t    }\n    33\t\n    34\t    @KafkaListener(topics = \&quot;${hy.saas.cluster-cache.app-status-topic:saas-app-status}\&quot;,\n    35\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    36\t    )\n...\n    48\t                String lessCode = eventMsg.getLesseeCode();\n    49\t                String appCode = eventMsg.getAppCode();\n    50\t                Integer appStatus = eventMsg.getAppStatus();\n    51\t                try {\n    52\t                    AppInvolvedTableServiceImpl.clearAppStatusCache(lessCode, appCode);\n    53\t                    log.info(\&quot;通知更新认证中心应用状态完成：msg：{}\&quot;, LocaleUtil.getMessage(\&quot;AppInfoStatusController.controllerMap.msg4\&quot;, null));\n    54\t                } catch (Exception e) {\n    55\t                    log.warn(\&quot;处理Kafka的应用安装消息{}_{},报错，可以忽略不处理。{}\&quot;, lessCode, appCode, e.getMessage());\n    56\t                    log.error(\&quot;{}_{} 修改应用状态为：{}，失败了。\&quot;, lessCode, appCode, appStatus, e);\n    57\t                }\n    58\t            } else {\n    59\t                log.warn(\&quot;kafka消息内容为空，无法通知更新认证中心应用状态\&quot;);\n    60\t            }\n    61\t        } finally {\n    62\t            ack.acknowledge();\n    63\t        }\n    64\t    }\n    65\t\n    66\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/listener/TokenRevokedEventListener.java\n...\n    59\t\n    60\t        //移除缓存\n    61\t        userCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());\n    62\t\n    63\t        Authentication userAuthentication = ObjectUtil.isNull(tokenRevokedEvent.getOAuth2Authentication()) ? null : tokenRevokedEvent.getOAuth2Authentication().getUserAuthentication();\n    64\t        if (userAuthentication != null) {\n    65\t            Map&lt;String, Object&gt; detail = (Map&lt;String, Object&gt;) userAuthentication.getDetails();\n    66\t            if (MapUtils.isNotEmpty(detail)) {\n    67\t                Object clientId = detail.get(CLIENT_ID);\n    68\t                loginLogDTO.setMac(MapUtils.getString(detail, LOGIN_MAC));\n    69\t                loginLogDTO.setClientId(ObjectUtil.isNull(clientId) ? null : clientId.toString());\n    70\t            }\n    71\t        }\n    72\t\n    73\t        //发出登出需要记录日志的事件\n    74\t        applicationContext.publishEvent(new LogOutLogEvent(loginLogDTO));\n    75\t    }\n    76\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/MetaDataCacheService.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service;\n     2\t\n     3\t/**\n     4\t * @author: ysh\n     5\t * @project: hy-authentication-center\n     6\t * @className: MetaDataCacheService\n     7\t * @time: 2022-11-23 16:56\n     8\t * @desc: 元数据缓存接口\n     9\t **/\n    10\tpublic interface MetaDataCacheService {\n    11\t\n    12\t    /**\n    13\t     * 清除缓存\n    14\t     * @param lessCode 租户编号\n    15\t     * @param appCode 应用编号\n    16\t     */\n    17\t    void cacheClear(String lessCode, String appCode);\n    18\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述：本地缓存和redis2级缓存，解决redis的性能问题\n    20\t *\n    21\t *\n    22\t * <AUTHOR> by fuxinrong\n    23\t * @date 2022/1/11 11:16\n    24\t **/\n    25\t@Slf4j\n    26\tpublic class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener&lt;TokenRevokedEvent&gt; {\n    27\t\n    28\t    private static final String READ_AUTHENTICATION = \&quot;readAuthentication:\&quot;;\n    29\t    private static final String READ_AUTHENTICATION_FOR_REFRESH_TOKEN = \&quot;readAuthenticationForRefreshToken:\&quot;;\n    30\t    private static final String READ_REFRESH_TOKEN = \&quot;readRefreshToken:\&quot;;\n    31\t    private static final String READ_ACCESS_TOKEN = \&quot;readAccessToken:\&quot;;\n    32\t    private static final String REFRESH_TOKEN_TO_ACCESS_TOKEN = \&quot;refreshToken_to_accessToken:\&quot;;\n    33\t    private final ExpiringCache&lt;String,Object&gt; localTokenCache;\n    34\t    private final HyRedisTokenStore hyRedisTokenStore;\n    35\t\n    36\t    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {\n    37\t        localTokenCache = new ExpiringCache&lt;&gt;();\n    38\t        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);\n    39\t    }\n    40\t\n    41\t    public void setRemovingDelta(int removingDelta) {\n    42\t        hyRedisTokenStore.setRemovingDelta(removingDelta);\n    43\t    }\n    44\t\n    45\t    public void setPrefix(String prefix) {\n    46\t        hyRedisTokenStore.setPrefix(prefix);\n    47\t    }\n    48\t\n    49\t    public void setAuthenticationKeyGenerator(AuthenticationKeyGenerator authenticationKeyGenerator) {\n    50\t        hyRedisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);\n    51\t    }\n    52\t\n    53\t    public void setSerializationStrategy(RedisTokenStoreSerializationStrategy serializationStrategy) {\n    54\t        hyRedisTokenStore.setSerializationStrategy(serializationStrategy);\n    55\t    }\n...\nPath: hy-auth-common-business-starter/hy-auth-common-security-oauth2-starter/src/main/java/cn/hy/auth/common/business/oauth2/starter/TokenAutoConfiguration.java\n...\n     2\t\n     3\timport cn.hy.auth.common.security.oauth2.token.store.HyInMemoryTokenStore;\n     4\timport cn.hy.auth.common.security.oauth2.token.store.HyRedisLocalCacheTokenStore;\n     5\timport cn.hy.auth.common.security.oauth2.token.store.HyRedisTokenStore;\n     6\timport cn.hy.auth.common.security.oauth2.token.store.JdbcWithMemoryTokenStore;\n     7\timport lombok.extern.slf4j.Slf4j;\n     8\timport org.springframework.beans.factory.annotation.Value;\n     9\timport org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;\n    10\timport org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;\n    11\timport org.springframework.context.annotation.Bean;\n    12\timport org.springframework.context.annotation.Configuration;\n    13\timport org.springframework.data.redis.connection.RedisConnectionFactory;\n...\n   126\t    public TokenStore redisLoaclCacheTokenStore(RedisConnectionFactory redisConnectionFactory, AuthenticationKeyGenerator authenticationKeyGenerator) {\n   127\t        log.debug(\&quot;使用redisLocalCache 2级缓存token信息,启用HyRedisMemoryTokenStore实例对象。removingDelta：【{}】ms\&quot;,this.removingDelta);\n   128\t        HyRedisLocalCacheTokenStore redisTokenStore = new HyRedisLocalCacheTokenStore(redisConnectionFactory);\n   129\t        redisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);\n   130\t        redisTokenStore.setRemovingDelta(removingDelta);\n   131\t        return redisTokenStore;\n   132\t    }\n   133\t\n   134\t\n   135\t\n   136\t    /**\n   137\t     * 创建TokenStore bean redis模式  数据库+内存\n   138\t     *\n   139\t     * @param dataSource dataSource\n   140\t     * @return 返回TokenStore\n   141\t     */\n   142\t    @Bean\n   143\t    @ConditionalOnMissingBean(TokenStore.class)\n   144\t    @ConditionalOnProperty(\n   145\t            prefix = \&quot;auth.token.store\&quot;,\n   146\t            name = \&quot;type\&quot;,\n   147\t            havingValue = \&quot;cacheJdbc\&quot;\n   148\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n...\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     *...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01PeMrGfAa9hGMJzKbDBuwf8&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:52:11.581Z&quot;,&quot;response_text&quot;:&quot;现在让我查看具体的缓存注解使用和缓存策略：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;eb00431a-b179-4fda-8e1e-618c7c167f25&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01WvNpNrGDxSEBu1mYww8wMr&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u627e\\u9879\\u76ee\\u4e2d\\u4f7f\\u7528@Cacheable\\u3001@CacheEvict\\u3001@CachePut\\u7b49Spring\\u7f13\\u5b58\\u6ce8\\u89e3\\u7684\\u5177\\u4f53\\u5b9e\\u73b0\\uff0c\\u4ee5\\u53ca\\u7f13\\u5b58\\u914d\\u7f6e\\u548c\\u7f13\\u5b58\\u7b56\\u7565\\u7684\\u8be6\\u7ec6\\u4ee3\\u7801\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看更多关于缓存注解的使用：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/account/service/impl/UserAccountLockServiceImpl.java:\n     1\tpackage cn.hy.auth.custom.user.account.service.impl;\n     2\t\n     3\timport cn.hutool.core.date.DateUtil;\n     4\timport cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;\n     5\timport cn.hy.auth.custom.common.context.AuthContext;\n     6\timport cn.hy.auth.custom.common.domain.authinfo.AppAuthAccountLockPolicyDTO;\n     7\timport cn.hy.auth.custom.common.utils.IpUtils;\n     8\timport cn.hy.auth.custom.user.account.dao.UserAccountLockDao;\n     9\timport cn.hy.auth.custom.user.account.dao.UserLoginFailureDao;\n    10\timport cn.hy.auth.custom.user.account.domain.UserAccountLock;\n    11\timport cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;\n    12\timport cn.hy.auth.custom.user.account.domain.UserLoginFailure;\n    13\timport cn.hy.auth.custom.user.account.service.UserAccountLockService;\n    14\timport cn.hy.auth.custom.user.account.service.UserAccountService;\n    15\timport cn.hy.id.IdWorker;\n    16\timport lombok.AllArgsConstructor;\n    17\timport org.apache.commons.lang3.time.DateUtils;\n    18\timport org.springframework.stereotype.Service;\n    19\t\n    20\timport java.math.BigDecimal;\n    21\timport java.util.Date;\n    22\timport java.util.HashMap;\n    23\timport java.util.Map;\n    24\timport java.util.concurrent.ConcurrentHashMap;\n    25\t\n    26\t/**\n    27\t * 类描述：用户账号锁定服务\n    28\t *\n    29\t * <AUTHOR> by fuxinrong\n    30\t * @date 2022/4/14 11:49\n    31\t **/\n    32\t@Service\n    33\t@AllArgsConstructor\n    34\tpublic class UserAccountLockServiceImpl implements UserAccountLockService, ValidateErrorService {\n    35\t    private final UserAccountLockDao userAccountLockDao;\n    36\t    private final UserLoginFailureDao userLoginFailureDao;\n    37\t    private final IdWorker idWorker;\n    38\t    private final UserAccountService userAccountService;\n    39\t    private final ConcurrentHashMap&lt;String, Object&gt; parallelLockMap = new ConcurrentHashMap&lt;&gt;();\n    40\t    @Override\n    41\t    public UserLoginFailure saveLoginFail(Long userId, String username) {\n    42\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n    43\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n    44\t            return null;\n    45\t        }\n    46\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    47\t        String appCode = AuthContext.getContext().getAppCode();\n    48\t        // 保存登录失败次数和相关信息，\n    49\t        synchronized (getSerialNoRecordLoadingLock(lesseeCode+appCode+username)){\n    50\t            UserLoginFailure userLoginFailure = userLoginFailureDao.selectByUid(userId);\n    51\t            if (userLoginFailure == null){\n    52\t                userLoginFailure = UserLoginFailure.builder()\n    53\t                        .id(new BigDecimal(idWorker.nextId()))\n    54\t                        .createUserId(new BigDecimal(userId))\n    55\t                        .lastUpdateUserId(new BigDecimal(userId))\n    56\t                        .createTime(new Date())\n    57\t                        .lastUpdateTime(new Date())\n    58\t                        .loginTime(new Date())\n    59\t                        .userId(new BigDecimal(userId))\n    60\t                        .dataVersion(\&quot;1\&quot;)\n    61\t                        .loginFailureCount(1L)\n    62\t                        .build();\n    63\t                userLoginFailureDao.insert(userLoginFailure);\n    64\t            } else {\n    65\t                // 判断是自增，还是从0开始输入\n    66\t                // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）\n    67\t                boolean restLoginFailureCount;\n    68\t                if (\&quot;minuteTime\&quot;.equals(accountLockPolicy.getLockType())){\n    69\t                    // 指定分钟后自动恢复\n    70\t                    String loginErrorThreshold = accountLockPolicy.getLoginErrorThreshold();\n    71\t                    int loginErrorThresholdInt = Integer.parseInt(loginErrorThreshold);\n    72\t                    restLoginFailureCount = DateUtil.compare(new Date(),DateUtils.addMinutes(userLoginFailure.getLoginTime(),loginErrorThresholdInt)) &gt;= 0;\n    73\t                } else {\n    74\t                    // 次日之后可自动恢复\n    75\t                    restLoginFailureCount = DateUtil.compare(DateUtil.parse(DateUtil.today()),userLoginFailure.getLoginTime()) &gt;= 0;\n    76\t                }\n    77\t                updateUserLoginFailure(userLoginFailure,restLoginFailureCount);\n    78\t            }\n    79\t            // 判断是否需要锁定账号\n    80\t            processLockAccount(userLoginFailure,accountLockPolicy);\n    81\t            return userLoginFailure;\n    82\t        }\n    83\t    }\n    84\t\n    85\t    private Object getSerialNoRecordLoadingLock(String identify) {\n    86\t         return parallelLockMap.computeIfAbsent(identify, t -&gt; new Object());\n    87\t    }\n    88\t\n    89\t    @Override\n    90\t    public boolean isUserAccountLock(Long userId) {\n    91\t        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断\n    92\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n    93\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n    94\t            // 未启用\n    95\t            return false;\n    96\t        }\n    97\t        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);\n    98\t        if (userAccountLock ==null) {\n    99\t            return false;\n   100\t        }\n   101\t        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())&gt;= 0) {\n   102\t            userAccountLockDao.deleteByUid(userId);\n   103\t            // 已过锁定有效期\n   104\t            return false;\n   105\t        }\n   106\t        return true;\n   107\t    }\n   108\t\n   109\t    @Override\n   110\t    public UserAccountLock getUserAccountLock(Long userId) {\n   111\t        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断\n   112\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n   113\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n   114\t            // 未启用\n   115\t            return null;\n   116\t        }\n   117\t        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);\n   118\t        if (userAccountLock ==null) {\n   119\t            return null;\n   120\t        }\n   121\t        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())&gt;= 0) {\n   122\t            userAccountLockDao.deleteByUid(userId);\n   123\t            // 已过锁定有效期\n   124\t            return null;\n   125\t        }\n   126\t        return userAccountLock;\n   127\t    }\n   128\t\n   129\t    @Override\n   130\t    public void clearLoginFail(Long userId, String username) {\n   131\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n   132\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n   133\t            // 未启用\n   134\t            return ;\n   135\t        }\n   136\t        userLoginFailureDao.deleteByUid(userId);\n   137\t\n   138\t    }\n   139\t\n   140\t    @Override\n   141\t    public void lockUserAccount(String userName, String phone, String lockType) {\n   142\t        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(userName, phone);\n   143\t        UserAccountLock lock = new UserAccountLock();\n   144\t        lock.setId(new BigDecimal(idWorker.nextId()));\n   145\t        lock.setCreateUserId(new BigDecimal(1));\n   146\t        lock.setCreateUserName(\&quot;admin\&quot;);\n   147\t        lock.setCreateTime(new Date());\n   148\t        lock.setDataVersion(\&quot;1\&quot;);\n   149\t\n   150\t        lock.setLockEndTime(DateUtil.parse(\&quot;2099-01-01\&quot;));\n   151\t        lock.setClientType(\&quot;4\&quot;);\n   152\t        lock.setLastUpdateUserId(new BigDecimal(1L));\n   153\t        lock.setLastUpdateTime(new Date());\n   154\t        lock.setSequence(1L);\n   155\t        lock.setUserId(new BigDecimal(userByUserNameOrPhone.getId()));\n   156\t        lock.setLockStartTime(new Date());\n   157\t        lock.setLastUpdateUserName(\&quot;admin\&quot;);\n   158\t        lock.setLockRecodeType(lockType);\n   159\t        userAccountLockDao.insert(lock);\n   160\t    }\n   161\t\n   162\t    @Override\n   163\t    public Map&lt;String, Object&gt; isUserAccountLockByUserNameOrMobile(String phoneNumber) {\n   164\t        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(phoneNumber, phoneNumber);\n   165\t        Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   166\t        map.put(\&quot;userId\&quot;, \&quot;\&quot;);\n   167\t        if (userByUserNameOrPhone == null) {\n   168\t            map.put(\&quot;userExist\&quot;, false);\n   169\t            map.put(\&quot;judge\&quot;, false);\n   170\t            return map;\n   171\t        }\n   172\t        boolean result = isUserAccountLock(userByUserNameOrPhone.getId());\n   173\t        map.put(\&quot;judge\&quot;, result);\n   174\t        map.put(\&quot;userExist\&quot;, true);\n   175\t        map.put(\&quot;userId\&quot;, userByUserNameOrPhone.getId());\n   176\t        map.put(\&quot;userAccountName\&quot;, userByUserNameOrPhone.getUserAccountName());\n   177\t        return map;\n   178\t    }\n   179\t\n   180\t\n   181\t    private void processLockAccount(UserLoginFailure userLoginFailure,AppAuthAccountLockPolicyDTO accountLockPolicy){\n   182\t        if (userLoginFailure.getLoginFailureCount() &gt;= Integer.parseInt(accountLockPolicy.getLoginErrorThreshold())){\n   183\t            userAccountLockDao.deleteByUid(userLoginFailure.getUserId());\n   184\t            UserAccountLock userAccountLock = UserAccountLock.builder()\n   185\t                    .id(new BigDecimal(idWorker.nextId()))\n   186\t                    .createUserId(userLoginFailure.getUserId())\n   187\t                    .lastUpdateUserId(userLoginFailure.getUserId())\n   188\t                    .userId(userLoginFailure.getUserId())\n   189\t                    .ip(IpUtils.getIpAddress())\n   190\t                    .lockStartTime(new Date())\n   191\t                    .createTime(new Date())\n   192\t                    .lastUpdateTime(new Date())\n   193\t                    .dataVersion(\&quot;1\&quot;)\n   194\t                    .clientType(AuthContext.getContext().loginState().getClientType().name())\n   195\t                    .lockEndTime(getLockEndTime(accountLockPolicy))\n   196\t                    .build();\n   197\t            userAccountLockDao.insert(userAccountLock);\n   198\t        }\n   199\t    }\n   200\t    private Date getLockEndTime(AppAuthAccountLockPolicyDTO accountLockPolicy) {\n   201\t        // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）\n   202\t        if (\&quot;forever\&quot;.equals(accountLockPolicy.getLockType())){\n   203\t            // 锁定100年\n   204\t            return DateUtils.addYears(DateUtil.parse(DateUtil.today()),100);\n   205\t        }else if (\&quot;minuteTime\&quot;.equals(accountLockPolicy.getLockType())){\n   206\t            String lockMinute = accountLockPolicy.getLockMinute();\n   207\t            int lockMinuteInt = Integer.parseInt(lockMinute);\n   208\t            return DateUtils.addMinutes(new Date(),lockMinuteInt);\n   209\t        }else {\n   210\t            // 次日\n   211\t            return DateUtils.addDays(DateUtil.parse(DateUtil.today()),1);\n   212\t        }\n   213\t    }\n   214\t\n   215\t\n   216\t    private void updateUserLoginFailure(UserLoginFailure userLoginFailure, boolean restLoginFailureCount){\n   217\t        if (restLoginFailureCount){\n   218\t            // 可以从1开始了\n   219\t            userLoginFailure.setLoginFailureCount(1L);\n   220\t        }else {\n   221\t            // 自增\n   222\t            Long loginFailureCount = userLoginFailure.getLoginFailureCount();\n   223\t            if (loginFailureCount==null){\n   224\t                loginFailureCount = 0L;\n   225\t            }\n   226\t            loginFailureCount++;\n   227\t            userLoginFailure.setLoginFailureCount(loginFailureCount);\n   228\t        }\n   229\t        userLoginFailure.setLoginTime(new Date());\n   230\t        userLoginFailure.setLastUpdateTime(new Date());\n   231\t        userLoginFailureDao.updateByPrimaryKey(userLoginFailure);\n   232\t    }\n   233\t\n   234\t    @Override\n   235\t    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {\n   236\t        userLoginFailureDao.resetFailCount(userId);\n   237\t    }\n   238\t\n   239\t    @Override\n   240\t    public Map&lt;String, Object&gt; isUserAccountLockByEmail(String email) {\n   241\t        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);\n   242\t        Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   243\t        map.put(\&quot;userId\&quot;, \&quot;\&quot;);\n   244\t        if (userLoginAccountDTO == null) {\n   245\t            map.put(\&quot;userExist\&quot;, false);\n   246\t            map.put(\&quot;judge\&quot;, false);\n   247\t            return map;\n   248\t        }\n   249\t        boolean result = isUserAccountLock(userLoginAccountDTO.getId());\n   250\t        map.put(\&quot;judge\&quot;, result);\n   251\t        map.put(\&quot;userExist\&quot;, true);\n   252\t        map.put(\&quot;userId\&quot;, userLoginAccountDTO.getId());\n   253\t        map.put(\&quot;userAccountName\&quot;, userLoginAccountDTO.getUserAccountName());\n   254\t        return map;\n   255\t    }\n   256\t\n   257\t    @Override\n   258\t    public void lockUserAccountByEmail(String email, String lockType) {\n   259\t        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);\n   260\t        UserAccountLock lock = new UserAccountLock();\n   261\t        lock.setId(new BigDecimal(idWorker.nextId()));\n   262\t        lock.setCreateUserId(new BigDecimal(1));\n   263\t        lock.setCreateUserName(\&quot;admin\&quot;);\n   264\t        lock.setCreateTime(new Date());\n   265\t        lock.setDataVersion(\&quot;1\&quot;);\n   266\t        lock.setLockEndTime(DateUtil.parse(\&quot;2099-01-01\&quot;));\n   267\t        lock.setClientType(\&quot;4\&quot;);\n   268\t        lock.setLastUpdateUserId(new BigDecimal(1L));\n   269\t        lock.setLastUpdateTime(new Date());\n   270\t        lock.setSequence(1L);\n   271\t        lock.setUserId(new BigDecimal(userLoginAccountDTO.getId()));\n   272\t        lock.setLockStartTime(new Date());\n   273\t        lock.setLastUpdateUserName(\&quot;admin\&quot;);\n   274\t        lock.setLockRecodeType(lockType);\n   275\t        userAccountLockDao.insert(lock);\n   276\t    }\n   277\t\n   278\t}\n   279\t\nTotal lines in file: 279\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01K8FCeusfd96mLbnvo2Et9t&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:52:17.792Z&quot;,&quot;response_text&quot;:&quot;现在让我查看更多关于缓存注解的使用：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9c7d1f57-51c5-4488-9539-afdc09fa5978&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_011AmTUNnBeU4tpXP1YxnFp4&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u627e\\u9879\\u76ee\\u4e2d\\u5177\\u4f53\\u4f7f\\u7528Spring\\u7f13\\u5b58\\u6ce8\\u89e3(@Cacheable\\u3001@CacheEvict\\u3001@CachePut)\\u7684\\u670d\\u52a1\\u7c7b\\u548c\\u65b9\\u6cd5\\uff0c\\u7279\\u522b\\u662f\\u591a\\u79df\\u6237\\u5e94\\u7528\\u76f8\\u5173\\u7684\\u7f13\\u5b58\\u5b9e\\u73b0\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看具体使用缓存注解的服务类：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   229\t\n   230\t    @Override\n   231\t    public Set&lt;Long&gt; getOnlineUserId() {\n   232\t        Cache cache = cacheManager.getCache(ROOT_CACHE_NAME);\n   233\t        Object nativeCache = cache.getNativeCache();\n   234\t        if (ObjectUtil.isNull(nativeCache)) {\n   235\t            return Collections.emptySet();\n   236\t        }\n   237\t        NativeCacheEnum nativeCacheEnum = NativeCacheEnum.classOf(cache);\n   238\t        if (ObjectUtil.isNull(nativeCacheEnum)) {\n   239\t            log.debug(\&quot;没有找到对应的缓存类型[{}]\&quot;, cache.getName());\n   240\t            return Collections.emptySet();\n   241\t        }\n   242\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(nativeCache).rootCacheName(CachePrefixUtil.get() + ROOT_CACHE_NAME).build();\n   243\t        return nativeDiffCacheContext.getNativeCacheHandleMap(nativeCacheEnum.getType()).handle(nativeCacheDTO);\n   244\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/KickOutTokenStoreServiceImpl.java\n...\n    43\t\n    44\t    @Override\n    45\t    public KickOutEnum getKickOutTokenMsg(String token) {\n    46\t        if (cacheManager == null) {\n    47\t            log.error(\&quot;缓存管理器为null，无法获取缓存。\&quot;);\n    48\t            return null;\n    49\t        }\n    50\t\n    51\t        Cache cache = cacheManager.getCache(CACHENAME);\n    52\t        if (cache == null) {\n    53\t            log.info(\&quot;缓存为null，判断是否为剔出的token。token:{}\&quot;, token);\n    54\t            return null;\n    55\t        }\n    56\t\n    57\t        Cache.ValueWrapper valueWrapper = cache.get(token);\n    58\t        if (valueWrapper == null) {\n    59\t            return null;\n    60\t        }\n    61\t\n    62\t        Object value = valueWrapper.get();\n    63\t        if (value == null) {\n    64\t            return null;\n    65\t        }\n    66\t\n    67\t        if (!(value instanceof String)) {\n    68\t            log.warn(\&quot;缓存中的值类型不正确，期望为String，实际为{}\&quot;, value.getClass().getName());\n    69\t            return null;\n    70\t        }\n    71\t\n    72\t        try {\n    73\t            return KickOutEnum.getByCode((String) value);\n    74\t        } catch (Exception e) {\n    75\t            log.error(\&quot;处理缓存值时发生异常\&quot;, e);\n    76\t            return null;\n    77\t        }\n    78\t    }\n    79\t\n    80\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/annotation/NativeCacheHandle.java\n     1\tpackage cn.hy.auth.custom.user.cache.annotation;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     4\t\n     5\timport java.lang.annotation.*;\n     6\t\n     7\t/**\n     8\t * 类描述: 缓存类型枚举类\n     9\t *\n    10\t * <AUTHOR>    11\t * @date ：创建于 2020/11/30\n    12\t */\n    13\t@Documented\n    14\t@Inherited\n    15\t@Target(ElementType.TYPE)\n    16\t@Retention(RetentionPolicy.RUNTIME)\n    17\tpublic @interface NativeCacheHandle {\n    18\t\n    19\t    NativeCacheEnum value();\n    20\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-center-boot/src/main/resources/config/application-dev.yml\n...\n   240\t        - /log/cache/clear\n   241\t        - /*/cache/clear/**\n   242\t        - /license/*\n   243\t        - /verifyCode/sliderCaptcha/**\n   244\t        - /appInfo/status\n   245\t        - /meta/data/cache/clear\n   246\t        - /**/user/checkUserAccount\n   247\t        - /free/log/setLevel\n   248\t    verifyCode:\n   249\t      maxFailedCount: 1000\n   250\t      # 验证码设置容错，越大代表容错率越高\n   251\t      tolerant: 0.03f\n   252\t  license:\n   253\t    excludesPattern: /**/actuator/**,/license/*\n   254\t    filterOrder: -********\n   255\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   256\t  dataEngine:\n   257\t    switch:\n   258\t      placeholder: false\n   259\tauth:\n   260\t  cache:\n   261\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   262\t    type: redis\n   263\t    #过期时间(s)\n   264\t    expire_time: 6000\n   265\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   266\t  token:\n   267\t    store:\n   268\t      type: redis\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-prod.yml\n...\n   232\t        - /**/user/birdThirdMember\n   233\t        - /**/user/bingThirdAccount\n   234\t        - /**/user/removeThirdBing\n   235\t      non-business:\n   236\t        - /**/actuator/**\n   237\t        - /route/cache/clear\n   238\t        - /log/cache/clear\n   239\t        - /*/cache/clear/**\n   240\t        - /license/*\n   241\t        - /verifyCode/sliderCaptcha/**\n   242\t        - /appInfo/status\n   243\t        - /meta/data/cache/clear\n   244\t        - /**/user/checkUserAccount\n   245\t        - /free/log/setLevel\n   246\t    verifyCode:\n   247\t      # 0 表示每次登录都要输入验证码\n   248\t      maxFailedCount: 1\n   249\t      # 验证码设置容错，越大代表容错率越高\n   250\t      tolerant: 0.03f\n   251\t  license:\n   252\t    excludesPattern: /**/actuator/**,/license/*\n   253\t    filterOrder: -********\n   254\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   255\t  dataEngine:\n   256\t    switch:\n   257\t      placeholder: false\n   258\tauth:\n   259\t  system_config:\n   260\t    #服务基准配置信息缓存类型,caffeine(caffeine内存模式，默认),redis(redis模式)\n   261\t    cache: caffeine\n   262\t    #过期时间(s)\n   263\t    expire_time: 3600\n   264\t  cache:\n   265\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   266\t    type: caffeine\n   267\t    #过期时间(s)\n   268\t    expire_time: 6000\n   269\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   270\t  token:\n   271\t    store:\n   272\t      type: jvm\n   273\t\n   274\t# 滑块验证码配置， 详细请看 cloud.tianai.captcha.autoconfiguration.ImageCaptchaProperties 类\n   275\tcaptcha:\n   276\t  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider\n   277\t  prefix: captcha\n   278\t  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整\n   279\t  expire:\n   280\t    # 默认缓存时间 2分钟\n   281\t    default: 60000\n   282\t    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些\n   283\t    WORD_IMAGE_CLICK: 20000\n   284\t  # 使用加载系统自带的资源， 默认是 false\n   285\t  init-default-resource: true\n   286\t  cache:\n   287\t    # 缓存控制， 默认为false不开启\n   288\t    enabled: true\n   289\t    # 验证码会提前缓存一些生成好的验证数据， 默认是20\n   290\t    cacheSize: 20\n   291\t    # 缓存拉取失败后等待时间 默认是 5秒钟\n   292\t    wait-time: 1000\n   293\t    # 缓存检查间隔 默认是2秒钟\n   294\t    period: 5000\n   295\t    secondary:\n   296\t      # 二次验证， 默认false 不开启\n   297\t      enabled: false\n...\nPath: hy-auth-center-boot/src/main/resources/config/application.yml\n...\n   175\t        - /verifyCode/sliderCaptcha/**\n   176\t        - /meta/data/cache/clear\n   177\t        - /**/user/checkUserAccount\n   178\t    verifyCode:\n   179\t      maxFailedCount: 1\n   180\t      # 验证码设置容错，越大代表容错率越高\n   181\t      tolerant: 0.03f\n   182\t  license:\n   183\t    excludesPattern: /**/actuator/**,/license/*\n   184\t    filterOrder: -********\n   185\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   186\t  dataEngine:\n   187\t    switch:\n   188\t      placeholder: false\n   189\tauth:\n   190\t  cache:\n   191\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   192\t    type: caffeine\n   193\t    #过期时间(s)\n   194\t    expire_time: 6000\n   195\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   196\t  token:\n   197\t    store:\n   198\t      type: jvm\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/AutoOfflineCacheConfiguration.java\n...\n    65\t    @Qualifier(\&quot;autoOfflineCacheManager\&quot;)\n    66\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n    67\t        log.debug(\&quot;autoOfflineCacheManager redis cache 配置\&quot;);\n    68\t\n    69\t        //初始化一个RedisCacheWriter\n    70\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n    71\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n    72\t                //不允许存入空值\n    73\t                .disableCachingNullValues()\n    74\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n    75\t                //设置默认超过期时间是3600秒\n    76\t                .entryTtl(Duration.ofSeconds(expireTime));\n    77\t        //初始化RedisCacheManager\n    78\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n    79\t    }\n    80\t\n    81\t\n    82\t\n    83\t}\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-cloud.yml\n...\n   171\t        - /**/user/account/current\n   172\t        - /**/user/association/current\n   173\t        - /**/free/account/online/list\n   174\t        - /**/user/pwd\n   175\t        - /**/history/login/last\n   176\t        - /**/user/currentLoginUser\n   177\t      non-business:\n   178\t        - /**/actuator/**\n   179\t        - /route/cache/clear\n   180\t        - /log/cache/clear\n   181\t        - /license/*\n   182\t        - /verifyCode/sliderCaptcha/**\n   183\t    verifyCode:\n   184\t      maxFailedCount: 1\n   185\t      # 验证码设置容错，越大代表容错率越高\n   186\t      tolerant: 0.03f\n   187\t  license:\n   188\t    excludesPattern: /**/actuator/**,/license/*\n   189\t    filterOrder: -********\n   190\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   191\t  dataEngine:\n   192\t    switch:\n   193\t      placeholder: f...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01WvNpNrGDxSEBu1mYww8wMr&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:52:31.792Z&quot;,&quot;response_text&quot;:&quot;现在让我查看具体使用缓存注解的服务类：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f89357c9-3194-4c1c-adaf-bbc0646401dd&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_011JKRz5CfgMaM1uxeiAYZwj&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u641c\\u7d22\\u9879\\u76ee\\u4e2d\\u5305\\u542b@Cacheable\\u3001@CacheEvict\\u3001@CachePut\\u3001@Caching\\u7b49Spring\\u7f13\\u5b58\\u6ce8\\u89e3\\u7684\\u5177\\u4f53\\u65b9\\u6cd5\\u5b9e\\u73b0\\uff0c\\u4ee5\\u53caAppInvolvedTableServiceImpl\\u7b49\\u670d\\u52a1\\u7c7b\\u7684\\u7f13\\u5b58\\u4f7f\\u7528\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查找具体使用@Cacheable等注解的类：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.common.business.tool.redis.service.RedisService;\n     4\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     5\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     6\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     7\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     8\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     9\timport lombok.AllArgsConstructor;\n    10\timport org.springframework.stereotype.Service;\n    11\t\n    12\timport java.util.List;\n    13\timport java.util.Set;\n    14\timport java.util.stream.Collectors;\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java\n...\n    10\t\n    11\t/**\n    12\t * 类描述: 继承Caffeine缓存，重新获取cahce方法适应与多租户多应用\n    13\t *\n    14\t * <AUTHOR>    15\t * @date ：创建于 2020/12/1\n    16\t */\n    17\tpublic class LesseeCaffeineCacheManager extends CaffeineCacheManager {\n    18\t\n    19\t    @Override\n    20\t    public void setCacheNames(Collection&lt;String&gt; cacheNames) {\n    21\t        if (ObjectUtil.isNull(cacheNames)) {\n    22\t            super.setCacheNames(null);\n    23\t            return;\n    24\t        }\n    25\t        String prefix = CachePrefixUtil.get();\n    26\t        Collection&lt;String&gt; newCacheNames = cacheNames.stream().filter(ObjectUtil::isNotNull).map(name -&gt; prefix + name).collect(Collectors.toSet());\n    27\t        super.setCacheNames(newCacheNames);\n    28\t    }\n    29\t\n    30\t    @Override\n    31\t    public Cache getCache(String name) {\n    32\t        String prefix = CachePrefixUtil.get();\n    33\t        return super.getCache(prefix + name);\n    34\t    }\n    35\t\n    36\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   134\t        if (ObjectUtil.isNull(userAccountDTO)) {\n   135\t            String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n   136\t            synchronized (getLock(lockIdentify)){\n   137\t                try {\n   138\t                    userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n   139\t                    if (userAccountDTO == null){\n   140\t                        // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n   141\t                        userAccountDTO = loadUserAccountByLoginNameAndCache();\n   142\t                    }\n   143\t                    if (userAccountDTO == null) {\n   144\t                        userAccountDTO = loadUserAccountByTokenStore();\n   145\t                    }\n   146\t                }finally {\n   147\t                    removeLock(lockIdentify);\n   148\t                }\n   149\t            }\n   150\t        }\n   151\t        return userAccountDTO;\n   152\t    }\n...\n   208\t\n   209\t    @Override\n   210\t    public void removeCacheByUserId(@NotBlank Long userId) {\n   211\t        String tokenId = getTokenIdByUserId(userId);\n   212\t        if (StringUtils.isBlank(tokenId)) {\n   213\t            log.warn(\&quot;按用户主键[{}]获取tokenId失败,获取为空\&quot;, userId);\n   214\t            return;\n   215\t        }\n   216\t        //移除缓存信息\n   217\t        deleteUserCacheByTokenId(tokenId);\n   218\t    }\n   219\t\n   220\t    @Override\n   221\t    public void removeCacheByLoginName(@NotBlank String loginName) {\n   222\t        Long userId = getUserIdFromCacheByLoginName(loginName);\n   223\t        if (ObjectUtil.isNull(userId)) {\n   224\t            log.warn(\&quot;按登录账号[{}]获取用户主键失败，主键为空\&quot;, loginName);\n   225\t            return;\n   226\t        }\n   227\t        removeCacheByUserId(userId);\n   228\t    }\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\n   569\t    /**\n   570\t     * 缓存用户信息\n   571\t     *\n   572\t     * @param tokenId     tokenId\n   573\t     * @param userAccount 用户账号信息\n   574\t     * @return 返回用户账号信息\n   575\t     */\n   576\t    private UserAccountDTO putUserToCache(String tokenId, UserAccountDTO userAccount) {\n   577\t        //获取生成账号缓存标识\n   578\t        String userCacheKey = getUserCacheKey(userAccount.getId());\n   579\t        // 租户应用信息\n   580\t        setLesseeAppCodeToUserAccount(userAccount);\n   581\t        // 缓存用户信息\n   582\t        try {\n   583\t\n   584\t            getRootCache().put(userCacheKey, objectMapper.writeValueAsString(userAccount));\n   585\t\n   586\t            log.debug(\&quot;缓存用户信息：用户主键:[{}]-缓存标识:[{}]\&quot;, userAccount.getId(), userCacheKey);\n   587\t        } catch (JsonProcessingException e) {\n   588\t            log.warn(\&quot;json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}\&quot;,userAccount,e.getMessage());\n   589\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/environment/service/OauthSysMServiceImpl.java\n...\n    90\t                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);\n    91\t                DataEngineContextProxy.getContext().setLesseeCode(lesseeCodeBefore);\n    92\t                DataEngineContextProxy.getContext().setAppCode(appCodeBefore);\n    93\t            }\n    94\t        }\n    95\t        return cachedUuid;\n    96\t    }\n    97\t\n    98\t    @Override\n    99\t    public String clearCache() {\n   100\t        // 打印缓存信息\n   101\t        if (log.isInfoEnabled()){\n   102\t            log.info(\&quot;Cache stats: {}\&quot;, cache.stats());\n   103\t        }\n   104\t        // 清空缓存\n   105\t        cache.invalidateAll();\n   106\t        return \&quot;Cache cleared\&quot;;\n   107\t    }\n   108\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceTest.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述: redis缓存测试类\n    20\t *\n    21\t * <AUTHOR>    22\t * @date ：创建于 2020/12/3\n    23\t */\n    24\t@RunWith(SpringJUnit4ClassRunner.class)\n    25\t@ContextConfiguration(classes = {RedisCacheServiceImpl.class})\n    26\tpublic class RedisCacheServiceTest {\n    27\t\n    28\t    @Resource(name = \&quot;redisCacheServiceImpl\&quot;)\n    29\t    private NativeDiffCacheService nativeDiffCacheService;\n    30\t\n    31\t    @MockBean\n    32\t    private RedisService redisService;\n    33\t\n    34\t    @Test\n    35\t    public void handle() {\n    36\t        when(redisService.getValuesByPrefix(any())).thenReturn(Collections.singletonList(1L));\n    37\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().rootCacheName(\&quot;hy_auth\&quot;).build();\n    38\t        nativeDiffCacheService.handle(nativeCacheDTO);\n    39\t    }\n    40\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n...\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     * @return 返回用户缓存信息\n    30\t     */\n    31\t    //UserAccountDTO getCacheByUserId(@NotNull Long userId);\n    32\t\n    33\t    /**\n    34\t     * 按登录账号查询用户信息，没有找到则从数据库中获取并放入缓存\n    35\t     *\n    36\t     * @param loginName 登录账号\n    37\t     * @return 返回用户缓存信息\n    38\t     */\n    39\t    UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName);\n    40\t\n    41\t    /**\n    42\t     * 按登录账号查询用户主键，没有找到则从数据库中获取并放入缓存\n    43\t     *\n    44\t     * @param loginName 登录账号\n    45\t     * @return 返回用户主键\n    46\t     */\n    47\t    //Long getUserIdByLoginName(@NotBlank String loginName);\n    48\t\n    49\t    /**\n    50\t     * 按用户主键移除缓存信息\n    51\t     *\n    52\t     * @param userId 用户主键\n    53\t     */\n    54\t    void removeCacheByUserId(@NotBlank Long userId);\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_011AmTUNnBeU4tpXP1YxnFp4&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:52:49.093Z&quot;,&quot;response_text&quot;:&quot;现在让我查找具体使用@Cacheable等注解的类：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;23ce3854-6615-439a-96bd-2d7a15fc5261&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对项目代码的深入分析，我现在可以为您详细解析这个认证中心项目的二级缓存实现和缓存一致性保证机制。\n\n## 项目二级缓存实现分析\n\n### 1. 二级缓存架构设计\n\n这个项目实现了一个完整的二级缓存架构，主要包含以下层次：\n\n#### **第一级缓存：本地缓存（Caffeine）**\n- 使用 Caffeine 作为本地内存缓存\n- 提供最快的访问速度\n- 支持多租户隔离\n\n#### **第二级缓存：分布式缓存（Redis）**\n- 使用 Redis 作为分布式缓存\n- 支持集群间数据共享\n- 提供持久化能力\n\n### 2. 缓存配置实现\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n@ConditionalOnProperty(\n        prefix = \&quot;auth.cache\&quot;,\n        name = \&quot;type\&quot;,\n        havingValue = \&quot;caffeine\&quot;,\n        matchIfMissing = true\n)\npublic CacheManager cacheManagerWithCaffeine() {\n    log.debug(\&quot;Caffeine cache 配置\&quot;);\n    LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    Caffeine caffeine = Caffeine.newBuilder()\n            //cache的初始容量值\n            .initialCapacity(100)\n            //maximumSize用来控制cache的最大缓存数量\n            .maximumSize(maximumSize)\n            .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    cacheManager.setCaffeine(caffeine);\n    //不允许设置空值\n    cacheManager.setAllowNullValues(false);\n    return cacheManager;\n}\n```\n&lt;/augment_code_snippet&gt;\n\n### 3. 多租户缓存隔离\n\n项目通过自定义的 `LesseeCaffeineCacheManager` 实现多租户缓存隔离：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@Override\npublic Cache getCache(String name) {\n    String prefix = CachePrefixUtil.get();\n    return super.getCache(prefix + name);\n}\n```\n&lt;/augment_code_snippet&gt;\n\n缓存前缀工具类确保不同租户和应用的缓存完全隔离：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-common/src/main/java/cn/hy/auth/custom/common/utils/CachePrefixUtil.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\npublic static String get() {\n    //租户编码\n    String lesseeCode = AuthContext.getContext().getLesseeCode();\n    //应用编码\n    String appCode = AuthContext.getContext().getAppCode();\n    \n    StringJoiner stringJoiner = new StringJoiner(\&quot;_\&quot;);\n    stringJoiner.add(lesseeCode).add(appCode);\n    if (ObjectUtil.isNotNull(loginStateDTO)) {\n        ClientTypeEnum clientType = loginStateDTO.getClientType();\n        if (ObjectUtil.isNotNull(clientType)) {\n            stringJoiner.add(String.valueOf(clientType.getCode()));\n        }\n    }\n    \n    return stringJoiner.toString() + \&quot;_\&quot;;\n}\n```\n&lt;/augment_code_snippet&gt;\n\n### 4. Token存储的二级缓存\n\n项目实现了专门的Token二级缓存存储：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n/**\n * 类描述：本地缓存和redis2级缓存，解决redis的性能问题\n */\n@Slf4j\npublic class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener&lt;TokenRevokedEvent&gt; {\n    \n    private final ExpiringCache&lt;String,Object&gt; localTokenCache;\n    private final HyRedisTokenStore hyRedisTokenStore;\n\n    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {\n        localTokenCache = new ExpiringCache&lt;&gt;();\n        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);\n    }\n```\n&lt;/augment_code_snippet&gt;\n\n## 缓存一致性保证机制\n\n### 1. 基于Kafka的分布式缓存同步\n\n项目使用Kafka消息队列来保证分布式环境下的缓存一致性：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@KafkaListener(topics = \&quot;${hy.saas.cluster-cache.topic:saas-app}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    try {\n        if (message.isPresent()) {\n            String msg = (String)message.get();\n            log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n            EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n            try {\n                // 清除各种缓存\n                metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n                if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n                }else {\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n                }\n```\n&lt;/augment_code_snippet&gt;\n\n### 2. Token状态变更的实时同步\n\n&lt;augment_code_snippet path=\&quot;hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenStatusChangedKafkaListener.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@KafkaListener(topics = \&quot;${hy.saas.token.status.topic:hy-auth-sys-token-status}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    // 处理token状态变更\n    if (\&quot;revoked\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);\n    } else if (\&quot;refreshToken\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);\n    }\n```\n&lt;/augment_code_snippet&gt;\n\n### 3. 事件驱动的缓存失效\n\n项目使用Spring事件机制来处理缓存失效：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/listener/TokenRevokedEventListener.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n//移除缓存\nuserCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());\n```\n&lt;/augment_code_snippet&gt;\n\n### 4. 手动缓存清理接口\n\n提供了RESTful接口用于手动清理缓存：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@PostMapping(\&quot;/meta/data/cache/clear\&quot;)\npublic Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    metaDataCacheService.cacheClear(lessCode, appCode);\n    Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    return resultMap;\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## 缓存一致性保证策略总结\n\n### 1. **多层次缓存架构**\n- **本地缓存（Caffeine）**：提供最快访问速度\n- **分布式缓存（Redis）**：保证数据持久化和集群共享\n- **数据库**：作为最终数据源\n\n### 2. **一致性保证机制**\n- **消息队列同步**：使用Kafka确保集群间缓存同步\n- **事件驱动失效**：通过Spring事件机制实现实时缓存失效\n- **定时过期策略**：设置合理的缓存过期时间\n- **手动清理机制**：提供API接口支持手动缓存清理\n\n### 3. **多租户隔离**\n- **前缀隔离**：通过租户编码+应用编码+客户端类型构建缓存前缀\n- **独立缓存空间**：确保不同租户的缓存完全隔离\n- **上下文感知**：基于当前请求上下文自动选择正确的缓存空间\n\n### 4. **性能优化**\n- **读写分离**：本地缓存承担读压力，Redis处理分布式同步\n- **异步处理**：缓存失效操作异步执行，不影响主业务流程\n- **批量操作**：支持批量缓存操作提高效率\n\n这个项目的二级缓存设计非常完善，既保证了高性能访问，又确保了分布式环境下的数据一致性，同时很好地支持了多租户架构的需求。&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;基于我对项目代码的深入分析，我现在可以为您详细解析这个认证中心项目的二级缓存实现和缓存一致性保证机制。\n\n## 项目二级缓存实现分析\n\n### 1. 二级缓存架构设计\n\n这个项目实现了一个完整的二级缓存架构，主要包含以下层次：\n\n#### **第一级缓存：本地缓存（Caffeine）**\n- 使用 Caffeine 作为本地内存缓存\n- 提供最快的访问速度\n- 支持多租户隔离\n\n#### **第二级缓存：分布式缓存（Redis）**\n- 使用 Redis 作为分布式缓存\n- 支持集群间数据共享\n- 提供持久化能力\n\n### 2. 缓存配置实现\n\n````java path=hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java mode=EXCERPT\n@Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n@ConditionalOnProperty(\n        prefix = \&quot;auth.cache\&quot;,\n        name = \&quot;type\&quot;,\n        havingValue = \&quot;caffeine\&quot;,\n        matchIfMissing = true\n)\npublic CacheManager cacheManagerWithCaffeine() {\n    log.debug(\&quot;Caffeine cache 配置\&quot;);\n    LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    Caffeine caffeine = Caffeine.newBuilder()\n            //cache的初始容量值\n            .initialCapacity(100)\n            //maximumSize用来控制cache的最大缓存数量\n            .maximumSize(maximumSize)\n            .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    cacheManager.setCaffeine(caffeine);\n    //不允许设置空值\n    cacheManager.setAllowNullValues(false);\n    return cacheManager;\n}\n````\n\n### 3. 多租户缓存隔离\n\n项目通过自定义的 `LesseeCaffeineCacheManager` 实现多租户缓存隔离：\n\n````java path=hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java mode=EXCERPT\n@Override\npublic Cache getCache(String name) {\n    String prefix = CachePrefixUtil.get();\n    return super.getCache(prefix + name);\n}\n````\n\n缓存前缀工具类确保不同租户和应用的缓存完全隔离：\n\n````java path=hy-auth-custom-business/hy-auth-custom-common/src/main/java/cn/hy/auth/custom/common/utils/CachePrefixUtil.java mode=EXCERPT\npublic static String get() {\n    //租户编码\n    String lesseeCode = AuthContext.getContext().getLesseeCode();\n    //应用编码\n    String appCode = AuthContext.getContext().getAppCode();\n    \n    StringJoiner stringJoiner = new StringJoiner(\&quot;_\&quot;);\n    stringJoiner.add(lesseeCode).add(appCode);\n    if (ObjectUtil.isNotNull(loginStateDTO)) {\n        ClientTypeEnum clientType = loginStateDTO.getClientType();\n        if (ObjectUtil.isNotNull(clientType)) {\n            stringJoiner.add(String.valueOf(clientType.getCode()));\n        }\n    }\n    \n    return stringJoiner.toString() + \&quot;_\&quot;;\n}\n````\n\n### 4. Token存储的二级缓存\n\n项目实现了专门的Token二级缓存存储：\n\n````java path=hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java mode=EXCERPT\n/**\n * 类描述：本地缓存和redis2级缓存，解决redis的性能问题\n */\n@Slf4j\npublic class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener&lt;TokenRevokedEvent&gt; {\n    \n    private final ExpiringCache&lt;String,Object&gt; localTokenCache;\n    private final HyRedisTokenStore hyRedisTokenStore;\n\n    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {\n        localTokenCache = new ExpiringCache&lt;&gt;();\n        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);\n    }\n````\n\n## 缓存一致性保证机制\n\n### 1. 基于Kafka的分布式缓存同步\n\n项目使用Kafka消息队列来保证分布式环境下的缓存一致性：\n\n````java path=hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java mode=EXCERPT\n@KafkaListener(topics = \&quot;${hy.saas.cluster-cache.topic:saas-app}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    try {\n        if (message.isPresent()) {\n            String msg = (String)message.get();\n            log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n            EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n            try {\n                // 清除各种缓存\n                metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n                if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n                }else {\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n                }\n````\n\n### 2. Token状态变更的实时同步\n\n````java path=hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenStatusChangedKafkaListener.java mode=EXCERPT\n@KafkaListener(topics = \&quot;${hy.saas.token.status.topic:hy-auth-sys-token-status}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    // 处理token状态变更\n    if (\&quot;revoked\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);\n    } else if (\&quot;refreshToken\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);\n    }\n````\n\n### 3. 事件驱动的缓存失效\n\n项目使用Spring事件机制来处理缓存失效：\n\n````java path=hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/listener/TokenRevokedEventListener.java mode=EXCERPT\n//移除缓存\nuserCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());\n````\n\n### 4. 手动缓存清理接口\n\n提供了RESTful接口用于手动清理缓存：\n\n````java path=hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java mode=EXCERPT\n@PostMapping(\&quot;/meta/data/cache/clear\&quot;)\npublic Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    metaDataCacheService.cacheClear(lessCode, appCode);\n    Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    return resultMap;\n}\n````\n\n## 缓存一致性保证策略总结\n\n### 1. **多层次缓存架构**\n- **本地缓存（Caffeine）**：提供最快访问速度\n- **分布式缓存（Redis）**：保证数据持久化和集群共享\n- **数据库**：作为最终数据源\n\n### 2. **一致性保证机制**\n- **消息队列同步**：使用Kafka确保集群间缓存同步\n- **事件驱动失效**：通过Spring事件机制实现实时缓存失效\n- **定时过期策略**：设置合理的缓存过期时间\n- **手动清理机制**：提供API接口支持手动缓存清理\n\n### 3. **多租户隔离**\n- **前缀隔离**：通过租户编码+应用编码+客户端类型构建缓存前缀\n- **独立缓存空间**：确保不同租户的缓存完全隔离\n- **上下文感知**：基于当前请求上下文自动选择正确的缓存空间\n\n### 4. **性能优化**\n- **读写分离**：本地缓存承担读压力，Redis处理分布式同步\n- **异步处理**：缓存失效操作异步执行，不影响主业务流程\n- **批量操作**：支持批量缓存操作提高效率\n\n这个项目的二级缓存设计非常完善，既保证了高性能访问，又确保了分布式环境下的数据一致性，同时很好地支持了多租户架构的需求。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java\n...\n    25\t\n    26\t/**\n    27\t * 认证中心涉及的应用信息--系统表\n    28\t * 该表全局只有一个，无需增加表名前缀\n    29\t *\n    30\t * <AUTHOR>    31\t * @date 2020-12-07 10:37\n    32\t **/\n    33\t@Service\n    34\t@Slf4j\n    35\tpublic class AppInvolvedTableServiceImpl implements AppAuthSystemTableService {\n    36\t\n    37\t    private static final String TABLE_NAME = \&quot;auth_app_involved\&quot;;\n    38\t\n    39\t    private final JdbcTemplate jdbcTemplate;\n    40\t\n    41\t    private final CacheManager cacheManager;\n    42\t    private final IdWorker idWorker;\n    43\t    static final String ROOT_CACHE_NAME = \&quot;hy_auth_auth_app_involved\&quot;;\n    44\t\n    45\t    private static final Pattern DATABASE_NAME_PATTERN = Pattern.compile(\&quot;jdbc:(?&lt;db&gt;\\\\w+):.*((//)|@)(?&lt;host&gt;.+):(?&lt;port&gt;\\\\d+)(/|(;DatabaseName=)|:)(?&lt;dbName&gt;\\\\w+.+)\\\\?\&quot;);\n    46\t    private final  ConcurrentHashMap&lt;String,Object&gt; parallelLockMap = new ConcurrentHashMap&lt;&gt;();\n    47\t\n    48\t    @Autowired\n    49\t    private DataSourceProperties dataSourceProperties;\n...\n   141\t        String key = lesseeCode+\&quot;_\&quot;+appCode;\n   142\t        Map&lt;String, Object&gt; appInfo = getAppInfoFromCache(key);\n   143\t        if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n   144\t            synchronized (getLock(key)){\n   145\t                try {\n   146\t                    appInfo = getAppInfoFromCache(key);\n   147\t                    if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n   148\t                        log.debug(\&quot;从数据库加载应用{},AppInvolved信息缓存\&quot;,key);\n   149\t                        String sql =\&quot;Select *  From \&quot; + TABLE_NAME + \&quot; Where lessee_code=? And app_code=? limit 1\&quot;;\n   150\t                        List&lt;Map&lt;String, Object&gt;&gt; result = jdbcTemplate.queryForList(sql, lesseeCode, appCode);\n   151\t                        if (CollectionUtils.isEmpty(result)) {\n   152\t                            String noInitAppSql = \&quot;SELECT COUNT(1) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?\&quot;;\n   153\t                            Integer i = jdbcTemplate.queryForObject(noInitAppSql, Integer.class, getDataBaseName(dataSourceProperties.getUrl()), lesseeCode + \&quot;_\&quot; + appCode + \&quot;_auth_config\&quot;);\n   154\t                            if (i &gt; 0) {\n   155\t                                Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   156\t                                map.put(\&quot;lessee_code\&quot;, lesseeCode);\n   157\t                                map.put(\&quot;app_code\&quot;, appCode);\n   158\t                                map.put(\&quot;status\&quot;, 1);\n   159\t                                result.add(map);\n   160\t                            }\n   161\t                        }\n   162\t                        if (CollectionUtils.isNotEmpty(result)){\n   163\t                            appInfo = CollectionUtils.isNotEmpty(result)?result.get(0):Collections.emptyMap();\n   164\t                            getRootCache().put(key,JSON.toJSONString(appInfo));\n   165\t                        }\n   166\t                    }\n   167\t                }finally {\n   168\t                    removeLock(key);\n   169\t                }\n   170\t            }\n   171\t        }\n   172\t        return appInfo;\n   173\t    }\n   174\t\n   175\t    public static String getDataBaseName(String url) {\n   176\t        Matcher m = DATABASE_NAME_PATTERN.matcher(url);\n   177\t        if(m.find()) {\n   178\t            return m.group(\&quot;dbName\&quot;);\n   179\t        }\n   180\t        return \&quot;\&quot;;\n   181\t    }\n   182\t\n   183\t    private Map&lt;String,Object&gt; getAppInfoFromCache(String key){\n   184\t        String appInvolvedStr = getRootCache().get(key, String.class);\n   185\t        if (StringUtils.isBlank(appInvolvedStr)){\n   186\t            return null;\n   187\t        }\n   188\t        return JSON.parseObject(appInvolvedStr);\n   189\t    }\n   190\t    private Object getLock(String identify) {\n   191\t        // 线程安全\n   192\t        return parallelLockMap.computeIfAbsent(identify, t -&gt; new Object());\n   193\t\n   194\t    }\n   195\t\n   196\t    private void removeLock(String identify) {\n   197\t        // 线程安全\n   198\t        parallelLockMap.remove(identify);\n   199\t    }\n   200\t\n   201\t    /**\n   202\t     * 获取根部缓存信息\n   203\t     *\n   204\t     * @return 返回缓存对象\n   205\t     */\n   206\t    private Cache getRootCache() {\n   207\t        String cacheName = ROOT_CACHE_NAME;\n   208\t        log.debug(\&quot;AppInvolvedTableServiceImpl cacheName: {}\&quot;,cacheName);\n   209\t       // Boolean autoAttachCachePrefix = CachePrefixUtil.isAutoAttachCcahePrefix();\n   210\t        //try {\n   211\t          //  CachePrefixUtil.setAutoAttachCachePrefix(false);\n   212\t            return cacheManager.getCache(cacheName);\n   213\t       // }finally {\n   214\t            // 恢复原样\n   215\t            //CachePrefixUtil.setAutoAttachCachePrefix(autoAttachCachePrefix);\n   216\t        //}\n   217\t    }\n   218\t    /**\n   219\t     * 修改应用信息的状态\n   220\t     *\n   221\t     * @param lesseeCode 租户编码\n   222\t     * @param appCode    应用编码\n   223\t     * @return .\n   224\t     */\n   225\t    public int updateAppInfoStatus(String lesseeCode, String appCode,Integer status) {\n   226\t        String sql =\&quot;update \&quot; + TABLE_NAME + \&quot; set status = ? Where lessee_code=? And app_code=? ;\&quot;;\n   227\t        int update = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n   228\t        // 情况缓存信息\n   229\t        String key = lesseeCode+\&quot;_\&quot;+appCode;\n   230\t        Cache rootCache = getRootCache();\n   231\t        if (rootCache != null){\n   232\t            //getRootCache().put(key,\&quot;\&quot;);\n   233\t            rootCache.evict(key);\n   234\t        }\n   235\t        AuthContext.getContext().setLesseeCode(lesseeCode);\n   236\t        AuthContext.getContext().setAppCode(appCode);\n   237\t        rootCache = getRootCache();\n   238\t        if (rootCache != null){\n   239\t            //getRootCache().put(key,\&quot;\&quot;);\n   240\t            rootCache.evict(key);\n   241\t        }\n   242\t        log.info(\&quot;清除应用{},AppInvolved信息缓存\&quot;,key);\n   243\t        return update;\n   244\t    }\n   245\t\n   246\t    public void clearAppStatusCache(String lesseeCode, String appCode) {\n   247\t        // 清空缓存信息\n   248\t        String key = lesseeCode + \&quot;_\&quot; + appCode;\n   249\t        Cache rootCache = getRootCache();\n   250\t        if (rootCache != null) {\n   251\t            //getRootCache().put(key,\&quot;\&quot;);\n   252\t            rootCache.evict(key);\n   253\t        }\n   254\t        AuthContext.getContext().setLesseeCode(lesseeCode);\n   255\t        AuthContext.getContext().setAppCode(appCode);\n   256\t        rootCache = getRootCache();\n   257\t        if (rootCache != null) {\n   258\t            //getRootCache().put(key,\&quot;\&quot;);\n   259\t            rootCache.evict(key);\n   260\t        }\n   261\t        log.info(\&quot;通知清除{}应用AppInvolved信息缓存\&quot;, key);\n   262\t    }\n   263\t\n   264\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-dev.yml\n...\n   240\t        - /log/cache/clear\n   241\t        - /*/cache/clear/**\n   242\t        - /license/*\n   243\t        - /verifyCode/sl...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_011JKRz5CfgMaM1uxeiAYZwj&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:53:03.254Z&quot;,&quot;response_text&quot;:&quot;基于我对项目代码的深入分析，我现在可以为您详细解析这个认证中心项目的二级缓存实现和缓存一致性保证机制。\n\n## 项目二级缓存实现分析\n\n### 1. 二级缓存架构设计\n\n这个项目实现了一个完整的二级缓存架构，主要包含以下层次：\n\n#### **第一级缓存：本地缓存（Caffeine）**\n- 使用 Caffeine 作为本地内存缓存\n- 提供最快的访问速度\n- 支持多租户隔离\n\n#### **第二级缓存：分布式缓存（Redis）**\n- 使用 Redis 作为分布式缓存\n- 支持集群间数据共享\n- 提供持久化能力\n\n### 2. 缓存配置实现\n\n````java path=hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java mode=EXCERPT\n@Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n@ConditionalOnProperty(\n        prefix = \&quot;auth.cache\&quot;,\n        name = \&quot;type\&quot;,\n        havingValue = \&quot;caffeine\&quot;,\n        matchIfMissing = true\n)\npublic CacheManager cacheManagerWithCaffeine() {\n    log.debug(\&quot;Caffeine cache 配置\&quot;);\n    LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    Caffeine caffeine = Caffeine.newBuilder()\n            //cache的初始容量值\n            .initialCapacity(100)\n            //maximumSize用来控制cache的最大缓存数量\n            .maximumSize(maximumSize)\n            .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    cacheManager.setCaffeine(caffeine);\n    //不允许设置空值\n    cacheManager.setAllowNullValues(false);\n    return cacheManager;\n}\n````\n\n### 3. 多租户缓存隔离\n\n项目通过自定义的 `LesseeCaffeineCacheManager` 实现多租户缓存隔离：\n\n````java path=hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java mode=EXCERPT\n@Override\npublic Cache getCache(String name) {\n    String prefix = CachePrefixUtil.get();\n    return super.getCache(prefix + name);\n}\n````\n\n缓存前缀工具类确保不同租户和应用的缓存完全隔离：\n\n````java path=hy-auth-custom-business/hy-auth-custom-common/src/main/java/cn/hy/auth/custom/common/utils/CachePrefixUtil.java mode=EXCERPT\npublic static String get() {\n    //租户编码\n    String lesseeCode = AuthContext.getContext().getLesseeCode();\n    //应用编码\n    String appCode = AuthContext.getContext().getAppCode();\n    \n    StringJoiner stringJoiner = new StringJoiner(\&quot;_\&quot;);\n    stringJoiner.add(lesseeCode).add(appCode);\n    if (ObjectUtil.isNotNull(loginStateDTO)) {\n        ClientTypeEnum clientType = loginStateDTO.getClientType();\n        if (ObjectUtil.isNotNull(clientType)) {\n            stringJoiner.add(String.valueOf(clientType.getCode()));\n        }\n    }\n    \n    return stringJoiner.toString() + \&quot;_\&quot;;\n}\n````\n\n### 4. Token存储的二级缓存\n\n项目实现了专门的Token二级缓存存储：\n\n````java path=hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java mode=EXCERPT\n/**\n * 类描述：本地缓存和redis2级缓存，解决redis的性能问题\n */\n@Slf4j\npublic class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener&lt;TokenRevokedEvent&gt; {\n    \n    private final ExpiringCache&lt;String,Object&gt; localTokenCache;\n    private final HyRedisTokenStore hyRedisTokenStore;\n\n    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {\n        localTokenCache = new ExpiringCache&lt;&gt;();\n        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);\n    }\n````\n\n## 缓存一致性保证机制\n\n### 1. 基于Kafka的分布式缓存同步\n\n项目使用Kafka消息队列来保证分布式环境下的缓存一致性：\n\n````java path=hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java mode=EXCERPT\n@KafkaListener(topics = \&quot;${hy.saas.cluster-cache.topic:saas-app}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    try {\n        if (message.isPresent()) {\n            String msg = (String)message.get();\n            log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n            EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n            try {\n                // 清除各种缓存\n                metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n                if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n                }else {\n                    hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n                }\n````\n\n### 2. Token状态变更的实时同步\n\n````java path=hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenStatusChangedKafkaListener.java mode=EXCERPT\n@KafkaListener(topics = \&quot;${hy.saas.token.status.topic:hy-auth-sys-token-status}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    // 处理token状态变更\n    if (\&quot;revoked\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);\n    } else if (\&quot;refreshToken\&quot;.equals(type)){\n        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);\n    }\n````\n\n### 3. 事件驱动的缓存失效\n\n项目使用Spring事件机制来处理缓存失效：\n\n````java path=hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/listener/TokenRevokedEventListener.java mode=EXCERPT\n//移除缓存\nuserCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());\n````\n\n### 4. 手动缓存清理接口\n\n提供了RESTful接口用于手动清理缓存：\n\n````java path=hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java mode=EXCERPT\n@PostMapping(\&quot;/meta/data/cache/clear\&quot;)\npublic Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    metaDataCacheService.cacheClear(lessCode, appCode);\n    Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    return resultMap;\n}\n````\n\n## 缓存一致性保证策略总结\n\n### 1. **多层次缓存架构**\n- **本地缓存（Caffeine）**：提供最快访问速度\n- **分布式缓存（Redis）**：保证数据持久化和集群共享\n- **数据库**：作为最终数据源\n\n### 2. **一致性保证机制**\n- **消息队列同步**：使用Kafka确保集群间缓存同步\n- **事件驱动失效**：通过Spring事件机制实现实时缓存失效\n- **定时过期策略**：设置合理的缓存过期时间\n- **手动清理机制**：提供API接口支持手动缓存清理\n\n### 3. **多租户隔离**\n- **前缀隔离**：通过租户编码+应用编码+客户端类型构建缓存前缀\n- **独立缓存空间**：确保不同租户的缓存完全隔离\n- **上下文感知**：基于当前请求上下文自动选择正确的缓存空间\n\n### 4. **性能优化**\n- **读写分离**：本地缓存承担读压力，Redis处理分布式同步\n- **异步处理**：缓存失效操作异步执行，不影响主业务流程\n- **批量操作**：支持批量缓存操作提高效率\n\n这个项目的二级缓存设计非常完善，既保证了高性能访问，又确保了分布式环境下的数据一致性，同时很好地支持了多租户架构的需求。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;request_id&quot;:&quot;3d2e9904-398c-4cd6-ab38-26d039f9d8b5&quot;,&quot;status&quot;:&quot;cancelled&quot;,&quot;chatItemType&quot;:&quot;agentic-turn-delimiter&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;d8ef45dc-1e4e-489d-8215-29bb8e54f8dd&quot;,&quot;request_message&quot;:&quot;并发情况下能保证一致性吗&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;并发情况下能保证一致性吗&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对代码的深入分析，这个项目在并发情况下的一致性保证存在一些问题和改进空间。让我详细分析：\n\n## 并发一致性问题分析\n\n### 1. **本地缓存的并发控制**\n\n项目使用了同步锁来处理并发访问：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\nif (ObjectUtil.isNull(userAccountDTO)) {\n    String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n    synchronized (getLock(lockIdentify)){\n        try {\n            userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n            if (userAccountDTO == null){\n                // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n                userAccountDTO = loadUserAccountByLoginNameAndCache();\n            }\n            if (userAccountDTO == null) {\n                userAccountDTO = loadUserAccountByTokenStore();\n            }\n        }finally {\n            removeLock(lockIdentify);\n        }\n    }\n}\n```\n&lt;/augment_code_snippet&gt;\n\n**问题分析：**\n- ✅ **优点**：使用了细粒度锁，按照租户+应用+登录名进行锁定\n- ❌ **问题**：锁的粒度可能过细，在高并发下可能出现缓存穿透\n\n### 2. **应用信息缓存的并发控制**\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\nString key = lesseeCode+\&quot;_\&quot;+appCode;\nMap&lt;String, Object&gt; appInfo = getAppInfoFromCache(key);\nif (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n    synchronized (getLock(key)){\n        try {\n            appInfo = getAppInfoFromCache(key);\n            if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n                log.debug(\&quot;从数据库加载应用{},AppInvolved信息缓存\&quot;,key);\n                // 数据库查询逻辑\n                if (CollectionUtils.isNotEmpty(result)){\n                    appInfo = CollectionUtils.isNotEmpty(result)?result.get(0):Collections.emptyMap();\n                    getRootCache().put(key,JSON.toJSONString(appInfo));\n                }\n            }\n        }finally {\n            removeLock(key);\n        }\n    }\n}\n```\n&lt;/augment_code_snippet&gt;\n\n**问题分析：**\n- ✅ **优点**：使用了双重检查锁定模式（Double-Checked Locking）\n- ✅ **优点**：避免了缓存击穿问题\n- ❌ **问题**：锁对象管理可能存在内存泄漏风险\n\n## 主要并发一致性问题\n\n### 1. **缓存更新的原子性问题**\n\n&lt;augment_code_snippet path=\&quot;hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\npublic int updateAppInfoStatus(String lesseeCode, String appCode,Integer status) {\n    String sql =\&quot;update \&quot; + TABLE_NAME + \&quot; set status = ? Where lessee_code=? And app_code=? ;\&quot;;\n    int update = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n    // 情况缓存信息\n    String key = lesseeCode+\&quot;_\&quot;+appCode;\n    Cache rootCache = getRootCache();\n    if (rootCache != null){\n        rootCache.evict(key);\n    }\n    // ... 重复清理逻辑\n    return update;\n}\n```\n&lt;/augment_code_snippet&gt;\n\n**问题：**\n- ❌ **数据库更新和缓存清理不是原子操作**\n- ❌ **可能出现数据库已更新但缓存清理失败的情况**\n- ❌ **没有使用事务来保证一致性**\n\n### 2. **分布式缓存同步的延迟问题**\n\n项目使用Kafka进行缓存同步，但存在以下问题：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\n@KafkaListener(topics = \&quot;${hy.saas.cluster-cache.topic:saas-app}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    // 异步处理缓存清理\n    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n}\n```\n&lt;/augment_code_snippet&gt;\n\n**问题：**\n- ❌ **异步消息处理存在延迟**\n- ❌ **消息可能丢失或重复处理**\n- ❌ **不同节点间存在短暂的数据不一致窗口**\n\n### 3. **二级缓存的一致性问题**\n\n在Token存储的二级缓存中：\n\n&lt;augment_code_snippet path=\&quot;hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```java\nprivate final ExpiringCache&lt;String,Object&gt; localTokenCache;\nprivate final HyRedisTokenStore hyRedisTokenStore;\n```\n&lt;/augment_code_snippet&gt;\n\n**问题：**\n- ❌ **本地缓存和Redis缓存可能不同步**\n- ❌ **没有明确的缓存更新策略**\n- ❌ **可能出现本地缓存过期但Redis缓存仍然有效的情况**\n\n## 并发一致性改进建议\n\n### 1. **使用分布式锁**\n\n```java\n// 建议使用Redis分布式锁\n@Autowired\nprivate RedisTemplate&lt;String, String&gt; redisTemplate;\n\npublic UserAccountDTO getUserAccountByLoginName(String loginName) {\n    String lockKey = \&quot;lock:user:\&quot; + CachePrefixUtil.get() + loginName;\n    String lockValue = UUID.randomUUID().toString();\n    \n    try {\n        // 获取分布式锁\n        Boolean acquired = redisTemplate.opsForValue()\n            .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));\n        \n        if (Boolean.TRUE.equals(acquired)) {\n            // 执行缓存加载逻辑\n            return loadUserFromCacheOrDb(loginName);\n        } else {\n            // 等待并重试\n            Thread.sleep(50);\n            return getUserAccountFromCache(loginName);\n        }\n    } finally {\n        // 释放锁\n        releaseLock(lockKey, lockValue);\n    }\n}\n```\n\n### 2. **使用事务保证原子性**\n\n```java\n@Transactional\npublic int updateAppInfoStatus(String lesseeCode, String appCode, Integer status) {\n    try {\n        // 1. 更新数据库\n        int result = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n        \n        // 2. 发送缓存清理消息\n        kafkaTemplate.send(\&quot;cache-clear-topic\&quot;, \n            new CacheClearEvent(lesseeCode, appCode));\n        \n        return result;\n    } catch (Exception e) {\n        // 事务回滚，保证一致性\n        throw new RuntimeException(\&quot;更新失败\&quot;, e);\n    }\n}\n```\n\n### 3. **实现缓存版本控制**\n\n```java\npublic class VersionedCacheEntry&lt;T&gt; {\n    private T data;\n    private long version;\n    private long timestamp;\n    \n    // 比较版本，确保使用最新数据\n    public boolean isNewerThan(VersionedCacheEntry&lt;T&gt; other) {\n        return this.version &gt; other.version;\n    }\n}\n```\n\n### 4. **使用Canal或Debezium进行数据库变更监听**\n\n```java\n// 监听数据库变更，实时同步缓存\n@EventListener\npublic void handleDatabaseChange(DatabaseChangeEvent event) {\n    if (event.getTable().equals(\&quot;auth_app_involved\&quot;)) {\n        // 实时清理相关缓存\n        String key = event.getLesseeCode() + \&quot;_\&quot; + event.getAppCode();\n        cacheManager.getCache(\&quot;hy_auth_auth_app_involved\&quot;).evict(key);\n        \n        // 通知其他节点\n        kafkaTemplate.send(\&quot;cache-sync-topic\&quot;, new CacheSyncEvent(key));\n    }\n}\n```\n\n## 总结\n\n**当前项目的并发一致性保证：**\n\n✅ **已实现的保证：**\n- 本地缓存使用了同步锁\n- 使用了双重检查锁定模式\n- 实现了基于Kafka的分布式缓存同步\n- 提供了手动缓存清理机制\n\n❌ **存在的问题：**\n- 缺乏分布式锁，无法处理跨节点并发\n- 数据库更新和缓存清理不是原子操作\n- 异步消息处理存在延迟和丢失风险\n- 二级缓存间可能存在不一致\n- 锁对象管理可能导致内存泄漏\n\n**建议的改进方案：**\n1. 引入Redis分布式锁\n2. 使用事务保证操作原子性\n3. 实现缓存版本控制机制\n4. 使用数据库变更监听工具\n5. 增加缓存一致性检查和修复机制\n6. 实现更完善的异常处理和重试机制\n\n总的来说，当前项目在单机环境下能较好地保证并发一致性，但在分布式高并发环境下仍有改进空间。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;并发情况下能保证一致性吗&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;,&quot;folderRoot&quot;:&quot;D:/flowservice/hy-authentication-center&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-09T09:54:28.247Z&quot;,&quot;response_text&quot;:&quot;基于我对代码的深入分析，这个项目在并发情况下的一致性保证存在一些问题和改进空间。让我详细分析：\n\n## 并发一致性问题分析\n\n### 1. **本地缓存的并发控制**\n\n项目使用了同步锁来处理并发访问：\n\n````java path=hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java mode=EXCERPT\nif (ObjectUtil.isNull(userAccountDTO)) {\n    String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n    synchronized (getLock(lockIdentify)){\n        try {\n            userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n            if (userAccountDTO == null){\n                // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n                userAccountDTO = loadUserAccountByLoginNameAndCache();\n            }\n            if (userAccountDTO == null) {\n                userAccountDTO = loadUserAccountByTokenStore();\n            }\n        }finally {\n            removeLock(lockIdentify);\n        }\n    }\n}\n````\n\n**问题分析：**\n- ✅ **优点**：使用了细粒度锁，按照租户+应用+登录名进行锁定\n- ❌ **问题**：锁的粒度可能过细，在高并发下可能出现缓存穿透\n\n### 2. **应用信息缓存的并发控制**\n\n````java path=hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java mode=EXCERPT\nString key = lesseeCode+\&quot;_\&quot;+appCode;\nMap&lt;String, Object&gt; appInfo = getAppInfoFromCache(key);\nif (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n    synchronized (getLock(key)){\n        try {\n            appInfo = getAppInfoFromCache(key);\n            if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n                log.debug(\&quot;从数据库加载应用{},AppInvolved信息缓存\&quot;,key);\n                // 数据库查询逻辑\n                if (CollectionUtils.isNotEmpty(result)){\n                    appInfo = CollectionUtils.isNotEmpty(result)?result.get(0):Collections.emptyMap();\n                    getRootCache().put(key,JSON.toJSONString(appInfo));\n                }\n            }\n        }finally {\n            removeLock(key);\n        }\n    }\n}\n````\n\n**问题分析：**\n- ✅ **优点**：使用了双重检查锁定模式（Double-Checked Locking）\n- ✅ **优点**：避免了缓存击穿问题\n- ❌ **问题**：锁对象管理可能存在内存泄漏风险\n\n## 主要并发一致性问题\n\n### 1. **缓存更新的原子性问题**\n\n````java path=hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java mode=EXCERPT\npublic int updateAppInfoStatus(String lesseeCode, String appCode,Integer status) {\n    String sql =\&quot;update \&quot; + TABLE_NAME + \&quot; set status = ? Where lessee_code=? And app_code=? ;\&quot;;\n    int update = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n    // 情况缓存信息\n    String key = lesseeCode+\&quot;_\&quot;+appCode;\n    Cache rootCache = getRootCache();\n    if (rootCache != null){\n        rootCache.evict(key);\n    }\n    // ... 重复清理逻辑\n    return update;\n}\n````\n\n**问题：**\n- ❌ **数据库更新和缓存清理不是原子操作**\n- ❌ **可能出现数据库已更新但缓存清理失败的情况**\n- ❌ **没有使用事务来保证一致性**\n\n### 2. **分布式缓存同步的延迟问题**\n\n项目使用Kafka进行缓存同步，但存在以下问题：\n\n````java path=hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java mode=EXCERPT\n@KafkaListener(topics = \&quot;${hy.saas.cluster-cache.topic:saas-app}\&quot;,\n        groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n)\npublic void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    // 异步处理缓存清理\n    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n}\n````\n\n**问题：**\n- ❌ **异步消息处理存在延迟**\n- ❌ **消息可能丢失或重复处理**\n- ❌ **不同节点间存在短暂的数据不一致窗口**\n\n### 3. **二级缓存的一致性问题**\n\n在Token存储的二级缓存中：\n\n````java path=hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java mode=EXCERPT\nprivate final ExpiringCache&lt;String,Object&gt; localTokenCache;\nprivate final HyRedisTokenStore hyRedisTokenStore;\n````\n\n**问题：**\n- ❌ **本地缓存和Redis缓存可能不同步**\n- ❌ **没有明确的缓存更新策略**\n- ❌ **可能出现本地缓存过期但Redis缓存仍然有效的情况**\n\n## 并发一致性改进建议\n\n### 1. **使用分布式锁**\n\n```java\n// 建议使用Redis分布式锁\n@Autowired\nprivate RedisTemplate&lt;String, String&gt; redisTemplate;\n\npublic UserAccountDTO getUserAccountByLoginName(String loginName) {\n    String lockKey = \&quot;lock:user:\&quot; + CachePrefixUtil.get() + loginName;\n    String lockValue = UUID.randomUUID().toString();\n    \n    try {\n        // 获取分布式锁\n        Boolean acquired = redisTemplate.opsForValue()\n            .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));\n        \n        if (Boolean.TRUE.equals(acquired)) {\n            // 执行缓存加载逻辑\n            return loadUserFromCacheOrDb(loginName);\n        } else {\n            // 等待并重试\n            Thread.sleep(50);\n            return getUserAccountFromCache(loginName);\n        }\n    } finally {\n        // 释放锁\n        releaseLock(lockKey, lockValue);\n    }\n}\n```\n\n### 2. **使用事务保证原子性**\n\n```java\n@Transactional\npublic int updateAppInfoStatus(String lesseeCode, String appCode, Integer status) {\n    try {\n        // 1. 更新数据库\n        int result = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n        \n        // 2. 发送缓存清理消息\n        kafkaTemplate.send(\&quot;cache-clear-topic\&quot;, \n            new CacheClearEvent(lesseeCode, appCode));\n        \n        return result;\n    } catch (Exception e) {\n        // 事务回滚，保证一致性\n        throw new RuntimeException(\&quot;更新失败\&quot;, e);\n    }\n}\n```\n\n### 3. **实现缓存版本控制**\n\n```java\npublic class VersionedCacheEntry&lt;T&gt; {\n    private T data;\n    private long version;\n    private long timestamp;\n    \n    // 比较版本，确保使用最新数据\n    public boolean isNewerThan(VersionedCacheEntry&lt;T&gt; other) {\n        return this.version &gt; other.version;\n    }\n}\n```\n\n### 4. **使用Canal或Debezium进行数据库变更监听**\n\n```java\n// 监听数据库变更，实时同步缓存\n@EventListener\npublic void handleDatabaseChange(DatabaseChangeEvent event) {\n    if (event.getTable().equals(\&quot;auth_app_involved\&quot;)) {\n        // 实时清理相关缓存\n        String key = event.getLesseeCode() + \&quot;_\&quot; + event.getAppCode();\n        cacheManager.getCache(\&quot;hy_auth_auth_app_involved\&quot;).evict(key);\n        \n        // 通知其他节点\n        kafkaTemplate.send(\&quot;cache-sync-topic\&quot;, new CacheSyncEvent(key));\n    }\n}\n```\n\n## 总结\n\n**当前项目的并发一致性保证：**\n\n✅ **已实现的保证：**\n- 本地缓存使用了同步锁\n- 使用了双重检查锁定模式\n- 实现了基于Kafka的分布式缓存同步\n- 提供了手动缓存清理机制\n\n❌ **存在的问题：**\n- 缺乏分布式锁，无法处理跨节点并发\n- 数据库更新和缓存清理不是原子操作\n- 异步消息处理存在延迟和丢失风险\n- 二级缓存间可能存在不一致\n- 锁对象管理可能导致内存泄漏\n\n**建议的改进方案：**\n1. 引入Redis分布式锁\n2. 使用事务保证操作原子性\n3. 实现缓存版本控制机制\n4. 使用数据库变更监听工具\n5. 增加缓存一致性检查和修复机制\n6. 实现更完善的异常处理和重试机制\n\n总的来说，当前项目在单机环境下能较好地保证并发一致性，但在分布式高并发环境下仍有改进空间。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-bacc7893-33ec-4dbd-bc26-60ed35a41be5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e74907e4-be93-4f6a-be07-f7ac5556fd5e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-584ecd34-dd21-4a6f-ac2e-0aae40e93ad5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-a146a99e-05f2-4191-b8d7-c35f6f0b54f7&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-df7dd835-958f-4cb7-b64a-baa7ad12056d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4edc679e-3d44-47c5-8a61-1ef78ff9e89d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-36cffe45-1a38-498c-b676-54d99495ab7f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-96f20072-2ca9-471d-96c8-6d6be269a59d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5a7d02e9-53b6-40cb-a7c5-7e6e28565895&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ef0701be-4e05-48db-ae45-a710efcfd979&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-659b0a1a-b2ed-4a26-a163-be057331ccd7&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;f844a4bb-129b-4888-a673-c55e83433f2d;toolu_vrtx_01C54WHTMKYvg3mUz83Kce8d&quot;:{&quot;requestId&quot;:&quot;f844a4bb-129b-4888-a673-c55e83433f2d&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01C54WHTMKYvg3mUz83Kce8d&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./configs\n./configs\\hy-authentication-center.init.yml\n./configs\\hy-authentication-center.run.yml\n./docker-compose-auth-center.yml\n./hy-auth-center-boot\n./hy-auth-center-boot\\Dockerfile\n./hy-auth-center-boot\\Dockerfile_backup\n./hy-auth-center-boot\\pom.xml\n./hy-auth-center-boot\\src\n./hy-auth-center-boot\\target\n./hy-auth-common-business\n./hy-auth-common-business-starter\n./hy-auth-common-business-starter\\hy-auth-common-security-oauth2-starter\n./hy-auth-common-business-starter\\hy-auth-common-security-session-starter\n./hy-auth-common-business-starter\\pom.xml\n./hy-auth-common-business\\hy-auth-common-business-security\n./hy-auth-common-business\\hy-auth-common-business-tool\n./hy-auth-common-business\\pom.xml\n./hy-auth-custom-business\n./hy-auth-custom-business-starter\n./hy-auth-custom-business-starter\\pom.xml\n./hy-auth-custom-business-starter\\src\n./hy-auth-custom-business-starter\\target\n./hy-auth-custom-business\\hy-auth-custom-common\n./hy-auth-custom-business\\hy-auth-custom-multi-app\n./hy-auth-custom-business\\hy-auth-custom-route\n./hy-auth-custom-business\\hy-auth-custom-security\n./hy-auth-custom-business\\hy-auth-custom-user\n./hy-auth-custom-business\\hy-auth-sdk\n./hy-auth-custom-business\\hy-auth-sdk-starter\n./hy-auth-custom-business\\pom.xml\n./hy-auth-custom-dubbo\n./hy-auth-custom-dubbo\\target\n./hy-auth-custom-multi-datasource\n./hy-auth-custom-multi-datasource\\pom.xml\n./hy-auth-custom-multi-datasource\\src\n./hy-auth-custom-multi-datasource\\target\n./hy-auth-test\n./hy-auth-test\\pom.xml\n./license\n./license\\license\n./logs\n./logs\\debug.log.2024-07-04-14.0\n./logs\\debug.log.2024-07-04-15.0\n./logs\\debug.log.2024-07-04-16.0\n./logs\\debug.log.2024-07-04-17.0\n./logs\\debug.log.2024-07-04-18.0\n./logs\\debug.log.2024-07-04-19.0\n./logs\\debug.log.2024-07-09-15.0\n./logs\\debug.log.2024-07-09-16.0\n./logs\\debug.log.2024-07-17-12.0\n./logs\\debug.log.2024-07-17-14.0\n./logs\\debug.log.2024-07-17-19.0\n./logs\\debug.log.2024-07-17-20.0\n./logs\\debug.log.none.none.2025-04-15-10.0\n./logs\\debug.log.none.none.2025-04-15-11.0\n./logs\\error.log.2024-07-04-14.0\n./logs\\error.log.2024-07-04-15.0\n./logs\\error.log.2024-07-04-17.0\n./logs\\error.log.2024-07-04-19.0\n./logs\\error.log.2024-07-09-15.0\n./logs\\error.log.2024-07-17-12.0\n./logs\\error.log.2024-07-17-14.0\n./logs\\error.log.2024-07-17-19.0\n./logs\\error.log.2024-07-17-20.0\n./logs\\error.log.none.none.2025-04-15-10.0\n./logs\\error.log.none.none.2025-04-15-11.0\n./logs\\info.log.2024-07-04-14.0\n./logs\\info.log.2024-07-04-15.0\n./logs\\info.log.2024-07-04-16.0\n./logs\\info.log.2024-07-04-17.0\n./logs\\info.log.2024-07-04-18.0\n./logs\\info.log.2024-07-04-19.0\n./logs\\info.log.2024-07-09-15.0\n./logs\\info.log.2024-07-09-16.0\n./logs\\info.log.2024-07-17-12.0\n./logs\\info.log.2024-07-17-14.0\n./logs\\info.log.2024-07-17-19.0\n./logs\\info.log.2024-07-17-20.0\n./logs\\info.log.none.none.2025-04-15-10.0\n./logs\\info.log.none.none.2025-04-15-11.0\n./logs\\trace.log.2024-07-04-14.0\n./logs\\trace.log.2024-07-04-15.0\n./logs\\trace.log.2024-07-04-19.0\n./logs\\trace.log.2024-07-09-15.0\n./logs\\trace.log.2024-07-17-12.0\n./logs\\trace.log.2024-07-17-14.0\n./logs\\trace.log.2024-07-17-19.0\n./logs\\trace.log.2024-07-17-20.0\n./logs\\trace.log.none.none.2025-04-15-10.0\n./logs\\trace.log.none.none.2025-04-15-11.0\n./logs\\warn.log.2024-07-04-14.0\n./logs\\warn.log.2024-07-04-15.0\n./logs\\warn.log.2024-07-04-17.0\n./logs\\warn.log.2024-07-04-18.0\n./logs\\warn.log.2024-07-04-19.0\n./logs\\warn.log.2024-07-09-15.0\n./logs\\warn.log.2024-07-17-12.0\n./logs\\warn.log.2024-07-17-14.0\n./logs\\warn.log.2024-07-17-19.0\n./logs\\warn.log.2024-07-17-20.0\n./logs\\warn.log.none.none.2025-04-15-10.0\n./logs\\warn.log.none.none.2025-04-15-11.0\n./pom.xml\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;955a401b-3f18-45d6-9ce7-5f3eea2a3c8f;toolu_vrtx_01QZAdgXLKnW7ZFJBQb7Aowp&quot;:{&quot;requestId&quot;:&quot;955a401b-3f18-45d6-9ce7-5f3eea2a3c8f&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01QZAdgXLKnW7ZFJBQb7Aowp&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;0d7a84bb-8877-49ef-a1c6-9d1f97fb73d1;toolu_vrtx_01R4hET83HKHcmaJxc5CpfAv&quot;:{&quot;requestId&quot;:&quot;0d7a84bb-8877-49ef-a1c6-9d1f97fb73d1&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01R4hET83HKHcmaJxc5CpfAv&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceTest.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述: redis缓存测试类\n    20\t *\n    21\t * <AUTHOR>    22\t * @date ：创建于 2020/12/3\n    23\t */\n    24\t@RunWith(SpringJUnit4ClassRunner.class)\n    25\t@ContextConfiguration(classes = {RedisCacheServiceImpl.class})\n    26\tpublic class RedisCacheServiceTest {\n    27\t\n    28\t    @Resource(name = \&quot;redisCacheServiceImpl\&quot;)\n    29\t    private NativeDiffCacheService nativeDiffCacheService;\n    30\t\n    31\t    @MockBean\n    32\t    private RedisService redisService;\n    33\t\n    34\t    @Test\n    35\t    public void handle() {\n    36\t        when(redisService.getValuesByPrefix(any())).thenReturn(Collections.singletonList(1L));\n    37\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().rootCacheName(\&quot;hy_auth\&quot;).build();\n    38\t        nativeDiffCacheService.handle(nativeCacheDTO);\n    39\t    }\n    40\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     4\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     5\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     6\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     7\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     8\timport com.github.benmanes.caffeine.cache.Cache;\n     9\timport com.google.common.collect.Sets;\n    10\timport org.apache.commons.lang3.StringUtils;\n    11\timport org.springframework.stereotype.Service;\n    12\t\n    13\timport java.util.Set;\n    14\timport java.util.concurrent.ConcurrentMap;\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/test/java/cn/hy/auth/custom/multi/utils/TestCacheConfiguration.java\n...\n    84\t\n    85\t    /**\n    86\t     * 因为按key模糊获取数据的时候没有提供对应的方法，所有如果是redis的时候暴露处理方便使用\n    87\t     *\n    88\t     * @param factory factory\n    89\t     * @return RedisTemplate\n    90\t     */\n    91\t    @Bean\n    92\t    @ConditionalOnMissingBean(RedisTemplate.class)\n    93\t    @ConditionalOnProperty(\n    94\t            prefix = \&quot;auth.cache\&quot;,\n    95\t            name = \&quot;type\&quot;,\n    96\t            havingValue = \&quot;redis\&quot;\n    97\t    )\n    98\t    public RedisTemplate redisTemplate(RedisConnectionFactory factory) {\n    99\t        // 创建一个模板类\n   100\t        RedisTemplate&lt;String, Object&gt; template = new RedisTemplate&lt;&gt;();\n   101\t        // 将刚才的redis连接工厂设置到模板类中\n   102\t        template.setConnectionFactory(factory);\n   103\t\n   104\t        // 设置redis的String/Value的默认序列化方式\n   105\t        template.setKeySerializer(RedisSerializer.string());\n   106\t        template.setValueSerializer(RedisSerializer.java());\n   107\t        template.setHashKeySerializer(RedisSerializer.string());\n   108\t        template.setHashValueSerializer(RedisSerializer.java());\n   109\t        template.afterPropertiesSet();\n   110\t        return template;\n   111\t    }\n   112\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/annotation/NativeCacheHandle.java\n     1\tpackage cn.hy.auth.custom.user.cache.annotation;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     4\t\n     5\timport java.lang.annotation.*;\n     6\t\n     7\t/**\n     8\t * 类描述: 缓存类型枚举类\n     9\t *\n    10\t * <AUTHOR>    11\t * @date ：创建于 2020/11/30\n    12\t */\n    13\t@Documented\n    14\t@Inherited\n    15\t@Target(ElementType.TYPE)\n    16\t@Retention(RetentionPolicy.RUNTIME)\n    17\tpublic @interface NativeCacheHandle {\n    18\t\n    19\t    NativeCacheEnum value();\n    20\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\n   569\t    /**\n   570\t     * 缓存用户信息\n   571\t     *\n   572\t     * @param tokenId     tokenId\n   573\t     * @param userAccount 用户账号信息\n   574\t     * @return 返回用户账号信息\n   575\t     */\n   576\t    private UserAccountDTO putUserToCache(String tokenId, UserAccountDTO userAccount) {\n   577\t        //获取生成账号缓存标识\n   578\t        String userCacheKey = getUserCacheKey(userAccount.getId());\n   579\t        // 租户应用信息\n   580\t        setLesseeAppCodeToUserAccount(userAccount);\n   581\t        // 缓存用户信息\n   582\t        try {\n   583\t\n   584\t            getRootCache().put(userCacheKey, objectMapper.writeValueAsString(userAccount));\n   585\t\n   586\t            log.debug(\&quot;缓存用户信息：用户主键:[{}]-缓存标识:[{}]\&quot;, userAccount.getId(), userCacheKey);\n   587\t        } catch (JsonProcessingException e) {\n   588\t            log.warn(\&quot;json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}\&quot;,userAccount,e.getMessage());\n   589\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordRedisManage.java\n...\n    30\t\n    31\t    @Override\n    32\t    public void put(String key, Object value, Date expireDate) {\n    33\t        redisTemplate.opsForValue().set(key, value);\n    34\t        redisTemplate.expireAt(key, expireDate);\n    35\t    }\n    36\t\n    37\t    @Override\n    38\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    39\t        redisTemplate.opsForValue().set(key, value);\n    40\t        redisTemplate.expire(key, timeout, unit);\n    41\t    }\n    42\t\n    43\t    @Override\n    44\t    public Object get(String key) {\n    45\t        return redisTemplate.opsForValue().get(key);\n    46\t    }\n    47\t\n    48\t    @Override\n    49\t    public boolean invalidate(String key) {\n    50\t        redisTemplate.opsForValue().getOperations().delete(key);\n    51\t        return true;\n    52\t    }\n    53\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/environment/service/OauthSysMServiceImpl.java\n...\n    90\t                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);\n    91\t                DataEngineContextProxy.getContext().setLesseeCode(lesseeCodeBefore);\n    92\t                DataEngineContextProxy.getContext().setAppCode(appCodeBefore);\n    93\t            }\n    94\t        }\n    95\t        return cachedUuid;\n    96\t    }\n    97\t\n    98\t    @Override\n    99\t    public String clearCache() {\n   100\t        // 打印缓存信息\n   101\t        if (log.isInfoEnabled()){\n   102\t            log.info(\&quot;Cache stats: {}\&quot;, cache.stats());\n   103\t        }\n   104\t        // 清空缓存\n   105\t        cache.invalidateAll();\n   106\t        return \&quot;Cache cleared\&quot;;\n   107\t    }\n   108\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/enums/NativeCacheEnum.java\n...\n     7\t\n     8\t/**\n     9\t * 类描述: 缓存类型枚举\n    10\t *\n    11\t * <AUTHOR>    12\t * @date ：创建于 2020/11/30\n    13\t */\n    14\t@Getter\n    15\t@AllArgsConstructor\n    16\tpublic enum NativeCacheEnum {\n    17\t\n    18\t    /**\n    19\t     * caffeine缓存\n    20\t     */\n    21\t    CAFFEINE(CaffeineCache.class, \&quot;caffeine\&quot;),\n    22\t    /**\n    23\t     * redis缓存\n    24\t     */\n    25\t    REDIS(RedisCache.class, \&quot;redis\&quot;);\n    26\t\n    27\t    /**\n    28\t     * class类\n    29...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ce93fcac-1f72-4d87-969a-8f1066de3ee2;toolu_vrtx_01NVZBMipzzauRpTtFvjg4tD&quot;:{&quot;requestId&quot;:&quot;ce93fcac-1f72-4d87-969a-8f1066de3ee2&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01NVZBMipzzauRpTtFvjg4tD&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java\n     1\tpackage cn.hy.auth.custom.multi.cache;\n     2\t\n     3\timport cn.hutool.core.util.ObjectUtil;\n     4\timport cn.hy.auth.custom.common.utils.CachePrefixUtil;\n     5\timport org.springframework.cache.Cache;\n     6\timport org.springframework.cache.caffeine.CaffeineCacheManager;\n     7\t\n     8\timport java.util.Collection;\n     9\timport java.util.stream.Collectors;\n    10\t\n    11\t/**\n    12\t * 类描述: 继承Caffeine缓存，重新获取cahce方法适应与多租户多应用\n    13\t *\n    14\t * <AUTHOR>    15\t * @date ：创建于 2020/12/1\n    16\t */\n    17\tpublic class LesseeCaffeineCacheManager extends CaffeineCacheManager {\n    18\t\n    19\t    @Override\n    20\t    public void setCacheNames(Collection&lt;String&gt; cacheNames) {\n    21\t        if (ObjectUtil.isNull(cacheNames)) {\n    22\t            super.setCacheNames(null);\n    23\t            return;\n    24\t        }\n    25\t        String prefix = CachePrefixUtil.get();\n    26\t        Collection&lt;String&gt; newCacheNames = cacheNames.stream().filter(ObjectUtil::isNotNull).map(name -&gt; prefix + name).collect(Collectors.toSet());\n    27\t        super.setCacheNames(newCacheNames);\n    28\t    }\n    29\t\n    30\t    @Override\n    31\t    public Cache getCache(String name) {\n    32\t        String prefix = CachePrefixUtil.get();\n    33\t        return super.getCache(prefix + name);\n    34\t    }\n    35\t\n    36\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     4\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     5\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     6\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     7\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     8\timport com.github.benmanes.caffeine.cache.Cache;\n     9\timport com.google.common.collect.Sets;\n    10\timport org.apache.commons.lang3.StringUtils;\n    11\timport org.springframework.stereotype.Service;\n    12\t\n    13\timport java.util.Set;\n    14\timport java.util.concurrent.ConcurrentMap;\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n     1\tpackage cn.hy.auth.custom.business.starter;\n     2\t\n     3\timport cn.hy.auth.custom.common.utils.CachePrefixUtil;\n     4\timport cn.hy.auth.custom.multi.cache.LesseeCaffeineCacheManager;\n     5\timport com.github.benmanes.caffeine.cache.Caffeine;\n     6\timport lombok.extern.slf4j.Slf4j;\n     7\timport org.springframework.beans.factory.annotation.Value;\n     8\timport org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;\n     9\timport org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;\n    10\timport org.springframework.cache.CacheManager;\n    11\timport org.springframework.cache.caffeine.CaffeineCacheManager;\n    12\timport org.springframework.context.annotation.Bean;\n    13\timport org.springframework.context.annotation.Configuration;\n    14\timport org.springframework.data.redis.cache.RedisCacheConfiguration;\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/test/java/cn/hy/auth/custom/multi/utils/TestCacheConfiguration.java\n...\n    47\t    public CacheManager cacheManagerWithCaffeine() {\n    48\t        log.debug(\&quot;Test Caffeine cache 配置\&quot;);\n    49\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    50\t        Caffeine caffeine = Caffeine.newBuilder()\n    51\t                //cache的初始容量值\n    52\t                .initialCapacity(100)\n    53\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    54\t                .maximumSize(5000)\n    55\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    56\t        cacheManager.setCaffeine(caffeine);\n    57\t        //不允许设置空值\n    58\t        cacheManager.setAllowNullValues(false);\n    59\t        return cacheManager;\n    60\t    }\n    61\t\n    62\t    @Bean\n    63\t    @ConditionalOnMissingBean(CacheManager.class)\n    64\t    @ConditionalOnProperty(\n    65\t            prefix = \&quot;auth.cache\&quot;,\n    66\t            name = \&quot;type\&quot;,\n    67\t            havingValue = \&quot;redis\&quot;\n    68\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-common/src/main/java/cn/hy/auth/custom/common/utils/CachePrefixUtil.java\n     1\tpackage cn.hy.auth.custom.common.utils;\n     2\t\n     3\timport cn.hutool.core.util.ObjectUtil;\n     4\timport cn.hy.auth.custom.common.context.AuthContext;\n     5\timport cn.hy.auth.custom.common.domain.LoginStateDTO;\n     6\timport cn.hy.auth.custom.common.enums.ClientTypeEnum;\n     7\timport org.apache.commons.lang3.StringUtils;\n     8\t\n     9\timport java.util.StringJoiner;\n    10\timport java.util.function.Supplier;\n    11\t\n    12\t/**\n    13\t * 类描述: 获取缓存前缀\n    14\t *\n    15\t * <AUTHOR>    16\t * @date ：创建于 2020/12/1\n    17\t */\n    18\tpublic class CachePrefixUtil {\n    19\t    private static final ThreadLocal&lt;Boolean&gt; LOCAL = ThreadLocal.withInitial(() -&gt; Boolean.TRUE);\n    20\t    private CachePrefixUtil() {\n    21\t\n    22\t    }\n    23\t\n    24\t    public static Boolean isAutoAttachCcahePrefix(){\n    25\t        return LOCAL.get();\n    26\t    }\n    27\t    public static void setAutoAttachCachePrefix(Boolean b){\n    28\t         LOCAL.set(b);\n    29\t    }\n    30\t    public static void resetAutoAttachCachePrefix(){\n    31\t        LOCAL.remove();\n    32\t    }\n    33\t    /**\n    34\t     * 返回缓存前缀，格式:租户编码_应用编码_客户端标识\n    35\t     *\n    36\t     * @return 返回前缀\n    37\t     */\n    38\t    public static String get() {\n    39\t        // 不需要自动添加缓存前缀\n    40\t        if (!Boolean.TRUE.equals(isAutoAttachCcahePrefix())){\n    41\t            return \&quot;\&quot;;\n    42\t        }\n    43\t        //租户编码\n    44\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    45\t        //应用编码\n    46\t        String appCode = AuthContext.getContext().getAppCode();\n    47\t        if (StringUtils.isBlank(lesseeCode) &amp;&amp; StringUtils.isBlank(appCode)){\n    48\t            return \&quot;\&quot;;\n    49\t        }\n    50\t        LoginStateDTO loginStateDTO = AuthContext.getContext().loginState();\n    51\t\n    52\t        StringJoiner stringJoiner = new StringJoiner(\&quot;_\&quot;);\n    53\t        stringJoiner.add(lesseeCode).add(appCode);\n    54\t        if (ObjectUtil.isNotNull(loginStateDTO)) {\n    55\t            ClientTypeEnum clientType = loginStateDTO.getClientType();\n    56\t            if (ObjectUtil.isNotNull(clientType)) {\n    57\t                stringJoiner.add(String.valueOf(clientType.getCode()));\n    58\t            }\n    59\t        }\n    60\t\n    61\t        return stringJoiner.toString() + \&quot;_\&quot;;\n    62\t    }\n    63\t\n    64\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n     1\tpackage cn.hy.auth.custom.user.cache.service;\n     2\t\n     3\timport cn.hy.auth.custom.common.domain.UserAccountDTO;\n     4\timport cn.hy.auth.custom.common.domain.UserLoginInfoDTO;\n     5\t\n     6\timport javax.validation.constraints.NotBlank;\n     7\timport java.util.Set;\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     * @return 返回用户缓存信息\n    30\t     */\n    31\t    //UserAccountDTO getCacheByUserId(@NotNull Long userId);\n    32\t\n    33\t    /**\n    34\t     * 按登录账号查询用户信息，没有找到则从数据库中获取并放入缓存\n    35\t     *\n    36\t     * @param loginName 登录账号\n    37\t     * @return 返回用户缓存信息\n    38\t     */\n    39\t    UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName);\n    40\t\n    41\t    /**\n    42\t     * 按登录账号查询用户主键，没有找到则从数据库中获取并放入缓存\n    43\t     *\n    44\t     * @param loginName 登录账号\n    45\t     * @return 返回用户主键\n    46\t     */\n    47\t    //Long getUserIdByLoginName(@NotBlank String loginName);\n    48\t\n    49\t    /**\n    50\t     * 按用户主键移除缓存信息\n    51\t     *\n    52\t     * @param userId 用户主键\n    53\t     */\n    54\t    void removeCacheByUserId(@NotBlank Long userId);\n    55\t\n    56\t    /**\n    57\t     * 按登录账号移除缓存信息\n    58\t     *\n    59\t     * @param loginName 登录账号\n    60\t     */\n    61\t    void removeCacheByLoginName(@NotBlank String loginName);\n    62\t\n    63\t    /**\n    64\t     * 查询在线用户\n    65\t     *\n    66\t     * @return 返回在线用户的主键信息\n    67\t     */\n    68\t    Set&lt;Long&gt; getOnlineUserId();\n    69\t\n    70\t    /**\n    71\t     * 按tokenId查询用户登录数据\n    72\t     *\n    73\t     * @param tokenId ..\n    74\t     * @return 返回登录信息\n    75\t     */\n    76\t    UserLoginInfoDTO getUserLoginInfoByTokenId(@NotBlank String tokenId);\n    77\t\n    78\t\n    79\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n    44\t\n    45\t/**\n    46\t * 类描述: 用户缓存信息实现类\n    47\t *\n    48\t * <AUTHOR>    49\t * @date ：创建于 2020/11/27\n    50\t */\n    51\t@Slf4j\n    52\t@Service\n    53\tpublic class UserCacheServiceImpl implements UserCacheService, UserCacheTokenService {\n    54\t    private final TokenStore tokenStore;\n    55\t    private final ObjectMapper objectMapper;\n    56\t    /**\n    57\t     * 缓存实现类，该处已实现租户应用间的隔离\n    58\t     */\n    59\t    private final CacheManager cacheManager;\n    60\t\n    61\t    /**\n    62\t     * 用户账号信息实现类\n    63\t     */\n    64\t    private final UserAccountService userAccountService;\n    65\t\n    66\t    /**\n    67\t     * 缓存在线人员不同缓存的处理实现\n    68\t     */\n    69\t    private final NativeDiffCacheContext nativeDiffCacheContext;\n    70\t\n    71\t    /**\n    72\t     * 获取租户应用实现\n    73\t     */\n    74\t    private final AppInfoParseProxy appInfoParseProxy;\n    75\t\n    76\t    /**\n    77\t     * 用户登录信息\n    78\t     */\n    79\t    private final AuthLoginInfoService authLoginInfoService;\n    80\t\n    81\t    /**\n    82\t     * 跟主键标识\n    83\t     */\n    84\t    private static final String ROOT_CACHE_NAME = \&quot;hy_auth\&quot;;\n    85\t    /**\n    86\t     * 登录账号\n    87\t     */\n    88\t    private static final String USER_NAME = \&quot;username\&quot;;\n...\n   134\t        if (ObjectUtil.isNull(userAccountDTO)) {\n   135\t            String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n   136\t            synchronized (getLock(lockIdentify)){\n   137\t                try {\n   138\t                    userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n   139\t                    if (userAccountDTO == null){\n   140\t                        // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n   141\t                        userAccountDTO = loadUserAccountByLoginNameAndCache();\n   142\t                    }\n   143\t                    if (userAccountDTO == null) {\n   144\t                        userAccountDTO = loadUserAccountByTokenStore();\n   145\t                    }\n   146\t                }finally {\n   147\t                    removeLock(lockIdentify);\n   148\t                }\n   149\t            }\n   150\t        }\n   151\t        return userAccountDTO;\n   152\t    }\n...\n   229\t\n   230\t    @Override\n   231\t    public Set&lt;Long&gt; getOnlineUserId() {\n   232\t        Cache cache = cacheManager.getCache(ROOT_CACHE_NAME);\n   233\t        Object nativeCache = cache.getNativeCache();\n   234\t        if (ObjectUtil.isNull(nativeCache)) {\n   235\t            return Collections.emptySet();\n   236\t        }\n   237\t        NativeCacheEnum nativeCacheEnum = NativeCacheEnum.classOf(cache);\n   238\t        if (ObjectUtil.isNull(nativeCacheEnum)) {\n   239\t            log.debug(\&quot;没有找到对应的缓存类型[{}]\&quot;, cache.getName());\n   240\t            return Collections.emptySet();\n   241\t        }\n   242\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(nativeCache).rootCacheName(CachePrefixUtil.get() + ROOT_CACHE_NAME).build();\n   243\t        return nativeDiffCacheContext.getNativeCacheHandleMap(nativeCacheEnum.getType()).handle(nativeCacheDTO);\n   244\t    }\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/client/OauthClientDetailsServiceImpl.java\n...\n    84\t\n    85\t    /**\n    86\t     * 获取根部缓存信息\n    87\t     *\n    88\t     * @return 返回缓存对象\n    89\t     */\n    90\t    private Cache getRootCache() {\n    91\t        //租户编码\n    92\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    93\t        //应用编码\n    94\t        S...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;13314a64-d5cc-4ff2-b597-a6a4b28f4e8b;toolu_vrtx_01PeMrGfAa9hGMJzKbDBuwf8&quot;:{&quot;requestId&quot;:&quot;13314a64-d5cc-4ff2-b597-a6a4b28f4e8b&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01PeMrGfAa9hGMJzKbDBuwf8&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenConfigChangedKafkaListener.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：监听更新token配置信息，用于刷新更新缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2024/6/13 15:49\n    22\t **/\n    23\t@Service\n    24\t@Slf4j\n    25\tpublic class TokenConfigChangedKafkaListener {\n    26\t    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;\n    27\t    public TokenConfigChangedKafkaListener(HyClientDetailServiceProvider hyClientDetailServiceProvider) {\n    28\t        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;\n    29\t    }\n    30\t\n    31\t    @KafkaListener(topics = \&quot;${hy.saas.tokenConfig.cluster-cache.topic:saas-app-token-config}\&quot;,\n    32\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    33\t    )\n    34\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    35\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    36\t        try {\n    37\t            if (message.isPresent()) {\n    38\t                String msg = (String)message.get();\n    39\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    40\t                JSONObject jsonObject = JSON.parseObject(msg);\n    41\t                String lessCode = (String) jsonObject.get(\&quot;lessCode\&quot;);\n    42\t                String appCode = (String) jsonObject.get(\&quot;appCode\&quot;);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    if (ObjectUtil.isEmpty(appCode)|| \&quot;appmanage\&quot;.equals(appCode)){\n    46\t                        hyClientDetailServiceProvider.clearCache(lessCode);\n    47\t                    } else {\n    48\t                        hyClientDetailServiceProvider.clearCache(lessCode,appCode);\n    49\t                    }\n    50\t                } catch (Exception e) {\n    51\t                    log.warn(\&quot;处理Kafka的token 配置變化消息{}_{},报错，可以忽略不处理。{}\&quot;,lessCode,appCode,e.getMessage());\n    52\t                }\n    53\t            }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/TokenStatusChangedKafkaListener.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述：监听更新token配置信息，用于刷新更新缓存\n    18\t *\n    19\t * <AUTHOR> by fuxinrong\n    20\t * @date 2024/6/13 15:49\n    21\t **/\n    22\t@Slf4j\n    23\t@Service\n    24\t@ConditionalOnProperty(\n    25\t        prefix = \&quot;auth.token.store\&quot;,\n    26\t        name = \&quot;type\&quot;,\n    27\t        havingValue = \&quot;redisLocalCache\&quot;\n    28\t)\n    29\tpublic class TokenStatusChangedKafkaListener {\n    30\t    private final HyRedisLocalCacheTokenStore hyRedisLocalCacheTokenStore;\n    31\t    public TokenStatusChangedKafkaListener(TokenStore hyRedisLocalCacheTokenStore) {\n    32\t        this.hyRedisLocalCacheTokenStore = (HyRedisLocalCacheTokenStore) hyRedisLocalCacheTokenStore;\n    33\t    }\n    34\t\n    35\t    @KafkaListener(topics = \&quot;${hy.saas.token.status.topic:hy-auth-sys-token-status}\&quot;,\n    36\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    37\t    )\n    38\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    39\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    40\t        try {\n    41\t            if (message.isPresent()) {\n    42\t                String msg = (String)message.get();\n    43\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    44\t                JSONObject jsonObject = JSON.parseObject(msg);\n    45\t                String type = (String) jsonObject.get(\&quot;type\&quot;);\n    46\t                String token = (String) jsonObject.get(\&quot;token\&quot;);\n    47\t                String refreshToken = (String) jsonObject.get(\&quot;refreshToken\&quot;);\n    48\t                try {\n    49\t                    if (\&quot;revoked\&quot;.equals(type)){\n    50\t                        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);\n    51\t                    } else if (\&quot;refreshToken\&quot;.equals(type)){\n    52\t                        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);\n    53\t                    }\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/AuthAppStatusKafkaListener.java\n...\n    14\t\n    15\t/**\n    16\t * <AUTHOR>    17\t * @title: AuthAppStatusKafkaListener\n    18\t * @description: 监听应用状态变更情况，清除缓存\n    19\t * @date 2024/7/1\n    20\t */\n    21\t@Service\n    22\t@Slf4j\n    23\tpublic class AuthAppStatusKafkaListener {\n    24\t\n    25\t    @Value(\&quot;${hy.saas.cluster-cache.app-status-topic:saas-app-status}\&quot;)\n    26\t    private String appStatusTopic;\n    27\t\n    28\t    private final AppInvolvedTableServiceImpl AppInvolvedTableServiceImpl;\n    29\t\n    30\t    public AuthAppStatusKafkaListener(cn.hy.auth.custom.multi.systable.AppInvolvedTableServiceImpl appInvolvedTableServiceImpl) {\n    31\t        AppInvolvedTableServiceImpl = appInvolvedTableServiceImpl;\n    32\t    }\n    33\t\n    34\t    @KafkaListener(topics = \&quot;${hy.saas.cluster-cache.app-status-topic:saas-app-status}\&quot;,\n    35\t            groupId = \&quot;#{authKafkaGroupIdUtil.buildSaasGroupId()}\&quot;\n    36\t    )\n...\n    48\t                String lessCode = eventMsg.getLesseeCode();\n    49\t                String appCode = eventMsg.getAppCode();\n    50\t                Integer appStatus = eventMsg.getAppStatus();\n    51\t                try {\n    52\t                    AppInvolvedTableServiceImpl.clearAppStatusCache(lessCode, appCode);\n    53\t                    log.info(\&quot;通知更新认证中心应用状态完成：msg：{}\&quot;, LocaleUtil.getMessage(\&quot;AppInfoStatusController.controllerMap.msg4\&quot;, null));\n    54\t                } catch (Exception e) {\n    55\t                    log.warn(\&quot;处理Kafka的应用安装消息{}_{},报错，可以忽略不处理。{}\&quot;, lessCode, appCode, e.getMessage());\n    56\t                    log.error(\&quot;{}_{} 修改应用状态为：{}，失败了。\&quot;, lessCode, appCode, appStatus, e);\n    57\t                }\n    58\t            } else {\n    59\t                log.warn(\&quot;kafka消息内容为空，无法通知更新认证中心应用状态\&quot;);\n    60\t            }\n    61\t        } finally {\n    62\t            ack.acknowledge();\n    63\t        }\n    64\t    }\n    65\t\n    66\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/listener/TokenRevokedEventListener.java\n...\n    59\t\n    60\t        //移除缓存\n    61\t        userCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());\n    62\t\n    63\t        Authentication userAuthentication = ObjectUtil.isNull(tokenRevokedEvent.getOAuth2Authentication()) ? null : tokenRevokedEvent.getOAuth2Authentication().getUserAuthentication();\n    64\t        if (userAuthentication != null) {\n    65\t            Map&lt;String, Object&gt; detail = (Map&lt;String, Object&gt;) userAuthentication.getDetails();\n    66\t            if (MapUtils.isNotEmpty(detail)) {\n    67\t                Object clientId = detail.get(CLIENT_ID);\n    68\t                loginLogDTO.setMac(MapUtils.getString(detail, LOGIN_MAC));\n    69\t                loginLogDTO.setClientId(ObjectUtil.isNull(clientId) ? null : clientId.toString());\n    70\t            }\n    71\t        }\n    72\t\n    73\t        //发出登出需要记录日志的事件\n    74\t        applicationContext.publishEvent(new LogOutLogEvent(loginLogDTO));\n    75\t    }\n    76\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/MetaDataCacheService.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service;\n     2\t\n     3\t/**\n     4\t * @author: ysh\n     5\t * @project: hy-authentication-center\n     6\t * @className: MetaDataCacheService\n     7\t * @time: 2022-11-23 16:56\n     8\t * @desc: 元数据缓存接口\n     9\t **/\n    10\tpublic interface MetaDataCacheService {\n    11\t\n    12\t    /**\n    13\t     * 清除缓存\n    14\t     * @param lessCode 租户编号\n    15\t     * @param appCode 应用编号\n    16\t     */\n    17\t    void cacheClear(String lessCode, String appCode);\n    18\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-oauth2/src/main/java/cn/hy/auth/common/security/oauth2/token/store/HyRedisLocalCacheTokenStore.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述：本地缓存和redis2级缓存，解决redis的性能问题\n    20\t *\n    21\t *\n    22\t * <AUTHOR> by fuxinrong\n    23\t * @date 2022/1/11 11:16\n    24\t **/\n    25\t@Slf4j\n    26\tpublic class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener&lt;TokenRevokedEvent&gt; {\n    27\t\n    28\t    private static final String READ_AUTHENTICATION = \&quot;readAuthentication:\&quot;;\n    29\t    private static final String READ_AUTHENTICATION_FOR_REFRESH_TOKEN = \&quot;readAuthenticationForRefreshToken:\&quot;;\n    30\t    private static final String READ_REFRESH_TOKEN = \&quot;readRefreshToken:\&quot;;\n    31\t    private static final String READ_ACCESS_TOKEN = \&quot;readAccessToken:\&quot;;\n    32\t    private static final String REFRESH_TOKEN_TO_ACCESS_TOKEN = \&quot;refreshToken_to_accessToken:\&quot;;\n    33\t    private final ExpiringCache&lt;String,Object&gt; localTokenCache;\n    34\t    private final HyRedisTokenStore hyRedisTokenStore;\n    35\t\n    36\t    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {\n    37\t        localTokenCache = new ExpiringCache&lt;&gt;();\n    38\t        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);\n    39\t    }\n    40\t\n    41\t    public void setRemovingDelta(int removingDelta) {\n    42\t        hyRedisTokenStore.setRemovingDelta(removingDelta);\n    43\t    }\n    44\t\n    45\t    public void setPrefix(String prefix) {\n    46\t        hyRedisTokenStore.setPrefix(prefix);\n    47\t    }\n    48\t\n    49\t    public void setAuthenticationKeyGenerator(AuthenticationKeyGenerator authenticationKeyGenerator) {\n    50\t        hyRedisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);\n    51\t    }\n    52\t\n    53\t    public void setSerializationStrategy(RedisTokenStoreSerializationStrategy serializationStrategy) {\n    54\t        hyRedisTokenStore.setSerializationStrategy(serializationStrategy);\n    55\t    }\n...\nPath: hy-auth-common-business-starter/hy-auth-common-security-oauth2-starter/src/main/java/cn/hy/auth/common/business/oauth2/starter/TokenAutoConfiguration.java\n...\n     2\t\n     3\timport cn.hy.auth.common.security.oauth2.token.store.HyInMemoryTokenStore;\n     4\timport cn.hy.auth.common.security.oauth2.token.store.HyRedisLocalCacheTokenStore;\n     5\timport cn.hy.auth.common.security.oauth2.token.store.HyRedisTokenStore;\n     6\timport cn.hy.auth.common.security.oauth2.token.store.JdbcWithMemoryTokenStore;\n     7\timport lombok.extern.slf4j.Slf4j;\n     8\timport org.springframework.beans.factory.annotation.Value;\n     9\timport org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;\n    10\timport org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;\n    11\timport org.springframework.context.annotation.Bean;\n    12\timport org.springframework.context.annotation.Configuration;\n    13\timport org.springframework.data.redis.connection.RedisConnectionFactory;\n...\n   126\t    public TokenStore redisLoaclCacheTokenStore(RedisConnectionFactory redisConnectionFactory, AuthenticationKeyGenerator authenticationKeyGenerator) {\n   127\t        log.debug(\&quot;使用redisLocalCache 2级缓存token信息,启用HyRedisMemoryTokenStore实例对象。removingDelta：【{}】ms\&quot;,this.removingDelta);\n   128\t        HyRedisLocalCacheTokenStore redisTokenStore = new HyRedisLocalCacheTokenStore(redisConnectionFactory);\n   129\t        redisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);\n   130\t        redisTokenStore.setRemovingDelta(removingDelta);\n   131\t        return redisTokenStore;\n   132\t    }\n   133\t\n   134\t\n   135\t\n   136\t    /**\n   137\t     * 创建TokenStore bean redis模式  数据库+内存\n   138\t     *\n   139\t     * @param dataSource dataSource\n   140\t     * @return 返回TokenStore\n   141\t     */\n   142\t    @Bean\n   143\t    @ConditionalOnMissingBean(TokenStore.class)\n   144\t    @ConditionalOnProperty(\n   145\t            prefix = \&quot;auth.token.store\&quot;,\n   146\t            name = \&quot;type\&quot;,\n   147\t            havingValue = \&quot;cacheJdbc\&quot;\n   148\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n...\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     *...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;322b195a-3f46-4ab0-92a9-6c1db78eac07;toolu_vrtx_01K8FCeusfd96mLbnvo2Et9t&quot;:{&quot;requestId&quot;:&quot;322b195a-3f46-4ab0-92a9-6c1db78eac07&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01K8FCeusfd96mLbnvo2Et9t&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/account/service/impl/UserAccountLockServiceImpl.java:\n     1\tpackage cn.hy.auth.custom.user.account.service.impl;\n     2\t\n     3\timport cn.hutool.core.date.DateUtil;\n     4\timport cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;\n     5\timport cn.hy.auth.custom.common.context.AuthContext;\n     6\timport cn.hy.auth.custom.common.domain.authinfo.AppAuthAccountLockPolicyDTO;\n     7\timport cn.hy.auth.custom.common.utils.IpUtils;\n     8\timport cn.hy.auth.custom.user.account.dao.UserAccountLockDao;\n     9\timport cn.hy.auth.custom.user.account.dao.UserLoginFailureDao;\n    10\timport cn.hy.auth.custom.user.account.domain.UserAccountLock;\n    11\timport cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;\n    12\timport cn.hy.auth.custom.user.account.domain.UserLoginFailure;\n    13\timport cn.hy.auth.custom.user.account.service.UserAccountLockService;\n    14\timport cn.hy.auth.custom.user.account.service.UserAccountService;\n    15\timport cn.hy.id.IdWorker;\n    16\timport lombok.AllArgsConstructor;\n    17\timport org.apache.commons.lang3.time.DateUtils;\n    18\timport org.springframework.stereotype.Service;\n    19\t\n    20\timport java.math.BigDecimal;\n    21\timport java.util.Date;\n    22\timport java.util.HashMap;\n    23\timport java.util.Map;\n    24\timport java.util.concurrent.ConcurrentHashMap;\n    25\t\n    26\t/**\n    27\t * 类描述：用户账号锁定服务\n    28\t *\n    29\t * <AUTHOR> by fuxinrong\n    30\t * @date 2022/4/14 11:49\n    31\t **/\n    32\t@Service\n    33\t@AllArgsConstructor\n    34\tpublic class UserAccountLockServiceImpl implements UserAccountLockService, ValidateErrorService {\n    35\t    private final UserAccountLockDao userAccountLockDao;\n    36\t    private final UserLoginFailureDao userLoginFailureDao;\n    37\t    private final IdWorker idWorker;\n    38\t    private final UserAccountService userAccountService;\n    39\t    private final ConcurrentHashMap&lt;String, Object&gt; parallelLockMap = new ConcurrentHashMap&lt;&gt;();\n    40\t    @Override\n    41\t    public UserLoginFailure saveLoginFail(Long userId, String username) {\n    42\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n    43\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n    44\t            return null;\n    45\t        }\n    46\t        String lesseeCode = AuthContext.getContext().getLesseeCode();\n    47\t        String appCode = AuthContext.getContext().getAppCode();\n    48\t        // 保存登录失败次数和相关信息，\n    49\t        synchronized (getSerialNoRecordLoadingLock(lesseeCode+appCode+username)){\n    50\t            UserLoginFailure userLoginFailure = userLoginFailureDao.selectByUid(userId);\n    51\t            if (userLoginFailure == null){\n    52\t                userLoginFailure = UserLoginFailure.builder()\n    53\t                        .id(new BigDecimal(idWorker.nextId()))\n    54\t                        .createUserId(new BigDecimal(userId))\n    55\t                        .lastUpdateUserId(new BigDecimal(userId))\n    56\t                        .createTime(new Date())\n    57\t                        .lastUpdateTime(new Date())\n    58\t                        .loginTime(new Date())\n    59\t                        .userId(new BigDecimal(userId))\n    60\t                        .dataVersion(\&quot;1\&quot;)\n    61\t                        .loginFailureCount(1L)\n    62\t                        .build();\n    63\t                userLoginFailureDao.insert(userLoginFailure);\n    64\t            } else {\n    65\t                // 判断是自增，还是从0开始输入\n    66\t                // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）\n    67\t                boolean restLoginFailureCount;\n    68\t                if (\&quot;minuteTime\&quot;.equals(accountLockPolicy.getLockType())){\n    69\t                    // 指定分钟后自动恢复\n    70\t                    String loginErrorThreshold = accountLockPolicy.getLoginErrorThreshold();\n    71\t                    int loginErrorThresholdInt = Integer.parseInt(loginErrorThreshold);\n    72\t                    restLoginFailureCount = DateUtil.compare(new Date(),DateUtils.addMinutes(userLoginFailure.getLoginTime(),loginErrorThresholdInt)) &gt;= 0;\n    73\t                } else {\n    74\t                    // 次日之后可自动恢复\n    75\t                    restLoginFailureCount = DateUtil.compare(DateUtil.parse(DateUtil.today()),userLoginFailure.getLoginTime()) &gt;= 0;\n    76\t                }\n    77\t                updateUserLoginFailure(userLoginFailure,restLoginFailureCount);\n    78\t            }\n    79\t            // 判断是否需要锁定账号\n    80\t            processLockAccount(userLoginFailure,accountLockPolicy);\n    81\t            return userLoginFailure;\n    82\t        }\n    83\t    }\n    84\t\n    85\t    private Object getSerialNoRecordLoadingLock(String identify) {\n    86\t         return parallelLockMap.computeIfAbsent(identify, t -&gt; new Object());\n    87\t    }\n    88\t\n    89\t    @Override\n    90\t    public boolean isUserAccountLock(Long userId) {\n    91\t        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断\n    92\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n    93\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n    94\t            // 未启用\n    95\t            return false;\n    96\t        }\n    97\t        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);\n    98\t        if (userAccountLock ==null) {\n    99\t            return false;\n   100\t        }\n   101\t        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())&gt;= 0) {\n   102\t            userAccountLockDao.deleteByUid(userId);\n   103\t            // 已过锁定有效期\n   104\t            return false;\n   105\t        }\n   106\t        return true;\n   107\t    }\n   108\t\n   109\t    @Override\n   110\t    public UserAccountLock getUserAccountLock(Long userId) {\n   111\t        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断\n   112\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n   113\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n   114\t            // 未启用\n   115\t            return null;\n   116\t        }\n   117\t        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);\n   118\t        if (userAccountLock ==null) {\n   119\t            return null;\n   120\t        }\n   121\t        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())&gt;= 0) {\n   122\t            userAccountLockDao.deleteByUid(userId);\n   123\t            // 已过锁定有效期\n   124\t            return null;\n   125\t        }\n   126\t        return userAccountLock;\n   127\t    }\n   128\t\n   129\t    @Override\n   130\t    public void clearLoginFail(Long userId, String username) {\n   131\t        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();\n   132\t        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){\n   133\t            // 未启用\n   134\t            return ;\n   135\t        }\n   136\t        userLoginFailureDao.deleteByUid(userId);\n   137\t\n   138\t    }\n   139\t\n   140\t    @Override\n   141\t    public void lockUserAccount(String userName, String phone, String lockType) {\n   142\t        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(userName, phone);\n   143\t        UserAccountLock lock = new UserAccountLock();\n   144\t        lock.setId(new BigDecimal(idWorker.nextId()));\n   145\t        lock.setCreateUserId(new BigDecimal(1));\n   146\t        lock.setCreateUserName(\&quot;admin\&quot;);\n   147\t        lock.setCreateTime(new Date());\n   148\t        lock.setDataVersion(\&quot;1\&quot;);\n   149\t\n   150\t        lock.setLockEndTime(DateUtil.parse(\&quot;2099-01-01\&quot;));\n   151\t        lock.setClientType(\&quot;4\&quot;);\n   152\t        lock.setLastUpdateUserId(new BigDecimal(1L));\n   153\t        lock.setLastUpdateTime(new Date());\n   154\t        lock.setSequence(1L);\n   155\t        lock.setUserId(new BigDecimal(userByUserNameOrPhone.getId()));\n   156\t        lock.setLockStartTime(new Date());\n   157\t        lock.setLastUpdateUserName(\&quot;admin\&quot;);\n   158\t        lock.setLockRecodeType(lockType);\n   159\t        userAccountLockDao.insert(lock);\n   160\t    }\n   161\t\n   162\t    @Override\n   163\t    public Map&lt;String, Object&gt; isUserAccountLockByUserNameOrMobile(String phoneNumber) {\n   164\t        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(phoneNumber, phoneNumber);\n   165\t        Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   166\t        map.put(\&quot;userId\&quot;, \&quot;\&quot;);\n   167\t        if (userByUserNameOrPhone == null) {\n   168\t            map.put(\&quot;userExist\&quot;, false);\n   169\t            map.put(\&quot;judge\&quot;, false);\n   170\t            return map;\n   171\t        }\n   172\t        boolean result = isUserAccountLock(userByUserNameOrPhone.getId());\n   173\t        map.put(\&quot;judge\&quot;, result);\n   174\t        map.put(\&quot;userExist\&quot;, true);\n   175\t        map.put(\&quot;userId\&quot;, userByUserNameOrPhone.getId());\n   176\t        map.put(\&quot;userAccountName\&quot;, userByUserNameOrPhone.getUserAccountName());\n   177\t        return map;\n   178\t    }\n   179\t\n   180\t\n   181\t    private void processLockAccount(UserLoginFailure userLoginFailure,AppAuthAccountLockPolicyDTO accountLockPolicy){\n   182\t        if (userLoginFailure.getLoginFailureCount() &gt;= Integer.parseInt(accountLockPolicy.getLoginErrorThreshold())){\n   183\t            userAccountLockDao.deleteByUid(userLoginFailure.getUserId());\n   184\t            UserAccountLock userAccountLock = UserAccountLock.builder()\n   185\t                    .id(new BigDecimal(idWorker.nextId()))\n   186\t                    .createUserId(userLoginFailure.getUserId())\n   187\t                    .lastUpdateUserId(userLoginFailure.getUserId())\n   188\t                    .userId(userLoginFailure.getUserId())\n   189\t                    .ip(IpUtils.getIpAddress())\n   190\t                    .lockStartTime(new Date())\n   191\t                    .createTime(new Date())\n   192\t                    .lastUpdateTime(new Date())\n   193\t                    .dataVersion(\&quot;1\&quot;)\n   194\t                    .clientType(AuthContext.getContext().loginState().getClientType().name())\n   195\t                    .lockEndTime(getLockEndTime(accountLockPolicy))\n   196\t                    .build();\n   197\t            userAccountLockDao.insert(userAccountLock);\n   198\t        }\n   199\t    }\n   200\t    private Date getLockEndTime(AppAuthAccountLockPolicyDTO accountLockPolicy) {\n   201\t        // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）\n   202\t        if (\&quot;forever\&quot;.equals(accountLockPolicy.getLockType())){\n   203\t            // 锁定100年\n   204\t            return DateUtils.addYears(DateUtil.parse(DateUtil.today()),100);\n   205\t        }else if (\&quot;minuteTime\&quot;.equals(accountLockPolicy.getLockType())){\n   206\t            String lockMinute = accountLockPolicy.getLockMinute();\n   207\t            int lockMinuteInt = Integer.parseInt(lockMinute);\n   208\t            return DateUtils.addMinutes(new Date(),lockMinuteInt);\n   209\t        }else {\n   210\t            // 次日\n   211\t            return DateUtils.addDays(DateUtil.parse(DateUtil.today()),1);\n   212\t        }\n   213\t    }\n   214\t\n   215\t\n   216\t    private void updateUserLoginFailure(UserLoginFailure userLoginFailure, boolean restLoginFailureCount){\n   217\t        if (restLoginFailureCount){\n   218\t            // 可以从1开始了\n   219\t            userLoginFailure.setLoginFailureCount(1L);\n   220\t        }else {\n   221\t            // 自增\n   222\t            Long loginFailureCount = userLoginFailure.getLoginFailureCount();\n   223\t            if (loginFailureCount==null){\n   224\t                loginFailureCount = 0L;\n   225\t            }\n   226\t            loginFailureCount++;\n   227\t            userLoginFailure.setLoginFailureCount(loginFailureCount);\n   228\t        }\n   229\t        userLoginFailure.setLoginTime(new Date());\n   230\t        userLoginFailure.setLastUpdateTime(new Date());\n   231\t        userLoginFailureDao.updateByPrimaryKey(userLoginFailure);\n   232\t    }\n   233\t\n   234\t    @Override\n   235\t    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {\n   236\t        userLoginFailureDao.resetFailCount(userId);\n   237\t    }\n   238\t\n   239\t    @Override\n   240\t    public Map&lt;String, Object&gt; isUserAccountLockByEmail(String email) {\n   241\t        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);\n   242\t        Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   243\t        map.put(\&quot;userId\&quot;, \&quot;\&quot;);\n   244\t        if (userLoginAccountDTO == null) {\n   245\t            map.put(\&quot;userExist\&quot;, false);\n   246\t            map.put(\&quot;judge\&quot;, false);\n   247\t            return map;\n   248\t        }\n   249\t        boolean result = isUserAccountLock(userLoginAccountDTO.getId());\n   250\t        map.put(\&quot;judge\&quot;, result);\n   251\t        map.put(\&quot;userExist\&quot;, true);\n   252\t        map.put(\&quot;userId\&quot;, userLoginAccountDTO.getId());\n   253\t        map.put(\&quot;userAccountName\&quot;, userLoginAccountDTO.getUserAccountName());\n   254\t        return map;\n   255\t    }\n   256\t\n   257\t    @Override\n   258\t    public void lockUserAccountByEmail(String email, String lockType) {\n   259\t        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);\n   260\t        UserAccountLock lock = new UserAccountLock();\n   261\t        lock.setId(new BigDecimal(idWorker.nextId()));\n   262\t        lock.setCreateUserId(new BigDecimal(1));\n   263\t        lock.setCreateUserName(\&quot;admin\&quot;);\n   264\t        lock.setCreateTime(new Date());\n   265\t        lock.setDataVersion(\&quot;1\&quot;);\n   266\t        lock.setLockEndTime(DateUtil.parse(\&quot;2099-01-01\&quot;));\n   267\t        lock.setClientType(\&quot;4\&quot;);\n   268\t        lock.setLastUpdateUserId(new BigDecimal(1L));\n   269\t        lock.setLastUpdateTime(new Date());\n   270\t        lock.setSequence(1L);\n   271\t        lock.setUserId(new BigDecimal(userLoginAccountDTO.getId()));\n   272\t        lock.setLockStartTime(new Date());\n   273\t        lock.setLastUpdateUserName(\&quot;admin\&quot;);\n   274\t        lock.setLockRecodeType(lockType);\n   275\t        userAccountLockDao.insert(lock);\n   276\t    }\n   277\t\n   278\t}\n   279\t\nTotal lines in file: 279\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;eb00431a-b179-4fda-8e1e-618c7c167f25;toolu_vrtx_01WvNpNrGDxSEBu1mYww8wMr&quot;:{&quot;requestId&quot;:&quot;eb00431a-b179-4fda-8e1e-618c7c167f25&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01WvNpNrGDxSEBu1mYww8wMr&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   229\t\n   230\t    @Override\n   231\t    public Set&lt;Long&gt; getOnlineUserId() {\n   232\t        Cache cache = cacheManager.getCache(ROOT_CACHE_NAME);\n   233\t        Object nativeCache = cache.getNativeCache();\n   234\t        if (ObjectUtil.isNull(nativeCache)) {\n   235\t            return Collections.emptySet();\n   236\t        }\n   237\t        NativeCacheEnum nativeCacheEnum = NativeCacheEnum.classOf(cache);\n   238\t        if (ObjectUtil.isNull(nativeCacheEnum)) {\n   239\t            log.debug(\&quot;没有找到对应的缓存类型[{}]\&quot;, cache.getName());\n   240\t            return Collections.emptySet();\n   241\t        }\n   242\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(nativeCache).rootCacheName(CachePrefixUtil.get() + ROOT_CACHE_NAME).build();\n   243\t        return nativeDiffCacheContext.getNativeCacheHandleMap(nativeCacheEnum.getType()).handle(nativeCacheDTO);\n   244\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/KickOutTokenStoreServiceImpl.java\n...\n    43\t\n    44\t    @Override\n    45\t    public KickOutEnum getKickOutTokenMsg(String token) {\n    46\t        if (cacheManager == null) {\n    47\t            log.error(\&quot;缓存管理器为null，无法获取缓存。\&quot;);\n    48\t            return null;\n    49\t        }\n    50\t\n    51\t        Cache cache = cacheManager.getCache(CACHENAME);\n    52\t        if (cache == null) {\n    53\t            log.info(\&quot;缓存为null，判断是否为剔出的token。token:{}\&quot;, token);\n    54\t            return null;\n    55\t        }\n    56\t\n    57\t        Cache.ValueWrapper valueWrapper = cache.get(token);\n    58\t        if (valueWrapper == null) {\n    59\t            return null;\n    60\t        }\n    61\t\n    62\t        Object value = valueWrapper.get();\n    63\t        if (value == null) {\n    64\t            return null;\n    65\t        }\n    66\t\n    67\t        if (!(value instanceof String)) {\n    68\t            log.warn(\&quot;缓存中的值类型不正确，期望为String，实际为{}\&quot;, value.getClass().getName());\n    69\t            return null;\n    70\t        }\n    71\t\n    72\t        try {\n    73\t            return KickOutEnum.getByCode((String) value);\n    74\t        } catch (Exception e) {\n    75\t            log.error(\&quot;处理缓存值时发生异常\&quot;, e);\n    76\t            return null;\n    77\t        }\n    78\t    }\n    79\t\n    80\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n   133\t    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   134\t        log.debug(\&quot;systemConfig redis cache 配置\&quot;);\n   135\t\n   136\t        //初始化一个RedisCacheWriter\n   137\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   138\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   139\t                //不允许存入空值\n   140\t                .disableCachingNullValues()\n   141\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   142\t                //设置默认超过期时间是60000秒\n   143\t                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));\n   144\t\n   145\t        //初始化RedisCacheManager\n   146\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   147\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/annotation/NativeCacheHandle.java\n     1\tpackage cn.hy.auth.custom.user.cache.annotation;\n     2\t\n     3\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     4\t\n     5\timport java.lang.annotation.*;\n     6\t\n     7\t/**\n     8\t * 类描述: 缓存类型枚举类\n     9\t *\n    10\t * <AUTHOR>    11\t * @date ：创建于 2020/11/30\n    12\t */\n    13\t@Documented\n    14\t@Inherited\n    15\t@Target(ElementType.TYPE)\n    16\t@Retention(RetentionPolicy.RUNTIME)\n    17\tpublic @interface NativeCacheHandle {\n    18\t\n    19\t    NativeCacheEnum value();\n    20\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-center-boot/src/main/resources/config/application-dev.yml\n...\n   240\t        - /log/cache/clear\n   241\t        - /*/cache/clear/**\n   242\t        - /license/*\n   243\t        - /verifyCode/sliderCaptcha/**\n   244\t        - /appInfo/status\n   245\t        - /meta/data/cache/clear\n   246\t        - /**/user/checkUserAccount\n   247\t        - /free/log/setLevel\n   248\t    verifyCode:\n   249\t      maxFailedCount: 1000\n   250\t      # 验证码设置容错，越大代表容错率越高\n   251\t      tolerant: 0.03f\n   252\t  license:\n   253\t    excludesPattern: /**/actuator/**,/license/*\n   254\t    filterOrder: -********\n   255\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   256\t  dataEngine:\n   257\t    switch:\n   258\t      placeholder: false\n   259\tauth:\n   260\t  cache:\n   261\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   262\t    type: redis\n   263\t    #过期时间(s)\n   264\t    expire_time: 6000\n   265\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   266\t  token:\n   267\t    store:\n   268\t      type: redis\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-prod.yml\n...\n   232\t        - /**/user/birdThirdMember\n   233\t        - /**/user/bingThirdAccount\n   234\t        - /**/user/removeThirdBing\n   235\t      non-business:\n   236\t        - /**/actuator/**\n   237\t        - /route/cache/clear\n   238\t        - /log/cache/clear\n   239\t        - /*/cache/clear/**\n   240\t        - /license/*\n   241\t        - /verifyCode/sliderCaptcha/**\n   242\t        - /appInfo/status\n   243\t        - /meta/data/cache/clear\n   244\t        - /**/user/checkUserAccount\n   245\t        - /free/log/setLevel\n   246\t    verifyCode:\n   247\t      # 0 表示每次登录都要输入验证码\n   248\t      maxFailedCount: 1\n   249\t      # 验证码设置容错，越大代表容错率越高\n   250\t      tolerant: 0.03f\n   251\t  license:\n   252\t    excludesPattern: /**/actuator/**,/license/*\n   253\t    filterOrder: -********\n   254\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   255\t  dataEngine:\n   256\t    switch:\n   257\t      placeholder: false\n   258\tauth:\n   259\t  system_config:\n   260\t    #服务基准配置信息缓存类型,caffeine(caffeine内存模式，默认),redis(redis模式)\n   261\t    cache: caffeine\n   262\t    #过期时间(s)\n   263\t    expire_time: 3600\n   264\t  cache:\n   265\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   266\t    type: caffeine\n   267\t    #过期时间(s)\n   268\t    expire_time: 6000\n   269\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   270\t  token:\n   271\t    store:\n   272\t      type: jvm\n   273\t\n   274\t# 滑块验证码配置， 详细请看 cloud.tianai.captcha.autoconfiguration.ImageCaptchaProperties 类\n   275\tcaptcha:\n   276\t  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider\n   277\t  prefix: captcha\n   278\t  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整\n   279\t  expire:\n   280\t    # 默认缓存时间 2分钟\n   281\t    default: 60000\n   282\t    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些\n   283\t    WORD_IMAGE_CLICK: 20000\n   284\t  # 使用加载系统自带的资源， 默认是 false\n   285\t  init-default-resource: true\n   286\t  cache:\n   287\t    # 缓存控制， 默认为false不开启\n   288\t    enabled: true\n   289\t    # 验证码会提前缓存一些生成好的验证数据， 默认是20\n   290\t    cacheSize: 20\n   291\t    # 缓存拉取失败后等待时间 默认是 5秒钟\n   292\t    wait-time: 1000\n   293\t    # 缓存检查间隔 默认是2秒钟\n   294\t    period: 5000\n   295\t    secondary:\n   296\t      # 二次验证， 默认false 不开启\n   297\t      enabled: false\n...\nPath: hy-auth-center-boot/src/main/resources/config/application.yml\n...\n   175\t        - /verifyCode/sliderCaptcha/**\n   176\t        - /meta/data/cache/clear\n   177\t        - /**/user/checkUserAccount\n   178\t    verifyCode:\n   179\t      maxFailedCount: 1\n   180\t      # 验证码设置容错，越大代表容错率越高\n   181\t      tolerant: 0.03f\n   182\t  license:\n   183\t    excludesPattern: /**/actuator/**,/license/*\n   184\t    filterOrder: -********\n   185\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   186\t  dataEngine:\n   187\t    switch:\n   188\t      placeholder: false\n   189\tauth:\n   190\t  cache:\n   191\t    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)\n   192\t    type: caffeine\n   193\t    #过期时间(s)\n   194\t    expire_time: 6000\n   195\t  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)\n   196\t  token:\n   197\t    store:\n   198\t      type: jvm\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/AutoOfflineCacheConfiguration.java\n...\n    65\t    @Qualifier(\&quot;autoOfflineCacheManager\&quot;)\n    66\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n    67\t        log.debug(\&quot;autoOfflineCacheManager redis cache 配置\&quot;);\n    68\t\n    69\t        //初始化一个RedisCacheWriter\n    70\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n    71\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n    72\t                //不允许存入空值\n    73\t                .disableCachingNullValues()\n    74\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n    75\t                //设置默认超过期时间是3600秒\n    76\t                .entryTtl(Duration.ofSeconds(expireTime));\n    77\t        //初始化RedisCacheManager\n    78\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n    79\t    }\n    80\t\n    81\t\n    82\t\n    83\t}\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-cloud.yml\n...\n   171\t        - /**/user/account/current\n   172\t        - /**/user/association/current\n   173\t        - /**/free/account/online/list\n   174\t        - /**/user/pwd\n   175\t        - /**/history/login/last\n   176\t        - /**/user/currentLoginUser\n   177\t      non-business:\n   178\t        - /**/actuator/**\n   179\t        - /route/cache/clear\n   180\t        - /log/cache/clear\n   181\t        - /license/*\n   182\t        - /verifyCode/sliderCaptcha/**\n   183\t    verifyCode:\n   184\t      maxFailedCount: 1\n   185\t      # 验证码设置容错，越大代表容错率越高\n   186\t      tolerant: 0.03f\n   187\t  license:\n   188\t    excludesPattern: /**/actuator/**,/license/*\n   189\t    filterOrder: -********\n   190\t  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开\n   191\t  dataEngine:\n   192\t    switch:\n   193\t      placeholder: f...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;9c7d1f57-51c5-4488-9539-afdc09fa5978;toolu_vrtx_011AmTUNnBeU4tpXP1YxnFp4&quot;:{&quot;requestId&quot;:&quot;9c7d1f57-51c5-4488-9539-afdc09fa5978&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_011AmTUNnBeU4tpXP1YxnFp4&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.user.cache.service.impl.cache;\n     2\t\n     3\timport cn.hy.auth.common.business.tool.redis.service.RedisService;\n     4\timport cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;\n     5\timport cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;\n     6\timport cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;\n     7\timport cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;\n     8\timport cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;\n     9\timport lombok.AllArgsConstructor;\n    10\timport org.springframework.stereotype.Service;\n    11\t\n    12\timport java.util.List;\n    13\timport java.util.Set;\n    14\timport java.util.stream.Collectors;\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n...\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/cache/LesseeCaffeineCacheManager.java\n...\n    10\t\n    11\t/**\n    12\t * 类描述: 继承Caffeine缓存，重新获取cahce方法适应与多租户多应用\n    13\t *\n    14\t * <AUTHOR>    15\t * @date ：创建于 2020/12/1\n    16\t */\n    17\tpublic class LesseeCaffeineCacheManager extends CaffeineCacheManager {\n    18\t\n    19\t    @Override\n    20\t    public void setCacheNames(Collection&lt;String&gt; cacheNames) {\n    21\t        if (ObjectUtil.isNull(cacheNames)) {\n    22\t            super.setCacheNames(null);\n    23\t            return;\n    24\t        }\n    25\t        String prefix = CachePrefixUtil.get();\n    26\t        Collection&lt;String&gt; newCacheNames = cacheNames.stream().filter(ObjectUtil::isNotNull).map(name -&gt; prefix + name).collect(Collectors.toSet());\n    27\t        super.setCacheNames(newCacheNames);\n    28\t    }\n    29\t\n    30\t    @Override\n    31\t    public Cache getCache(String name) {\n    32\t        String prefix = CachePrefixUtil.get();\n    33\t        return super.getCache(prefix + name);\n    34\t    }\n    35\t\n    36\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/controller/MetaDataCacheController.java\n...\n    12\t\n    13\t/**\n    14\t * @author: ysh\n    15\t * @project: hy-authentication-center\n    16\t * @className: MetaDataController\n    17\t * @time: 2022-11-23 16:55\n    18\t * @desc: 元数据相关接口\n    19\t **/\n    20\t@AllArgsConstructor\n    21\t@RestController\n    22\tpublic class MetaDataCacheController {\n    23\t\n    24\t    private final MetaDataCacheService metaDataCacheService;\n    25\t\n    26\t    /**\n    27\t     * 清除元数据缓存（目前给paas调用）\n    28\t     * @param lessCode 租户编号\n    29\t     * @param appCode 应用编号\n    30\t     * @return 结果\n    31\t     */\n    32\t    @PostMapping(\&quot;/meta/data/cache/clear\&quot;)\n    33\t    public Map&lt;String,String&gt; cacheClear(@RequestParam(\&quot;lessCode\&quot;) String lessCode, @RequestParam(\&quot;appCode\&quot;) String appCode) {\n    34\t        metaDataCacheService.cacheClear(lessCode, appCode);\n    35\t        Map&lt;String, String&gt; resultMap = new HashMap&lt;&gt;(1);\n    36\t        resultMap.put(\&quot;result\&quot;, LocaleUtil.getMessage(\&quot;MetaDataCacheController.controllerMap.msg1\&quot;, \&quot;\&quot;));\n    37\t        return resultMap;\n    38\t    }\n    39\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceTest.java\n...\n    19\t\n    20\t/**\n    21\t * 类描述: Caffeine缓存类测试\n    22\t *\n    23\t * <AUTHOR>    24\t * @date ：创建于 2020/12/3\n    25\t */\n    26\t@RunWith(SpringJUnit4ClassRunner.class)\n    27\t@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})\n    28\tpublic class CaffeineCacheServiceTest {\n    29\t\n    30\t    @Resource(name = \&quot;caffeineCacheServiceImpl\&quot;)\n    31\t    private NativeDiffCacheService nativeDiffCacheService;\n    32\t\n    33\t    @MockBean\n    34\t    private Cache cache;\n    35\t\n    36\t    @Test\n    37\t    public void handle() {\n    38\t        ConcurrentHashMap map = new ConcurrentHashMap();\n    39\t        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);\n    40\t\n    41\t        when(cache.asMap()).thenReturn(map);\n    42\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();\n    43\t        Set&lt;Long&gt; userId = nativeDiffCacheService.handle(nativeCacheDTO);\n    44\t        Assert.assertEquals(map.size(), userId.size());\n    45\t    }\n    46\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   134\t        if (ObjectUtil.isNull(userAccountDTO)) {\n   135\t            String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);\n   136\t            synchronized (getLock(lockIdentify)){\n   137\t                try {\n   138\t                    userAccountDTO = getUserAccountFromCacheByLoginName(loginName);\n   139\t                    if (userAccountDTO == null){\n   140\t                        // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了\n   141\t                        userAccountDTO = loadUserAccountByLoginNameAndCache();\n   142\t                    }\n   143\t                    if (userAccountDTO == null) {\n   144\t                        userAccountDTO = loadUserAccountByTokenStore();\n   145\t                    }\n   146\t                }finally {\n   147\t                    removeLock(lockIdentify);\n   148\t                }\n   149\t            }\n   150\t        }\n   151\t        return userAccountDTO;\n   152\t    }\n...\n   208\t\n   209\t    @Override\n   210\t    public void removeCacheByUserId(@NotBlank Long userId) {\n   211\t        String tokenId = getTokenIdByUserId(userId);\n   212\t        if (StringUtils.isBlank(tokenId)) {\n   213\t            log.warn(\&quot;按用户主键[{}]获取tokenId失败,获取为空\&quot;, userId);\n   214\t            return;\n   215\t        }\n   216\t        //移除缓存信息\n   217\t        deleteUserCacheByTokenId(tokenId);\n   218\t    }\n   219\t\n   220\t    @Override\n   221\t    public void removeCacheByLoginName(@NotBlank String loginName) {\n   222\t        Long userId = getUserIdFromCacheByLoginName(loginName);\n   223\t        if (ObjectUtil.isNull(userId)) {\n   224\t            log.warn(\&quot;按登录账号[{}]获取用户主键失败，主键为空\&quot;, loginName);\n   225\t            return;\n   226\t        }\n   227\t        removeCacheByUserId(userId);\n   228\t    }\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\n   569\t    /**\n   570\t     * 缓存用户信息\n   571\t     *\n   572\t     * @param tokenId     tokenId\n   573\t     * @param userAccount 用户账号信息\n   574\t     * @return 返回用户账号信息\n   575\t     */\n   576\t    private UserAccountDTO putUserToCache(String tokenId, UserAccountDTO userAccount) {\n   577\t        //获取生成账号缓存标识\n   578\t        String userCacheKey = getUserCacheKey(userAccount.getId());\n   579\t        // 租户应用信息\n   580\t        setLesseeAppCodeToUserAccount(userAccount);\n   581\t        // 缓存用户信息\n   582\t        try {\n   583\t\n   584\t            getRootCache().put(userCacheKey, objectMapper.writeValueAsString(userAccount));\n   585\t\n   586\t            log.debug(\&quot;缓存用户信息：用户主键:[{}]-缓存标识:[{}]\&quot;, userAccount.getId(), userCacheKey);\n   587\t        } catch (JsonProcessingException e) {\n   588\t            log.warn(\&quot;json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}\&quot;,userAccount,e.getMessage());\n   589\t        }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/environment/service/OauthSysMServiceImpl.java\n...\n    90\t                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);\n    91\t                DataEngineContextProxy.getContext().setLesseeCode(lesseeCodeBefore);\n    92\t                DataEngineContextProxy.getContext().setAppCode(appCodeBefore);\n    93\t            }\n    94\t        }\n    95\t        return cachedUuid;\n    96\t    }\n    97\t\n    98\t    @Override\n    99\t    public String clearCache() {\n   100\t        // 打印缓存信息\n   101\t        if (log.isInfoEnabled()){\n   102\t            log.info(\&quot;Cache stats: {}\&quot;, cache.stats());\n   103\t        }\n   104\t        // 清空缓存\n   105\t        cache.invalidateAll();\n   106\t        return \&quot;Cache cleared\&quot;;\n   107\t    }\n   108\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/test/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceTest.java\n...\n    17\t\n    18\t/**\n    19\t * 类描述: redis缓存测试类\n    20\t *\n    21\t * <AUTHOR>    22\t * @date ：创建于 2020/12/3\n    23\t */\n    24\t@RunWith(SpringJUnit4ClassRunner.class)\n    25\t@ContextConfiguration(classes = {RedisCacheServiceImpl.class})\n    26\tpublic class RedisCacheServiceTest {\n    27\t\n    28\t    @Resource(name = \&quot;redisCacheServiceImpl\&quot;)\n    29\t    private NativeDiffCacheService nativeDiffCacheService;\n    30\t\n    31\t    @MockBean\n    32\t    private RedisService redisService;\n    33\t\n    34\t    @Test\n    35\t    public void handle() {\n    36\t        when(redisService.getValuesByPrefix(any())).thenReturn(Collections.singletonList(1L));\n    37\t        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().rootCacheName(\&quot;hy_auth\&quot;).build();\n    38\t        nativeDiffCacheService.handle(nativeCacheDTO);\n    39\t    }\n    40\t}...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/UserCacheService.java\n...\n     8\t\n     9\t/**\n    10\t * 类描述: 用户缓存信息\n    11\t *\n    12\t * <AUTHOR>    13\t * @date ：创建于 2020/11/27\n    14\t */\n    15\tpublic interface UserCacheService {\n    16\t\n    17\t    /**\n    18\t     * 缓存用户信息\n    19\t     *\n    20\t     * @param userId 用户主键\n    21\t     * @return 返回用户缓存信息\n    22\t     */\n    23\t    //UserAccountDTO cacheUser(@NotNull Long userId);\n    24\t\n    25\t    /**\n    26\t     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存\n    27\t     *\n    28\t     * @param userId 用户主键\n    29\t     * @return 返回用户缓存信息\n    30\t     */\n    31\t    //UserAccountDTO getCacheByUserId(@NotNull Long userId);\n    32\t\n    33\t    /**\n    34\t     * 按登录账号查询用户信息，没有找到则从数据库中获取并放入缓存\n    35\t     *\n    36\t     * @param loginName 登录账号\n    37\t     * @return 返回用户缓存信息\n    38\t     */\n    39\t    UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName);\n    40\t\n    41\t    /**\n    42\t     * 按登录账号查询用户主键，没有找到则从数据库中获取并放入缓存\n    43\t     *\n    44\t     * @param loginName 登录账号\n    45\t     * @return 返回用户主键\n    46\t     */\n    47\t    //Long getUserIdByLoginName(@NotBlank String loginName);\n    48\t\n    49\t    /**\n    50\t     * 按用户主键移除缓存信息\n    51\t     *\n    52\t     * @param userId 用户主键\n    53\t     */\n    54\t    void removeCacheByUserId(@NotBlank Long userId);\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;f89357c9-3194-4c1c-adaf-bbc0646401dd;toolu_vrtx_011JKRz5CfgMaM1uxeiAYZwj&quot;:{&quot;requestId&quot;:&quot;f89357c9-3194-4c1c-adaf-bbc0646401dd&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_011JKRz5CfgMaM1uxeiAYZwj&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: hy-auth-custom-business/hy-auth-custom-multi-app/src/main/java/cn/hy/auth/custom/multi/systable/AppInvolvedTableServiceImpl.java\n...\n    25\t\n    26\t/**\n    27\t * 认证中心涉及的应用信息--系统表\n    28\t * 该表全局只有一个，无需增加表名前缀\n    29\t *\n    30\t * <AUTHOR>    31\t * @date 2020-12-07 10:37\n    32\t **/\n    33\t@Service\n    34\t@Slf4j\n    35\tpublic class AppInvolvedTableServiceImpl implements AppAuthSystemTableService {\n    36\t\n    37\t    private static final String TABLE_NAME = \&quot;auth_app_involved\&quot;;\n    38\t\n    39\t    private final JdbcTemplate jdbcTemplate;\n    40\t\n    41\t    private final CacheManager cacheManager;\n    42\t    private final IdWorker idWorker;\n    43\t    static final String ROOT_CACHE_NAME = \&quot;hy_auth_auth_app_involved\&quot;;\n    44\t\n    45\t    private static final Pattern DATABASE_NAME_PATTERN = Pattern.compile(\&quot;jdbc:(?&lt;db&gt;\\\\w+):.*((//)|@)(?&lt;host&gt;.+):(?&lt;port&gt;\\\\d+)(/|(;DatabaseName=)|:)(?&lt;dbName&gt;\\\\w+.+)\\\\?\&quot;);\n    46\t    private final  ConcurrentHashMap&lt;String,Object&gt; parallelLockMap = new ConcurrentHashMap&lt;&gt;();\n    47\t\n    48\t    @Autowired\n    49\t    private DataSourceProperties dataSourceProperties;\n...\n   141\t        String key = lesseeCode+\&quot;_\&quot;+appCode;\n   142\t        Map&lt;String, Object&gt; appInfo = getAppInfoFromCache(key);\n   143\t        if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n   144\t            synchronized (getLock(key)){\n   145\t                try {\n   146\t                    appInfo = getAppInfoFromCache(key);\n   147\t                    if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){\n   148\t                        log.debug(\&quot;从数据库加载应用{},AppInvolved信息缓存\&quot;,key);\n   149\t                        String sql =\&quot;Select *  From \&quot; + TABLE_NAME + \&quot; Where lessee_code=? And app_code=? limit 1\&quot;;\n   150\t                        List&lt;Map&lt;String, Object&gt;&gt; result = jdbcTemplate.queryForList(sql, lesseeCode, appCode);\n   151\t                        if (CollectionUtils.isEmpty(result)) {\n   152\t                            String noInitAppSql = \&quot;SELECT COUNT(1) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?\&quot;;\n   153\t                            Integer i = jdbcTemplate.queryForObject(noInitAppSql, Integer.class, getDataBaseName(dataSourceProperties.getUrl()), lesseeCode + \&quot;_\&quot; + appCode + \&quot;_auth_config\&quot;);\n   154\t                            if (i &gt; 0) {\n   155\t                                Map&lt;String, Object&gt; map = new HashMap&lt;&gt;();\n   156\t                                map.put(\&quot;lessee_code\&quot;, lesseeCode);\n   157\t                                map.put(\&quot;app_code\&quot;, appCode);\n   158\t                                map.put(\&quot;status\&quot;, 1);\n   159\t                                result.add(map);\n   160\t                            }\n   161\t                        }\n   162\t                        if (CollectionUtils.isNotEmpty(result)){\n   163\t                            appInfo = CollectionUtils.isNotEmpty(result)?result.get(0):Collections.emptyMap();\n   164\t                            getRootCache().put(key,JSON.toJSONString(appInfo));\n   165\t                        }\n   166\t                    }\n   167\t                }finally {\n   168\t                    removeLock(key);\n   169\t                }\n   170\t            }\n   171\t        }\n   172\t        return appInfo;\n   173\t    }\n   174\t\n   175\t    public static String getDataBaseName(String url) {\n   176\t        Matcher m = DATABASE_NAME_PATTERN.matcher(url);\n   177\t        if(m.find()) {\n   178\t            return m.group(\&quot;dbName\&quot;);\n   179\t        }\n   180\t        return \&quot;\&quot;;\n   181\t    }\n   182\t\n   183\t    private Map&lt;String,Object&gt; getAppInfoFromCache(String key){\n   184\t        String appInvolvedStr = getRootCache().get(key, String.class);\n   185\t        if (StringUtils.isBlank(appInvolvedStr)){\n   186\t            return null;\n   187\t        }\n   188\t        return JSON.parseObject(appInvolvedStr);\n   189\t    }\n   190\t    private Object getLock(String identify) {\n   191\t        // 线程安全\n   192\t        return parallelLockMap.computeIfAbsent(identify, t -&gt; new Object());\n   193\t\n   194\t    }\n   195\t\n   196\t    private void removeLock(String identify) {\n   197\t        // 线程安全\n   198\t        parallelLockMap.remove(identify);\n   199\t    }\n   200\t\n   201\t    /**\n   202\t     * 获取根部缓存信息\n   203\t     *\n   204\t     * @return 返回缓存对象\n   205\t     */\n   206\t    private Cache getRootCache() {\n   207\t        String cacheName = ROOT_CACHE_NAME;\n   208\t        log.debug(\&quot;AppInvolvedTableServiceImpl cacheName: {}\&quot;,cacheName);\n   209\t       // Boolean autoAttachCachePrefix = CachePrefixUtil.isAutoAttachCcahePrefix();\n   210\t        //try {\n   211\t          //  CachePrefixUtil.setAutoAttachCachePrefix(false);\n   212\t            return cacheManager.getCache(cacheName);\n   213\t       // }finally {\n   214\t            // 恢复原样\n   215\t            //CachePrefixUtil.setAutoAttachCachePrefix(autoAttachCachePrefix);\n   216\t        //}\n   217\t    }\n   218\t    /**\n   219\t     * 修改应用信息的状态\n   220\t     *\n   221\t     * @param lesseeCode 租户编码\n   222\t     * @param appCode    应用编码\n   223\t     * @return .\n   224\t     */\n   225\t    public int updateAppInfoStatus(String lesseeCode, String appCode,Integer status) {\n   226\t        String sql =\&quot;update \&quot; + TABLE_NAME + \&quot; set status = ? Where lessee_code=? And app_code=? ;\&quot;;\n   227\t        int update = jdbcTemplate.update(sql, status, lesseeCode, appCode);\n   228\t        // 情况缓存信息\n   229\t        String key = lesseeCode+\&quot;_\&quot;+appCode;\n   230\t        Cache rootCache = getRootCache();\n   231\t        if (rootCache != null){\n   232\t            //getRootCache().put(key,\&quot;\&quot;);\n   233\t            rootCache.evict(key);\n   234\t        }\n   235\t        AuthContext.getContext().setLesseeCode(lesseeCode);\n   236\t        AuthContext.getContext().setAppCode(appCode);\n   237\t        rootCache = getRootCache();\n   238\t        if (rootCache != null){\n   239\t            //getRootCache().put(key,\&quot;\&quot;);\n   240\t            rootCache.evict(key);\n   241\t        }\n   242\t        log.info(\&quot;清除应用{},AppInvolved信息缓存\&quot;,key);\n   243\t        return update;\n   244\t    }\n   245\t\n   246\t    public void clearAppStatusCache(String lesseeCode, String appCode) {\n   247\t        // 清空缓存信息\n   248\t        String key = lesseeCode + \&quot;_\&quot; + appCode;\n   249\t        Cache rootCache = getRootCache();\n   250\t        if (rootCache != null) {\n   251\t            //getRootCache().put(key,\&quot;\&quot;);\n   252\t            rootCache.evict(key);\n   253\t        }\n   254\t        AuthContext.getContext().setLesseeCode(lesseeCode);\n   255\t        AuthContext.getContext().setAppCode(appCode);\n   256\t        rootCache = getRootCache();\n   257\t        if (rootCache != null) {\n   258\t            //getRootCache().put(key,\&quot;\&quot;);\n   259\t            rootCache.evict(key);\n   260\t        }\n   261\t        log.info(\&quot;通知清除{}应用AppInvolved信息缓存\&quot;, key);\n   262\t    }\n   263\t\n   264\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/CaffeineCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述: caffeine缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@Service\n    23\t@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)\n    24\tpublic class CaffeineCacheServiceImpl implements NativeDiffCacheService {\n    25\t\n    26\t    @Override\n    27\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    28\t        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();\n    29\t        ConcurrentMap concurrentMap = caffeineCache.asMap();\n    30\t\n    31\t        //需要返回的用户主键集合\n    32\t        Set&lt;Long&gt; userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());\n    33\t        concurrentMap.forEach((k, v) -&gt; {\n    34\t            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {\n    35\t                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));\n    36\t            }\n    37\t        });\n    38\t\n    39\t        return userIds;\n    40\t    }\n    41\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/cache/RedisCacheServiceImpl.java\n...\n    15\t\n    16\t/**\n    17\t * 类描述:redis缓存处理\n    18\t *\n    19\t * <AUTHOR>    20\t * @date ：创建于 2020/11/30\n    21\t */\n    22\t@AllArgsConstructor\n    23\t@Service\n    24\t@NativeCacheHandle(value = NativeCacheEnum.REDIS)\n    25\tpublic class RedisCacheServiceImpl implements NativeDiffCacheService {\n    26\t\n    27\t    private final RedisService redisService;\n    28\t\n    29\t    @Override\n    30\t    public Set&lt;Long&gt; handle(NativeCacheDTO nativeCacheDTO) {\n    31\t        String cacheName = String.format(\&quot;%s::%s\&quot;, nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);\n    32\t        List&lt;Object&gt; cacheUserIds = redisService.getValuesByPrefix(cacheName);\n    33\t        return cacheUserIds.stream().map(userId -&gt; Long.valueOf(userId.toString())).collect(Collectors.toSet());\n    34\t    }\n    35\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/cache/meta/service/impl/MetaDataCacheServiceImpl.java\n     1\tpackage cn.hy.auth.custom.security.cache.meta.service.impl;\n     2\t\n     3\timport cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;\n     4\timport cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;\n     5\timport lombok.AllArgsConstructor;\n     6\timport org.springframework.stereotype.Service;\n     7\t\n     8\t/**\n     9\t * @author: ysh\n    10\t * @project: hy-authentication-center\n    11\t * @className: MetaDataCacheServiceImpl\n    12\t * @time: 2022-11-23 16:56\n    13\t * @desc: 元数据缓存实现类\n    14\t **/\n    15\t@Service\n    16\t@AllArgsConstructor\n    17\tpublic class MetaDataCacheServiceImpl implements MetaDataCacheService {\n    18\t\n    19\t    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;\n    20\t\n    21\t    @Override\n    22\t    public void cacheClear(String lessCode, String appCode) {\n    23\t        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);\n    24\t    }\n    25\t}\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/expiry/CacheExpiryFunction.java\n...\n    10\t\n    11\tpublic class CacheExpiryFunction implements Expiry&lt;String, Object&gt; {\n    12\t    @Override\n    13\t    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {\n    14\t        if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {\n    15\t            // 错误次数在晚上12点自动过期\n    16\t            LocalDateTime currentDateTime = LocalDateTime.now();\n    17\t            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);\n    18\t            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);\n    19\t            return TimeUnit.SECONDS.toNanos(secondsDiff);\n    20\t        } else if (key != null &amp;&amp; key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {\n    21\t            // 修改密码的临时权限字符串过期时间为10分钟\n    22\t            return TimeUnit.MINUTES.toNanos(4);\n    23\t        }\n    24\t        // 其余的在1分钟后过期\n    25\t        return TimeUnit.MINUTES.toNanos(1);\n    26\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/UserCacheServiceImpl.java\n...\n   311\t    @Override\n   312\t    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {\n   313\t        Long userId = getUserIdFormPrincipal(oAuth2Authentication);\n   314\t        if (ObjectUtil.isNotNull(userId)) {\n   315\t            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   316\t            if (userAccountDTO == null){\n   317\t                userAccountDTO = getUserAccountByUserIdFromDb(userId);\n   318\t            }\n   319\t            if (ObjectUtil.isNotNull(userAccountDTO)) {\n   320\t                putUserToCache(tokenId, userAccountDTO);\n   321\t            }\n   322\t            return userAccountDTO;\n   323\t        }\n   324\t        String loginName = getLoginNameFormDetail(oAuth2Authentication);\n   325\t        if (StringUtils.isBlank(loginName)){\n   326\t            return null;\n   327\t        }\n...\n   486\t\n   487\t    @Override\n   488\t    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {\n   489\t        Long userId = getOriginalUserIdByTokenId(tokenId);\n   490\t        if (ObjectUtil.isNull(userId)) {\n   491\t            log.warn(\&quot;按tokenId[{}]获取用户主键失败,获取为空\&quot;, tokenId);\n   492\t            return;\n   493\t        }\n   494\t        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);\n   495\t        if (userAccountDTO == null) {\n   496\t            log.warn(\&quot;按用户主键[{}]获取用户信息失败,获取为空\&quot;, userId);\n   497\t            return;\n   498\t        }\n   499\t        //移除登录账号与用户主键关联信息\n   500\t        removeLoginNameCache(userAccountDTO.getUserAccountName());\n   501\t        //移除用户信息\n   502\t        removeUserCache(userId);\n   503\t        //移除token用户信息\n   504\t        removeUserTokenCache(tokenId, userId);\n   505\t         // 移除用户上次登录信息\n   506\t        removeUserLoginInfo(tokenId);\n   507\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-security/src/main/java/cn/hy/auth/custom/security/oauth2/CacheClearController.java\n...\n    16\t\n    17\t/**\n    18\t * 类描述：清理缓存\n    19\t *\n    20\t * <AUTHOR> by fuxinrong\n    21\t * @date 2022/6/8 15:24\n    22\t **/\n    23\t@RestController\n    24\t@RequestMapping(\&quot;/cache/cache/clear\&quot;)\n    25\t@Slf4j\n    26\tpublic class CacheClearController {\n    27\t    private final CacheManager cacheManager;\n    28\t\n    29\t    public CacheClearController( @Qualifier(\&quot;hyAuthAutoCacheManager\&quot;)CacheManager cacheManager) {\n    30\t        this.cacheManager = cacheManager;\n    31\t    }\n    32\t\n    33\t\n    34\t    @GetMapping(\&quot;/AppInvolvedTable\&quot;)\n    35\t    public String clearCache(String lessCode,String appCode){\n    36\t        StringBuilder stringBuilder = new StringBuilder(\&quot;hy_auth_auth_app_involved_\&quot;);\n    37\t        if (StringUtils.isNotBlank(lessCode)){\n    38\t            stringBuilder.append(lessCode);\n    39\t            if (StringUtils.isNotBlank(appCode)){\n    40\t                stringBuilder.append(\&quot;_\&quot;).append(appCode);\n    41\t            }\n    42\t        }\n    43\t        clearCache(stringBuilder.toString());\n    44\t        return \&quot;success\&quot;;\n    45\t    }\n...\nPath: hy-auth-custom-business/hy-auth-custom-user/src/main/java/cn/hy/auth/custom/user/cache/service/impl/ForgetPasswordMapCacheManage.java\n...\n    10\t\n    11\tpublic class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {\n    12\t\n    13\t    protected final Cache&lt;String, Object&gt; cache = Caffeine.newBuilder()\n    14\t            .expireAfter(new CacheExpiryFunction())\n    15\t            .build();\n    16\t\n    17\t\n    18\t    @Override\n    19\t    public void put(String key, Object value) {\n    20\t        cache.put(key, value);\n    21\t    }\n    22\t\n    23\t    @Override\n    24\t    public void put(String key, Object value, Date expireDate) {\n    25\t        cache.put(key, value);\n    26\t    }\n    27\t\n    28\t    @Override\n    29\t    public void put(String key, Object value, long timeout, TimeUnit unit) {\n    30\t        cache.put(key, value);\n    31\t    }\n    32\t\n    33\t    @Override\n    34\t    public Object get(String key) {\n    35\t        return cache.getIfPresent(key);\n    36\t    }\n    37\t\n    38\t    @Override\n    39\t    public boolean invalidate(String key) {\n    40\t        cache.invalidate(key);\n    41\t        return true;\n    42\t    }\n    43\t}\n...\nPath: hy-auth-custom-business-starter/src/main/java/cn/hy/auth/custom/business/starter/CacheAutoConfiguration.java\n...\n    56\t\n    57\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n    58\t    @ConditionalOnProperty(\n    59\t            prefix = \&quot;auth.cache\&quot;,\n    60\t            name = \&quot;type\&quot;,\n    61\t            havingValue = \&quot;caffeine\&quot;,\n    62\t            matchIfMissing = true\n    63\t    )\n    64\t    public CacheManager cacheManagerWithCaffeine() {\n    65\t        log.debug(\&quot;Caffeine cache 配置\&quot;);\n    66\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    67\t        Caffeine caffeine = Caffeine.newBuilder()\n    68\t                //cache的初始容量值\n    69\t                .initialCapacity(100)\n    70\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    71\t                .maximumSize(maximumSize)\n    72\t                .expireAfterWrite(expireTime, TimeUnit.SECONDS);\n    73\t        cacheManager.setCaffeine(caffeine);\n    74\t        //不允许设置空值\n    75\t        cacheManager.setAllowNullValues(false);\n    76\t        return cacheManager;\n    77\t    }\n...\n    90\t    public CacheManager systemConfigCacheManagerCaffeine() {\n    91\t        log.debug(\&quot;systemConfig Caffeine cache 配置\&quot;);\n    92\t        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();\n    93\t        Caffeine caffeine = Caffeine.newBuilder()\n    94\t                //cache的初始容量值\n    95\t                .initialCapacity(100)\n    96\t                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，\n    97\t                .maximumSize(systemConfigMaximumSize)\n    98\t                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);\n    99\t        cacheManager.setCaffeine(caffeine);\n   100\t        //不允许设置空值\n   101\t        cacheManager.setAllowNullValues(false);\n   102\t        return cacheManager;\n   103\t    }\n   104\t\n   105\t    @Bean(\&quot;hyAuthAutoCacheManager\&quot;)\n   106\t    @ConditionalOnProperty(\n   107\t            prefix = \&quot;auth.cache\&quot;,\n   108\t            name = \&quot;type\&quot;,\n   109\t            havingValue = \&quot;redis\&quot;\n   110\t    )\n   111\t    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {\n   112\t        log.debug(\&quot;redis cache 配置\&quot;);\n   113\t\n   114\t        //初始化一个RedisCacheWriter\n   115\t        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);\n   116\t        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()\n   117\t                //不允许存入空值\n   118\t                .disableCachingNullValues()\n   119\t                .computePrefixWith(compute -&gt; CachePrefixUtil.get() + compute + \&quot;::\&quot;)\n   120\t                //设置默认超过期时间是60000秒\n   121\t                .entryTtl(Duration.ofSeconds(expireTime));\n   122\t\n   123\t        //初始化RedisCacheManager\n   124\t        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);\n   125\t    }\n   126\t\n   127\t    @Bean(\&quot;hyAuthSystemConfigCacheManager\&quot;)\n   128\t    @ConditionalOnProperty(\n   129\t            prefix = \&quot;auth.system_config.cache\&quot;,\n   130\t            name = \&quot;type\&quot;,\n   131\t            havingValue = \&quot;redis\&quot;\n   132\t    )\n...\nPath: hy-auth-center-boot/src/main/java/cn/hy/auth/boot/listener/SaasAppKafkaListener.java\n...\n    36\t    public void processAsyncTask(ConsumerRecord&lt;?, ?&gt; record, Acknowledgment ack) {\n    37\t        Optional&lt;Object&gt; message = Optional.ofNullable(record.value());\n    38\t        try {\n    39\t            if (message.isPresent()) {\n    40\t                String msg = (String)message.get();\n    41\t                log.debug(\&quot;消费了： Topic:{},Message:{}\&quot;,record.topic(),msg);\n    42\t                EventMsg eventMsg = JSON.parseObject(msg, EventMsg.class);\n    43\t                try {\n    44\t                    // 清除各种缓存\n    45\t                    metaDataCacheService.cacheClear(eventMsg.getLessCode(), eventMsg.getAppCode());\n    46\t                    if (\&quot;appmanage\&quot;.equals(eventMsg.getAppCode())){\n    47\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode());\n    48\t                    }else {\n    49\t                        hyClientDetailServiceProvider.clearCache(eventMsg.getLessCode(), eventMsg.getAppCode());\n    50\t                    }\n...\nPath: hy-auth-center-boot/src/main/resources/config/application-dev.yml\n...\n   240\t        - /log/cache/clear\n   241\t        - /*/cache/clear/**\n   242\t        - /license/*\n   243\t        - /verifyCode/sl...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;7914bae3-1efc-4cd3-add5-237ca73a2401&quot;}},&quot;agentExecutionMode&quot;:&quot;auto&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;}" />
      </map>
    </option>
  </component>
</project>
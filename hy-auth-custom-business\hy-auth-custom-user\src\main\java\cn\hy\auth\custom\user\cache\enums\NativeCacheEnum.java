package cn.hy.auth.custom.user.cache.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.data.redis.cache.RedisCache;

/**
 * 类描述: 缓存类型枚举
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@Getter
@AllArgsConstructor
public enum NativeCacheEnum {

    /**
     * caffeine缓存
     */
    CAFFEINE(CaffeineCache.class, "caffeine"),
    /**
     * redis缓存
     */
    REDIS(RedisCache.class, "redis");

    /**
     * class类
     */
    private Class clz;

    /**
     * 缓存类型
     */
    private String type;

    /**
     * 按获取类型返回对应的缓存标识
     *
     * @param clz 缓存类，都应该是org.springframework.cache.Cache的实现类型
     * @return 返回对应的枚举
     */
    public static NativeCacheEnum classOf(Object clz) {
        NativeCacheEnum[] enums = NativeCacheEnum.values();
        for (NativeCacheEnum e : enums) {
            if (e.getClz().isInstance(clz)) {
                return e;
            }
        }
        return null;
    }
}

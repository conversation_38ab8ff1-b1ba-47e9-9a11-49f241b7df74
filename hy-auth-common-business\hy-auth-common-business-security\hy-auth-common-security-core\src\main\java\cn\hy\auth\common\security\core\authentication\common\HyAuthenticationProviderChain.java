package cn.hy.auth.common.security.core.authentication.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 对外提供所有AuthenticationProvider实现类
 * 同时编排provider执行顺序
 *
 * <AUTHOR>
 * @date 2019-08-13 1:23
 */
@Component
@Slf4j
public class HyAuthenticationProviderChain implements ApplicationContextAware {

    private final List<AbstractAuthenticationProvider> authenticationProviders = new ArrayList<>();

    private ApplicationContext applicationContext;

    @SuppressWarnings("NullableProblems")
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 从spring容器提取出来所有自定义的provider，并按order升序排序
     * 也就是说，order越小，越先执行
     *
     * @return 排序后的自定义provider
     */
    public synchronized List<AuthenticationProvider> getAuthenticationProviders() {
        this.applicationContext.getBeansOfType(AbstractAuthenticationProvider.class).forEach((k, v) ->
                {
                    if (!this.authenticationProviders.contains(v)) {
                        this.authenticationProviders.add(v);
                    }
                }
        );
        log.debug("排序前:{}", authenticationProviders);
        //对多个provider执行排序
        Collections.sort(authenticationProviders);
        log.debug("排序后:{}", authenticationProviders);
        //重新转换为AuthenticationProvider
        return this.authenticationProviders.stream().map(p -> (AuthenticationProvider) p).collect(Collectors.toList());
    }

}

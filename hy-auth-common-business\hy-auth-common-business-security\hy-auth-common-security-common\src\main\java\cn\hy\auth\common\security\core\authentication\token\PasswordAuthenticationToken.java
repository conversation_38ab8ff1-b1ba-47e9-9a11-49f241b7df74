package cn.hy.auth.common.security.core.authentication.token;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/7 17:16
 */
public class PasswordAuthenticationToken extends ReAuthenticationToken {

    /**
     * 密码
     */
    private final Object credentials;

    public PasswordAuthenticationToken(Object principal, Object credentials, Map<String, Object> additional) {
        // 因为刚开始并没有认证，因此用户没有任何权限，并且设置没有认证的信息（setAuthenticated(false)）
        super(null);
        this.principal = principal;
        this.additional = additional;
        this.credentials = credentials;
        this.setAuthenticated(false);
    }

    @Override
    public ReAuthenticationType getType() {
        return ReAuthenticationType.PASSWORD;
    }

    @Override
    public ReAuthenticationToken createSuccessAuthentication(UserDetails userDetails, Authentication authentication) {
        PasswordAuthenticationToken result = new PasswordAuthenticationToken(userDetails, authentication.getCredentials(), additional);
        result.setDetails(authentication.getDetails());
        super.setAuthenticated(true);
        return result;
    }

    @Override
    public Object getCredentials() {
        return credentials;
    }
}

package cn.hy.auth.custom.route.controller;

import cn.hy.auth.custom.route.service.OauthRequestRoutingService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 15:44
 **/
@RestController
@RequestMapping("/route/cache/clear")
public class OauthRequestRoutingController {
    private OauthRequestRoutingService oauthRequestRoutingService;

    public OauthRequestRoutingController(OauthRequestRoutingService oauthRequestRoutingService) {
        this.oauthRequestRoutingService = oauthRequestRoutingService;
    }

    @GetMapping("")
    public String clearCache(){
        return oauthRequestRoutingService.clearCache();
    }
}

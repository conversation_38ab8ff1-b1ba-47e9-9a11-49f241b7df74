package cn.hy.auth.custom.common.utils;

import cn.hy.id.IdWorker;
import cn.hy.metadata.engine.common.utils.SpringUtil;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 类描述：uuid生成工具类
 *
 * <AUTHOR> by <PERSON><PERSON><PERSON><PERSON>
 * @date 2022/11/23 17:37
 **/
@Component
public class UUIDRandomUtils {

    /***
     *  获取增长式的32位uuid
     * @return 32位uuid
     */
    public static String getIncreaseUuid() {
        return System.currentTimeMillis() + getUuid(19);
    }

    /**
     * 获取指定长度的UUID
     *
     * @param length 长度
     * @return uuid
     */
    public static String getUuid(int length) {
        return UUID.randomUUID().toString().replace("-", "").substring(0, length);
    }

    /***
     *  获取雪花ID
     * @return 20位uuid
     */
    public static Long getSnowUuid() {
        return SpringUtil.getBean(IdWorker.class).nextId();
    }

    private UUIDRandomUtils() {
    }
}

package cn.hy.auth.common.security.core.authentication.reauthentication;

import cn.hy.auth.common.security.core.authentication.token.ReAuthenticationToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * <AUTHOR>
 * @date 2024/6/11 10:03
 */
@RequiredArgsConstructor
@Slf4j
public class ReAuthenticationProvider implements AuthenticationProvider {

    private final UserDetailsService myUserDetailsService;

    protected boolean hideUserNotFoundExceptions = false;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (!(authentication instanceof ReAuthenticationToken)) {
            throw new AuthenticationServiceException("Unsupported authentication type");
        }

        UserDetails userDetails;
        try {
            String username = (String) authentication.getPrincipal();
            userDetails = myUserDetailsService.loadUserByUsername(username);
        } catch (UsernameNotFoundException ex) {
            if (hideUserNotFoundExceptions) {
                throw new BadCredentialsException("Bad credentials");
            } else {
                throw ex;
            }
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }

        // 认证成功
        return ((ReAuthenticationToken) authentication).createSuccessAuthentication(userDetails, authentication);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return ReAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

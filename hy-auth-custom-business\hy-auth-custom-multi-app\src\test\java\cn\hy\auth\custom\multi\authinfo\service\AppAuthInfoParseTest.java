package cn.hy.auth.custom.multi.authinfo.service;

import cn.hy.auth.custom.common.domain.authinfo.AppAuthLoginFieldDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.auth.custom.common.enums.LoginInitPwdActionEnum;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.Test;

import java.util.HashMap;

/**
 * 认证规则解析测试
 *
 * <AUTHOR>
 * @date 2020-12-08 10:20
 **/
public class AppAuthInfoParseTest {

    /**
     * 验证json字符串的解析，避免key值不符导致解析不到相关值
     */
    @Test
    public void test() {
        AppAuthStrategyDTO authInfo = JSONObject.parseObject(getJson(), AppAuthStrategyDTO.class);

        Assertions.assertThat(authInfo).isNotNull();
        Assertions.assertThat(StringUtils.isNotBlank(authInfo.getLesseeAccessName())).isTrue();
        Assertions.assertThat(StringUtils.isNotBlank(authInfo.getAppAccessName())).isTrue();
        Assertions.assertThat(authInfo.getLoginRule()).isNotNull();
        Assertions.assertThat(authInfo.getLogoutRule()).isNotNull();
        Assertions.assertThat(authInfo.getUserAccountMapping()).isNotNull();
        Assertions.assertThat(authInfo.getUserInfoMapping()).isNotNull();

        Assertions.assertThat(authInfo.getLoginRule().getLoginSupportType()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getLoginField()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getCompetitionPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getIpAllowListPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getMacAllowListPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getMacAllowListPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getAccountStatusPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getPwdStatusPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getAccountLockPolicy()).isNotNull();
        Assertions.assertThat(authInfo.getLoginRule().getAccountValidityDatePolicy()).isNotNull();

        //---------------内容合法性校验------------------
        //登录支持类型
        Assertions.assertThat(authInfo.getLoginRule().getLoginSupportType().contains(LoginTypeEnum.USERNAME_PASSWORD))
                .isTrue();
        Assertions.assertThat(authInfo.getLoginRule().getLoginSupportType().contains(LoginTypeEnum.SMS_CODE)).isTrue();
        Assertions.assertThat(authInfo.getLoginRule().getLoginSupportType().contains(LoginTypeEnum.OAUTH2_PASSWORD))
                .isTrue();
        Assertions.assertThat(authInfo.getLoginRule().getLoginSupportType().contains(LoginTypeEnum.OAUTH2_CLIENT))
                .isTrue();
        //登录字段
        Assertions.assertThat((int) authInfo.getLoginRule()
                .getLoginField()
                .stream()
                .map(AppAuthLoginFieldDTO::getFiled)
                .filter(StringUtils::isNotBlank)
                .count()).isEqualTo(2);
        //登录竞争策略(未启用,既不阻止也不踢人)
        Assertions.assertThat(authInfo.getLoginRule().getCompetitionPolicy().isAllClientBlockLogin()).isFalse();
        //IP限制策略
        Assertions.assertThat(authInfo.getLoginRule().getIpAllowListPolicy().isEnable()).isFalse();
        //Mac限制策略
        Assertions.assertThat(authInfo.getLoginRule().getMacAllowListPolicy().isEnable()).isFalse();
        //账号状态(默认为启用)
        Assertions.assertThat(authInfo.getLoginRule().getAccountStatusPolicy().parseTheValue(new HashMap<>())).isTrue();
        //密码状态
        Assertions.assertThat(authInfo.getLoginRule().getPwdStatusPolicy().getInitPwdAction().equals(
                LoginInitPwdActionEnum.IGNORE)).isTrue();
        //密码有效期(未启用，返回null)
        Assertions.assertThat(authInfo.getLoginRule().getPwdExpirePolicy().parseLastChangeTime(new HashMap<>())).isNull();
        //账号锁定
        Assertions.assertThat(authInfo.getLoginRule().getAccountLockPolicy().isEnable()).isFalse();
        //账号有效期
        Assertions.assertThat(authInfo.getLoginRule().getAccountValidityDatePolicy().parseStartTime(new HashMap<>())).isNull();
        Assertions.assertThat(authInfo.getLoginRule().getAccountValidityDatePolicy().parseEndTime(new HashMap<>())).isNull();
    }

    private String getJson() {
        return "{\"lesseeAccessName\":\"hy\",\"appAccessName\":\"1\",\"loginRule\":{\"loginSupportType\":[\"USERNAME_PASSWORD\",\"SMS_CODE\",\"OAUTH2_PASSWORD\",\"OAUTH2_CLIENT\"],\"loginField\":[{\"filed\":\"user_account_name\",\"matchRegExp\":\"^[\\\\u4E00-\\\\u9FA5a-zA-Z0-9]{1,32}$\",\"sort\":1},{\"filed\":\"mobile\",\"matchRegExp\":\"^[1](([3|5|8][\\\\\\\\d])|([4][4,5,6,7,8,9])|([6][2,5,6,7])|([7][^9])|([9][1,8,9]))[\\\\\\\\d]{8}$\",\"sort\":2}],\"competitionPolicy\":{\"enable\":false,\"loginClientTypeNum\":********,\"sameClientTypePolicy\":\"blockLogin\",\"remark\":\"loginClientTypeNum支持同时在线的客户端类型数量，sameClientTypePolicy同端登录的登录竞争处理策略\"},\"ipAllowListPolicy\":{\"enable\":false,\"ip\":\"ip\",\"remark\":\"指定用户账号表的某个字段，多个ip地址用逗号隔开。默认是不限制ip。如果启用该策略的话，会通过元数据引擎接口往用户表里增加ip字段\"},\"macAllowListPolicy\":{\"enable\":false,\"mac\":\"mac\",\"remark\":\"指定用户账号表的某个字段，多个mac地址用逗号隔开。默认是不限制mac。如果启用该策略的话，会通过元数据引擎接口往用户表里增加mac字段\"},\"accountStatusPolicy\":{\"enable\":false,\"accountStatus\":\"account_status\",\"remark\":\"指定用户账号表的某个字段，account_status,0:禁用，1：启用。默认是1。如果启用该策略的话，会通过元数据引擎接口往用户表里增加account_status字段\"},\"pwdStatusPolicy\":{\"enable\":false,\"pwdStatus\":\"pwd_status\",\"initPwdAction\":\"初始密码处理方式，onlyRemind：仅提醒，mustBeModified：强制修改,不能登录成功，ignore：忽略\",\"remark\":\"指定用户账号表的某个字段，pwd_status,1重置初始状态, 其他值为正常状态。默认是1。如果启用该策略的话，会通过元数据引擎接口往用户表里增加pwd_status字段\"},\"pwdExpirePolicy\":{\"enable\":false,\"pwdLastChangeTime\":\"pwd_last_change_time\",\"pwdLiftTime\":30,\"expireRemindThreshold\":7,\"remark\":\"指定用户账号表的某个字段，pwd_last_change_time。默认是null。如果启用该策略的话，会通过元数据引擎接口往用户表里增加pwd_last_change_time字段\"},\"accountLockPolicy\":{\"enable\":false,\"loginErrorThreshold\":10,\"lockType\":\"sameDay\",\"lockMinute\":50,\"remark\":\"从指定的表里查询是锁定了。登录时判断是否锁定，同时判断是否能解锁（锁定有效期）。1、账号锁定表：id，uid，lock_start_time,lock_end_time,ip,mac,client_type.2、登录错误记录表usr_series_login_failure_info：id，uid，logintime，login_failure_count. 如果启用该策略的话，会通过元数据引擎接口往插入表\"},\"accountValidityDatePolicy\":{\"enable\":false,\"startEffectiveTime\":\"start_effective_time\",\"endEffectiveTime\":\"end_effective_time\",\"remark\":\"指定用户账号表的start_effective_time和end_effective_time字段，如果启用该策略的话，会通过元数据引擎接口往用户表里增加上面2个字段\"}},\"logoutRule\":{\"noOperationOfflineRule\":{\"enable\":false,\"noOperationOfflineThreshold\":108000}},\"userAccountMapping\":{\"tableName\":\"user_account\",\"uid\":\"id\",\"password\":\"password\",\"username\":\"user_account_name\",\"mobile\":\"mobile\"},\"userInfoMapping\":{\"sqlView\":\"select * from user_account\",\"uidAlias\":\"id\",\"remark\":\"通过视图来查询和用户账号相关联的用户信息\"}}";
    }
}

package cn.hy.auth.custom.security.third.common;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.SocialUsersConnectionService;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import cn.hy.auth.custom.user.account.dao.HrMemberDao;
import cn.hy.auth.custom.user.account.domain.ThirdUserBindDTO;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.Optional;


/**
 * 用户应用信息获取、删除、新增
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class SocialUsersConnectionImpl implements SocialUsersConnectionService {

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private HrMemberDao hrMemberDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TableRelocateUtil tableRelocateUtil;

    /**
     * 查询用户账号名
     *
     * @param lesseeCode     租户编码
     * @param appCode        应用编码
     * @param providerId     第三方服务商ID
     * @param providerUserId 第三方服务商用户ID
     * @return
     */
    @Override
    public List<String> findUserAccountName(String lesseeCode, String appCode, String providerId, String providerUserId) {
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append(" select user_account_name from " + getTableName(lesseeCode, appCode, "third_user_relation ") + " where provider_id=?" +
                " and  provider_user_id=? ");
        return jdbcTemplate.queryForList(sqlBuilder.toString(), String.class, providerId, providerUserId);
    }

    /**
     * 解除绑定信息
     *
     * @param lesseeCode     租户编码
     * @param appCode        应用编码
     * @param providerId     第三方服务商ID
     * @param providerUserId 第三方服务商用户ID
     * @return
     */
    @Override
    public void deleteBlankUserAccountName(String lesseeCode, String appCode, String providerId, String providerUserId) {
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append(" delete from " + getTableName(lesseeCode, appCode, "third_user_relation") + " where provider_id='" + providerId + "'" +
                " and  provider_user_id='" + providerUserId + "' ");
        jdbcTemplate.execute(sqlBuilder.toString());
    }

    /**
     * 绑定第三方用户信息
     *
     * @param lesseeCode       租户编码
     * @param appCode          应用编码
     * @param providerUserInfo 第三方服务商用户信息
     * @return 主键
     */
    @Override
    public long insertProviderUserInfo(String lesseeCode, String appCode, ProviderUserInfo providerUserInfo) {
        Long id = UUIDRandomUtils.getSnowUuid();
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append("   replace into " + getTableName(lesseeCode, appCode, "third_user_info "));
        sqlBuilder.append(" ( id,provider_id,provider_user_id,user_account_name,user_info," +
                "             create_user_id,create_time,last_update_user_id,last_update_time," +
                "             data_version ) values ");
        sqlBuilder.append(" (" + id + ",'" + providerUserInfo.getProviderId() + "','" + providerUserInfo.getProviderUserid() + "'," +
                "'" + providerUserInfo.getUserDisplayName() + "','" + providerUserInfo.getUserInfoJson() + "'," +
                " '1',now(),'1',now(),1 ) ");
        jdbcTemplate.update(sqlBuilder.toString());
        return id;
    }

    @Override
    public Map<String, Object> getThirdUserFromBaseApp(String userId, ProviderType providerType) {
        // TODO 需要判断处理是微信的还是钉钉的才行，否则可能会出错
        Map<String, Object> map = hrMemberDao.queryForMap(userId);
        return map;
    }


    @Override
    public Boolean bindThirdUser(String providerId, String userId, String userAccount) {
        ThirdUserBindDTO thirdUser = new ThirdUserBindDTO();
        thirdUser.setThirdUserId(userId);
        thirdUser.setProviderId(providerId);
        thirdUser.setProviderUserId(userId);
        thirdUser.setUserAccountName(userAccount);
        userAccountService.bingThirdAccount(thirdUser);
        return Boolean.TRUE;
    }

    @Override
    public void updateHrMember(@NonNull String providerId, @NonNull String userAccountName, @NonNull String providerUserid) {
        try {
            List<Map<String, Object>> hrCodeByAccount = hrMemberDao.getHrCodeByAccount(userAccountName);
            if (CollectionUtils.isEmpty(hrCodeByAccount)) {
                return;
            }
            String hrCode = (String) (hrCodeByAccount.get(0).get("hr_member_code"));
            if (providerId.equals(ProviderType.DING_TALK.getCode())) {
                hrMemberDao.updateByHrCode(hrCode, providerUserid, null);
            } else if (providerId.equals(ProviderType.WECHAT.getCode())) {
                hrMemberDao.updateByHrCode(hrCode, null, providerUserid);
            }
        } catch (Exception e) {
            log.error("回填人员表数据失败:{}", e);
        }
    }

    /**
     * 修改表名
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @param tableCode  表编码
     * @return
     */
    @Override
    public String getTableName(String lesseeCode, String appCode, String tableCode) {
        Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(tableCode);
        if (tableMeta.isPresent()){
            TableMData tableMData = tableMeta.get();
            lesseeCode = tableMData.getLessCode();
            appCode = tableMData.getAppCode();
        }
        StringBuilder builder = new StringBuilder(128);
        return builder.append(lesseeCode).append("_").append(appCode).append("_").append(tableCode).toString();
    }
}

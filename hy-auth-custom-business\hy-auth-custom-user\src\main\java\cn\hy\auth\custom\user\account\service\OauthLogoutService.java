package cn.hy.auth.custom.user.account.service;

/**
 * 类描述: 退出登录
 *
 * <AUTHOR>
 * @date ：创建于 2020/10/29
 */
public interface OauthLogoutService {

    /**
     * 删除token
     *
     * @param tokenId tokenId
     * @
     */
    void revokeToken(String tokenId);

    /**
     *  通过用户名踢人下线，移除token信息
     * @param lessCode 租户编码
     * @param userName 用户账号名
     * @param kickOutEnum .
     */
    void kickOut(String lessCode,String userName,KickOutEnum kickOutEnum);


}

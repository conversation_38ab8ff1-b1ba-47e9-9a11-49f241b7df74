package cn.hy.auth.boot.login;

import cn.hy.auth.custom.user.utils.PasswordAesUtil;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 * @date 2020-12-04 16:13
 **/
public class MockUserData {

    private JdbcTemplate jdbcTemplate;

    public MockUserData(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void clear() {
        jdbcTemplate.execute("drop table letest_apptest_auth_config");
        jdbcTemplate.execute("drop table letest_apptest_user_account");
        jdbcTemplate.execute("drop table letest_apptest_user_login_info");
    }

    public void initUserTable() {
        String sqlConfig = "CREATE TABLE IF NOT EXISTS letest_apptest_auth_config ( config_content LONGTEXT NOT NULL );";

        String sqlAccount = "CREATE TABLE IF NOT EXISTS letest_apptest_user_account (\n" + "  id bigint(18) DEFAULT NULL,\n" +
                "  user_account_name varchar(255) DEFAULT NULL,\n" + "  login_name varchar(255) DEFAULT NULL,\n" +
                "  password varchar(255) DEFAULT NULL,\n" + "  sex varchar(255) DEFAULT NULL,\n" +
                "  mobile varchar(255) DEFAULT NULL\n" + ") ";
        String sqlLoignLog = "CREATE TABLE IF NOT EXISTS letest_apptest_user_login_info (\n" +
                "  error_message text  COMMENT '错误信息',\n" +
                "  create_user_id decimal(20,0) NOT NULL COMMENT '创建人主键',\n" +
                "  create_time datetime NOT NULL COMMENT '创建时间',\n" +
                "  create_user_name varchar(255)  DEFAULT NULL COMMENT '创建人名称',\n" +
                "  data_version varchar(32)  NOT NULL COMMENT '数据版本',\n" +
                "  ip varchar(128)  DEFAULT NULL COMMENT 'ip地址',\n" +
                "  client_type int(8) DEFAULT NULL COMMENT '客户端类型',\n" +
                "  last_update_user_id decimal(20,0) NOT NULL COMMENT '最后修改人主键',\n" +
                "  type int(6) DEFAULT NULL COMMENT '类型',\n" +
                "  client_id varchar(32)  NOT NULL COMMENT '客户端id',\n" + "  mac text  COMMENT 'mac',\n" +
                "  last_update_time datetime NOT NULL COMMENT '最后修改时间',\n" +
                "  login_count decimal(20,0) DEFAULT NULL COMMENT '登录次数',\n" +
                "  sequence int(8) NOT NULL COMMENT '排序序号',\n" +
                "  user_id varchar(32)  NOT NULL COMMENT '用户主键',\n" +
                "  user_account_name varchar(32)  DEFAULT NULL COMMENT '用户账号名',\n" +
                "  error_code varchar(32)  DEFAULT NULL COMMENT '错误码',\n" +
                "  id decimal(20,0) NOT NULL COMMENT '主键',\n" +
                "  last_update_user_name varchar(255)  DEFAULT NULL COMMENT '最后修改人名称',\n" +
                "  login_type varchar(32)  DEFAULT NULL COMMENT '登录方式',\n" + "  PRIMARY KEY (id),\n" +
                "  UNIQUE KEY idx_nCpAMGlh (sequence) USING BTREE\n" + ") ;";

        jdbcTemplate.execute(sqlConfig);
        jdbcTemplate.execute(sqlAccount);
        jdbcTemplate.execute(sqlLoignLog);
    }

    public void initUserData() {
        String sqlConfig = "INSERT INTO letest_apptest_auth_config (config_content) VALUES ('{\"lesseeAccessName\":\"hy\",\"appAccessName\":\"1\",\"loginRule\":{\"loginSupportType\":[\"USERNAME_PASSWORD\",\"SMS_CODE\",\"OAUTH2_PASSWORD\",\"OAUTH2_CLIENT\"],\"loginField\":[{\"filed\":\"user_account_name\",\"matchRegExp\":\"^[\\\\u4E00-\\\\u9FA5a-zA-Z0-9]{1,32}$\",\"sort\":1},{\"filed\":\"mobile\",\"matchRegExp\":\"0?(13|14|15|17|18|19)[0-9]{9}\",\"sort\":2}],\"competitionPolicy\":{\"enable\":false,\"loginClientTypeNum\":********,\"sameClientTypePolicy\":\"blockLogin\",\"remark\":\"loginClientTypeNum支持同时在线的客户端类型数量，sameClientTypePolicy同端登录的登录竞争处理策略\"},\"ipAllowListPolicy\":{\"enable\":false,\"ip\":\"ip\",\"remark\":\"指定用户账号表的某个字段，多个ip地址用逗号隔开。默认是不限制ip。如果启用该策略的话，会通过元数据引擎接口往用户表里增加ip字段\"},\"macAllowListPolicy\":{\"enable\":false,\"mac\":\"mac\",\"remark\":\"指定用户账号表的某个字段，多个mac地址用逗号隔开。默认是不限制mac。如果启用该策略的话，会通过元数据引擎接口往用户表里增加mac字段\"},\"accountStatusPolicy\":{\"enable\":false,\"accountStatus\":\"account_status\",\"remark\":\"指定用户账号表的某个字段，account_status,0:禁用，1：启用。默认是1。如果启用该策略的话，会通过元数据引擎接口往用户表里增加account_status字段\"},\"pwdStatusPolicy\":{\"enable\":false,\"pwdStatus\":\"pwd_status\",\"initPwdAction\":\"初始密码处理方式，onlyRemind：仅提醒，mustBeModified：强制修改,不能登录成功，ignore：忽略\",\"remark\":\"指定用户账号表的某个字段，pwd_status,1重置初始状态, 其他值为正常状态。默认是1。如果启用该策略的话，会通过元数据引擎接口往用户表里增加pwd_status字段\"},\"pwdExpirePolicy\":{\"enable\":false,\"pwdLastChangeTime\":\"pwd_last_change_time\",\"pwdLiftTime\":30,\"expireRemindThreshold\":7,\"remark\":\"指定用户账号表的某个字段，pwd_last_change_time。默认是null。如果启用该策略的话，会通过元数据引擎接口往用户表里增加pwd_last_change_time字段\"},\"accountLockPolicy\":{\"enable\":false,\"loginErrorThreshold\":10,\"lockType\":\"sameDay\",\"lockMinute\":50,\"remark\":\"从指定的表里查询是锁定了。登录时判断是否锁定，同时判断是否能解锁（锁定有效期）。1、账号锁定表：id，uid，lock_start_time,lock_end_time,ip,mac,client_type.2、登录错误记录表usr_series_login_failure_info：id，uid，logintime，login_failure_count. 如果启用该策略的话，会通过元数据引擎接口往插入表\"},\"accountValidityDatePolicy\":{\"enable\":false,\"startEffectiveTime\":\"start_effective_time\",\"endEffectiveTime\":\"end_effective_time\",\"remark\":\"指定用户账号表的start_effective_time和end_effective_time字段，如果启用该策略的话，会通过元数据引擎接口往用户表里增加上面2个字段\"}},\"logoutRule\":{\"noOperationOfflineRule\":{\"enable\":false,\"noOperationOfflineThreshold\":108000}},\"userAccountMapping\":{\"tableName\":\"user_account\",\"uid\":\"id\",\"password\":\"password\",\"username\":\"user_account_name\",\"mobile\":\"mobile\"},\"userInfoMapping\":{\"sqlView\":\"select * from user_account\",\"uidAlias\":\"id\",\"remark\":\"通过视图来查询和用户账号相关联的用户信息\"}}');";

        //明文密码
        String pwd1 = PasswordAesUtil.encryptPwd("123456");
        String sqlAccount1 = "INSERT INTO letest_apptest_user_account (id, user_account_name, login_name, password, sex, mobile) VALUES ('1', 'zhangsan', 'zhangsan', '" + pwd1 + "', '男', '***********');";

        String pwd2 = PasswordAesUtil.encryptPwd("1");
        String sqlAccount2 = "INSERT INTO letest_apptest_user_account (id, user_account_name, login_name, password, sex, mobile) VALUES ('2', 'lisi', 'lisi', '" + pwd2 + "', '男', '***********');";

        String pwd3 = PasswordAesUtil.encryptPwd("1");
        String sqlAccount3 = "INSERT INTO letest_apptest_user_account (id, user_account_name, login_name, password, sex, mobile) VALUES ('3', 'wangwu', 'wangwu', '" + pwd3 + "', '男', '***********');";

        jdbcTemplate.execute(sqlConfig);
        jdbcTemplate.execute(sqlAccount1);
        jdbcTemplate.execute(sqlAccount2);
        jdbcTemplate.execute(sqlAccount3);
    }

}

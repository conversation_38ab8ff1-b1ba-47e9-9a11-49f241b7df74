package cn.hy.auth.common.security.core.authentication.face;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 社交登录认证提供者
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Data
@Slf4j
public class HyFaceAuthenticationProvider implements AuthenticationProvider {

    protected boolean hideUserNotFoundExceptions = false;
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    private final UserDetailsService myUserDetailsService;

    public HyFaceAuthenticationProvider(UserDetailsService myUserDetailsService) {
        this.myUserDetailsService = myUserDetailsService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) {
        //最后的校验，直接返回结果了。这个authentication就是HySocialAuthenticationToken
        HyFaceAuthenticationToken authenticationToken = (HyFaceAuthenticationToken) authentication;
        String userName = (String) authenticationToken.getPrincipal();
        try {
            UserDetails loadedUser = myUserDetailsService.loadUserByUsername(userName);
            //这时候已经认证成功了
            HyFaceAuthenticationToken authenticationResult = new HyFaceAuthenticationToken(loadedUser,authenticationToken.getFacePic(),loadedUser.getAuthorities());
            authenticationResult.setDetails(authenticationToken.getDetails());
            return authenticationResult;
        } catch (UsernameNotFoundException ex) {
            if (hideUserNotFoundExceptions) {
                throw new BadCredentialsException(messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.badCredentials",
                        "Bad credentials"));
            } else {
                throw ex;
            }
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }

    }


    @Override
    public boolean supports(Class<?> authentication) {
        //该SmsCodeAuthenticationProvider智支持SmsCodeAuthenticationToken的token认证
        return HyFaceAuthenticationToken.class.isAssignableFrom(authentication);
    }

}

package cn.hy.auth.custom.common.enums;

/**
 * 客户端类型
 *
 * <AUTHOR>
 * @date 2020-12-14 12:08
 **/
public enum ClientTypeEnum {
    /**
     * WPF客户端登录
     */
    WPF(1),
    /**
     * 安卓移动端
     */
    PHONE_ANDROID(2),
    /**
     * 安卓移动端
     */
    PHONE_APPLE(3),
    /**
     * 网页客户端
     */
    WEB(4),
    /**
     * 其他终端登录
     */
    OTHER(5);

    private int code;

    public int getCode() {
        return code;
    }

    ClientTypeEnum(int code) {
        this.code = code;
    }

    public static ClientTypeEnum codeOf(Integer code) {
        ClientTypeEnum[] enums = ClientTypeEnum.values();
        for (ClientTypeEnum em : enums) {
            if (code.equals(em.getCode())) {
                return em;
            }
        }
        return null;
    }

    public static ClientTypeEnum codeOf(String code) {
        try {
            Integer intValue = Integer.parseInt(code);
            return codeOf(intValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }

}

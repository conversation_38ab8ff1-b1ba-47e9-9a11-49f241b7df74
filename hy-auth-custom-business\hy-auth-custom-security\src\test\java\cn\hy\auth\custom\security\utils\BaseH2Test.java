package cn.hy.auth.custom.security.utils;

import cn.hy.auth.custom.security.AppAuthCustomSecurityTest;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * 集成测试
 *
 * <AUTHOR>
 * @date 2020/6/16
 */
@ActiveProfiles("test")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = AppAuthCustomSecurityTest.class)
@Transactional // 数据库自动回滚
public abstract class BaseH2Test {

}

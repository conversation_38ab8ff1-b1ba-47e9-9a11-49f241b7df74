package cn.hy.auth.common.security.oauth2.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: CAIPENG
 * @create: 2019-09-05 09:55
 **/
@Slf4j
public class HttpContextUtil {
    private HttpContextUtil() {
    }

    public static HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (null != servletRequestAttributes) {
                return servletRequestAttributes.getRequest();
            }
        } catch (Exception var1) {
            log.error(var1.getMessage());
        }

        return null;
    }
}

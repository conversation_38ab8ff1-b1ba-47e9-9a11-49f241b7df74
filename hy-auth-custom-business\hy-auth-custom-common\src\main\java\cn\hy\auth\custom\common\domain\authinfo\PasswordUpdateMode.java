package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lizehai
 * @Date:2022/6/14
 **/
@Data
public class PasswordUpdateMode implements Serializable {
//"pwdUpdatePolicy": {
//            "enable": false,
//            "pwdLastChangeTimeFiled": "pwd_last_change_time",
//            "pwdUpdateMode": {
//                "mode": "app",
//                "app": {
//                    "pwdLiftTime": 30,
//                    "pwdLiftTimeUnit": "year/month/week/day",
//                    "expireRemindThreshold": 7
//                },
//                "user": {
//                    "pwdLiftTimeUnit": "year/month/week/day",
//                    "pwdLiftTimeField": "interval"
//                }
//            },
//            "remark": "密码强制更新策略,有应用级别和用户级别的2种模式"
//        }
    /**
     * 模式
     */
    private String mode;
    private App app;
    private User user;

    @Data
    public static class App implements Serializable {
        /**
         * 密码修改时间间隔
         */
        private Integer pwdLiftTime;
        /**
         * 时间间隔单位 year/month/week/day
         */
        private String pwdLiftTimeUnit;
        /**
         * 过期提醒阈值
         */
        private Integer expireRemindThreshold;

    }
    @Data
    public static class User implements Serializable {
        /**
         * 时间间隔单位 year/month/week/day
         */
        private String pwdLiftTimeUnit;

        /**
         * 字段映射用
         */
        private String pwdLiftTimeField;
    }
}

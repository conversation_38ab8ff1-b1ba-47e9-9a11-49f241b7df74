package cn.hy.auth.custom.common.params.service.impl;

import cn.hy.auth.custom.common.params.dao.ParamsConfigMapper;
import cn.hy.auth.custom.common.params.service.ParamsConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/3/13
 */
@Service
public class ParamsConfigServiceImpl implements ParamsConfigService {

    @Resource
    private ParamsConfigMapper mapper;

    /**
     * 获取配置参数的值
     * @param lesseeCode 租户
     * @param appCode 应用
     * @param key 配置键
     * @return 。
     */
    public String getConfigValue(String lesseeCode,String appCode,String key){
        String value = null;
        if (StringUtils.isAnyBlank(lesseeCode,appCode,key)){
            return value;
        }
        // bizgw
        value = mapper.getAddressByBizgw(lesseeCode, appCode, key);
        // app_config
        if (StringUtils.isBlank(value)) {
            value = mapper.getAddressByConfig(lesseeCode, appCode, key);
        }
        // 租户 appmanage
        if (StringUtils.isBlank(value)){
            value = mapper.getLesseeAddress(lesseeCode, key);
        }
        return value;
    }
}

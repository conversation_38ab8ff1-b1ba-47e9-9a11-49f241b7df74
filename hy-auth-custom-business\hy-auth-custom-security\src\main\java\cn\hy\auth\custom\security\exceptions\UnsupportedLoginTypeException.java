package cn.hy.auth.custom.security.exceptions;

import org.springframework.security.core.AuthenticationException;

/**
 * 已存在登录状态，阻止当前登录请求
 *
 * <AUTHOR>
 * @date 2020-11-27 11:23
 **/
public class UnsupportedLoginTypeException extends AuthenticationException {
    /**
     * Constructs an {@code AuthenticationException} with the specified message and root
     * cause.
     *
     * @param msg the detail message
     * @param t   the root cause
     */
    public UnsupportedLoginTypeException(String msg, Throwable t) {
        super(msg, t);
    }

    /**
     * Constructs an {@code AuthenticationException} with the specified message and no
     * root cause.
     *
     * @param msg the detail message
     */
    public UnsupportedLoginTypeException(String msg) {
        super(msg);
    }
}

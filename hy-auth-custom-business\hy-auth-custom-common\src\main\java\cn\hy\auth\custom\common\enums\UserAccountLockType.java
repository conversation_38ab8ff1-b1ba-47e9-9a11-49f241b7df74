package cn.hy.auth.custom.common.enums;


import cn.hy.auth.custom.common.utils.LocaleUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UserAccountLockType {

    LOGIN_ERROR_TIME("0", "您输入错误次数超过限制"),

    MOBILE_SMS("1", "忘记密码手机号找回方式错误次数过多"),

    SECURITY_QUESTION("2", "忘记密码密保找回方式错误次数过多"),

    MOBILE_LOGIN("3", "手机号登录验证码错误次数过多"),

    EMAIL_FORGET_PWD("4", "忘记密码邮箱找回方式错误次数过多");

    private String type;

    private String desc;

    public static String getDescByType(String type) {

        for (UserAccountLockType value : values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return LOGIN_ERROR_TIME.getDesc();
    }

    public String getDesc() {
        return LocaleUtil.getMessage("UserAccountLockType." + this.name(), desc);
    }
}

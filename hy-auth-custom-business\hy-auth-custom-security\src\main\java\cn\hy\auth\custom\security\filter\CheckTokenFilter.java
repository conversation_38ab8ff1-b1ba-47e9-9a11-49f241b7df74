package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述: 验证token过滤器
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/3
 */
@Slf4j
public class CheckTokenFilter implements Filter {


    /**
     * //token在参数列表中的属性名
     */
    private static final String TOKEN_ON_PARAM_KEY = "token";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        chain.doFilter(request, response);

        //刷新成功
        if (((HttpServletResponse) response).getStatus() != HttpStatus.OK.value()) {
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        String token = httpRequest.getParameter(TOKEN_ON_PARAM_KEY);

        log.debug("验证token[{}]成功，IP:{}", token, IpUtils.getIpAddress());

    }

}

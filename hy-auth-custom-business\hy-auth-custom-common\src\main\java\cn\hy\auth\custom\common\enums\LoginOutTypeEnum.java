package cn.hy.auth.custom.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/4
 */
@Getter
@AllArgsConstructor
public enum LoginOutTypeEnum {

    /**
     * 登录成功
     */
    LOGIN_SUCCESS(1, "登录成功"),
    /**
     * 登录失败
     */
    LOGIN_FAILURE(2, "登录失败"),
    /**
     * 登出成功
     */
    LOGOUT_SUCCESS(3, "登出成功");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 描述
     */
    private String name;
}

package cn.hy.auth.custom.common.domain;

import lombok.*;

import java.io.Serializable;
import java.util.Map;

/**
 * 类描述：用户账号
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/10/14 10:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class UserAccountDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String userAccountName;

    /**
     * 密码
     */
    private String password;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 账号是否启用
     */
    private Boolean enabled;
    /**
     *  账号状态 1:正常，其他非正常状态
     */
    private Integer status;
    /**
     * 账号有效开始时间戳
     */
    private Long accountStartEffectiveTime;
    /**
     * 账号有效截止时间戳
     */
    private Long accountEndEffectiveTime;
    /**
     * 密码最后有效期时间戳
     */
    private Long passwordExpireTimestamp;
    /**
     * 账号是否未被锁定
     */
    private Boolean accountNonLocked;

    /**
     * 租户编码
     */
    private String lesseeCode;

    /**
     * 租户编码
     */
    private String appCode;

    /**
     * 账号信息
     */
    private Map<String, Object> account;

    /**
     * 用户信息
     */
    private Map<String, Object> user;

    /**
     * 是否为超级管理员
     *
     * @return 是否为超级管理员
     */
    public boolean isAdministrator() {
        // TODO 3.2实现，后续需要在UserAccountService中赋值
        return true;
    }

    /**
     * 用户是否为启用状态
     *
     * @return true:启用；false:禁用
     */
    public boolean isEnabled() {
        // enable 优先级高
        if (enabled == null) {
            if (status == null){
                //该属性为空，表示不支持禁用用户
                return true;
            } else {
                return status==1;
            }
        }
        return enabled;
    }

    /**
     * 账号是否在有效期内
     *
     * @return true:账号有效；false:账号无效
     */
    public boolean isAccountNonExpired() {
        if (this.accountStartEffectiveTime == null && this.accountEndEffectiveTime == null) {
            //该属性为空，表示无需校验用户有效期
            return true;
        }
        if (this.accountStartEffectiveTime == null) {
            //有效开始时间未空，则只校验截止时间
            return System.currentTimeMillis() <= accountEndEffectiveTime;
        }
        if (this.accountEndEffectiveTime == null) {
            //有效截止时间为空，则只校验开始时间
            return System.currentTimeMillis() >= accountStartEffectiveTime;
        }

        return System.currentTimeMillis() >= accountStartEffectiveTime &&
                System.currentTimeMillis() <= accountEndEffectiveTime;
    }

    /**
     * 账号是否没被锁定
     *
     * @return true:没锁定；false:已被锁定
     */
    public boolean isAccountNonLocked() {
        // TODO 3.2实现，结合用户锁定、解锁功能处理
        return true;
    }

    /**
     * 账号密码是否没有过期
     *
     * @return true:没过期；false:已过期
     */
    public boolean isPasswordNonExpired() {
        if (this.passwordExpireTimestamp == null) {
            //该属性为空，表示无需校验密码有效期
            return true;
        }
        return System.currentTimeMillis() <= passwordExpireTimestamp;
    }
}

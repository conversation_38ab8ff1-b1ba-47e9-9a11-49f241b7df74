package cn.hy.auth.custom.security.verifycode;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 返回响应体
 * hy-authentication-center-cn.hy.auth.custom.security.verifycode
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/4/14 21:10
 */
@Data
@AllArgsConstructor
public class DefaultResponse<R> implements Serializable {
    private String code;
    private String msg;
    private long timestamp = System.currentTimeMillis();
    private R result;

    public DefaultResponse() {
    }

    private DefaultResponse(String code, String msg, R result) {
        this.result = result;
        this.code = code;
        this.msg = msg;
    }

    /**
     * 成功返回
     *
     * @param code   业务编号
     * @param msg    提示信息
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultResponse<R> success(String code, String msg, R result) {
        return new DefaultResponse<>(code, msg, result);
    }

    /**
     * 成功返回
     *
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultResponse<R> success(R result) {
        return success(ReturnCode.SUCCESS, result);
    }

    /**
     * 成功返回
     *
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultResponse<R> successSaas(R result) {
        return success(ReturnCode.SUCCESS_SAAS, result);
    }

    /**
     * 成功返回
     *
     * @param code   提示信息
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultResponse<R> success(ReturnCode code, R result) {
        return success(code.code(), code.msg(), result);
    }


    /**
     * 失败返回，结果集不设置
     *
     * @param code 业务编号
     * @param msg  提示信息
     * @return 响应对象
     */
    public static DefaultResponse<String> failure(String code, String msg) {
        return new DefaultResponse<>(code, msg, null);
    }

    /**
     * 失败返回，结果集不设置
     *
     * @param code 业务提示
     * @return 响应对象
     */
    public static DefaultResponse<String> failure(ReturnCode code) {
        return failure(code.code(), code.msg());
    }


}

package cn.hy.auth.multi.datasource;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.metadata.engine.common.utils.SpringUtil;
import cn.hy.paas.multi.datasource.service.DataSourceChooser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;

/**
 * 类描述：针对修改操作起作用.修改是从事务管理器里取的dataSource
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/6/12 18:36
 **/
@Slf4j
public class HyMultiDataSourceJpaTransactionManager extends JpaTransactionManager {
    @Override
    protected void doBegin(Object transaction, TransactionDefinition definition) {
        DataSourceChooser customDataSourceChooser = SpringUtil.getBean(DataSourceChooser.class);
        if (customDataSourceChooser != null && customDataSourceChooser.isUseDataSource()){
            // 进行多数据源选择
            // 为元数据的jpa选择数据源
            log.debug("JpaTransactionManager 为jpa选择数据源");
            customDataSourceChooser.chooseDataSourceByLessCodeAndAppCode(getLessCodeFromContext(),getAppCodeFromContext());
        }
        super.doBegin(transaction, definition);
    }

    private String getLessCodeFromContext() {
        return AuthContext.getContext().getLesseeCode();
    }
    private String getAppCodeFromContext() {
        return AuthContext.getContext().getAppCode();
    }

}

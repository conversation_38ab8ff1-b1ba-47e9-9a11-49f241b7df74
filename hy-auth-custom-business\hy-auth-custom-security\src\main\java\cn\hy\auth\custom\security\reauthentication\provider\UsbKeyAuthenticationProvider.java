package cn.hy.auth.custom.security.reauthentication.provider;

import cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationPreProvider;
import cn.hy.auth.common.security.core.authentication.token.UsbKeyAuthenticationToken;
import cn.hy.auth.common.security.core.properties.ReAuthenticationProperties;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.security.reauthentication.dao.AuthUsbkeyMapper;
import cn.hy.auth.custom.security.reauthentication.domain.AuthUsbkeyDO;
import cn.hy.auth.custom.security.reauthentication.utils.Sm2Util;
import cn.hy.auth.custom.user.account.dao.HrMemberDao;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Service;

import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * USBKey 认证
 *
 * <AUTHOR>
 * @date 2024/6/7 14:46
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsbKeyAuthenticationProvider extends ReAuthenticationPreProvider<UsbKeyAuthenticationToken> {

    private static final String USBKEY_TABLE_NAME = "auth_usbkey";

    private final ReAuthenticationProperties reAuthenticationProperties;

    private final TableRelocateUtil tableRelocateUtil;

    private final HrMemberDao hrMemberDao;

    private final AuthUsbkeyMapper authUsbkeyMapper;

    @Override
    public Authentication doAuthenticate(UsbKeyAuthenticationToken authentication) throws AuthenticationException {
        String username = (String) authentication.getPrincipal();

        AuthAssertUtils.isNotBlank(authentication.getSignature(), AuthErrorCodeEnum.A0101.code(),
                "参数" + reAuthenticationProperties.getSignatureParameter() + "不能为空");

        AuthUsbkeyDO usbkeyInfo = loadUsbkeyInfoByUsername(username);
        String certificate = usbkeyInfo.getPublicKey();
        String nonce = usbkeyInfo.getRandomNumber();
        if (StringUtils.isBlank(nonce)) {
            throw new BadCredentialsException("认证失败，请重新获取随机数！");
        }

        // USBKey 校验
        boolean verify;
        try {
            X509Certificate x509Certificate = Sm2Util.importX509Cert(certificate);
            verify = Sm2Util.verify(x509Certificate.getPublicKey(), nonce, authentication.getSignature());
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }

        if (!verify) {
            throw new BadCredentialsException("认证失败");
        }

        // 认证成功后, 清除一次性随机数, 防止重放攻击
        authUsbkeyMapper.revokeNonce(usbkeyInfo.getId());

        authentication.getAdditional().put("sn", usbkeyInfo.getSn());
        return authentication;
    }

    private AuthUsbkeyDO loadUsbkeyInfoByUsername(String username) {
        Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(USBKEY_TABLE_NAME);
        if (!tableMeta.isPresent()) {
            throw new AuthBusinessException("500", String.format("元数据表不存在: %s, 请配置对应的超级表信息", USBKEY_TABLE_NAME));
        }

        // 获取用户关联的人员编码
        List<Map<String, Object>> hrCodes = hrMemberDao.getHrCodeByAccount(username);
        if (CollectionUtils.isEmpty(hrCodes)) {
            throw new AuthBusinessException("500", "当前账号没有绑定人员！");
        }
        String hrCode = (String) hrCodes.get(0).get("hr_member_code");

        // 获取人员关联的 usbkey 信息
        List<AuthUsbkeyDO> usbkeyInfos = authUsbkeyMapper.selectByHrCode(hrCode);
        if (CollectionUtils.isEmpty(usbkeyInfos)) {
            throw new AuthBusinessException("500", String.format("当前人员[%s]未绑定 USBKEY！", hrCode));
        }
        if (usbkeyInfos.size() > 1) {
            String msg = String.format("根据人员编码[%s]最多只能查询到一条记录，但现在查询到[%s]条。", hrCode, usbkeyInfos.size());
            throw new AuthBusinessException("500", msg);
        }

        return usbkeyInfos.get(0);
    }

    @Override
    protected Class<UsbKeyAuthenticationToken> getAuthentication() {
        return UsbKeyAuthenticationToken.class;
    }
}

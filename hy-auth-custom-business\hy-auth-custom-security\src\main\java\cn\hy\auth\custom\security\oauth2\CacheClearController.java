package cn.hy.auth.custom.security.oauth2;

import cn.hy.auth.custom.security.oauth2.client.OauthClientDetailsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Objects;

/**
 * 类描述：清理缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/8 15:24
 **/
@RestController
@RequestMapping("/cache/cache/clear")
@Slf4j
public class CacheClearController {
    private final CacheManager cacheManager;

    public CacheClearController( @Qualifier("hyAuthAutoCacheManager")CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }


    @GetMapping("/AppInvolvedTable")
    public String clearCache(String lessCode,String appCode){
        StringBuilder stringBuilder = new StringBuilder("hy_auth_auth_app_involved_");
        if (StringUtils.isNotBlank(lessCode)){
            stringBuilder.append(lessCode);
            if (StringUtils.isNotBlank(appCode)){
                stringBuilder.append("_").append(appCode);
            }
        }
        clearCache(stringBuilder.toString());
        return "success";
    }

    @GetMapping("/userCache")
    public String clearUserCache(String lessCode,String clientType,String appCode){
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(lessCode)){
            stringBuilder.append(lessCode);
            if (StringUtils.isNotBlank(appCode)){
                stringBuilder.append("_").append(appCode);
            }
            stringBuilder.append("_").append(clientType);
        }
        clearCache(stringBuilder+"_hy_auth");
        return "success";
    }

    private void clearCache(String cacheNameKey){
        Collection<String> cacheNames = cacheManager.getCacheNames();
        if (CollectionUtils.isNotEmpty(cacheNames) ){
            for (String cacheName : cacheNames) {
                if (cacheName.contains(cacheNameKey)){
                    Cache cache = cacheManager.getCache(cacheName);
                    if (cache != null){
                        log.info("清除cache里的缓存数据. 名称：{}",cache.getName());
                        cache.clear();
                    }
                }
            }
        }

    }

}

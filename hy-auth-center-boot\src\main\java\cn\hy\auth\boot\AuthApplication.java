package cn.hy.auth.boot;

import cn.hy.auth.boot.config.LicenseConfiguration;
import cn.hy.auth.boot.init.InitDeploy;
import cn.hy.license.sdk.EnableHyLicense;
import cn.hy.license.sdk.properties.LicenseProperties;
import cn.hy.logging.autoconfigure.EnableHyLogging;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 启动入口类
 *
 * <AUTHOR>
 * @date 2020-11-18
 */
@EnableDiscoveryClient
@EnableHyLogging
@SpringBootApplication
@ComponentScan(basePackages = {"cn.hy.paas.multi","cn.hy.auth", "cn.hy.id", "cn.hy.dataengine", "cn.hy.metadata", "cn.hy.mdatasource"})
@MapperScan({"cn.hy.auth.**.dao"})
@EnableAsync
@Slf4j
@EnableHyLicense
@EnableDubbo(scanBasePackages = "cn.hy.auth.custom")
public class AuthApplication {
    public static volatile boolean AUTH_IS_UP = false;
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(AuthApplication.class);
        InitDeploy initDeploy = new InitDeploy();
        springApplication.addListeners(initDeploy);
        springApplication.addListeners(initDeploy.new InitDatabase());
        springApplication.run(args);
        AUTH_IS_UP =true;
    }

    @Configuration
    public static class EnableHyLicenseConfiguration {
        @Bean
        public LicenseProperties turnOffLicense(){
            // 根据最新需求，不再开启license功能
            return new LicenseProperties(false);
        }
    }

}

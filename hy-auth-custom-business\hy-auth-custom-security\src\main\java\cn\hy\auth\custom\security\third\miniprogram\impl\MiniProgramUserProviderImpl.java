package cn.hy.auth.custom.security.third.miniprogram.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.service.HySocialUserInfoApi;
import cn.hy.auth.common.security.core.authentication.social.service.MiniProgramFreeLogin;
import cn.hy.auth.common.security.core.authentication.social.service.SocialUsersConnectionService;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.security.third.miniprogram.domain.MiniProgramProviderUser;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 微信小程序用户信息获取
 *
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Component
@Slf4j
public class MiniProgramUserProviderImpl extends AbstractOAuth2ApiBinding implements HySocialUserInfoApi, MiniProgramFreeLogin {

    @Value("${third.proxy.miniprogram.url:https://api.weixin.qq.com}")
    private String baseUrl;

    @Autowired
    private SocialUsersConnectionService socialUsersConnectionService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 响应码
     */
    private static final String ERROR_CODE_PARAMS = "errcode";

    private static final String INIT_USER_ACCOUNT_NAME = "init_%s";

    @Override
    public ProviderUserInfo getProviderUserInfo(String lesseeCode, String appCode, String code, ProviderAppAccessToken providerAppAccessToken,
                                                ProviderInfo providerInfo, Map<String, Object> requestParamMap){
        try {
            MiniProgramProviderUser miniProgramProviderUser = new MiniProgramProviderUser();
            // 获取openId Url
            String getOpenIdUrl = baseUrl+"/sns/jscode2session?appid="+providerInfo.getAppKey()+"&secret="+providerInfo.getAppSecret()+"&js_code="+code+"&grant_type=authorization_code";
            // 获取openId
            String responseOpenId = getRestTemplate().getForObject(getOpenIdUrl, String.class);
            String openid = null;
            String unionid = null;
            if (responseOpenId != null){
                JSONObject resObjectOpen = JSONObject.parseObject(responseOpenId);
                unionid = resObjectOpen.getString("unionid");
                openid = resObjectOpen.getString("openid");
                if (Objects.isNull(openid)){
                    log.error("获取openid错误：{}",responseOpenId);
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("MiniProgramUserProviderImpl.result.msg1", null)+responseOpenId);
                }
            }
            miniProgramProviderUser.setProviderId(providerInfo.getProviderId());
            miniProgramProviderUser.setProviderUserid(openid);
            miniProgramProviderUser.setUnionId(unionid);
            if (StringUtils.isBlank(miniProgramProviderUser.getUserDisplayName())){
                miniProgramProviderUser.setUserDisplayName(String.format(INIT_USER_ACCOUNT_NAME,UUIDRandomUtils.getUuid(20)));
            }
            return miniProgramProviderUser;
        }catch (Exception e){
            log.error("查询微信小程序用户信息失败,{},{},{}",e,lesseeCode,appCode);
            throw new AuthBusinessException("500",LocaleUtil.getMessage("MiniProgramUserProviderImpl.result.msg2", null)+e.getMessage());
        }
    }

    @Override
    public ProviderUserInfo getProviderUserInfo(String code, ProviderAppAccessToken appAccessToken, ProviderInfo providerInfo) {
        return null;
    }


    @Override
    public String getPhoneNumber( ProviderAppAccessToken providerAppAccessToken,String code){
        try {
            // https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=ACCESS_TOKEN
            String phoneNumber = null;
            String getPhoneUrl = baseUrl + "/wxa/business/getuserphonenumber?access_token=" + providerAppAccessToken.getAccessToken();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            Map<String,Object> requestBody = new HashMap<>();
            requestBody.put("code",code);
            String requestJson = new ObjectMapper().writeValueAsString(requestBody);
            HttpEntity<String> entity = new HttpEntity<>(requestJson, headers);
            // 获取phoneNumber
            String responsePhone = getRestTemplate().postForObject(getPhoneUrl,entity,String.class);
            if (responsePhone != null){
                JSONObject resObjectOpen = JSONObject.parseObject(responsePhone);
                if (resObjectOpen.getInteger(ERROR_CODE_PARAMS) != 0) {
                    log.error("查询微信小程序用户手机号信息失败!");
                    throw new AuthBusinessException("500",LocaleUtil.getMessage("MiniProgramUserProviderImpl.result.msg3", null)+responsePhone);
                }
                phoneNumber = JSONObject.parseObject(resObjectOpen.getString("phone_info")).getString("phoneNumber");
            }
            return phoneNumber;
        } catch (Exception e) {
            log.error("获取用户手机号信息失败,{}",e);
            throw new AuthBusinessException("500",LocaleUtil.getMessage("MiniProgramUserProviderImpl.result.msg4", null)+e.getMessage());
        }
    }

    @Override
    public void bingThirdAccount(String lesseeCode, String appCode, String providerId, String userAccountName, String provideruserId) {
        StringBuilder delSqlBuilder = new StringBuilder(256);
        delSqlBuilder.append(" delete from " + socialUsersConnectionService.getTableName(lesseeCode, appCode, "third_user_relation ") + "where provider_id='" + providerId + "'" +
                " and user_account_name= '" + userAccountName + "'");
        if (StringUtils.isNotBlank(provideruserId)) {
            delSqlBuilder.append("and provider_user_id = '" + provideruserId + "'");
        }
        jdbcTemplate.execute(delSqlBuilder.toString());
        StringBuilder saveSqlBuilder = new StringBuilder(256);
        saveSqlBuilder.append("REPLACE INTO " + socialUsersConnectionService.getTableName(lesseeCode, appCode, "third_user_relation ") + " (id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name, last_update_time,`sequence`, provider_id, provider_user_id, provider_union_id, user_account_name) ");
        saveSqlBuilder.append("VALUES(" + UUIDRandomUtils.getSnowUuid() + ", '0', 1, 'admin', now(), 1, 'admin', now(), 1, '" + providerId + "', '" + provideruserId + "', NULL, '" + userAccountName + "')");
        jdbcTemplate.execute(saveSqlBuilder.toString());
    }

    @Override
    public void addUserAccount(String lesseeCode, String appCode, String userAccountName) {
        StringBuilder saveSqlBuilder = new StringBuilder(256);
        saveSqlBuilder.append("REPLACE INTO " + socialUsersConnectionService.getTableName(lesseeCode, appCode, "user_account ") + " (`create_user_id`, `create_user_name`, `create_time`, `data_version`, `last_update_user_id`, `pwd_status`, `last_update_time`, `password`, `user_account_name`, `id`, `username`, `last_update_user_name`)");
        saveSqlBuilder.append("VALUES(1, 'admin', now(), '0', 1, 1, now(), '', '" + userAccountName + "', " + UUIDRandomUtils.getSnowUuid() + ", '" + userAccountName + "', 'admin')");
        jdbcTemplate.execute(saveSqlBuilder.toString());
    }

    @Override
    public String updateUserAccountName(String lesseeCode, String appCode, String newAccountName, String oldAccountName) {
        try {
            if (Objects.isNull(newAccountName)){
                return null;
            }
            List<String> updateList = Lists.newArrayList();
            // 判断是否已存在改手机号的账号，存在就走删除
            if (CollectionUtil.isNotEmpty(findUserAccount(lesseeCode,appCode,newAccountName))){
                updateList.add("DELETE FROM "+socialUsersConnectionService.getTableName(lesseeCode,appCode,"user_account ")+"WHERE `user_account_name` = '"+oldAccountName+"'");
            }else {
                // 修改user_account 表语句拼接
                updateList.add("UPDATE "+socialUsersConnectionService.getTableName(lesseeCode,appCode,"user_account ")+ "set `user_account_name` = '"+newAccountName+"', `username` = '"+newAccountName+"' WHERE `user_account_name` = '"+oldAccountName+"'");
            }
            // 修改third_user_info 表语句拼接
            updateList.add("UPDATE "+socialUsersConnectionService.getTableName(lesseeCode,appCode,"third_user_info ")+ "set `user_account_name` = '"+newAccountName+"' WHERE `user_account_name` = '"+oldAccountName+"'");
            // 修改third_user_relation 表语句拼接
            updateList.add("UPDATE "+socialUsersConnectionService.getTableName(lesseeCode,appCode,"third_user_relation ")+ "set `user_account_name` = '"+newAccountName+"' WHERE `user_account_name` = '"+oldAccountName+"'");
            jdbcTemplate.batchUpdate(updateList.toArray(new String[0]));
            return newAccountName;
        }catch (Exception e){
            log.error("用户账号名更新失败：{}",oldAccountName);
            return null;
        }
    }

    private List<Map<String,Object>> findUserAccount(String lesseeCode,String appCode,String userAccountName){
        String selectSql = "select user_account_name AS userAccountName from "+socialUsersConnectionService.getTableName(lesseeCode,appCode,"user_account ")+" where user_account_name = '"+userAccountName+"'";
        return jdbcTemplate.queryForList(selectSql);
    }
}

{"groups": [{"name": "hy.security.oauth2.route", "type": "cn.hy.auth.custom.route.properties.RouteProperties", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties"}, {"name": "hy.security.oauth2.route.host-connection", "type": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties", "sourceMethod": "getHostConnection()"}], "properties": [{"name": "hy.security.oauth2.route.enable-third", "type": "java.lang.String", "description": "是否启用外部认证", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties", "defaultValue": "2.X"}, {"name": "hy.security.oauth2.route.filter-pattern", "type": "java.util.List<java.lang.String>", "description": "过滤器的拦截的url pattern", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties"}, {"name": "hy.security.oauth2.route.host-connection.connect-timeout-millis", "type": "java.lang.Integer", "description": "The connection timeout in millis. Defaults to 6000.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": 6000}, {"name": "hy.security.oauth2.route.host-connection.connection-request-timeout-millis", "type": "java.lang.Integer", "description": "The timeout in milliseconds used when requesting a connection from the connection manager. Defaults to -1, undefined use the system default.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": 10000}, {"name": "hy.security.oauth2.route.host-connection.max-per-route-connections", "type": "java.lang.Integer", "description": "The maximum number of connections that can be used by a single route.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": 20}, {"name": "hy.security.oauth2.route.host-connection.max-total-connections", "type": "java.lang.Integer", "description": "The maximum number of total connections the proxy can hold open to backends.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": 200}, {"name": "hy.security.oauth2.route.host-connection.socket-timeout-millis", "type": "java.lang.Integer", "description": "The socket timeout in millis. Defaults to 30000.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": 30000}, {"name": "hy.security.oauth2.route.host-connection.time-to-live", "type": "java.lang.Long", "description": "The lifetime for the connection pool.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties", "defaultValue": -1}, {"name": "hy.security.oauth2.route.host-connection.time-unit", "type": "java.util.concurrent.TimeUnit", "description": "The time unit for timeToLive.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties$HostConnectionProperties"}, {"name": "hy.security.oauth2.route.ignore-headers", "type": "java.util.List<java.lang.String>", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties", "defaultValue": "Connection"}, {"name": "hy.security.oauth2.route.initial-stream-buffer-size", "type": "java.lang.Integer", "description": "Setting for SendResponseServiceImpl for the initial stream buffer size.", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties", "defaultValue": 8192}, {"name": "hy.security.oauth2.route.request-system-area-recognizer-script", "type": "java.lang.String", "description": "识别请求属于哪个系统的groovy脚本. 暂时没支持登录的请求，后续可以直接在这里写逻辑进行支持", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties", "defaultValue": "if (token == null || token.toString().length()==0){return null;};if (token.toString().split(\"\\\\.\").length > 3){return \"3.X\";} else {return \"2.X\";}"}, {"name": "hy.security.oauth2.route.server-hosts", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "key:systemAreaIdentify value: ip+port 多个地址使用 逗号隔开 https://blog.csdn.net/MTone1/article/details/91413539", "sourceType": "cn.hy.auth.custom.route.properties.RouteProperties"}], "hints": []}
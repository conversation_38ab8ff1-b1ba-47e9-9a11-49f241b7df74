package cn.hy.auth.custom.multi.authinfo;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/1 15:44
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/authInfo")
@Slf4j
public class AuthInfoController {
    private final AppAuthStrategyManager appAuthStrategyManager;
    @GetMapping("/cache/clear")
    public Map<String,Object> clearCache(@RequestParam(value = "less_code",required = false) String lessCode, @RequestParam(value = "app_code",required = false)String appCode){
        AuthContext context = AuthContext.getContext();
        context.setLesseeCode(lessCode).setAppCode(appCode);
        appAuthStrategyManager.clearCache(AuthContext.getContext().getLesseeCode());
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("code",AuthErrorCodeEnum.SUCCESS.code());
        resultMap.put("result",true);
        return resultMap;
    }
}

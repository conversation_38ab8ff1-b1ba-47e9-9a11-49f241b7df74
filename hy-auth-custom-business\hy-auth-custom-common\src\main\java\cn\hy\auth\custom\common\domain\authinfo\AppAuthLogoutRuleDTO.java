package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * 登出规则
 *
 * <AUTHOR>
 * @date 2020-11-30 15:15
 **/
@Data
public class AppAuthLogoutRuleDTO implements Serializable {
    private static final long serialVersionUID = 8422588942712226964L;

    /**
     * 无操作自动下线时间阈值对象
     */
    private AppAuthOperationOfflinePolicyDTO noOperationOfflinePolicy;
}

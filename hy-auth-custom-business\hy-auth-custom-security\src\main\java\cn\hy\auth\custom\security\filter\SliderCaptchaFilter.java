package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.security.verifycode.ReturnCode;
import cn.hy.auth.custom.security.verifycode.VerifyCodeCreator;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *  登陆验证码校验
 * hy-authentication-center-cn.hy.auth.custom.security.filter
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/4/14 17:55
 */
@Order(-985)    //执行顺序要在登录之前，在初始化认证规则之后
@Component
@Slf4j
public class SliderCaptchaFilter extends AbstractAuthFilter {
    private static final String       REQUEST_METHOD      = "POST";
    private static final String       LOGIN_URL           ="/login";
    private static final String  MOBILE_VER_PARAMS   =  "verflagId";
    private static final String  MOBILE_VER_ID   =    "kz3elz6glghlum";

    private RequestMatcher requiresAuthenticationRequestMatcher;
    /**
     * 验证码
     */
    @Autowired
    private VerifyCodeCreator verifyCodeCreator;

    public SliderCaptchaFilter(){
        super();
        requiresAuthenticationRequestMatcher = new AntPathRequestMatcher(LOGIN_URL, REQUEST_METHOD);
    }

    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String verflagId = request.getParameter(MOBILE_VER_PARAMS);
        if(StringUtils.isEmpty(verflagId) || !verflagId.equals(MOBILE_VER_ID)){
            if (requiresAuthenticationRequestMatcher.matches(request)) {
                //先验证验证码
                String verCodeId = request.getParameter(LoginParamterConts.VER_CODE_ID);
                String loginName = request.getParameter(LoginParamterConts.USER_NAME);
                VerifyCodeCreator.VerifyResult result = verifyCodeCreator.recheckSliderCaptchaResultForLogin(verCodeId, loginName);
                if(!result.getCode().equals(ReturnCode.SUCCESS.code())){
                    throw new AuthBusinessException(AuthErrorCodeEnum.A0205.code(), JSON.toJSONString(result));
                }
            }
        }
        filterChain.doFilter(request, response);
    }
}

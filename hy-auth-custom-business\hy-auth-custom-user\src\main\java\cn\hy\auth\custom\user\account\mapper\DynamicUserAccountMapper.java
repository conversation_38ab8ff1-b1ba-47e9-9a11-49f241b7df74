package cn.hy.auth.custom.user.account.mapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.authinfo.*;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.user.account.dao.UserAccountDao;
import cn.hy.auth.custom.user.account.domain.SysdevUserOnlineStatusDTO;
import cn.hy.auth.custom.user.account.domain.ThirdUserRelationDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import com.alibaba.fastjson.JSON;
import com.github.abel533.sql.SqlMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.*;


/**
 * 用户账号信息动态映射类
 * 1、根据映射，使用表实际字段组装查询sql语句；
 * 2、查询结果后，根据映射关系，将对于字段值赋值给用户对象的属性
 *
 * <AUTHOR>
 * @date 2020-11-25 10:53
 **/
@Component
@AllArgsConstructor
@Slf4j
public class DynamicUserAccountMapper {

    private UserAccountDao userAccountDao;

    /**
     * 从上下文中获取账户字段映射对象
     *
     * @return 账户字段映射对象
     */
    private AppAuthUserAccountMapping getAccountMapping() {
        return AuthContext.getContext().appAuthStrategy().getUserAccountMapping();
    }

    //region 查询

    /**
     * 根据多个字段条件查询用户信息，只有一个字段符合指定值，就返回用户信息
     *
     * @param fieldCodes 字段列表
     * @param value      指定值
     * @return 用户信息
     */
    public UserAccountDTO selectByMultiField(List<String> fieldCodes, Object value) {
        return selectOneByFileds(fieldCodes, value);
    }

    /**
     * 根据用户主键获取用户信息
     *
     * @param userId 用户主键
     * @return 单个用信息
     */
    public UserAccountDTO selectById(Long userId) {
        List<Map<String, Object>> userDatas = userAccountDao.queryByUid(getAccountMapping().getTableName(), getAccountMapping().getUid(), String.valueOf(userId));
        if (userDatas.size() > 1) {
            String msg = String.format("根据用户id【%s】应该最多只能查询到一条记录，但现在查询到【%s】条。", userId, userDatas.size());
            throw new AuthBusinessException(AuthErrorCodeEnum.A0203.code(), msg);
        }
        return convertToDTO(userDatas.size() == 1 ? userDatas.get(0) : null);
    }

    /**
     * 根据用户名获取用户信息
     *
     * @param userName 用户账号
     * @return 单个用信息
     */
    public UserAccountDTO selectByUserName(String userName) {
        return selectOneByFiled(getAccountMapping().getUsername(), userName);
    }

    /**
     * 根据单个字段条件获取一条用户账号信息
     *
     * @param mappedFieldCode 映射字段编码，认证服务内部定义的属性编码
     * @param value           字段值
     * @return 单个用信息
     */
    public UserAccountDTO selectOneByFiled(String mappedFieldCode, Object value) {
        if (StringUtils.isBlank(mappedFieldCode) || Objects.isNull(value) || StringUtils.isBlank(value.toString())) {
            return null;
        }

        return selectOneByFileds(Lists.newArrayList(mappedFieldCode), value);
    }

    private UserAccountDTO getMobileLoginUserDatas(Object value){
        boolean mobileLogin = LoginTypeEnum.SMS_CODE.toString().equals(AuthContext.getContext().loginState().getLoginTypeString());

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        String mobileCipherText = paramMap.get("mobileCipherText");

        String valueStr = StringUtils.isBlank(mobileCipherText) ? Objects.toString(value, "") : mobileCipherText;
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();

        List<Map<String, Object>> userDatas =  userAccountDao.queryUserInfoByJoinTable(valueStr, mobileLogin, appCode, lesseeCode);

        if (userDatas.size() > 1) {
            String msg = String.format(LocaleUtil.getMessage("DynamicUserAccountMapper.result.msg1", ""), value, userDatas.size());
            throw new AuthBusinessException(AuthErrorCodeEnum.A0203.code(), msg);
        }

        return convertToDTO(userDatas.size() == 1 ? userDatas.get(0) : null);
    }

    /**
     * 根据多个字段条件获取一条用户账号信息
     *
     * @param fieldCodes 字段编码列表
     * @param value      字段值
     * @return 单个用信息
     */
    private UserAccountDTO selectOneByFileds(List<String> fieldCodes, Object value) {
        if (CollectionUtils.isEmpty(fieldCodes) || Objects.isNull(value) || StringUtils.isBlank(value.toString())) {
            return null;
        }
        boolean mobileLogin = LoginTypeEnum.SMS_CODE.toString().equals(AuthContext.getContext().loginState().getLoginTypeString());
        if (mobileLogin) {
            return getMobileLoginUserDatas(value);
        }
        //拼接多个字段查询的sql,多个条件的连接关系是OR
        StringBuilder sbSql = new StringBuilder("Select * From " + getAccountMapping().getTableName() + " Where ");
        for (String fieldCode : fieldCodes) {
            sbSql.append(" `").append(fieldCode).append("`=").append("#{value}");
            sbSql.append(" OR");
        }
        String sql = sbSql.substring(0, sbSql.length() - 2);
        // fix 性能，减少发出重复的sql语句
        String cacheKey = sql+value;
        Optional<UserAccountDTO> cacheUserAccountDTO = AuthContext.getContext().get(cacheKey);
        if (cacheUserAccountDTO.isPresent()){
            return cacheUserAccountDTO.get();
        }
        List<Map<String, Object>> userDatas = userAccountDao.queryUserInfo(getAccountMapping().getTableName(), fieldCodes, value);
        //List<Map<String, Object>> userDatas = sqlMapper.selectList(sql, paramMap);
        if (userDatas.size() > 1) {
            //log.info("gmt_create type {}",userDatas.get(0).get("gmt_create").getClass());
            String msg = String.format(LocaleUtil.getMessage("DynamicUserAccountMapper.result.msg1", ""), value, userDatas.size());
            throw new AuthBusinessException(AuthErrorCodeEnum.A0203.code(), msg);
        }

        UserAccountDTO userAccountDTO = convertToDTO(userDatas.size() == 1 ? userDatas.get(0) : null);
        if (userAccountDTO != null){
            AuthContext.getContext().set(cacheKey,userAccountDTO);
        }
        return userAccountDTO;
    }

    /**
     * 根据map数据集，转换UserAccountDTO对象
     *
     * @param userData 源数据
     * @return 转换后的dto对象
     */
    private UserAccountDTO convertToDTO(Map<String, Object> userData) {
        if (MapUtils.isEmpty(userData)) {
            return null;
        }


        AppAuthUserAccountMapping accountMapping = getAccountMapping();

        UserAccountDTO account = new UserAccountDTO();

        account.setId(MapUtils.getLong(userData, accountMapping.getUid()));
        account.setUserAccountName(MapUtils.getString(userData, accountMapping.getUsername()));
        account.setPassword(MapUtils.getString(userData, accountMapping.getPassword()));
        account.setEmail(MapUtils.getString(userData, accountMapping.getEmail()));
        account.setMobile(MapUtils.getString(userData, accountMapping.getMobile()));
        account.setAccount(userData);

        fillAccountStatus(account);
        fillAccountValidityDate(account);
        fillPasswordExpire(account);
        return account;
    }

    private void fillAccountStatus(@lombok.NonNull UserAccountDTO account) {
        AppAuthAccountStatusPolicyDTO policy = AuthContext.getContext()
                .appAuthStrategy()
                .getLoginRule()
                .getAccountStatusPolicy();
        Optional.ofNullable(policy).ifPresent(p -> account.setEnabled(p.parseTheValue(account.getAccount())));
    }

    private void fillAccountValidityDate(@lombok.NonNull UserAccountDTO account) {
        AppAuthAccountValidityDatePolicyDTO policy = AuthContext.getContext()
                .appAuthStrategy()
                .getLoginRule()
                .getAccountValidityDatePolicy();
        Optional.ofNullable(policy).ifPresent(p -> {
            account.setAccountStartEffectiveTime(p.parseStartTime(account.getAccount()));
            account.setAccountEndEffectiveTime(p.parseEndTime(account.getAccount()));
        });
    }

    private void fillPasswordExpire(@lombok.NonNull UserAccountDTO account) {
        AppAuthPwdExpirePolicyDTO policy = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdExpirePolicy();
        Optional.ofNullable(policy)
                .ifPresent(p -> account.setPasswordExpireTimestamp(policy.parseLastChangeTime(account.getAccount())));
    }

    //endregion

    public void updatePassword(Long userId, String password) {
        AuthAssertUtils.isNotNull(userId, AuthErrorCodeEnum.A0201.code(), AuthErrorCodeEnum.A0201.msg());
        AppAuthUserAccountMapping accountMapping = getAccountMapping();
        userAccountDao.updatePwd(accountMapping.getTableName(), accountMapping.getPassword(), password, accountMapping.getUid(), String.valueOf(userId));
    }

    public void updatePasswordModifyTime(Long userId) {
        AuthAssertUtils.isNotNull(userId, AuthErrorCodeEnum.A0201.code(), AuthErrorCodeEnum.A0201.msg());
        AppAuthUserAccountMapping accountMapping = getAccountMapping();
        AppAuthPwdUpdatePolicyDTO policy = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdUpdatePolicy();
        if (ObjectUtil.isNotNull(policy) && policy.getEnable()) {
            String pwdLastChangeTimeFiled = policy.getPwdLastChangeTimeFiled();
            userAccountDao.updatePwdModifyTime(accountMapping.getTableName(), pwdLastChangeTimeFiled, new Date(), accountMapping.getUid(), String.valueOf(userId));
        }
    }

    public void updatePasswordPwdStatus(UserAccountDTO userAccountDTO) {
        AuthAssertUtils.isNotNull(userAccountDTO.getId(), AuthErrorCodeEnum.A0201.code(), AuthErrorCodeEnum.A0201.msg());
        AppAuthUserAccountMapping accountMapping = getAccountMapping();
        AppAuthLoginPwdStatusPolicyDTO policy = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdStatusPolicy();
        if (ObjectUtil.isNotNull(policy) && policy.getEnable()) {
            String pwdStatus = policy.getPwdStatus();
            if (userAccountDTO.getAccount().containsKey(pwdStatus)) {
                userAccountDao.updatePwdModifyTime(accountMapping.getTableName(), pwdStatus, '2', accountMapping.getUid(), String.valueOf(userAccountDTO.getId()));
            }
        }
    }

    /**
     * 查询用户用户附加信息
     *
     * @param userId 用户表主键
     * @return 返回用户附加信息
     */
    public Map<String, Object> getUserAllInfoById(@NotNull Long userId) {
        List<AppAuthUserInfoMapping> userInfoMapping = AppAuthInfoParser.INSTANCE.getUserInfoMapping();
        if (validUserInfoMapping(userInfoMapping)) {
            //存在配置有为空的数据
            return Maps.newHashMap();
        }

        Map<String, Object> userAssociation = new HashMap<>(20);
        userInfoMapping.forEach(tableItem -> {
            //String sql = "Select * From " + tableItem.getTableName() + " Where " + tableItem.getUid() + "='" + userId + "'";
            //userAssociation.put(tableItem.getIdentifyCode(), userAccountDao.queryForList(sql));
            userAssociation.put(tableItem.getIdentifyCode(), userAccountDao.queryByUid(tableItem.getTableName(), tableItem.getUid(), String.valueOf(userId)));
        });
        return userAssociation;
    }


    /**
     * 验证账号关联信息是否存在配置为空
     *
     * @param userInfoMapping 配置信息
     * @return 返回是否存在为空, true存在为空的，false没有为空的配置
     */
    private Boolean validUserInfoMapping(List<AppAuthUserInfoMapping> userInfoMapping) {
        boolean hasNullValue = false;
        if (CollectionUtils.isEmpty(userInfoMapping)) {
            return true;
        }
        AppAuthUserInfoMapping appAuthUserInfoMapping;
        for (int i = 0, length = userInfoMapping.size(); i < length; i++) {
            appAuthUserInfoMapping = userInfoMapping.get(i);
            if (StringUtils.isBlank(appAuthUserInfoMapping.getIdentifyCode()) || StringUtils.isBlank(appAuthUserInfoMapping.getTableName()) ||
                    StringUtils.isBlank(appAuthUserInfoMapping.getUid())) {
                log.debug("存在关联用户配置信息为空:{}", JSON.toJSONString(appAuthUserInfoMapping));
                hasNullValue = true;
                break;
            }
        }
        return hasNullValue;
    }

    /**
     * 查询记录
     *
     * @param id .
     * @return .
     */
    public Map<String, Object> queryThirdById(String id) {
        return userAccountDao.queryThirdById(id);
    }

    /**
     * 查询记录
     *
     * @param id .
     * @return .
     */
    public Map<String, Object> queryThirdUserById(String id) {
        return userAccountDao.queryThirdUserById(id);
    }

    /**
     * 插入第三方关系信息
     *
     * @param relationDTO
     * @return .
     */
    public Integer saveThirdRelation(ThirdUserRelationDTO relationDTO) {
        return userAccountDao.saveThirdRelation(relationDTO);
    }

    /**
     * 插入第三方关系信息
     *
     * @return .
     */
    public void deleteThirdRelation(String providerId,String providerUserId,String userAccountName) {
       userAccountDao.deleteThirdRelation(providerId,providerUserId,userAccountName);
    }

    /**
     * 查询第三方关系信息
     *
     * @return .
     */
    public List<Map<String, Object>> findUserAccount(String providerId, String userAccountName,String providerUserId) {
          return userAccountDao.findUserAccount(providerId,userAccountName,providerUserId);
    }

    /**
     * 查询第三方关系信息
     *
     * @return .
     */
    public List<Map<String, Object>> findProviderUser(String providerId, String userAccountName,String providerUserId) {
        return userAccountDao.findProviderUser(providerId,userAccountName,providerUserId);
    }

    public Map<String, Object> selectOneByAccout(String userAccountName) {
        return userAccountDao.selectOneByAccout(userAccountName);
    }

    public UserLoginAccountDTO getUserByUserNameOrPhone(String userName, String phone) {
        return userAccountDao.selectUserIdByUserNameOrMobile(userName, phone);
    }

    public UserLoginAccountDTO selectUserIdByUserNameOrMobile(String userName, String phoneNumber) {
        return userAccountDao.selectUserIdByUserNameOrMobile(userName, phoneNumber);
    }

    public void changeLoginInfoByUserNameList(List<String> userNames, String status, String statusName) {
        userAccountDao.changeLoginInfoByUserNameList(userNames, status, statusName);
    }

    public Integer checkOnLineStatusTableIsExist(String lesseeCode, String appCode) {
        String targetTableName = lesseeCode + "_" + appCode + "_sysdev_user_online_status";
        return userAccountDao.checkOnLineStatusTableIsExist(targetTableName);
    }

    public void insertOrUpdateUserOnlineStataus(SysdevUserOnlineStatusDTO statusDTO) {
        SysdevUserOnlineStatusDTO dto = userAccountDao.selectRecodeByUserName(statusDTO.getUserAccountName());
        if (dto != null) {
            if (statusDTO.getOnlineStatus().equals(UserOnLineStatus.ONLINE.getStatus())) {
                statusDTO.setLoginTime(new Date());
                statusDTO.setLastLoginTime(dto.getLoginTime());
            } else {
                statusDTO.setLoginTime(dto.getLoginTime());
                statusDTO.setLastLoginTime(dto.getLastLoginTime());
            }
            userAccountDao.updateUserNameStatus(statusDTO);
        } else {
            statusDTO.setLoginTime(new Date());
            userAccountDao.insertUserNameStatus(statusDTO);
        }

    }

    public void changeLoginStatusNotInSet(Set<String> userNames, String status, String statusName) {
        userAccountDao.changeLoginStatusNotInSet(userNames, status, statusName);
    }

    public UserLoginAccountDTO getUserByUserNameOrPhoneLimitOne(String accountName, String mobile) {
        return userAccountDao.getUserByUserNameOrPhoneLimitOne(accountName, mobile);
    }

    public UserLoginAccountDTO getUserByUserEmail(String email) {
        return userAccountDao.selectUserByEmail(email);
    }

}

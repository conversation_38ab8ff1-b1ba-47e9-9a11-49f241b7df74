package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static cn.hy.auth.custom.common.constant.LoginParamterConts.CLIENT_TYPE;

/**
 * 处理登录的一些状态信息
 * 从参数中提取，设置到上下文
 *
 * <AUTHOR>
 * @date 2020-12-14 11:55
 **/
@Order(-970)
@Component
@Slf4j
public class LoginStateInitFilter extends AbstractAuthFilter {

    @Autowired
    @Lazy
    private AppInfoParseProxy appInfoParseProxy;

    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response,
                              FilterChain filterChain) throws ServletException, IOException {

        if (RequestTypeEnum.LOGIN.equals(appInfoParseProxy.parseType(request))) {
            log.info("登录请求，校验登录扩展参数。URL:【{}】", request.getRequestURL());

            validateParam(request);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否Client模式的登录
     * @param request .
     * @return .
     */
    private Boolean isClientLogin(HttpServletRequest request){
        String grantType = request.getParameter(LoginParamterConts.GRANT_TYPE);
        return "client_credentials".equals(grantType);
    }

    /**
     * 校验参数，并填充一些登录状态信息到上下文
     * @param request .
     */
    private void validateParam(HttpServletRequest request){
        if(isClientLogin(request)){
            //非Client模式的登录，才需要校验
            return;
        }

        //只有登录请求才需要初始化登录状态信息
        String clientType = request.getParameter(CLIENT_TYPE);

        AuthAssertUtils.isNotBlank(clientType,
                                   AuthErrorCodeEnum.A0101.code(), LocaleUtil.getMessage("LoginStateInitFilter.assertUtil.msg1", null) + CLIENT_TYPE + LocaleUtil.getMessage("LoginStateInitFilter.assertUtil.msg2", null));
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.codeOf(clientType);
        AuthAssertUtils.isNotNull(clientTypeEnum,
                                  AuthErrorCodeEnum.A0101.code(), LocaleUtil.getMessage("LoginStateInitFilter.assertUtil.msg3", null) + CLIENT_TYPE +"=" + clientType + LocaleUtil.getMessage("LoginStateInitFilter.assertUtil.msg4", null));

        AuthContext.getContext().loginState().setClientType(clientTypeEnum);
    }
}

package cn.hy.auth.custom.route.filter;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.route.common.TokenIdentifyConstant;
import cn.hy.auth.custom.route.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 认证请求的路由过滤器
 * 职责：根据token的特征，路由到3.x或者其他外部系统（如2.x）进行请求响应；
 * 执行优先级为最高
 *
 * <AUTHOR>
 * @date 2021-08-23 12:09
 **/
@Order(-1080)
@Component
@Slf4j
public class RequestRouteFilter extends AbstractAuthFilter {

    private UrlPathMatchService urlPathMatchService;
    private SystemIdentifyService systemIdentifyService;
    private RequestRoutingService requestRoutingService;

    private EnvironmentSymbolService environmentSymbolService;

    public RequestRouteFilter( UrlPathMatchService urlPathMatchService,
                              SystemIdentifyService systemIdentifyService,
                               RequestRoutingService requestRoutingService,
                               EnvironmentSymbolService environmentSymbolService) {
        this.urlPathMatchService = urlPathMatchService;
        this.systemIdentifyService = systemIdentifyService;
        this.requestRoutingService = requestRoutingService;
        this.environmentSymbolService = environmentSymbolService;
    }

    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response,
                              FilterChain filterChain) throws ServletException, IOException {
        long startTime = System.currentTimeMillis();
        AuthContext.getContext().set("_requestId_", UUIDRandomUtils.getUuid(32));
        AuthContext.getContext().set("_uri_",request.getRequestURI());
        try {
            if (!urlPathMatchService.shouldFilter(request)) {
                // 不处理，直接放行
                filterChain.doFilter(request, response);
                return;
            }
            String systemAreaIdentify = systemIdentifyService.getRequestSystemAreaIdentify(request);
            log.debug("systemAreaIdentify--->{}",systemAreaIdentify);
            if (StringUtils.isEmpty(systemAreaIdentify) || TokenIdentifyConstant.X3.equals(systemAreaIdentify)) {
                // 如果3.xToken中携带了环境标识，则尝试根据路径配置转发到对应环境服务
                String environmentSymbolFromToken = environmentSymbolService.getEnvironmentSymbolFromToken((String) systemIdentifyService.getToken(request));
                if (!StringUtils.isEmpty(environmentSymbolFromToken)){
                    // 环境标识作为系统标识，转发到对应环境服务
                    requestRoutingService.run(environmentSymbolFromToken, request, response, filterChain);
                }else {
                    // 不处理，直接放行
                    filterChain.doFilter(request, response);
                }
            } else {
                // 非3.x 能处理的，转发到外部进行处理
                log.debug("systemAreaIdentify = 【{}】,转发到外部系统进行处理", systemAreaIdentify);
                String accessToken = (String) systemIdentifyService.getToken(request);
                //如果是IAM请求TOKEN，设置头部信息
                if(systemAreaIdentify.contains(TokenIdentifyConstant.IAM)  && systemIdentifyService.getToken(request)!=null){
                    //设置特别的头部信息
                    HeaderMapRequestWrapper servletRequest=new HeaderMapRequestWrapper(request);
                    if(!accessToken.contains("bearer")){
                        servletRequest.addHeader("Blade-Auth","Bearer "+accessToken);
                    }else{
                        servletRequest.addHeader("Blade-Auth",accessToken);
                    }
                    log.info("Blade-Auth:{}",servletRequest.getHeader("Blade-Auth"));
                    requestRoutingService.run(systemAreaIdentify, servletRequest, response, filterChain);
                } else {
                    requestRoutingService.run(systemAreaIdentify, request, response, filterChain);
                }
            }
        }finally {
            log.debug("{}, 接收到请求时间 {},完成请求时间：{},耗时：{} ms",request.getRequestURI(),startTime,System.currentTimeMillis(),(System.currentTimeMillis()-startTime));
            //清空上下文
            AuthContext.getContext().reset();
        }

    }
}
package cn.hy.auth.custom.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @Author: zqb
 * @Description: 加密工具类
 * @CreateDate: 2019/8/16 15:36
 */
@Slf4j
public class AesUtil {

    private AesUtil() {
    }

    /**
     * 密钥长度必须是16
     */
    public static final String DEFAULT_KEY = "hy_auth_business";

    /**
     * 算法
     */
    private static final String PADDING = "AES/ECB/PKCS5Padding";

    /**
     * aes加密
     *
     * @param content 密码
     * @return 返回加密后的密码
     */
    public static String encryptByDefaultKey(String content) {
        try {
            return encrypt(content, DEFAULT_KEY);
        } catch (Exception e) {
            log.error(e.getMessage());
            return StringUtils.EMPTY;
        }
    }

    /**
     * base 64 encode
     *
     * @param bytes 待编码的byte[]
     * @return 编码后的base 64 code
     */
    private static String base64Encode(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    /**
     * base 64 decode
     *
     * @param base64Code 待解码的base 64 code
     * @return 解码后的byte[]
     */
    private static byte[] base64Decode(String base64Code) {
        try {
            return StringUtils.isEmpty(base64Code) ? null : Base64.decodeBase64(base64Code);
        } catch (Exception e) {
            return new byte[0];
        }

    }

    /**
     * AES加密
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的byte[]
     */
    private static byte[] aesEncryptToBytes(String content, String encryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128);
            Cipher cipher = Cipher.getInstance(PADDING);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));

            return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            return new byte[0];
        }

    }

    /**
     * AES加密为base 64 code
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的base 64 code
     */
    public static String encrypt(String content, String encryptKey) {
        try {
            return base64Encode(aesEncryptToBytes(content, encryptKey));
        } catch (Exception e) {
            return "";
        }

    }

    /**
     * AES解密
     *
     * @param encryptBytes 待解密的byte[]
     * @param decryptKey   解密密钥
     * @return 解密后的String
     */
    private static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128);

            Cipher cipher;
            cipher = Cipher.getInstance(PADDING);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
            byte[] decryptBytes = cipher.doFinal(encryptBytes);

            return new String(decryptBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 将base 64 code AES解密
     *
     * @param encryptStr 待解密的base 64 code
     * @param decryptKey 解密密钥
     * @return 解密后的string
     */
    public static String decrypt(String encryptStr, String decryptKey) {
        try {
            return StringUtils.isEmpty(encryptStr) ? null : aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
        } catch (Exception e) {
            return "";
        }

    }

    /**
     * 将base 64 code AES解密
     *
     * @param encryptStr 待解密的base 64 code
     * @return 解密后的string
     */
    public static String decryptByDefaultKey(String encryptStr) {
        return decrypt(encryptStr, DEFAULT_KEY);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        test();
    }
    public static void test() throws UnsupportedEncodingException {
        String str = "Abc12345";
        log.info("加密前,{}", str);

        log.info("加密密钥和解密密钥:{}", DEFAULT_KEY);

        String encrypt = encrypt(str, DEFAULT_KEY);
        log.info("加密后:{}", encrypt);
        String encodePwd = URLEncoder.encode(encrypt, "UTF-8");
        log.info("加密后URLEncode:{}", encodePwd);
        String decrypt = decrypt("2mYWe0XBwGqye/f4XFe7eQ==", "flowhysuseraesco");
        log.info("解密后:{}", decrypt);
        System.out.println(encrypt("Abc12345","flowhysuseraesco"));
    }
}

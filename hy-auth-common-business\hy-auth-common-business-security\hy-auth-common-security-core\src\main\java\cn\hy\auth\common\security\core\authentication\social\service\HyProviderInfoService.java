package cn.hy.auth.common.security.core.authentication.social.service;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;

/**
 * 类描述：第三方平台信息
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/13 15:27
 **/
public interface HyProviderInfoService {
    /**
     *  服务商标识
     * @return .
     */
    String getProviderId();


    /**
     *  第三方平台服务商信息
     * @return
     */
    ProviderInfo getProviderInfo(String lesseeCode, String appCode);
}

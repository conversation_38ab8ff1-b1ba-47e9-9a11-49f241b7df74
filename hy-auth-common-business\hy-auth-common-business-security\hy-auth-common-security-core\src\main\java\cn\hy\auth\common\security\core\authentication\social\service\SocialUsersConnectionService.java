package cn.hy.auth.common.security.core.authentication.social.service;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类描述：userconnection 表业务管理器
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/9 17:03
 **/
public interface SocialUsersConnectionService {
    /**
     *  .
     * @param lesseeCode .
     * @param appCode .
     * @param providerId .
     * @param providerUserId .
     * @return .
     */
    List<String> findUserAccountName(String lesseeCode, String appCode, String providerId, String providerUserId);

    /**
     *  删除空白的UserAccountName记录
     * @param lesseeCode .
     * @param appCode .
     * @param providerId .
     * @param providerUserId .
     */
    void deleteBlankUserAccountName(String lesseeCode,String appCode,String providerId,String providerUserId);


    /**
     *  插入providerUserInfo记录
     * @param lesseeCode .
     * @param appCode .
     * @param providerUserInfo .
     */
    long insertProviderUserInfo(String lesseeCode, String appCode, ProviderUserInfo providerUserInfo);


    /**
     *  查询出第三方账号信息
     * @param userId .
     * @param providerType .
     */
    Map<String,Object> getThirdUserFromBaseApp(String userId, ProviderType providerType);


    /**
     * * .
     * @param providerId
     * @param userId
     * @param userAccount
     * @return
     */
    Boolean bindThirdUser(String providerId, String userId, String userAccount);


    /**
     * 更新人员表信息
     * @param providerId
     * @param userAccountName
     * @param providerUserid
     */
    void updateHrMember(String providerId, String userAccountName, String providerUserid);

    /**
     * 修改表名
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @param tableCode  表编码
     * @return
     */
    String getTableName(String lesseeCode, String appCode, String tableCode);

}

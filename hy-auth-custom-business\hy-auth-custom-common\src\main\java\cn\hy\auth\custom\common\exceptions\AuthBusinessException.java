package cn.hy.auth.custom.common.exceptions;

import lombok.Getter;
import org.springframework.security.core.AuthenticationException;

/**
 * 类描述: 认证中心业务统一异常对象
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/18
 */
@Getter
public class AuthBusinessException extends AuthenticationException {

    /**
     * 异常码
     */
    private final String code;
    /**
     * 异常提示
     */
    private final String message;

    public AuthBusinessException(String code, String msg) {
        super(msg);
        this.code = code;
        this.message = msg;
    }

}
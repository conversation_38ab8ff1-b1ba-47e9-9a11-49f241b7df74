package cn.hy.auth.common.security.core.authentication.mobile;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.event.CheckUserAccountLockEvent;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeService;
import cn.hy.auth.common.security.core.authentication.mobile.service.ValidateCodeGenerator;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-24 10:52
 */
@RestController
@Slf4j
public class SmsController {

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private ValidateCodeGenerator smsCodeGenerator;

    @Autowired
    private SmsCodeSender smsCodeSender;

    @Autowired
    private SmsCodeService smsCodeService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AppConfigParamUtil appConfigParamUtil;


    @RequestMapping(value = "/code/sms", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> createSmsCode(@RequestBody SmsLoginVO smsLoginVO, HttpServletRequest request, HttpServletResponse response) throws IOException, ServletRequestBindingException {
        //保存验证码，可以考虑用手机号保存
        String mobile = ServletRequestUtils.getRequiredStringParameter(request, securityProperties.getSmsAuth().getMobileParameter());
        // 检查用户是否存在
        Boolean needToCipher = StringUtils.isNotBlank(smsLoginVO.getMobileCipherText());
        CheckUserAccountLockEvent checkUserAccountLockEvent = new CheckUserAccountLockEvent(needToCipher ? smsLoginVO.getMobileCipherText() : mobile);
        applicationContext.publishEvent(checkUserAccountLockEvent);
        String resultMessage = "";
        Map<String, Object> resultMap = new HashMap<>();
        if (checkUserAccountLockEvent.isLock()) {
            resultMessage = LocaleUtil.getMessage("SmsController.result.msg1", "");
        } else if (!checkUserAccountLockEvent.getUserExist()) {
            resultMessage = LocaleUtil.getMessage("SmsController.result.msg2", "");
        }
        if (StringUtils.isNotBlank(resultMessage)) {
            resultMap.put("message", resultMessage);
            resultMap.put("result", false);
            return resultMap;
        }
        //生成随机验证码
        ValidateCode smsCode = smsCodeGenerator.createCode(request, response);
        // 保存验证码
        resultMessage = smsCodeService.saveCode(needToCipher ? mobile + smsLoginVO.getMobileCipherText() : mobile, smsCode.getCode(), smsCode.getExpireTime());
        boolean saveSuccess = StringUtils.isBlank(resultMessage);
        if (saveSuccess) {
            //模拟手机发送，此时调用短信服务商接口
            Map<String, String> paramMap = ServletUtil.getParamMap(request);
            smsLoginVO.setSmsType(1);
            String sendCodeMessage = smsCodeSender.send(mobile, smsCode.getCode(), paramMap.get("app_code"), paramMap.get("lessee_code"), smsLoginVO, "sysdevSmsLimit");
            if (StringUtils.isNotBlank(sendCodeMessage)) {
                smsCodeService.removeCode(mobile, smsCode.getCode());
                saveSuccess = false;
                resultMessage = sendCodeMessage;
            }
        }
        resultMap.put("result", saveSuccess);
        resultMap.put("message", resultMessage);
        return resultMap;
    }
}

package cn.hy.auth.common.security.oauth2.token.store;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.security.oauth2.token.TokenStoreExtends;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.InMemoryTokenStore;
import org.springframework.security.oauth2.provider.token.store.JdbcTokenStore;

import javax.sql.DataSource;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 类描述: 数据库加内存模式混用
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/10
 */
public class JdbcWithMemoryTokenStore extends JdbcTokenStore implements TokenStore, TokenStoreExtends {


    public JdbcWithMemoryTokenStore(DataSource dataSource) {
        super(dataSource);
    }

    /**
     * 内存模式token缓存
     */
    private  TokenStore inMemoryTokenStore = new InMemoryTokenStore();

    public void setInMemoryTokenStore(TokenStore inMemoryTokenStore) {
        this.inMemoryTokenStore = inMemoryTokenStore;
    }

    /**
     * 获取认证信息
     *
     * @param token token信息
     * @return 返回认证信息
     */
    @Override
    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
        return readAuthentication(token.getValue());
    }

    /**
     * 获取认证信息
     *
     * @param token token值
     * @return 返回认证信息
     */
    @Override
    public OAuth2Authentication readAuthentication(String token) {
        //先执行内存
        OAuth2Authentication oAuth2Authentication = inMemoryTokenStore.readAuthentication(token);
        if (ObjectUtil.isNotNull(oAuth2Authentication)) {
            return oAuth2Authentication;
        }
        //执行数据库
        return super.readAuthentication(token);
    }

    /**
     * 存储token信息
     *
     * @param token          token信息
     * @param authentication 认证信息
     */
    @Override
    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        inMemoryTokenStore.storeAccessToken(token, authentication);
        super.storeAccessToken(token, authentication);
    }

    /**
     * 读取token信息
     *
     * @param tokenValue token值
     * @return 返回token信息
     */
    @Override
    public OAuth2AccessToken readAccessToken(String tokenValue) {
        OAuth2AccessToken oAuth2AccessToken = inMemoryTokenStore.readAccessToken(tokenValue);
        if (ObjectUtil.isNotNull(oAuth2AccessToken)) {
            return oAuth2AccessToken;
        }
        return super.readAccessToken(tokenValue);
    }

    /**
     * 移除token信息
     *
     * @param token token信息
     */
    @Override
    public void removeAccessToken(OAuth2AccessToken token) {
        inMemoryTokenStore.removeAccessToken(token);
        super.removeAccessToken(token);
    }

    /**
     * 刷新token信息
     *
     * @param refreshToken   刷新token信息
     * @param authentication 认证信息
     */
    @Override
    public void storeRefreshToken(OAuth2RefreshToken refreshToken, OAuth2Authentication authentication) {
        inMemoryTokenStore.storeRefreshToken(refreshToken, authentication);
        super.storeRefreshToken(refreshToken, authentication);
    }

    /**
     * 读取刷新的token信息
     *
     * @param tokenValue 刷新token主键
     * @return 返回刷新token的对象
     */
    @Override
    public OAuth2RefreshToken readRefreshToken(String tokenValue) {
        OAuth2RefreshToken oAuth2RefreshToken = inMemoryTokenStore.readRefreshToken(tokenValue);
        if (ObjectUtil.isNotNull(oAuth2RefreshToken)) {
            return oAuth2RefreshToken;
        }
        return super.readRefreshToken(tokenValue);
    }

    @Override
    public OAuth2Authentication readAuthenticationForRefreshToken(OAuth2RefreshToken token) {
        OAuth2Authentication oAuth2Authentication = inMemoryTokenStore.readAuthenticationForRefreshToken(token);
        if (ObjectUtil.isNotNull(oAuth2Authentication)) {
            return oAuth2Authentication;
        }
        return super.readAuthenticationForRefreshToken(token);
    }

    @Override
    public void removeRefreshToken(OAuth2RefreshToken token) {
        inMemoryTokenStore.removeRefreshToken(token);
        super.removeRefreshToken(token);
    }

    @Override
    public void removeAccessTokenUsingRefreshToken(OAuth2RefreshToken refreshToken) {
        inMemoryTokenStore.removeAccessTokenUsingRefreshToken(refreshToken);
        super.removeAccessTokenUsingRefreshToken(refreshToken);
    }

    @Override
    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
        OAuth2AccessToken oAuth2AccessToken = inMemoryTokenStore.getAccessToken(authentication);
        if (ObjectUtil.isNotNull(oAuth2AccessToken)) {
            return oAuth2AccessToken;
        }
        return super.getAccessToken(authentication);
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
        Collection<OAuth2AccessToken> accessTokens = inMemoryTokenStore.findTokensByClientIdAndUserName(clientId, userName);
        if (ObjectUtil.isNotNull(accessTokens)) {
            return accessTokens;
        }
        return super.findTokensByClientIdAndUserName(clientId, userName);
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
        Collection<OAuth2AccessToken> accessTokens = inMemoryTokenStore.findTokensByClientId(clientId);
        if (ObjectUtil.isNotNull(accessTokens)) {
            return accessTokens;
        }
        return super.findTokensByClientId(clientId);
    }

    @Override
    public Set<String> getNoExpiredTokenUserNames() {
        return new HashSet<>();
    }
}

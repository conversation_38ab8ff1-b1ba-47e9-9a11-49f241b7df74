package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户表字段映射(用户查询用户关联的所有信息,如账号表+用户表+角色表组成的视图)
 *
 * <AUTHOR>
 * @date 2020-12-01 09:58
 **/
@Data
public class AppAuthUserInfoMapping implements Serializable {
    private static final long serialVersionUID = -2415159576014974034L;
    /**
     * 查询用户相关信息的表名
     */
    private String tableName;
    /**
     * 用户id对应字段编码
     */
    private String uid;

    /**
     * 标识名称,即返回Map对象的KEY值
     */
    private String identifyCode;
    /**
     * 属性说明
     */
    private String remark;
}

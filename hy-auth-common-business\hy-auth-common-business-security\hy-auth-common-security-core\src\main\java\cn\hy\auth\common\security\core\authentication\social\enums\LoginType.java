package cn.hy.auth.common.security.core.authentication.social.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @className: 登陆类型
 * @time: 2022-11-9 17:21
 **/
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LoginType {
    /**
     *
     */
    THIRD_APP("app", "第三方APP"),
    /**
     *  aouth2 的code授权模式
     */
    THIRD_LINK("link", "第三方链接"),
    ;

    private String code;

    private String message;

    public static LoginType getByCode(String code) {
        for (LoginType messageEnum : LoginType.values()) {
            if (StrUtil.equalsIgnoreCase(code, messageEnum.code)) {
                return messageEnum;
            }
        }
        return null;
    }

}

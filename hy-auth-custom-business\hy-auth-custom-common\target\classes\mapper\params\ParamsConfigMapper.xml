<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.common.params.dao.ParamsConfigMapper" >

  <select id="getAddressByBizgw" resultType="string">
    select address from bizgw_plugin_driver_business_cfg where bus_cfg_key=#{key}
  </select>

  <select id="getAddressByConfig" resultType="string">
    select cfg_val from app_config where cfg_key=#{key}
  </select>

  <select id="getLesseeAddress" resultType="string">
    select address from ${lesseeCode}_appmanage_application_param_config where bus_cfg_key=#{key}
  </select>
</mapper>
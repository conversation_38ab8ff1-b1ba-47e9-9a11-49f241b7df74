package cn.hy.auth.common.security.core.authentication.social.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @author: he<PERSON><PERSON><PERSON>
 * @className: 第三方服务商类型
 * @time: 2022-11-9 17:21
 **/
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProviderType {

    DING_TALK("dingTalk", "钉钉"),

    WECHAT("wechat", "企业微信"),

    MINI_PROGRAM("miniProgram","微信小程序"),

    FKY("fky", "佛科院")
    ;

    private String code;

    private String message;

    public static ProviderType getByCode(String code) {
        for (ProviderType messageEnum : ProviderType.values()) {
            if (StrUtil.equalsIgnoreCase(code, messageEnum.code)) {
                return messageEnum;
            }
        }
        return null;
    }

}

package cn.hy.auth.common.security.core.authentication.gxfl.service;

import cn.hy.auth.common.security.core.authentication.gxfl.bean.GxflProviderUser;

import java.util.Map;

/**
 * 广西法律认证相关逻辑
 * <AUTHOR>
 * @Date 2024/3/13
 */
public interface GxflAuthService {

    /**
     * 第三方获取用户信息
     * @param lesseeCode
     * @param appcode
     * @param code
     * @return
     */
    GxflProviderUser getUserInfo(String lesseeCode, String appcode, String code,Map<String, Object> requestInfo);

    /**
     * 根据用户账号查询用户信息
     *
     * @param userInfo
     * @return
     */
    Map<String, Object> getByUserAccount(GxflProviderUser userInfo);

    /**
     * 用户注册
     *
     * @param userInfo
     * @return
     */
    Boolean registerUser(GxflProviderUser userInfo);


    /**
     * 查询表名
     *
     * @param lesseeCode
     * @param appCode
     * @param tableCode
     * @return
     */
    String getTableName(String lesseeCode, String appCode, String tableCode);
}

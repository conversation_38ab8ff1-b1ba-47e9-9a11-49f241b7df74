package cn.hy.auth.common.security.core.authentication.external;

import cn.hy.auth.common.security.core.properties.HyExternalProperties;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 佛科院回调登录Filter ，模仿UsernamePasswordAuthenticationFilter
 *
 * <AUTHOR>
 * @date 2022/09/08  15:36
 */
public class HyExternalAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 是否只处理post请求
     */
    private boolean postOnly = false;
    private static final String REQUEST_METHOD = "POST";
    private HyExternalPreAuthenticationProvider hyExternalPreAuthenticationProvider;
    private HyExternalProperties externalProperties;

    public HyExternalAuthenticationFilter(HyExternalProperties hySocialProperties, HyExternalPreAuthenticationProvider hySocialPreAuthenticationProvider) {
        //要拦截的请求
        super(new AntPathRequestMatcher(hySocialProperties.getAuthProcessingUrl(), REQUEST_METHOD));
        this.hyExternalPreAuthenticationProvider = hySocialPreAuthenticationProvider;
        this.externalProperties = hySocialProperties;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (this.postOnly && !REQUEST_METHOD.equals(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            String code = this.obtainCode(request);
            String providerId = this.obtainProviderId(request);
            String lessCode = this.obtainLesseeCode(request);
            String appCode = this.obtainAppCode(request);
            HyExternalAuthenticationToken authRequest = new HyExternalAuthenticationToken(code,providerId,lessCode,appCode);
            this.setDetails(request, authRequest);
            if (hyExternalPreAuthenticationProvider != null){
                hyExternalPreAuthenticationProvider.authenticate(authRequest);
            }
            //调用AuthenticationManager
            return this.getAuthenticationManager().authenticate(authRequest);
        }
    }

    private String obtainAppCode(HttpServletRequest request) {
        return request.getParameter(this.externalProperties.getAppCodeParamter());
    }

    private String obtainLesseeCode(HttpServletRequest request) {
        return request.getParameter(this.externalProperties.getLesseeCodeParamter());
    }

    private String obtainProviderId(HttpServletRequest request) {
        return request.getParameter(this.externalProperties.getProviderIdParamter());
    }

    /**
     * 获取oauth 的访问code
     *
     * @param request .
     * @return String
     */
    private String obtainCode(HttpServletRequest request) {
        return request.getParameter(this.externalProperties.getCodeParameter());
    }

    private void setDetails(HttpServletRequest request, HyExternalAuthenticationToken codeAuthenticationToken) {
        codeAuthenticationToken.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }


    public void setPostOnly(boolean postOnly) {
        this.postOnly = postOnly;
    }

}

package cn.hy.auth.custom.common.appinfo.service.impl;

import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.BaseAppInfoParser;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 最初的处理器，先判断有没有忽略url的配置
 *
 * <AUTHOR>
 * @date 2020-12-15 13:55
 **/
@Service
@Slf4j
@AllArgsConstructor
public class NonBusinessAppInfoParser extends BaseAppInfoParser {

    /**
     * 排序值，越小越靠前
     *
     * @return 排序值
     */
    @Override
    protected int order() {
        return 0;
    }

    /**
     * 支持的uri模板列表
     *
     * @return 支持的uri模板列表
     */
    @Override
    protected @NonNull List<String> uriPatterns() {
        return new ArrayList<>(requestTypeProperties.getNonBusiness());
    }

    /**
     * 解析得到租户、应用编码信息已经请求分类
     *
     * @param request 请求信息
     * @return 第一个属性：租户、应用编码；第二个属性：请求类型
     */
    @Override
    public ImmutablePair<AppInfoDTO, RequestTypeEnum> parse(HttpServletRequest request) {
        if (supports(request)) {
            return ImmutablePair.of(null, RequestTypeEnum.NON_BUSINESS);
        }
        return null;
    }

    /**
     * 解析请求类型信息
     *
     * @param request 请求信息
     * @return 请求分类
     */
    @Override
    public RequestTypeEnum parseType(HttpServletRequest request) {
        if (supports(request)) {
            return RequestTypeEnum.NON_BUSINESS;
        }
        return null;
    }
}

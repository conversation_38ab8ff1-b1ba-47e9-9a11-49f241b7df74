package cn.hy.auth.common.security.oauth2.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 类描述：oauth2 token服务的参数配置
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/13 10:39
 **/
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "hy.security.oauth2.token")
public class TokenServicesProperties {
    /**
     * access_token有效期 默认12小时.如果client有设置，则优先用client里的设置
     */
    private int accessTokenValiditySeconds = 60 * 60 * 12;
    /**
     * refresh_token有效期 默认30 天。如果client有设置，则优先用client里的设置
     */
    private int refreshTokenValiditySeconds = 60 * 60 * 24 * 30;
    /**
     * 是否重用reuseRefreshToken，默认为true
     */
    private boolean reuseRefreshToken = true;
    /**
     * 是否允许client模式下刷新token，默认为true
     */
    private boolean allowRefresh = true;
}

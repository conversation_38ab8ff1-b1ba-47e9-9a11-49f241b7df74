package cn.hy.auth.common.security.core.authentication.mobile.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @title: PwdChangeIntervalEvent
 * @description: 密码更换时间间隔事件
 * @date 2024/4/28
 */
@Getter
public class PwdChangeIntervalEvent extends ApplicationEvent {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否校验通过
     */
    private boolean checkPass;

    /**
     * 信息
     */
    private String msg;

    public PwdChangeIntervalEvent(String mobile) {
        super(mobile);
        this.mobile = mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setCheckPass(boolean checkPass) {
        this.checkPass = checkPass;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}

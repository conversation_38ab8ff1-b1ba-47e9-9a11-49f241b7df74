package cn.hy.auth.boot.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;


/**
 * 类描述: 配置允许跨域访问
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/16
 */
@Configuration
public class CorsFilterConfiguration {
    /**
     * 设置允许跨域访问
     *
     * @return 返回
     */
    @Bean
    public FilterRegistrationBean corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置所有地址的请求都可以
        config.addAllowedOrigin("*");
        // 设置为允许所有请求头信息
        config.addAllowedHeader("*");
        // 设置为支持所有请求方式
        config.addAllowedMethod("*");
        // 设置所有的请求路径都可以访问
        source.registerCorsConfiguration("/**", config);
        FilterRegistrationBean bean = new FilterRegistrationBean(new CorsFilter(source));
        //设置优先级为最高
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    }
}

package cn.hy.auth.custom.user.history.mapstruct;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.user.history.domain.UserLoginInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/17
 */
@Mapper
public interface LoginInfoStructMapper {

    public static final LoginInfoStructMapper INSTANCE = Mappers.getMapper(LoginInfoStructMapper.class);

    /**
     * dto装换成do
     *
     * @param userLoginInfoDTO dto对象
     * @return 返回对象
     */
    UserLoginInfoDO dtoTodo(UserLoginInfoDTO userLoginInfoDTO);

    /**
     * do装换成dto
     *
     * @param loginInfoDO do对象
     * @return 返回对象
     */
    UserLoginInfoDTO doToDto(UserLoginInfoDO loginInfoDO);
}

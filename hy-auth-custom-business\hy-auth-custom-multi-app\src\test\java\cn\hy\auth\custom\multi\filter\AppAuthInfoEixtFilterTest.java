package cn.hy.auth.custom.multi.filter;

import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.domain.authinfo.*;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.multi.authinfo.dao.AppAuthStrategyDao;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManagerImpl;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {AppAuthInfoEixtFilter.class, AppAuthStrategyManagerImpl.class})
public class AppAuthInfoEixtFilterTest {

    @MockBean
    CacheManager cacheManager;
    @MockBean
    AppAuthStrategyDao appAuthInfoDao;
    @Autowired
    AppAuthInfoEixtFilter appAuthInfoEixtFilter;
    @MockBean
    AppInfoParseProxy appInfoParseProxy;
    @SpyBean
    private AppAuthStrategyManager authInfoManager;


    HttpServletRequest request;
    HttpServletResponse response;
    FilterChain filterChain;

    String lesseeCode = "hy";
    String appCode = "iot";

    @Before
    public void setUp() throws Exception {
        Logger.getGlobal().setLevel(Level.ALL);

        request = Mockito.mock(HttpServletRequest.class);
        response = Mockito.mock(HttpServletResponse.class);
        filterChain = Mockito.mock(FilterChain.class);

        doReturn(mockAppAuthInfoJson()).when(appAuthInfoDao).getAppAuthStrategyJson();
        doReturn(lesseeCode).when(request).getParameter(eq("lessee_code"));
        doReturn(appCode).when(request).getParameter(eq("app_code"));
    }

    @Test
    public void doFilterInternal() {
        try {
            appAuthInfoEixtFilter.doFilterInternal(request, response, filterChain);

            verify(authInfoManager, times(1)).get();
            verify(filterChain, times(1)).doFilter(request, response);

        } catch (ServletException | IOException e) {
            throw new RuntimeException();
        }
    }


    private String mockAppAuthInfoJson() {
        AppAuthStrategyDTO appAuthInfoDTO = new AppAuthStrategyDTO();
        appAuthInfoDTO.setLesseeAccessName(lesseeCode);
        appAuthInfoDTO.setAppAccessName(appCode);
        appAuthInfoDTO.setLoginRule(new AppAuthLoginRuleDTO());
        appAuthInfoDTO.getLoginRule().setCompetitionPolicy(new AppAuthCompetitionPolicyDTO());
        appAuthInfoDTO.getLoginRule().getCompetitionPolicy().setSameClientTypePolicy("forceLogin").setEnable(true);

        appAuthInfoDTO.getLoginRule()
                .setLoginSupportType(Lists.newArrayList(LoginTypeEnum.OAUTH2_PASSWORD, LoginTypeEnum.USERNAME_PASSWORD,
                        LoginTypeEnum.SMS_CODE, LoginTypeEnum.OAUTH2_CLIENT));

        appAuthInfoDTO.getLoginRule()
                .setLoginField(Lists.newArrayList(new AppAuthLoginFieldDTO("user_account_name", "", 1),
                        new AppAuthLoginFieldDTO("mobile", "", 1)));

        appAuthInfoDTO.setLogoutRule(new AppAuthLogoutRuleDTO());
        appAuthInfoDTO.setLicenseRule(new AppAuthLicenseRuleDTO());
        appAuthInfoDTO.setPwdRule(new AppAuthPwdRuleDTO());

        appAuthInfoDTO.setUserAccountMapping(new AppAuthUserAccountMapping());
        appAuthInfoDTO.getUserAccountMapping().setTableName("user_account");
        appAuthInfoDTO.getUserAccountMapping().setUid("id");
        appAuthInfoDTO.getUserAccountMapping().setPassword("password");
        appAuthInfoDTO.getUserAccountMapping().setUsername("user_account_name");
        appAuthInfoDTO.getUserAccountMapping().setMobile("mobile");
        appAuthInfoDTO.getUserAccountMapping().setEmail("email");

        appAuthInfoDTO.setUserInfoMapping(new ArrayList<>());

        AppAuthUserInfoMapping appAuthUserInfoMapping = new AppAuthUserInfoMapping();
        appAuthUserInfoMapping.setTableName("user_account");
        appAuthUserInfoMapping.setUid("id");
        appAuthUserInfoMapping.setIdentifyCode("user");
        appAuthInfoDTO.getUserInfoMapping().add(appAuthUserInfoMapping);

        return JSONObject.toJSONString(appAuthInfoDTO);
    }
}
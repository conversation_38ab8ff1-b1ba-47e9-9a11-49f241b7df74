package cn.hy.auth.custom.security.reauthentication.utils;

import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.ByteArrayInputStream;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * sm2证书工具
 * <AUTHOR>
 * @date 2023/10/24 13:36
 */
public class Sm2Util {

    /**
     * 导入x509证书
     * @param cert pem的字符串内容
     */
    public static X509Certificate importX509Cert(String cert) throws CertificateException, NoSuchProviderException {
        // 引入BC库
        Security.addProvider(new BouncyCastleProvider());
        // 使用BC解析X.509证书
        String pemCertificateString = "-----BEGIN CERTIFICATE-----\n" +
                cert+ "\n-----END CERTIFICATE-----";
        byte[] pemCertificateBytes = pemCertificateString.getBytes();
        ByteArrayInputStream byteInputStream = new ByteArrayInputStream(pemCertificateBytes);
        CertificateFactory CF = CertificateFactory.getInstance("X.509", "BC"); // 从证书工厂中获取X.509的单例类
        Certificate C = CF.generateCertificate(byteInputStream);
        return (X509Certificate)C;
    }

    /**
     * sm2证书验签
     */
    public static boolean verify(PublicKey pk,String sourceData,String base64Signature) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
        // 生成SM2sign with sm3 签名验签算法实例
        Signature signature = Signature.getInstance(
                GMObjectIdentifiers.sm2sign_with_sm3.toString()
                , new BouncyCastleProvider());
        /*
         * 验签
         */
        // 签名需要使用公钥，使用公钥 初始化签名实例
        signature.initVerify(pk);
        // 写入待验签的签名原文到算法中
        signature.update(sourceData.getBytes());
        // 验签
        return signature.verify(Base64.getDecoder().decode(base64Signature));
    }
}

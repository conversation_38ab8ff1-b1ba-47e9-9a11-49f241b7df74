package cn.hy.auth.custom.user.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @title: UsmPwdChangeEnum
 * @description: 密码更换时间间隔
 * @date 2024/1/17
 */
@Getter
@AllArgsConstructor
public enum UsmPwdChangeEnum {

    NEVER("0", "永不"),

    ONE_MONTH("1", "一个月"),

    TWO_MONTH("2", "两个月"),

    THREE_MONTH("3", "三个月"),

    HALF_A_YEAR("4", "半年"),

    YEAR("5", "一年");

    private final String code;

    private final String name;

    public static UsmPwdChangeEnum getByCode(String code) {
        for (UsmPwdChangeEnum value : UsmPwdChangeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NEVER;
    }

}

package cn.hy.auth.custom.user.account.domain;

import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类描述: 修改密码对象
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "修改密码对象")
public class UserForgetPwdUpdateDTO {

    /**
     * 登录名
     */
    @ApiModelProperty(value = "登录名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 新密码
     */
    @ApiModelProperty(value = "新密码")
    @NotBlank(message = "新密码不允许为空")
    private String newPwd;

    /**
     * 确认密码
     */
    @ApiModelProperty(value = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    private String insurePwd;

    @ApiModelProperty(value = "临时权限字符串")
    @NotBlank(message = "临时权限字符串不能为空")
    private String tempPermissionIdentification;

    /**
     * 传输密码时使用到的密码加密方式：1为密码明文，2为帐号名明文+密码明文加密，3为密码明文加密
     *
     * @see EncryptTypeEnum
     */
    @ApiModelProperty(value = "密码加密方式")
    @NotNull(message = "密码加密方式不允许为空")
    @Range(min = 1,max = 3,message = "密码加密方式只允许输入1(密码明文),2(帐号名明文+密码明文加密),3(密码明文加密)")
    private Integer pwdEncryptionType;
    /**
     *  是否是paasPwd
     */
//    private Boolean paasPwd = false;
}

package cn.hy.auth.common.security.oauth2.token.store;

import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import cn.hy.auth.common.security.oauth2.token.TokenStoreExtends;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.DefaultAuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 类描述： 内存的方式存储token信息。
 * 新增刷新token时，旧token保留一段时间后再移除的功能，解决刷新token有并发请求报token过期问题。
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/1/5 15:37
 **/
@Slf4j
public class HyInMemoryTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener<TokenRevokedEvent> {


    private final ConcurrentHashMap<String, OAuth2AccessToken> removingAccessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Collection<OAuth2AccessToken>> removingUserNameToAccessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Collection<OAuth2AccessToken>> removingClientIdToAccessTokenStore = new ConcurrentHashMap<>();

    private static final int DEFAULT_FLUSH_INTERVAL = 1000;

    private final ConcurrentHashMap<String, OAuth2AccessToken> accessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, OAuth2AccessToken> authenticationToAccessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Collection<OAuth2AccessToken>> userNameToAccessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Collection<OAuth2AccessToken>> clientIdToAccessTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, OAuth2RefreshToken> refreshTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, String> accessTokenToRefreshTokenStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, OAuth2Authentication> authenticationStore = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, OAuth2Authentication> removingAuthenticationStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, OAuth2Authentication> refreshTokenAuthenticationStore = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, String> refreshTokenToAccessTokenStore = new ConcurrentHashMap<>();

    private final DelayQueue<TokenExpiry> expiryQueue = new DelayQueue<>();
    private final DelayQueue<TokenExpiry> removingExpiryQueue = new DelayQueue<>();
    private final ConcurrentHashMap<String, TokenExpiry> expiryMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, TokenExpiry> removingExpiryMap = new ConcurrentHashMap<>();

    private int flushInterval = DEFAULT_FLUSH_INTERVAL;

    private AuthenticationKeyGenerator authenticationKeyGenerator = new DefaultAuthenticationKeyGenerator();

    private AtomicInteger flushCounter = new AtomicInteger(0);
    private AtomicInteger flushRemovingCounter = new AtomicInteger(0);
    /**
     * 已移除token的保留时长，单位ms.默认30s
     */
    private int removingDelta = 30 * 1000;

    public int getRemovingDelta() {
        return removingDelta;
    }

    public void setRemovingDelta(int removingDelta) {
        Assert.isTrue(removingDelta>0, "removingDelta must be greater than 0");
        this.removingDelta = removingDelta;
    }

    /**
     * The number of tokens to store before flushing expired tokens. Defaults to 1000.
     *
     * @param flushInterval the interval to set
     */
    public void setFlushInterval(int flushInterval) {
        this.flushInterval = flushInterval;
    }

    /**
     * The interval (count of token inserts) between flushing expired tokens.
     *
     * @return the flushInterval the flush interval
     */
    public int getFlushInterval() {
        return flushInterval;
    }

    /**
     * Convenience method for super admin users to remove all tokens (useful for testing, not really in production)
     */
    public void clear() {
        accessTokenStore.clear();
        authenticationToAccessTokenStore.clear();
        clientIdToAccessTokenStore.clear();
        refreshTokenStore.clear();
        accessTokenToRefreshTokenStore.clear();
        authenticationStore.clear();
        refreshTokenAuthenticationStore.clear();
        refreshTokenToAccessTokenStore.clear();
        expiryQueue.clear();
        removingExpiryQueue.clear();
        //
        removingAccessTokenStore.clear();
        removingClientIdToAccessTokenStore.clear();
        removingUserNameToAccessTokenStore.clear();
        removingAuthenticationStore.clear();
    }

    public void setAuthenticationKeyGenerator(AuthenticationKeyGenerator authenticationKeyGenerator) {
        this.authenticationKeyGenerator = authenticationKeyGenerator;
    }

    public int getAccessTokenCount() {
        Assert.state(accessTokenStore.isEmpty() || accessTokenStore.size() >= accessTokenToRefreshTokenStore.size(),
                "Too many refresh tokens");
        Assert.state(accessTokenStore.size() == authenticationToAccessTokenStore.size(),
                "Inconsistent token store state");
        Assert.state(accessTokenStore.size() <= authenticationStore.size(), "Inconsistent authentication store state");
        return accessTokenStore.size();
    }

    public int getRefreshTokenCount() {
        Assert.state(refreshTokenStore.size() == refreshTokenToAccessTokenStore.size(),
                "Inconsistent refresh token store state");
        return accessTokenStore.size();
    }

    public int getExpiryTokenCount() {
        return expiryQueue.size();
    }

    /**
     *
     * @param authentication .
     * @return .
     */
    @Override
    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
        String key = authenticationKeyGenerator.extractKey(authentication);
        OAuth2AccessToken accessToken = authenticationToAccessTokenStore.get(key);
        if (accessToken != null
                && !key.equals(authenticationKeyGenerator.extractKey(readAuthentication(accessToken.getValue())))) {
            // Keep the stores consistent (maybe the same user is represented by this authentication but the details
            // have changed)
            storeAccessToken(accessToken, authentication);
        }
        return accessToken;
    }

    /**
     *
     * @param token .
     * @return .
     */
    @Override
    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
        log.debug(" start readAuthentication {}",token);
        OAuth2Authentication oAuth2Authentication = readAuthentication(token.getValue());
        log.debug(" end readAuthentication {}",token);
        return oAuth2Authentication;
    }

    /**
     *
     * @param token .
     * @return .
     */
    @Override
    public OAuth2Authentication readAuthentication(String token) {
        OAuth2Authentication oAuth2Authentication = this.authenticationStore.get(token);
        if (oAuth2Authentication == null){
            log.debug("readAuthentication oAuth2Authentication,token:【{}】",token);
            return this.removingAuthenticationStore.get(token);
        }
        return oAuth2Authentication;
    }

    @Override
    public OAuth2Authentication readAuthenticationForRefreshToken(OAuth2RefreshToken token) {
        return readAuthenticationForRefreshToken(token.getValue());
    }

    public OAuth2Authentication readAuthenticationForRefreshToken(String token) {
        return this.refreshTokenAuthenticationStore.get(token);
    }

    /**
     *
     * @param token .
     * @param authentication .
     */
    @Override
    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        if (this.flushCounter.incrementAndGet() >= this.flushInterval) {
            flush();
            this.flushCounter.set(0);
        }
        this.accessTokenStore.put(token.getValue(), token);
        this.authenticationStore.put(token.getValue(), authentication);
        this.authenticationToAccessTokenStore.put(authenticationKeyGenerator.extractKey(authentication), token);
        if (!authentication.isClientOnly()) {
            addToCollection(this.userNameToAccessTokenStore, getApprovalKey(token.getValue(),authentication), token);
        }
        addToCollection(this.clientIdToAccessTokenStore, authentication.getOAuth2Request().getClientId(), token);
        if (token.getExpiration() != null) {
            TokenExpiry expiry = new TokenExpiry(token.getValue(), token.getExpiration());
            // Remove existing expiry for this token if present
            expiryQueue.remove(expiryMap.put(token.getValue(), expiry));
            this.expiryQueue.put(expiry);
        }
        if (token.getRefreshToken() != null && token.getRefreshToken().getValue() != null) {
            this.refreshTokenToAccessTokenStore.put(token.getRefreshToken().getValue(), token.getValue());
            this.accessTokenToRefreshTokenStore.put(token.getValue(), token.getRefreshToken().getValue());
        }
    }

    private  String getApprovalKey(String tokenValue,OAuth2Authentication authentication) {
        // 增加租户号前缀，防止不同租户的用户名混淆，比如admin
        String lessCode = tokenValue!=null && tokenValue.split("\\.").length> 3 ? tokenValue.split("\\.")[0]+"_" : "";
        String userName = authentication.getUserAuthentication() == null ? ""
                : lessCode+authentication.getUserAuthentication().getName();
        return getApprovalKey(authentication.getOAuth2Request().getClientId(), userName);
    }
//    private String getApprovalKey(OAuth2Authentication authentication) {
//        String userName = authentication.getUserAuthentication() == null ? "" : authentication.getUserAuthentication()
//                .getName();
//        return getApprovalKey(authentication.getOAuth2Request().getClientId(), userName);
//    }

    private String getApprovalKey(String clientId, String userName) {
        return clientId + (userName == null ? "" : ":" + userName);
    }

    private void addToCollection(ConcurrentHashMap<String, Collection<OAuth2AccessToken>> store, String key,
                                 OAuth2AccessToken token) {
        if (!store.containsKey(key)) {
            synchronized (store) {
                if (!store.containsKey(key)) {
                    store.put(key, new HashSet<>());
                }
            }
        }
        store.get(key).add(token);
    }

    /**
     *
     * @param tokenValue .
     * @return .
     */
    @Override
    public OAuth2AccessToken readAccessToken(String tokenValue) {
        log.debug("readAccessToken start {}",tokenValue);
        OAuth2AccessToken oAuth2AccessToken = this.accessTokenStore.get(tokenValue);
        if (oAuth2AccessToken == null){
            log.debug("从待移除集合readAccessToken accessToken,tokenValue:【{}】",tokenValue);
            oAuth2AccessToken = this.removingAccessTokenStore.get(tokenValue);
        }
        log.debug("readAccessToken end {}",tokenValue);
        return oAuth2AccessToken;

    }

    /**
     *
     * @param refreshToken .
     * @param authentication .
     */
    @Override
    public void storeRefreshToken(OAuth2RefreshToken refreshToken, OAuth2Authentication authentication) {
        this.refreshTokenStore.put(refreshToken.getValue(), refreshToken);
        this.refreshTokenAuthenticationStore.put(refreshToken.getValue(), authentication);
    }

    /**
     *
     * @param tokenValue .
     * @return .
     */
    @Override
    public OAuth2RefreshToken readRefreshToken(String tokenValue) {
        return this.refreshTokenStore.get(tokenValue);
    }

    @Override
    public void removeRefreshToken(OAuth2RefreshToken refreshToken) {
        removeRefreshToken(refreshToken.getValue());
    }

    public void removeRefreshToken(String tokenValue) {
        this.refreshTokenStore.remove(tokenValue);
        this.refreshTokenAuthenticationStore.remove(tokenValue);
        this.refreshTokenToAccessTokenStore.remove(tokenValue);
    }

    @Override
    public void removeAccessTokenUsingRefreshToken(OAuth2RefreshToken refreshToken) {
        removeAccessTokenUsingRefreshToken(refreshToken.getValue());
    }

    private void removeAccessTokenUsingRefreshToken(String refreshToken) {
        String accessToken = this.refreshTokenToAccessTokenStore.remove(refreshToken);
        if (accessToken != null) {
            removeAccessToken(accessToken);
        }
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
        // username需要携带"租户号_"
        String userNameWithClientIdAndLessCode = getApprovalKey(clientId, userName);
        Collection<OAuth2AccessToken> result = userNameToAccessTokenStore.get(userNameWithClientIdAndLessCode);
        if (result == null){
            log.debug("从待移除集合中findTokensByClientIdAndUserName accessToken,clientId:【{}】,userName:【{}】 ",clientId,userName);
            result = removingUserNameToAccessTokenStore.get(userNameWithClientIdAndLessCode);
        }
        return result != null ? Collections.<OAuth2AccessToken>unmodifiableCollection(result) : Collections
                .<OAuth2AccessToken>emptySet();
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
        Collection<OAuth2AccessToken> result = clientIdToAccessTokenStore.get(clientId);
        if (result == null){
            log.debug("从待移除集合中findTokensByClientId accessToken,clientId:【{}】",clientId);
            result = removingClientIdToAccessTokenStore.get(clientId);
        }
        return result != null ? Collections.<OAuth2AccessToken>unmodifiableCollection(result) : Collections
                .<OAuth2AccessToken>emptySet();
    }

    /**
     * 清理过期token
     */
    private void flush() {
        TokenExpiry expiry = expiryQueue.poll();
        while (expiry != null) {
            removeAccessToken(expiry.getValue());
            expiry = expiryQueue.poll();
        }
    }
    /**
     * 清理过期token
     */
    private void flushRemoving() {
        TokenExpiry expiry = removingExpiryQueue.poll();
        while (expiry != null) {
            removeWaitingAccessToken(expiry.getValue());
            expiry = removingExpiryQueue.poll();
        }
    }
    @Override
    public void onApplicationEvent(TokenRevokedEvent tokenRevokedEvent) {
        log.debug("接收用户退出登录时间:tokenId = [{}],result = [{}]", tokenRevokedEvent.getTokenValue(), tokenRevokedEvent.isRevokeToken());
        if (tokenRevokedEvent.isRevokeToken()){
            removeWaitingAccessToken(tokenRevokedEvent.getTokenValue());
            flushRemoving();
        }
    }

    @Override
    public Set<String> getNoExpiredTokenUserNames() {
        Set<String> set = new HashSet<>();
        for (Map.Entry entry : this.accessTokenStore.entrySet()) {
            OAuth2AccessToken accessToken = (OAuth2AccessToken) entry.getValue();
            if (accessToken.getExpiration().after(new Date())) {
                OAuth2Authentication oAuth2Authentication = this.authenticationStore.get(entry.getKey());
                String userName = oAuth2Authentication.getName();
                if (StringUtils.isNotBlank(userName)) {
                    set.add(userName);
                }
            }
        }
        return set;
    }

    private static class TokenExpiry implements Delayed {

        private final long expiry;

        private final String value;

        public TokenExpiry(String value, Date date) {
            this.value = value;
            this.expiry = date.getTime();
        }

        @Override
        public int compareTo(Delayed other) {
            if (this == other) {
                return 0;
            }
            long diff = getDelay(TimeUnit.MILLISECONDS) - other.getDelay(TimeUnit.MILLISECONDS);
            return (diff == 0 ? 0 : ((diff < 0) ? -1 : 1));
        }

        @Override
        public long getDelay(TimeUnit unit) {
            return expiry - System.currentTimeMillis();
        }

        public String getValue() {
            return value;
        }

    }




    @Override
    public void removeAccessToken(OAuth2AccessToken accessToken) {
        removeAccessToken(accessToken.getValue());
    }

    /**
     *  移除AccessToken
     * @param tokenValue .
     */
    private void removeAccessToken(String tokenValue) {
        this.expiryMap.remove(tokenValue);
        OAuth2AccessToken removed = this.accessTokenStore.remove(tokenValue);
        this.accessTokenToRefreshTokenStore.remove(tokenValue);
        // Don't remove the refresh token - it's up to the caller to do that
        OAuth2Authentication authentication = this.authenticationStore.remove(tokenValue);
        if (authentication != null) {
            this.authenticationToAccessTokenStore.remove(authenticationKeyGenerator.extractKey(authentication));
            Collection<OAuth2AccessToken> tokens;
            String clientId = authentication.getOAuth2Request().getClientId();
            tokens = this.userNameToAccessTokenStore.get(getApprovalKey(removed.getValue(), authentication));
            if (tokens != null) {
                tokens.remove(removed);
            }
            tokens = this.clientIdToAccessTokenStore.get(clientId);
            if (tokens != null) {
                tokens.remove(removed);
            }
            this.authenticationToAccessTokenStore.remove(authenticationKeyGenerator.extractKey(authentication));
        }
        // 先清空该token的历史记录
        removeWaitingAccessToken(tokenValue);
        // add by fxr
        if (removed != null && !removed.isExpired()) {
            if (this.flushRemovingCounter.incrementAndGet() >= this.flushInterval) {
                flushRemoving();
                this.flushRemovingCounter.set(0);
            }

            log.debug("token：【{}】，【{}】 没有过期，放入待移除区域中。",removed.getValue(),removed.getExpiresIn());
            ((DefaultOAuth2AccessToken)removed).setExpiration(new Date(System.currentTimeMillis() + removingDelta));
            log.debug("token：【{}】，重新设置过期时间为：【{}】",removed.getValue(),removed.getExpiresIn());
            // 没有过期，存放在removing集合中
            this.removingAccessTokenStore.put(tokenValue, removed);
            if (authentication != null) {
                this.removingAuthenticationStore.put(tokenValue, authentication);
                String clientId = authentication.getOAuth2Request().getClientId();
                addToCollection(removingUserNameToAccessTokenStore,getApprovalKey(removed.getValue(), authentication),removed);
                addToCollection(removingClientIdToAccessTokenStore,clientId,removed);
            }
            TokenExpiry expiry = new TokenExpiry(removed.getValue(), removed.getExpiration());
            // Remove existing expiry for this token if present
            removingExpiryQueue.remove(removingExpiryMap.put(removed.getValue(), expiry));
            this.removingExpiryQueue.put(expiry);

        }

    }

    private void removeWaitingAccessToken(String tokenValue) {
        this.removingExpiryMap.remove(tokenValue);
        this.removingAccessTokenStore.remove(tokenValue);
        OAuth2Authentication authentication = this.removingAuthenticationStore.remove(tokenValue);
        if (authentication != null) {
            removingUserNameToAccessTokenStore.remove(getApprovalKey(tokenValue, authentication));
            removingClientIdToAccessTokenStore.remove(getApprovalKey(tokenValue, authentication));
        }
    }


}

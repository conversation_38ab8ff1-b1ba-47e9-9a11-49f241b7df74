package cn.hy.auth.custom.common.enums;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import lombok.AllArgsConstructor;

/**
 * 类描述: 认证中心错误码枚举
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/16
 */
@AllArgsConstructor
public enum AuthErrorCodeEnum {

    /**
     * 数据不存在
     */
    SUCCESS("00000", "请求处理成功"),
    /**
     * 请求参数缺失
     */
    A0101("A0101", "请求参数缺失"),
    /**
     * 请求参数缺失
     */
    A0102("A0102", "租户应用信息缺失"),
    /**
     * 请求参数非法
     */
    A0103("A0103", "非法请求"),
    /**
     * access_token不存在
     */
    A0342("A0342", "access_token不存在"),
    /**
     * refresh_token不存在
     */
    A0343("A0343", "refresh_token不存在"),

    /**
     * 用户登陆已过期
     */
    A0311("A0311", "用户登陆已过期"),

    /**
     * 旧密码不正确
     */
    A0120("A0120", "旧密码不正确"),

    /**
     * 新密码不能和旧密码一样
     */
    A0123("A0123", "新密码不能和旧密码一样"),

    /**
     * 不允许修改非本账号密码
     */
    A0124("A0124", "不允许修改非本账号密码"),

    /**
     * 加密方式帐号名明文+密码明文加密异常
     */
    A0125("A0125", "加密方式帐号名明文+密码明文加密校验异常"),

    A0126("A0126", "不允许修改服务内置账号密码"),

    /**
     * 用户账户不存在
     */
    A0201("A0201", "用户账户不存在"),
    /**
     * 用户账户不存在
     */
    A0202("A0202", "更新用户信息必须指定用户主键"),
    /**
     * 用户账户异常
     */
    A0203("A0203", "用户账户异常"),
    /**
     * 登录时用户信息错误
     */
    A0204("A0204", "用户名或密码错误"),

    /**
     * 密码错误次数达到配置值需要验证码
     */
    A0205("A0205", "需要验证码"),

    /**
     * 访问权限异常
     */
    A0300("A0300", "访问权限异常"),

    /**
     * 密码已过期异常
     */
    A0242("A0242", "密码已过期，请修改"),
    /**
     * 获取token异常
     */
    A0243("A0243", "跳转到修改页面之前获取token失败"),
    /**
     * 默认密码，请立即修改
     */
    A0245("A0245", "您的密码为默认密码，请立即修改"),

    /**
     * 一键登录失败，请登录绑定账号
     */
    A0246("A0246", "一键登录失败，请登录绑定账号"),
    /**
     * 密码更换时间间隔
     */
    A0247("A0247", "密码已到达指定修改时间，请修改"),
    /**
     * 绑定用户登录ip
     */
    A0248("A0248", "您的登录IP与绑定的IP不一致，请检查"),
    /**
     * 地址不在服务范围
     */
    A0422("A0422", "地址不在服务范围"),

    /**
     * 用户请求参数错误
     */
    A0400("A0400", "用户请求参数错误"),
    /**
     * token 已被踢出
     */
    A0401("A0401", "token 已被踢出"),
    /**
     * 用户输入内容非法
     */
    A0427("A0427", "请求 JSON 解析失败"),
    /**
     * 系统资源异常
     */
    B0300("B0300", "系统资源异常"),
    /**
     * 应用认证规则异常
     */
    B0301("B0301", "加载应用认证规则失败"),

    /**
     * 数据不存在
     */
    C0319("C0319", "数据不存在"),
    /**
     * 调用第三方服务出错
     */
    C0001("C0001", "调用第三方服务出错"),

    /**
     * 系统错误
     */
    B0001("B0001", "系统执行出错")
    ;

    /**
     * 响应码
     */
    private final String code;
    /***
     *  响应信息
     */
    private final String msg;

    /**
     * 返回异常编码
     *
     * @return 返回异常编码
     */
    public String code() {
        return code;
    }

    /**
     * 返回异常信息
     *
     * @return 返回异常信息
     */
    public String msg() {
        return LocaleUtil.getMessage("AuthErrorCodeEnum." + this.name(), this.msg);
    }

    @Override
    public String toString() {
        return "AuthErrorCodeEnum{" +
                "code='" + code + '\'' +
                ", msg='" + LocaleUtil.getMessage("AuthErrorCodeEnum." + this.name(), null) + '\'' +
                '}';
    }
}

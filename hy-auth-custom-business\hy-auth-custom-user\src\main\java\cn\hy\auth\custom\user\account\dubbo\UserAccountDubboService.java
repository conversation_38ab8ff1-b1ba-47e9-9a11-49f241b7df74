package cn.hy.auth.custom.user.account.dubbo;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.common.security.oauth2.token.DefaultTokenServicesWrapper;
import cn.hy.auth.custom.common.api.UserAccountApi;
import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.LoginStateDTO;
import cn.hy.auth.custom.common.domain.LoginUserHistoryDTO;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import cn.hy.auth.custom.user.account.controller.UserAccountController;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationManager;
import org.springframework.security.oauth2.provider.token.*;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Map;

/**
 * 类描述：dubbo 接口实现。
 * 已过期，兼容历史版，不再进行新的扩展
 * @see cn.hy.auth.sdk.service.UserAccountApi
 * <AUTHOR> by fuxinrong
 * @date 2023/2/8 10:21
 **/
@DubboService
@Slf4j
@Deprecated
public class UserAccountDubboService implements UserAccountApi {
    /**
     * token最少分片数
     */
    private static final int MIN_TOKEN_PART = 3;
    private final UserAccountController userAccountController;
    private final OAuth2AuthenticationManager oAuth2AuthenticationManager;
    private final UserCacheService userCacheService;
    private final ResourceServerTokenServices resourceServerTokenServices;
    @Autowired
    @Lazy
    private AppAuthStrategyManager authInfoManager;

    private final AccessTokenConverter accessTokenConverter = new DefaultAccessTokenConverter();
    public UserAccountDubboService(UserAccountController userAccountController,  UserCacheService userCacheService,TokenStore tokenStore,
                                   TokenServicesProperties tokenServicesProperties,
                                   ApplicationContext applicationContext,
                                   ClientDetailsService clientDetailsService) {
        this.userAccountController = userAccountController;
        this.userCacheService = userCacheService;
        this.resourceServerTokenServices = createTokenService(tokenStore, tokenServicesProperties, applicationContext, clientDetailsService);
        this.oAuth2AuthenticationManager = init(resourceServerTokenServices);
        log.info("创建了UserAccountDubboService");
    }

    private OAuth2AuthenticationManager init(ResourceServerTokenServices resourceServerTokenServices){
        OAuth2AuthenticationManager oauthAuthenticationManager = new OAuth2AuthenticationManager();
        oauthAuthenticationManager.setTokenServices(resourceServerTokenServices);
       return oauthAuthenticationManager;

    }
    @Override
    public Map<String, Object> checkToken(String value) {
        initCheckTokenContext(value);
        OAuth2AccessToken token = resourceServerTokenServices.readAccessToken(value);
        if (token == null) {
            throw new InvalidTokenException("Token was not recognised");
        }

        if (token.isExpired()) {
            throw new InvalidTokenException("Token has expired");
        }

        OAuth2Authentication authentication = resourceServerTokenServices.loadAuthentication(token.getValue());

        Map<String, Object> response = (Map<String, Object>)accessTokenConverter.convertAccessToken(token, authentication);

        // gh-1070
        response.put("active", true);	// Always true if token exists and not expired

        return response;
    }

    @Override
    public UserAccountDTO getCurrentUserAccount(String token) {
        initContext("/user/current",token);
        return userAccountController.getCurrentUserAccount();
    }

    @Override
    public Map<String, Object> getCurrentAccount(String token) {
        initContext("/user/account/current",token);
        return userAccountController.getCurrentAccount();
    }

    @Override
    public Map<String, Object> getCurrentAssociationUser(String token) {
        initContext("/user/association/current",token);
        return userAccountController.getCurrentAssociationUser();
    }

    @Override
    public LoginUserHistoryDTO getCurrentUserWithLoginInfo(String token) {
        initContext("/user/currentLoginUser",token);
        return userAccountController.getCurrentUserWithLoginInfo();
    }

    @Override
    public UserLoginInfoDTO getLastSuccessLoginInfo(String token) {
        initContext("/history/login/last",token);
        return userCacheService.getUserLoginInfoByTokenId(token);
    }

    private void initCheckTokenContext(String tokenValue){
        setAuthContext(tokenValue);
        setClientType(tokenValue);
        //parseTokenResultToSecurityContext(tokenValue);
        initLogPrefix("/oauth/check_token",tokenValue);
        setAppAuthStrategy();
    }
    private void initContext(String uri,String tokenValue){
        setAuthContext(tokenValue);
        setClientType(tokenValue);
        parseTokenResultToSecurityContext(tokenValue);
        initLogPrefix(uri,tokenValue);
        setAppAuthStrategy();
    }

    private void initLogPrefix(String uri,String tokenId) {
        AuthContext.getContext().set("_uri_",uri);
        AuthContext.getContext().set("_requestId_", UUIDRandomUtils.getUuid(32));
        String loginName = LoginUtil.getLoginName();
        if (StringUtils.isBlank(loginName) && StringUtils.isNotBlank(tokenId)){
            OAuth2Authentication oAuth2Authentication = resourceServerTokenServices.loadAuthentication(tokenId);
            if (oAuth2Authentication!=null){
                loginName = oAuth2Authentication.getName();
            }
        }
        AuthContext.getContext().set("_login_name_", loginName);
    }

    private void parseTokenResultToSecurityContext(String tokenValue){
        if (StringUtils.isBlank(tokenValue)){
            throw new InvalidTokenException("Invalid token (token not found)");
        }
        Authentication authResult = oAuth2AuthenticationManager.authenticate(new PreAuthenticatedAuthenticationToken(tokenValue, ""));
        SecurityContextHolder.getContext().setAuthentication(authResult);
    }

    private void setAuthContext(String tokenValue){
        AuthContext context = AuthContext.getContext();
        AppInfoDTO appInfoDTO = parseOfToken(tokenValue);
        if (appInfoDTO != null){
            context.setLesseeCode(appInfoDTO.getLesseeCode()).setAppCode(appInfoDTO.getAppCode());
        } else {
            throw new InvalidTokenException(LocaleUtil.getMessage("UserAccountDubboService.result.msg1", null)+tokenValue);
        }

    }
    private void setAppAuthStrategy(){
        AppAuthStrategyDTO authInfoDTO = new AppAuthStrategyDTO();
        if (authInfoManager != null) {
            authInfoDTO = authInfoManager.get();
        }
        AuthContext.getContext().setAppAuthStrategy(authInfoDTO);
    }

    private AppInfoDTO parseOfToken(String tokenId) {
        if (StringUtils.isBlank(tokenId)) {
            log.error("token为空，解析租户和应用信息失败。");
            return null;
        }
        tokenId = tokenId.trim();
        String[] tokenPart = tokenId.split("\\.");
        if (tokenPart.length < MIN_TOKEN_PART) {
            log.error("token内容不合法，解析租户和应用信息失败。tokenId：【{}】", tokenId);
            return null;
        }

        return AppInfoDTO.builder().lesseeCode(tokenPart[0]).appCode(tokenPart[1]).build();
    }
    /**
     * 获取登录类型
     *
     * @param token     ..
     */
    private void setClientType(String token) {
        if (StringUtils.isBlank(token)) {
            return;
        }
        String[] tokenPart = token.split("\\.");
        if (tokenPart.length < 4) {
            return;
        }
        LoginStateDTO loginStateDTO = new LoginStateDTO();
        loginStateDTO.setClientType(ClientTypeEnum.codeOf(tokenPart[2]));
        AuthContext.getContext().setLoginState(loginStateDTO);
    }

    private ResourceServerTokenServices createTokenService(TokenStore tokenStore,
                                                           TokenServicesProperties tokenServicesProperties,
                                                           ApplicationContext applicationContext,
                                                           ClientDetailsService clientDetailsService) {
        DefaultTokenServicesWrapper tokenServices = new DefaultTokenServicesWrapper(applicationContext, tokenServicesProperties.isReuseRefreshToken());
        tokenServices.setTokenStore(tokenStore);
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setClientDetailsService(clientDetailsService);
        Map<String, TokenEnhancer> tokenEnhancerMap = applicationContext.getBeansOfType(TokenEnhancer.class);
        if (!CollectionUtils.isEmpty(tokenEnhancerMap)) {
            TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
            tokenEnhancerChain.setTokenEnhancers(new ArrayList<>(tokenEnhancerMap.values()));
            tokenServices.setTokenEnhancer(tokenEnhancerChain);
        }
        // access_token默认有效时长为12个小时,优先使用client设置的有效期
        tokenServices.setAccessTokenValiditySeconds(tokenServicesProperties.getAccessTokenValiditySeconds());
        // refresh_token默认时长为30天,优先使用client设置的有效期
        tokenServices.setRefreshTokenValiditySeconds(tokenServicesProperties.getRefreshTokenValiditySeconds());
        return tokenServices;
    }
}

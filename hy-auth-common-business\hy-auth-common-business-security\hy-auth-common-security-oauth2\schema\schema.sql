/*
  数据设计3.0使用表
*/
CREATE Database If NOT Exists `hyap` default character set utf8mb4 collate utf8mb4_unicode_ci;

/*
使用hyap数据库
*/
USE `hyap`;

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- oauth_access_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_access_token` (
`token_id` varchar(256) DEFAULT NULL COMMENT '该字段的值是将access_token的值通过MD5加密后存储的',
`token` blob COMMENT '存储将OAuth2AccessToken.java对象序列化后的二进制数据, 是真实的AccessToken的数据值',
`authentication_id` varchar(128) NOT NULL COMMENT '该字段具有唯一性, 其值是根据当前的username(如果有),client_id与scope通过MD5加密生成的',
`user_name` varchar(256) DEFAULT NULL COMMENT '登录时的用户名',
`client_id` varchar(256) DEFAULT NULL,
`authentication` blob COMMENT '存储将OAuth2Authentication.java对象序列化后的二进制数据.',
`refresh_token` varchar(256) DEFAULT NULL COMMENT '该字段的值是将refresh_token的值通过MD5加密后存储的',
PRIMARY KEY (`authentication_id`)
);

-- ----------------------------
-- oauth_refresh_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_refresh_token` (
`token_id` varchar(256) DEFAULT NULL COMMENT '该字段的值是将refresh_token的值通过MD5加密后存储的',
`token` blob COMMENT '存储将OAuth2RefreshToken.java对象序列化后的二进制数据.',
`authentication` blob COMMENT '存储将OAuth2Authentication.java对象序列化后的二进制数据'
);

-- ----------------------------
-- oauth_refresh_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_code` (
`code` varchar(256) DEFAULT NULL COMMENT '存储服务端系统生成的code的值(未加密)',
`authentication` blob COMMENT '存储将AuthorizationRequestHolder.java对象序列化后的二进制数据'
);
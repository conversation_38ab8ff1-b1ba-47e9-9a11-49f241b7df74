package cn.hy.auth.custom.security.log.dao;

import cn.hy.auth.custom.security.log.domain.AuthSysLogHandle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AuthSysLogHandleMapper {

    /**
     *  根据uri请求地址获取处理的脚本信息
     * @param uri .
     * @return .
     */
    List<AuthSysLogHandle>  selectByUri(@Param("uri")String uri);

    /**
     *  根据uri请求地址获取处理的脚本信息
     * @return .
     */
    List<AuthSysLogHandle>  selectDefaultAuthLogHandle();
}
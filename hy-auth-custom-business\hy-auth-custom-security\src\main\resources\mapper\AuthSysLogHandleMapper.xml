<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.security.log.dao.AuthSysLogHandleMapper">
  <resultMap id="BaseResultMap" type="cn.hy.auth.custom.security.log.domain.AuthSysLogHandle">
    <id column="id" jdbcType="DECIMAL" property="id" />
    <result column="create_user_id" jdbcType="DECIMAL" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="sequence" jdbcType="BIGINT" property="sequence" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="data_version" jdbcType="VARCHAR" property="dataVersion" />
    <result column="last_update_user_id" jdbcType="DECIMAL" property="lastUpdateUserId" />
    <result column="last_update_user_name" jdbcType="VARCHAR" property="lastUpdateUserName" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="script" jdbcType="LONGVARCHAR" property="script" />
  </resultMap>

  <select id="selectByUri"  resultMap="BaseResultMap">
    select id, create_user_id, last_update_time, sequence, create_user_name, create_time, 
    data_version, last_update_user_id, last_update_user_name, uri, description, enable,
    script from auth_sys_log_custom_handle where uri = #{uri} and enable = 1 order by last_update_time;
  </select>
  <select id="selectDefaultAuthLogHandle" resultMap="BaseResultMap">
    select id, create_user_id, last_update_time, sequence, create_user_name, create_time,
    data_version, last_update_user_id, last_update_user_name, uri, description, enable,
    script from auth_sys_log_custom_handle where uri = '/*' and enable = 1 order by last_update_time;
  </select>
</mapper>
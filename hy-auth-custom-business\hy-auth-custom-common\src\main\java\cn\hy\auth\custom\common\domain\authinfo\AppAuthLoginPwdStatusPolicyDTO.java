package cn.hy.auth.custom.common.domain.authinfo;

import cn.hy.auth.custom.common.enums.LoginInitPwdActionEnum;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

/**
 * 登录密码状态处理策略
 *
 * <AUTHOR>
 * @date 2020-12-03 14:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthLoginPwdStatusPolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -481634578158861111L;

    /**
     * 密码状态对应帐号表的字段编码
     * 该字段值为1时，表示当前密码为初始密码
     */
    private String pwdStatus;
    /**
     * 初始密码处理方式，onlyRemind：仅提醒，mustBeModified：强制修改不能登录成功，ignore：忽略
     */
    private String initPwdAction;

    @JSONField(serialize = false)
    public LoginInitPwdActionEnum getInitPwdAction() {
        if (!isEnable()) {
            //未启用该策略，则不做密码状态判断
            return LoginInitPwdActionEnum.IGNORE;
        }

        return Optional.ofNullable(LoginInitPwdActionEnum.codeOf(this.initPwdAction))
                .orElse(LoginInitPwdActionEnum.IGNORE);
    }

}

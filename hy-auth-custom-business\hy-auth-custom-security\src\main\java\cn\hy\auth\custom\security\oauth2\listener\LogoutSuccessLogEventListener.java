package cn.hy.auth.custom.security.oauth2.listener;

import cn.hutool.core.date.DateUtil;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.security.oauth2.event.LogOutLogEvent;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import cn.hy.id.IdWorker;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 类描述: 登出日志事件监听
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/3
 */
@AllArgsConstructor
@Component
@Slf4j
public class LogoutSuccessLogEventListener implements ApplicationListener<LogOutLogEvent> {

    private final IdWorker idWorker;

    private final AuthLoginInfoService authLoginInfoService;

    //@Async
    @Override
    public void onApplicationEvent(LogOutLogEvent event) {
        log.debug("登出日志对象信息:{}", JSON.toJSONString(event.getSource()));
        UserLoginInfoDTO userLoginInfoDTO = event.getUserLoginInfoDTO();
        saveLoginInfo(userLoginInfoDTO);
    }

    private void saveLoginInfo(UserLoginInfoDTO userLoginInfoDTO) {
        try {
            userLoginInfoDTO.setId(idWorker.nextId());
            userLoginInfoDTO.setCreateTime(DateUtil.date());
            userLoginInfoDTO.setLastUpdateTime(DateUtil.date());

            //版本
            userLoginInfoDTO.setDataVersion("1");
            /*Long maxSequence = authLoginInfoService.getMaxSequence();
            if (maxSequence == null){
                maxSequence = 1L;
            }
            //排序号
            userLoginInfoDTO.setSequence(maxSequence);*/

            //插入数据
            authLoginInfoService.insert(userLoginInfoDTO);
        } catch (Exception e) {
            log.error("记录登出日志失败", e);
        }
    }
}

package cn.hy.auth.boot.exception;

import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import com.google.common.collect.Iterators;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path.Node;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类描述: 统一处理Controller层异常，返回错误信息
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/16
 */
@Slf4j
@RestControllerAdvice(annotations = {Controller.class, RestController.class})
public class ControllerExceptionHandler {

    /**
     * 错误码
     */
    private static final String OAUTH_ERROR_KEY = "error";
    /**
     * 错误描述
     */
    private static final String OAUTH_ERROR_DES_KEY = "error_description";

    /**
     * 逗号
     */
    private static final String COMMA = ",";
    /**
     * 冒号
     */
    private static final String COLON = ":";


    /**
     * 接口校验失败异常处理
     *
     * @param exception 异常信息
     * @return 返回
     */
    @ExceptionHandler(value = {BindException.class, ConstraintViolationException.class, MethodArgumentNotValidException.class})
    @ResponseBody
    public Map<String, String> bindException(Exception exception) {
        StringBuilder sb = new StringBuilder();

        if (exception instanceof BindException) {
            List<FieldError> errors = ((BindException) exception).getBindingResult().getFieldErrors();
            sb.append(COMMA).append(appendFieldError(errors));
        } else if (exception instanceof ConstraintViolationException) {
            Set<ConstraintViolation<?>> errors = ((ConstraintViolationException) exception).getConstraintViolations();
            for (ConstraintViolation<?> error : errors) {
                Node node = Iterators.getLast(error.getPropertyPath().iterator());
                sb.append(COMMA).append(node.getName()).append(COLON).append(error.getMessage());
            }
        } else if (exception instanceof MethodArgumentNotValidException) {
            List<FieldError> errors = ((MethodArgumentNotValidException) exception).getBindingResult().getFieldErrors();
            sb.append(COMMA).append(appendFieldError(errors));
        }
        String errorMsg = sb.toString().replaceFirst(COMMA, StringUtils.EMPTY);
        log.error(errorMsg);
        return responseData(AuthErrorCodeEnum.A0400.code(), errorMsg);
    }

    /**
     * 拼装装换
     *
     * @param errors 错误信息
     * @return 返回
     */
    private String appendFieldError(List<FieldError> errors) {
        StringBuilder sb = new StringBuilder();
        for (FieldError error : errors) {
            sb.append(COMMA).append(error.getField()).append(COLON)
                    .append(error.getDefaultMessage());
        }
        return sb.toString().replaceFirst(COMMA, StringUtils.EMPTY);
    }

    /**
     * json装换异常
     *
     * @param exception 异常信息
     * @return 返回异常信息
     */
    @ExceptionHandler(value = HttpMessageConversionException.class)
    @ResponseBody
    public Map<String, String> httpMessageConversionException(Exception exception) {
        log.error("JSON转换异常", exception);
        return responseData(AuthErrorCodeEnum.A0427.code(), AuthErrorCodeEnum.A0427.msg() + COLON + exception.getMessage());
    }

    /**
     * 类型装换异常
     *
     * @param exception 异常信息
     * @return 返回异常信息
     */
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public Map<String, String> methodArgumentTypeMismatchException(Exception exception) {
        log.error("字段类型转换异常", exception);
        return responseData(AuthErrorCodeEnum.A0400.code(), "[字段类型装换异常]: " + exception.getMessage());
    }

    /**
     * 未知异常
     *
     * @param exception 异常信息
     * @return 返回异常信息
     */
    @ExceptionHandler(value = MyBatisSystemException.class)
    @ResponseBody
    public Map<String, String> myBatisSystemException(Exception exception) {
        log.error("未处理异常", exception);
        return responseData(AuthErrorCodeEnum.B0001.code(), exception.getMessage());
    }

    /**
     * 未知异常
     *
     * @param exception 异常信息
     * @return 返回异常信息
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Map<String, String> exception(Exception exception) {
        log.error("未处理异常", exception);
        return responseData(AuthErrorCodeEnum.B0001.code(), AuthErrorCodeEnum.B0001.msg());
    }

    /**
     * 业务数据异常
     *
     * @param exception 异常信息
     * @return 返回异常信息
     */
    @ExceptionHandler(value = AuthBusinessException.class)
    @ResponseBody
    public Map<String, String> businessException(AuthBusinessException exception) {
        log.error("业务异常:", exception);
        return responseData(exception.getCode(), exception.getMessage());
    }

    /**
     * 返回信息
     *
     * @param error            错误码
     * @param errorDescription 错误描述
     * @return 返回异常信息
     */
    private Map<String, String> responseData(String error, String errorDescription) {

        Map<String, String> result = Maps.newHashMapWithExpectedSize(2);
        result.put(OAUTH_ERROR_KEY, error);
        result.put(OAUTH_ERROR_DES_KEY, errorDescription);

        return result;
    }

}

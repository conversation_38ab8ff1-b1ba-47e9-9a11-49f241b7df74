package cn.hy.auth.custom.security.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/3
 */
//@Configuration
public class FilterConfig {

    @Bean
    public CheckTokenFilter checkTokenFilter() {
        return new CheckTokenFilter();
    }

    @Bean("checkTokenFilterRegistrationBean")
    public FilterRegistrationBean<CheckTokenFilter> registerFilter(CheckTokenFilter checkTokenFilter) {
        FilterRegistrationBean<CheckTokenFilter> registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(checkTokenFilter);
        registrationBean.addUrlPatterns("/oauth/check_token");
        registrationBean.setOrder(2);
        return registrationBean;
    }
}

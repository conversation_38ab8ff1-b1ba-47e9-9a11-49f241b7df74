
SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- oauth_access_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_access_token` (
  `token_id` varchar(256) DEFAULT NULL COMMENT '该字段的值是将access_token的值通过MD5加密后存储的',
  `token` blob COMMENT '存储将OAuth2AccessToken.java对象序列化后的二进制数据, 是真实的AccessToken的数据值',
  `authentication_id` varchar(128) NOT NULL COMMENT '该字段具有唯一性, 其值是根据当前的username(如果有),client_id与scope通过MD5加密生成的',
  `user_name` varchar(256) DEFAULT NULL COMMENT '登录时的用户名',
  `client_id` varchar(256) DEFAULT NULL,
  `authentication` blob COMMENT '存储将OAuth2Authentication.java对象序列化后的二进制数据.',
  `refresh_token` varchar(256) DEFAULT NULL COMMENT '该字段的值是将refresh_token的值通过MD5加密后存储的',
  PRIMARY KEY (`authentication_id`)
);

-- ----------------------------
-- oauth_refresh_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_refresh_token` (
  `token_id` varchar(256) DEFAULT NULL COMMENT '该字段的值是将refresh_token的值通过MD5加密后存储的',
  `token` blob COMMENT '存储将OAuth2RefreshToken.java对象序列化后的二进制数据.',
  `authentication` blob COMMENT '存储将OAuth2Authentication.java对象序列化后的二进制数据'
);

-- ----------------------------
-- oauth_refresh_token 表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_code` (
  `code` varchar(256) DEFAULT NULL COMMENT '存储服务端系统生成的code的值(未加密)',
  `authentication` blob COMMENT '存储将AuthorizationRequestHolder.java对象序列化后的二进制数据'
);

-- 导出  表 hyap.oauth_request_routing 结构
DROP TABLE IF EXISTS `oauth_request_routing`;
CREATE TABLE IF NOT EXISTS `oauth_request_routing` (
  `id` bigint(20) NOT NULL,
  `system_area_identify` varchar(64) NOT NULL COMMENT '请求所属领域的系统标识，如2.x',
  `source_url` varchar(256) NOT NULL COMMENT '源url 相对地址',
  `target_url` varchar(256) NOT NULL COMMENT '目标url相对地址',
  `pre_route_script` text COMMENT '路由转发前的预处理脚本',
  `success_script` text COMMENT '调用成功后需要执行的groovy脚本 http code 为200',
  `failure_script` text COMMENT '调用失败后需要执行的groovy脚本 http code 非200',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `remark` varchar(128) DEFAULT NULL COMMENT '备注说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='请求转发到第三方系统的配置';

DROP TABLE IF EXISTS `auth_app_involved`;
CREATE TABLE `auth_app_involved` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`lessee_code` VARCHAR(32) NULL DEFAULT NULL,
	`app_code` VARCHAR(32) NULL DEFAULT NULL,
	`status` TINYINT(4) NULL DEFAULT '1' COMMENT '应用可登陆状态，0：不可登陆；1：可登陆',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB;

INSERT INTO `oauth_request_routing` (`id`, `system_area_identify`, `source_url`, `target_url`, `pre_route_script`, `success_script`, `failure_script`, `gmt_modified`, `gmt_create`, `remark`) VALUES
	(123456, '2.X', '/history/login/last', '/auth/history/login_info/last?clientId=client_hy_web', '', 'import cn.hutool.core.date.DateUtil;\r\nimport cn.hutool.json.JSONObject;\r\nimport cn.hutool.json.JSONUtil;\r\nimport org.apache.http.entity.BasicHttpEntity;\r\nimport org.apache.http.util.EntityUtils;\r\nimport org.springframework.web.context.request.RequestContextHolder;\r\nimport org.springframework.web.context.request.ServletRequestAttributes;\r\n\r\nimport javax.servlet.http.HttpServletResponse;\r\nimport java.io.*;\r\nimport java.util.HashMap;\r\nimport java.util.Map;\r\nimport cn.hy.auth.custom.route.service.route.impl.DefaultRouteExecuteInterceptor;\r\n\r\ndef parseTimeStrToStamp(String str){\r\n  if (str != null) {\r\n      return DateUtil.parseDate(str).toJdkDate().getTime();\r\n  } else {\r\n	return null;\r\n  }\r\n}\r\n\r\ndef userId = 2222222222\r\n\r\n// 转换结果\r\nString resultStr = EntityUtils.toString(httpResponse.getEntity());\r\nJSONObject jsonObject = JSONUtil.parseObj(resultStr);\r\nJSONObject httpResult = jsonObject.getJSONObject("result");\r\n\r\nMap<String, Object> parseResult = new HashMap<>(httpResult);\r\nparseResult.remove("userid");\r\nparseResult.remove("createUserid");\r\nparseResult.remove("lastUpdateUserid");\r\n\r\nparseResult.put("id", DefaultRouteExecuteInterceptor.getLongId(httpResult.getStr("id")));\r\nparseResult.put("userId", DefaultRouteExecuteInterceptor.getLongId(httpResult.getStr("userid")));\r\n\r\nparseResult.put("userAccountName", httpResult.get("loginname"));\r\nparseResult.put("createUserId", DefaultRouteExecuteInterceptor.getLongId(httpResult.getStr("createUserid")));\r\n\r\nparseResult.put("lastUpdateUserId", DefaultRouteExecuteInterceptor.getLongId(httpResult.getStr("lastUpdateUserid")));\r\nparseResult.put("createTime", parseTimeStrToStamp(httpResult.getStr("createTime")));\r\nparseResult.put("lastUpdateTime", parseTimeStrToStamp(httpResult.getStr("lastUpdateTime")));\r\nparseResult.put("lastLoginTime", parseTimeStrToStamp(httpResult.getStr("messageCreateTime")));\r\n\r\nbyte[] parseResultByte = JSONUtil.toJsonStr(parseResult).getBytes("UTF-8");\r\nBasicHttpEntity resetHttpEntity = new BasicHttpEntity();\r\nInputStream inputStream = new ByteArrayInputStream(parseResultByte);\r\nresetHttpEntity.setContent(inputStream);\r\n\r\nhttpResponse.setEntity(resetHttpEntity);', NULL, '2021-08-30 15:57:31', '2021-08-25 16:12:40', '获取上一次成功登录信息'),
	(123457, '2.X', '/user/current', '/auth/user/info/cur', NULL, 'def tablePrefix ="hy_hyims";\r\nimport cn.hy.auth.custom.route.common.SqlCreateUtil;\r\nimport cn.hutool.json.JSONArray;\r\nimport cn.hutool.json.JSONObject;\r\nimport cn.hutool.json.JSONUtil;\r\nimport org.apache.http.entity.BasicHttpEntity;\r\nimport org.apache.http.util.EntityUtils;\r\nimport org.springframework.web.context.request.RequestContextHolder;\r\nimport org.springframework.web.context.request.ServletRequestAttributes;\r\n\r\nimport javax.servlet.http.HttpServletResponse;\r\nimport java.io.ByteArrayInputStream;\r\nimport java.io.IOException;\r\nimport java.io.InputStream;\r\nimport java.util.HashMap;\r\nimport java.util.Map;\r\nimport cn.hy.auth.custom.route.service.route.impl.DefaultRouteExecuteInterceptor;\r\nimport cn.hutool.core.date.DateUtil;\r\n\r\n// 转换结果\r\n        Map<String, Object> parseResult = new HashMap<>();\r\n        String resultStr = EntityUtils.toString(httpResponse.getEntity());\r\n        JSONObject jsonObject = JSONUtil.parseObj(resultStr);\r\n        JSONObject httpResult = jsonObject.getJSONObject("result");\r\n\r\n        JSONObject account = httpResult.getJSONObject("account");\r\n\r\n        parseResult.put("id", DefaultRouteExecuteInterceptor.getLongId(account.getStr("id")));\r\n\r\n        parseResult.put("userAccountName", account.getStr("loginname"));\r\n        parseResult.put("password", account.getStr("password"));\r\n        parseResult.put("enabled", "1".equals(account.getStr("qiyonghuojinyongzhuangtai")));\r\n\r\n        // 转换3.0的account\r\n        Map<String, Object> parseAccount = new HashMap<>();\r\n        parseResult.put("account", parseAccount);\r\n        parseAccount.put("id", DefaultRouteExecuteInterceptor.getLongId(account.getStr("id")));\r\n        parseAccount.put("sys_2x_uuid", account.getStr("id"));\r\n        parseAccount.put("user_account_name", account.getStr("loginname"));\r\n        parseAccount.put("username", httpResult.get("member") != null ? httpResult.get("member").get("Name"):null);\r\n        parseAccount.put("password", "Ziykbgi8BMoadAYSAyiAxg==");\r\n        parseAccount.put("create_user_id", DefaultRouteExecuteInterceptor.getLongId(account.getStr("create_userid")));\r\n        parseAccount.put("last_update_time", account.get("last_update_time")==null?null:DateUtil.format(DateUtil.date(account.get("last_update_time")), "yyyy-MM-dd HH:mm:ss"));\r\n        parseAccount.put("create_time", account.get("create_time")==null?null:DateUtil.format(DateUtil.date(account.get("create_time")), "yyyy-MM-dd HH:mm:ss"));\r\n        parseAccount.put("last_update_user_id", DefaultRouteExecuteInterceptor.getLongId(account.getStr("last_update_userid")));\r\n        parseAccount.put("is_system_recode", "0");\r\n        parseAccount.put("sequence", 1);\r\n        parseAccount.put("data_version", "1");\r\n\r\n        // 转换3.0的user\r\n        Map<String, Object> parseUser = new HashMap<>();\r\n        parseResult.put("user", parseUser);\r\n\r\n        // 处理role的id，转long类型\r\n        JSONArray roles = httpResult.getJSONArray("roles");\r\n        if (roles != null) {\r\n            for (Object role : roles) {\r\n                ((JSONObject) role).putOpt("id", DefaultRouteExecuteInterceptor.getLongId(((JSONObject) role).getStr("id")));\r\n                ((JSONObject) role).putOpt("create_userid", DefaultRouteExecuteInterceptor.getLongId(((JSONObject) role).getStr("create_userid")));\r\n                ((JSONObject) role).putOpt("last_update_userid", DefaultRouteExecuteInterceptor.getLongId(((JSONObject) role).getStr("last_update_userid")));\r\n            }\r\n        }\r\n        parseUser.put("role", roles);\r\n        parseUser.put("organization", httpResult.get("organization"));\r\n        parseUser.put("member", httpResult.get("member"));\r\n\r\n        byte[] parseResultByte = JSONUtil.toJsonStr(parseResult).getBytes("UTF-8");\r\n        BasicHttpEntity resetHttpEntity = new BasicHttpEntity();\r\n        InputStream inputStream = new ByteArrayInputStream(parseResultByte);\r\n        resetHttpEntity.setContent(inputStream);\r\n\r\n        httpResponse.setEntity(resetHttpEntity);\r\n\r\n        Map<String, Object> userLastUpdateTime = cache.get(account.getStr("id"), new java.util.function.Function<String, Map<String, Object>>() {\r\n            @Override\r\n            public Map<String, Object> apply(String s) {\r\nString insertSql = SqlCreateUtil.createInsertSql(tablePrefix+"_user_account",  parseAccount);\r\n jdbcTemplate.execute(insertSql.replaceFirst("INSERT INTO", "REPLACE INTO"));\r\n                Map<String, Object> map = new HashMap<>();\r\n                map.put("last_update_time", account.getDate("last_update_time"));\r\n                return map;\r\n            }\r\n        });\r\n        if (DateUtil.compare(account.getDate("last_update_time"), (Date) userLastUpdateTime.get("last_update_time")) != 0) {\r\n            String insertSql = SqlCreateUtil.createInsertSql(tablePrefix+"_user_account",  parseAccount);\r\n jdbcTemplate.execute(insertSql.replaceFirst("INSERT INTO", "REPLACE INTO"));\r\n        }', NULL, '2021-08-30 17:59:18', '2021-08-25 18:07:54', '获取当前用户所有信息'),
	(123458, '2.X', '/oauth/check_token', '/oauth/check_token', NULL, NULL, NULL, '2021-08-26 10:50:20', '2021-08-26 10:39:45', '校验token');


SET FOREIGN_KEY_CHECKS=1;
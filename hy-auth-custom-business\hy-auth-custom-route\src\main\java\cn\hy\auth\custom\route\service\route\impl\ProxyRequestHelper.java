package cn.hy.auth.custom.route.service.route.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriTemplate;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProxyRequestHelper {


    private static final String UTF_8 = "UTF-8";

    /**
     * Form feed pattern.
     */
    private static final Pattern FORM_FEED_PATTERN = Pattern.compile("\f");

    /**
     * Colon pattern.
     */
    private static final Pattern COLON_PATTERN = Pattern.compile(":");


    public ProxyRequestHelper() {

    }



    public MultiValueMap<String, String> buildRequestQueryParams( HttpServletRequest request) {
        Map<String, List<String>> map = getQueryParams(request);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (map == null) {
            return params;
        }
        for (String key : map.keySet()) {
            for (String value : map.get(key)) {
                params.add(key, value);
            }
        }
        return params;
    }

    private Map<String, List<String>> getQueryParams(HttpServletRequest request) {
        Map<String, List<String>> qp = new LinkedHashMap<>();
        if (request.getQueryString() == null) {
            return null;
        }
        StringTokenizer st = new StringTokenizer(request.getQueryString(), "&");

        while (true) {
            while (st.hasMoreTokens()) {
                String s = st.nextToken();
                int i = s.indexOf('=');
                String name;
                String value;
                Object valueList;
                if (i > 0 && s.length() >= i + 1) {
                    name = s.substring(0, i);
                    value = s.substring(i + 1);
                    try {
                        name = URLDecoder.decode(name, UTF_8);
                    } catch (Exception var10) {
                    }

                    try {
                        value = URLDecoder.decode(value, UTF_8);
                    } catch (Exception var9) {
                    }

                    valueList =  qp.get(name);
                    if (valueList == null) {
                        valueList = new LinkedList();
                        qp.put(name, (List<String>) valueList);
                    }

                    ((List) valueList).add(value);
                } else if (i == -1) {
                    name = s;
                    value = "";

                    try {
                        name = URLDecoder.decode(name, UTF_8);
                    } catch (Exception var11) {
                    }

                    valueList = qp.get(name);
                    if (valueList == null) {
                        valueList = new LinkedList();
                        qp.put(name, (List<String>) valueList);
                    }

                    ((List) valueList).add(value);
                }
            }

            return qp;
        }

    }

    MultiValueMap<String, String> buildRequestHeaders(
            HttpServletRequest request) {

        MultiValueMap<String, String> headers = new HttpHeaders();
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (isIncludedHeader(name)) {
                    Enumeration<String> values = request.getHeaders(name);
                    while (values.hasMoreElements()) {
                        String value = values.nextElement();
                        headers.add(name, value);
                    }
                }
            }
        }

        if (!headers.containsKey(HttpHeaders.ACCEPT_ENCODING)) {
            headers.set(HttpHeaders.ACCEPT_ENCODING, "gzip");
        }
        return headers;
    }


    private boolean isIncludedHeader(String headerName) {
        String name = headerName.toLowerCase();

        switch (name) {
            case "host":

                return true;

            case "connection":
            case "content-length":
            case "server":
            case "transfer-encoding":
            case "x-application-context":
                return false;
            default:
                return true;
        }
    }


    /**
     * Get url encoded query string. Pay special attention to single parameters with no
     * values and parameter names with colon (:) from use of UriTemplate.
     *
     * @param params Un-encoded request parameters
     * @return url-encoded query String built from provided parameters
     */
    String getQueryString(MultiValueMap<String, String> params) {
        if (params.isEmpty()) {
            return "";
        }
        StringBuilder query = new StringBuilder();
        Map<String, Object> singles = new HashMap<>();
        for (String param : params.keySet()) {
            int i = 0;
            for (String value : params.get(param)) {
                query.append("&");
                query.append(param);
                if (!"".equals(value)) { // don't add =, if original is ?wsdl, output is
                    // not ?wsdl=
                    String key = param;
                    // if form feed is already part of param name double
                    // since form feed is used as the colon replacement below
                    if (key.contains("\f")) {
                        key = (FORM_FEED_PATTERN.matcher(key).replaceAll("\f\f"));
                    }
                    // colon is special to UriTemplate
                    if (key.contains(":")) {
                        key = COLON_PATTERN.matcher(key).replaceAll("\f");
                    }
                    key = key + i;
                    singles.put(key, value);
                    query.append("={");
                    query.append(key);
                    query.append("}");
                }
                i++;
            }
        }

        UriTemplate template = new UriTemplate("?" + query.toString().substring(1));
        return template.expand(singles).toString();
    }

}

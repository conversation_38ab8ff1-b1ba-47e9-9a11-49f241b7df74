package cn.hy.auth.common.security.core.authentication.social.service;


import cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;

/**
 * 用户登录账号的获取
 */
public interface IThirdLoginAccount {


    /**
     * 获取链接登录的用户账号
     *
     * @param code                   .
     * @param lesseeCode             .
     * @param appCode                .
     * @param providerUserInfo       .
     * @param authenticationToken    .
     * @param providerAppAccessToken .
     */
    String getUserAccount(String code, String lesseeCode, String appCode, ProviderUserInfo providerUserInfo,
                          HySocialAuthenticationToken authenticationToken, ProviderAppAccessToken providerAppAccessToken);
}

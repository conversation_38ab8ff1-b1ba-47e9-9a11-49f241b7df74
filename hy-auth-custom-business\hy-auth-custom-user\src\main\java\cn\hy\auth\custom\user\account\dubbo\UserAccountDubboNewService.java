package cn.hy.auth.custom.user.account.dubbo;

import cn.hutool.core.map.MapUtil;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.common.security.oauth2.token.DefaultTokenServicesWrapper;
import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.LoginStateDTO;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import cn.hy.auth.custom.user.account.controller.UserAccountController;
import cn.hy.auth.custom.user.account.service.KickOutEnum;
import cn.hy.auth.custom.user.cache.service.KickOutTokenStoreService;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationManager;
import org.springframework.security.oauth2.provider.token.*;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：dubbo 接口实现,新版隔离sdk代码后的实现
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/2/8 10:21
 **/
@DubboService
@Slf4j
public class UserAccountDubboNewService implements cn.hy.auth.custom.common.api.UserAccountApiV2 {
    /**
     * token最少分片数
     */
    private static final int MIN_TOKEN_PART = 3;
    private final UserAccountController userAccountController;
    private final OAuth2AuthenticationManager oAuth2AuthenticationManager;
    private final UserCacheService userCacheService;
    private final ResourceServerTokenServices resourceServerTokenServices;
    private final KickOutTokenStoreService kickOutTokenStoreService;
    @Autowired
    @Lazy
    private AppAuthStrategyManager authInfoManager;

    private final AccessTokenConverter accessTokenConverter = new DefaultAccessTokenConverter();

    public UserAccountDubboNewService(UserAccountController userAccountController, UserCacheService userCacheService, TokenStore tokenStore,
                                      TokenServicesProperties tokenServicesProperties,
                                      ApplicationContext applicationContext,
                                      ClientDetailsService clientDetailsService, KickOutTokenStoreService kickOutTokenStoreService) {
        this.userAccountController = userAccountController;
        this.userCacheService = userCacheService;
        this.kickOutTokenStoreService = kickOutTokenStoreService;
        this.resourceServerTokenServices = createTokenService(tokenStore, tokenServicesProperties, applicationContext, clientDetailsService);
        this.oAuth2AuthenticationManager = init(resourceServerTokenServices);
        log.info("创建了UserAccountDubboService");
    }

    private OAuth2AuthenticationManager init(ResourceServerTokenServices resourceServerTokenServices) {
        OAuth2AuthenticationManager oauthAuthenticationManager = new OAuth2AuthenticationManager();
        oauthAuthenticationManager.setTokenServices(resourceServerTokenServices);
        return oauthAuthenticationManager;

    }

    @Override
    public Map<String, Object> checkToken(String value) {
        initCheckTokenContext(value);
        try {
            OAuth2AccessToken token = resourceServerTokenServices.readAccessToken(value);
            if (token == null) {
                // 判断是否被踢出了
                KickOutEnum kickOutTokenEnum = kickOutTokenStoreService.getKickOutTokenMsg(value);
                if (kickOutTokenEnum != null) {
                    HashMap<String, String> map =
                            MapUtil.of("code", kickOutTokenEnum.getCode());
                    map.put("msg", kickOutTokenEnum.getMsg());
                    throw new InvalidTokenException(JSON.toJSONString(map));
                }
                throw new InvalidTokenException("Token was not recognised");
            }

            if (token.isExpired()) {
                throw new InvalidTokenException("Token has expired");
            }

            OAuth2Authentication authentication = resourceServerTokenServices.loadAuthentication(token.getValue());

            Map<String, Object> response = (Map<String, Object>) accessTokenConverter.convertAccessToken(token, authentication);

            // gh-1070
            response.put("active", true);    // Always true if token exists and not expired

            return response;
        } finally {
            clearContext();
        }

    }

    @Override
    public cn.hy.auth.sdk.domain.UserAccountDTO getCurUserAccount(String token) {
        initContext("/user/current", token);
        try {
            UserAccountDTO currentUserAccount = userAccountController.getCurrentUserAccount();
        return convertToSdkUserAccountDTO(currentUserAccount);
        } finally {
            clearContext();
        }

    }


    @Override
    public Map<String, Object> getCurAccount(String token) {
        initContext("/user/account/current", token);
        try {
            return userAccountController.getCurrentAccount();
        } finally {
            clearContext();
        }
    }

    @Override
    public Map<String, Object> getCurAssociationUser(String token) {
        initContext("/user/association/current", token);
        try {
            return userAccountController.getCurrentAssociationUser();
        } finally {
            clearContext();
        }
    }

    @Override
    public cn.hy.auth.sdk.domain.LoginUserHistoryDTO getCurUserWithLoginInfo(String token) {
        initContext("/user/currentLoginUser", token);
        try {
            cn.hy.auth.custom.common.domain.LoginUserHistoryDTO currentUserWithLoginInfo = userAccountController.getCurrentUserWithLoginInfo();
            if (currentUserWithLoginInfo == null) {
            return null;
            }
            cn.hy.auth.sdk.domain.LoginUserHistoryDTO sdkLoginUserHistoryDTO = new cn.hy.auth.sdk.domain.LoginUserHistoryDTO();
            sdkLoginUserHistoryDTO.setUserAccountDTO(convertToSdkUserAccountDTO(currentUserWithLoginInfo.getUserAccountDTO()));
            sdkLoginUserHistoryDTO.setUserLoginInfoDTO(convetToSdkUserLoginInfoDTO(currentUserWithLoginInfo.getUserLoginInfoDTO()));
            return sdkLoginUserHistoryDTO;
        } finally {
            clearContext();
        }

    }

    @Override
    public cn.hy.auth.sdk.domain.UserLoginInfoDTO getLastSuccessLoginInfo(String token) {
        initContext("/history/login/last", token);
        try {
            UserLoginInfoDTO userLoginInfo = userCacheService.getUserLoginInfoByTokenId(token);
        return convetToSdkUserLoginInfoDTO(userLoginInfo);
        } finally {
            clearContext();
        }

    }

    @Override
    public Map<String, Object> getAdditional(String token) {
        initContext("/user/additional", token);
        try {
            return userAccountController.additional();
        } finally {
            clearContext();
        }
    }

    private static cn.hy.auth.sdk.domain.UserLoginInfoDTO convetToSdkUserLoginInfoDTO(UserLoginInfoDTO userLoginInfo) {
        if (userLoginInfo == null) {
            return null;
        }
        cn.hy.auth.sdk.domain.UserLoginInfoDTO sdkUserLoginInfoDTO = new cn.hy.auth.sdk.domain.UserLoginInfoDTO();
        sdkUserLoginInfoDTO.setId(userLoginInfo.getId());
        sdkUserLoginInfoDTO.setClientType(userLoginInfo.getClientType());
        sdkUserLoginInfoDTO.setLastLoginIp(userLoginInfo.getLastLoginIp());
        sdkUserLoginInfoDTO.setUserId(userLoginInfo.getUserId());
        sdkUserLoginInfoDTO.setUserAccountName(userLoginInfo.getUserAccountName());
        sdkUserLoginInfoDTO.setClientId(userLoginInfo.getClientId());
        sdkUserLoginInfoDTO.setCreateUserId(userLoginInfo.getCreateUserId());
        sdkUserLoginInfoDTO.setCreateTime(userLoginInfo.getCreateTime());
        sdkUserLoginInfoDTO.setLoginCount(userLoginInfo.getLoginCount());
        sdkUserLoginInfoDTO.setLoginType(userLoginInfo.getLoginType());
        sdkUserLoginInfoDTO.setLastLoginTime(userLoginInfo.getLastLoginTime());
        sdkUserLoginInfoDTO.setLastRealLoginTime(userLoginInfo.getLastRealLoginTime());
        sdkUserLoginInfoDTO.setCurrentIp(userLoginInfo.getCurrentIp());
        sdkUserLoginInfoDTO.setCurrentLoginTime(userLoginInfo.getCurrentLoginTime());
        sdkUserLoginInfoDTO.setErrorCode(userLoginInfo.getErrorCode());
        sdkUserLoginInfoDTO.setErrorMessage(userLoginInfo.getErrorMessage());
        sdkUserLoginInfoDTO.setDataVersion(userLoginInfo.getDataVersion());
        sdkUserLoginInfoDTO.setMac(userLoginInfo.getMac());
        sdkUserLoginInfoDTO.setIp(userLoginInfo.getIp());
        sdkUserLoginInfoDTO.setSequence(userLoginInfo.getSequence());
        sdkUserLoginInfoDTO.setLastUpdateTime(userLoginInfo.getLastUpdateTime());
        sdkUserLoginInfoDTO.setLastUpdateUserId(userLoginInfo.getLastUpdateUserId());
        sdkUserLoginInfoDTO.setLastUpdateUserName(userLoginInfo.getLastUpdateUserName());
        sdkUserLoginInfoDTO.setCreateUserName(userLoginInfo.getCreateUserName());
        sdkUserLoginInfoDTO.setType(userLoginInfo.getType());
        return sdkUserLoginInfoDTO;
    }

    private void initCheckTokenContext(String tokenValue) {
        setAuthContext(tokenValue);
        setClientType(tokenValue);
        //parseTokenResultToSecurityContext(tokenValue);
        initLogPrefix("/oauth/check_token", tokenValue);
        setAppAuthStrategy();
    }

    private void initContext(String uri, String tokenValue) {
        setAuthContext(tokenValue);
        setClientType(tokenValue);
        parseTokenResultToSecurityContext(tokenValue);
        initLogPrefix(uri, tokenValue);
        setAppAuthStrategy();
        AuthContext.getContext().set("token", tokenValue);
    }

    private void clearContext() {
        AuthContext.getContext().reset();
    }

    private void initLogPrefix(String uri, String tokenId) {
        AuthContext.getContext().set("_uri_", uri);
        AuthContext.getContext().set("_requestId_", UUIDRandomUtils.getUuid(32));
        String loginName = LoginUtil.getLoginName();
        if (StringUtils.isBlank(loginName) && StringUtils.isNotBlank(tokenId)) {
            try {
                OAuth2Authentication oAuth2Authentication = resourceServerTokenServices.loadAuthentication(tokenId);
                if (oAuth2Authentication != null) {
                    loginName = oAuth2Authentication.getName();
                }

            } catch (Exception e) {
                // ignore
            }

        }
        AuthContext.getContext().set("_login_name_", loginName);
    }

    private void parseTokenResultToSecurityContext(String tokenValue) {
        if (StringUtils.isBlank(tokenValue)) {
            throw new InvalidTokenException("Invalid token (token not found)");
        }
        Authentication authResult = oAuth2AuthenticationManager.authenticate(new PreAuthenticatedAuthenticationToken(tokenValue, ""));
        SecurityContextHolder.getContext().setAuthentication(authResult);
    }

    private void setAuthContext(String tokenValue) {
        AuthContext context = AuthContext.getContext();
        AppInfoDTO appInfoDTO = parseOfToken(tokenValue);
        if (appInfoDTO != null) {
            context.setLesseeCode(appInfoDTO.getLesseeCode()).setAppCode(appInfoDTO.getAppCode());
        } else {
            throw new InvalidTokenException("非3.x有效的token. " + tokenValue);
        }

    }

    private void setAppAuthStrategy() {
        AppAuthStrategyDTO authInfoDTO = new AppAuthStrategyDTO();
        if (authInfoManager != null) {
            authInfoDTO = authInfoManager.get();
        }
        AuthContext.getContext().setAppAuthStrategy(authInfoDTO);
    }

    private AppInfoDTO parseOfToken(String tokenId) {
        if (StringUtils.isBlank(tokenId)) {
            log.error("token为空，解析租户和应用信息失败。");
            return null;
        }
        tokenId = tokenId.trim();
        String[] tokenPart = tokenId.split("\\.");
        if (tokenPart.length < MIN_TOKEN_PART) {
            log.error("token内容不合法，解析租户和应用信息失败。tokenId：【{}】", tokenId);
            return null;
        }

        return AppInfoDTO.builder().lesseeCode(tokenPart[0]).appCode(tokenPart[1]).build();
    }

    /**
     * 获取登录类型
     *
     * @param token ..
     */
    private void setClientType(String token) {
        if (StringUtils.isBlank(token)) {
            return;
        }
        String[] tokenPart = token.split("\\.");
        if (tokenPart.length < 4) {
            return;
        }
        LoginStateDTO loginStateDTO = new LoginStateDTO();
        loginStateDTO.setClientType(ClientTypeEnum.codeOf(tokenPart[2]));
        AuthContext.getContext().setLoginState(loginStateDTO);
    }

    private ResourceServerTokenServices createTokenService(TokenStore tokenStore,
                                                           TokenServicesProperties tokenServicesProperties,
                                                           ApplicationContext applicationContext,
                                                           ClientDetailsService clientDetailsService) {
        DefaultTokenServicesWrapper tokenServices = new DefaultTokenServicesWrapper(applicationContext, tokenServicesProperties.isReuseRefreshToken());
        tokenServices.setTokenStore(tokenStore);
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setClientDetailsService(clientDetailsService);
        Map<String, TokenEnhancer> tokenEnhancerMap = applicationContext.getBeansOfType(TokenEnhancer.class);
        if (!CollectionUtils.isEmpty(tokenEnhancerMap)) {
            TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
            tokenEnhancerChain.setTokenEnhancers(new ArrayList<>(tokenEnhancerMap.values()));
            tokenServices.setTokenEnhancer(tokenEnhancerChain);
        }
        // access_token默认有效时长为12个小时,优先使用client设置的有效期
        tokenServices.setAccessTokenValiditySeconds(tokenServicesProperties.getAccessTokenValiditySeconds());
        // refresh_token默认时长为30天,优先使用client设置的有效期
        tokenServices.setRefreshTokenValiditySeconds(tokenServicesProperties.getRefreshTokenValiditySeconds());
        return tokenServices;
    }

    private static cn.hy.auth.sdk.domain.UserAccountDTO convertToSdkUserAccountDTO(UserAccountDTO currentUserAccount) {
        if (currentUserAccount == null) {
            return null;
        }
        cn.hy.auth.sdk.domain.UserAccountDTO sdkUserAccountDTO = new cn.hy.auth.sdk.domain.UserAccountDTO();
        sdkUserAccountDTO.setId(currentUserAccount.getId());
        sdkUserAccountDTO.setAccount(currentUserAccount.getAccount());
        sdkUserAccountDTO.setUser(currentUserAccount.getUser());
        sdkUserAccountDTO.setAppCode(currentUserAccount.getAppCode());
        sdkUserAccountDTO.setEmail(currentUserAccount.getEmail());
        sdkUserAccountDTO.setMobile(currentUserAccount.getMobile());
        sdkUserAccountDTO.setLesseeCode(currentUserAccount.getLesseeCode());
        sdkUserAccountDTO.setEnabled(currentUserAccount.getEnabled());
        sdkUserAccountDTO.setStatus(currentUserAccount.getStatus());
        sdkUserAccountDTO.setPassword(currentUserAccount.getPassword());
        sdkUserAccountDTO.setAccountStartEffectiveTime(currentUserAccount.getAccountStartEffectiveTime());
        sdkUserAccountDTO.setAccountEndEffectiveTime(currentUserAccount.getAccountEndEffectiveTime());
        sdkUserAccountDTO.setUserAccountName(currentUserAccount.getUserAccountName());
        sdkUserAccountDTO.setAccountNonLocked(currentUserAccount.getAccountNonLocked());
        sdkUserAccountDTO.setPasswordExpireTimestamp(currentUserAccount.getPasswordExpireTimestamp());
        return sdkUserAccountDTO;
    }
}

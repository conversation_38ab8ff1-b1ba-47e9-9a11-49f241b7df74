package cn.hy.auth.custom.common.log.domain;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 类描述:系统日志类
 *
 * <AUTHOR>
 * @date ：创建于 2020/8/4
 */
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class LogDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 乐观锁
     */
    private Long dataVersion;

    /**
     * 创建人 非必填，直接要user_account即可
     */
    private Long createUserId;

    private Long lastUpdateUserId;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新
     */
    private Date lastUpdateTime;

    /**
     * 创建人用户账号
     */
    private String userAccount;


    /**
     * 业务流水号
     */
    private String transactionId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作对象(作业行为)
     */
    private String operationObj;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 操作结果,成功或者失败
     */
    private String operationResult;

    /**
     * 操作结果描述
     */
    private String operationResultDesc;


    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求方式
     */
    private String requiredMethod;
    /**
     *  get 方式的请求参数
     */
    private String queryString;

    /**
     * 租户主键
     */
    private String lesseeCode;


    /**
     * 应用主键
     */
    private String appCode;



    /**
     * 耗时,结束时间-开始时间
     */
    private String elapsedTime;
    /**
     *  备注描述
     */
    private String description;


    /**
     *  用户id
     */
    private String userId;
    /**
     *  客户端类型
     */
    private String clientType;

}
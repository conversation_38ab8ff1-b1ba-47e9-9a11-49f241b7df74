package cn.hy.auth.custom.business.starter;

import cn.hy.auth.custom.security.oauth2.token.HyAuthenticationKeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/14 17:03
 **/
@Configuration
public class TokenCustomAutoConfiguration {
    @Bean
    public AuthenticationKeyGenerator defaultAuthenticationKeyGenerator() {
        return new HyAuthenticationKeyGenerator();
    }
}
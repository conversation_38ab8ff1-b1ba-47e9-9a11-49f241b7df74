package cn.hy.auth.common.security.oauth2.token.store;

import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import cn.hy.auth.common.security.oauth2.token.TokenStoreExtends;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.common.ExpiringOAuth2RefreshToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStoreSerializationStrategy;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：本地缓存和redis2级缓存，解决redis的性能问题
 *
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/1/11 11:16
 **/
@Slf4j
public class HyRedisLocalCacheTokenStore implements TokenStore, TokenStoreExtends, ApplicationListener<TokenRevokedEvent> {

    private static final String READ_AUTHENTICATION = "readAuthentication:";
    private static final String READ_AUTHENTICATION_FOR_REFRESH_TOKEN = "readAuthenticationForRefreshToken:";
    private static final String READ_REFRESH_TOKEN = "readRefreshToken:";
    private static final String READ_ACCESS_TOKEN = "readAccessToken:";
    private static final String REFRESH_TOKEN_TO_ACCESS_TOKEN = "refreshToken_to_accessToken:";
    private final ExpiringCache<String,Object> localTokenCache;
    private final HyRedisTokenStore hyRedisTokenStore;

    public HyRedisLocalCacheTokenStore(RedisConnectionFactory connectionFactory) {
        localTokenCache = new ExpiringCache<>();
        hyRedisTokenStore = new HyRedisTokenStore(connectionFactory);
    }

    public void setRemovingDelta(int removingDelta) {
        hyRedisTokenStore.setRemovingDelta(removingDelta);
    }

    public void setPrefix(String prefix) {
        hyRedisTokenStore.setPrefix(prefix);
    }

    public void setAuthenticationKeyGenerator(AuthenticationKeyGenerator authenticationKeyGenerator) {
        hyRedisTokenStore.setAuthenticationKeyGenerator(authenticationKeyGenerator);
    }

    public void setSerializationStrategy(RedisTokenStoreSerializationStrategy serializationStrategy) {
        hyRedisTokenStore.setSerializationStrategy(serializationStrategy);
    }

    @Override
    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
        OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) localTokenCache.get(READ_AUTHENTICATION +token.getValue());
        if (oAuth2Authentication==null){
            oAuth2Authentication = hyRedisTokenStore.readAuthentication(token);
            if (oAuth2Authentication != null){
                localTokenCache.put(READ_AUTHENTICATION +token.getValue(),oAuth2Authentication,token.getExpiresIn(), TimeUnit.SECONDS);
            }
        }
        return oAuth2Authentication;
    }

    /**
     * refreshAccessToken 刷新token时调用
     * @param token a refresh token
     * @return .
     */
    @Override
    public OAuth2Authentication readAuthenticationForRefreshToken(OAuth2RefreshToken token) {
        String refreshToken = token.getValue();
        OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) localTokenCache.get(READ_AUTHENTICATION_FOR_REFRESH_TOKEN +refreshToken);
        if (oAuth2Authentication==null){
            oAuth2Authentication = hyRedisTokenStore.readAuthenticationForRefreshToken(token);
            if (oAuth2Authentication != null){
                int seconds = 1500;
                if (token instanceof ExpiringOAuth2RefreshToken) {
                    ExpiringOAuth2RefreshToken expiringRefreshToken = (ExpiringOAuth2RefreshToken) token;
                    seconds = Long.valueOf((expiringRefreshToken.getExpiration().getTime() - System.currentTimeMillis()) / 1000L)
                            .intValue();
                }
                localTokenCache.put(READ_AUTHENTICATION_FOR_REFRESH_TOKEN +refreshToken,oAuth2Authentication,seconds, TimeUnit.SECONDS);
            }
        }
        return oAuth2Authentication;
    }

    @Override
    public void removeAccessToken(OAuth2AccessToken accessToken) {
        removeCache(accessToken.getValue(),accessToken.getRefreshToken().getValue());
        hyRedisTokenStore.removeAccessToken(accessToken);
    }

    private void removeCache(String token,String refreshToken){
        String cacheToken = (String) localTokenCache.get(REFRESH_TOKEN_TO_ACCESS_TOKEN + refreshToken);
        localTokenCache.clear(REFRESH_TOKEN_TO_ACCESS_TOKEN+refreshToken);
        localTokenCache.clear(READ_AUTHENTICATION +token);
        localTokenCache.clear(READ_ACCESS_TOKEN+token);
        localTokenCache.clear(READ_AUTHENTICATION +cacheToken);
        localTokenCache.clear(READ_ACCESS_TOKEN+cacheToken);
        localTokenCache.clear(READ_AUTHENTICATION_FOR_REFRESH_TOKEN +refreshToken);
        localTokenCache.clear(READ_REFRESH_TOKEN+refreshToken);
    }
    @Override
    public void storeRefreshToken(OAuth2RefreshToken refreshToken, OAuth2Authentication authentication) {
        hyRedisTokenStore.storeRefreshToken(refreshToken,authentication);
    }

    @Override
    public OAuth2RefreshToken readRefreshToken(String refreshTokenValue) {
        OAuth2RefreshToken oAuth2RefreshToken = (OAuth2RefreshToken) localTokenCache.get(READ_REFRESH_TOKEN+refreshTokenValue);
        if (oAuth2RefreshToken == null){
            oAuth2RefreshToken = hyRedisTokenStore.readRefreshToken(refreshTokenValue);
            if (oAuth2RefreshToken != null){
                int seconds = 1500;
                if (oAuth2RefreshToken instanceof ExpiringOAuth2RefreshToken) {
                    ExpiringOAuth2RefreshToken expiringRefreshToken = (ExpiringOAuth2RefreshToken) oAuth2RefreshToken;
                    seconds = Long.valueOf((expiringRefreshToken.getExpiration().getTime() - System.currentTimeMillis()) / 1000L)
                            .intValue();
                }
                localTokenCache.put(READ_REFRESH_TOKEN+refreshTokenValue,oAuth2RefreshToken,seconds, TimeUnit.SECONDS);
            }
        }
       return oAuth2RefreshToken;
    }

    @Override
    public void removeRefreshToken(OAuth2RefreshToken refreshToken) {
        String  refreshTokenValue = refreshToken.getValue();
        // 刷新token会有消息来清理本地缓存
        localTokenCache.clear(READ_REFRESH_TOKEN+refreshTokenValue);
        localTokenCache.clear(READ_AUTHENTICATION_FOR_REFRESH_TOKEN +refreshTokenValue);
        hyRedisTokenStore.removeRefreshToken(refreshToken);
    }

    /**
     * refreshAccessToken 刷新token时调用
     * @param refreshToken The refresh token.
     */
    @Override
    public void removeAccessTokenUsingRefreshToken(OAuth2RefreshToken refreshToken) {
        // 刷新token会有消息来清理本地缓存
        String refreshTokenValue = refreshToken.getValue();
        localTokenCache.clear(READ_REFRESH_TOKEN+refreshTokenValue);
        localTokenCache.clear(READ_AUTHENTICATION_FOR_REFRESH_TOKEN +refreshTokenValue);
        hyRedisTokenStore.removeAccessTokenUsingRefreshToken(refreshToken);
    }

    @Override
    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
       return hyRedisTokenStore.getAccessToken(authentication);
    }

    @Override
    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        hyRedisTokenStore.storeAccessToken(token,authentication);
    }

    @Override
    public OAuth2Authentication readAuthentication(String token) {
        OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) localTokenCache.get(READ_AUTHENTICATION+token);
        if (oAuth2Authentication == null){
            oAuth2Authentication = hyRedisTokenStore.readAuthentication(token);
            if (oAuth2Authentication != null){
                long ttl = 180;
                OAuth2AccessToken accessToken = hyRedisTokenStore.getAccessToken(oAuth2Authentication);
                hyRedisTokenStore.resetRepeatLoginThreadLocal();
                if (accessToken != null){
                    ttl = accessToken.getExpiresIn();
                }
                localTokenCache.put(READ_AUTHENTICATION+token,oAuth2Authentication,ttl,TimeUnit.SECONDS);
            }
        }
        return oAuth2Authentication;
    }

    @Override
    public OAuth2AccessToken readAccessToken(String tokenValue) {
        OAuth2AccessToken oAuth2AccessToken = (OAuth2AccessToken) localTokenCache.get(READ_ACCESS_TOKEN+tokenValue);
        if (oAuth2AccessToken == null){
            oAuth2AccessToken = hyRedisTokenStore.readAccessToken(tokenValue);
            if (oAuth2AccessToken != null){
                localTokenCache.put(READ_ACCESS_TOKEN+tokenValue,oAuth2AccessToken,oAuth2AccessToken.getExpiresIn(),TimeUnit.SECONDS);
                localTokenCache.put(REFRESH_TOKEN_TO_ACCESS_TOKEN+oAuth2AccessToken.getRefreshToken().getValue(),tokenValue,oAuth2AccessToken.getExpiresIn(),TimeUnit.SECONDS);
            }
        }
        return oAuth2AccessToken;
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
        return hyRedisTokenStore.findTokensByClientIdAndUserName(clientId,userName);
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
        return hyRedisTokenStore.findTokensByClientId(clientId);
    }



    @Override
    public void onApplicationEvent(TokenRevokedEvent tokenRevokedEvent) {
        hyRedisTokenStore.onApplicationEvent(tokenRevokedEvent);
    }

    @Override
    public Set<String> getNoExpiredTokenUserNames() {
       return hyRedisTokenStore.getNoExpiredTokenUserNames();
    }

    public void onTokenRevoked(String token,String refreshToken){
        log.debug("用户登出，清理缓存，token:{},refreshToken:{}",token,refreshToken);
        removeCache(token,refreshToken);
    }
    public void onTokenRefreshed(String token,String refreshToken){
        log.debug("用户刷新token，清理缓存，token:{},refreshToken:{}",token,refreshToken);
        removeCache(token,refreshToken);
    }


}

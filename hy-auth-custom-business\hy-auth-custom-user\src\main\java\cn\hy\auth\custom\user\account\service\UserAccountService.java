package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.user.account.domain.ThirdUserBindDTO;
import cn.hy.auth.custom.user.account.domain.UserForgetPwdUpdateDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.domain.UserPwdUpdateDTO;
import lombok.NonNull;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/18
 */
public interface UserAccountService {


    /**
     * 根据多个字段条件查询用户信息，多个字段条件之间是“或”关系
     * 只要一个字段符合指定值，就返回用户信息
     *
     * @param fieldCodes 字段列表
     * @param value      指定值
     * @return 用户信息
     */
    UserAccountDTO getUserByMultiField(List<String> fieldCodes, Object value);

    /**
     * 按账号名称获取用户账号信息
     *
     * @param userName 登录账号
     * @return 返回对应的用户账号信息
     */
    UserAccountDTO getUserByUserName(String userName);
    /**
     * 按账号名称获取用户账号信息
     *
     * @param userName 登录账号
     * @return 返回对应的用户账号信息
     */
    UserAccountDTO getUserAllInfoByUserName(String userName);

    /**
     * 按用户主键查询用户信息，完整全量的用户信息
     *
     * @param userId 用户主键
     * @return 返回用户信息
     */
    UserAccountDTO getUserAllInfoByUserId(@NotNull Long userId);

    /**
     * 按登录账号查询用用户主键
     *
     * @param userId 账号
     * @return 返回对应账号的主键
     */
    //Long getUserIdByUserName(String userName);

    Map<String, Object> getUserAllInfoById(Long userId);

    /**
     * 密码修改
     *
     * @param tokenId          tokenId
     * @param userPwdUpdateDTO 密码对象
     */
    void updateUserPassword(String tokenId, UserPwdUpdateDTO userPwdUpdateDTO);

    /**
     * *
     * @param thirdUserBindDTO
     * @return
     */
    String bingThirdAccount(ThirdUserBindDTO thirdUserBindDTO);

    /**
     * 回填人员信息
     * @param userAccount
     * @param providerId
     * @param providerUserid
     * @return
     */
    Boolean birdThirdMember(@NonNull String userAccount, @NonNull String providerId, @NonNull String providerUserid,
                            Boolean isBindThirdUser);

    /**
     * 检查账号是否已被绑定
     * @param  providerId
     * @param  userAccountName
     * @return
     */
    Boolean checkUserAccount(String providerId, String userAccountName,String providerUserId);

    /**
     * *
     * @param providerId
     * @param telephone
     * @param userAccountName
     * @return
     */
    Boolean removeThirdBing(String providerId,String telephone, String userAccountName);

    void updateUserForgetPassword(UserForgetPwdUpdateDTO userForgetPwdUpdateDTO);

    UserLoginAccountDTO getUserByUserNameOrPhone(String userName, String phone);

    void changeLoginInfoByUserNameList(List<String> userNames, UserOnLineStatus userOnLineStatus);

    boolean checkOnLineStatusTableIsExist(String lesseeCode, String appCode);

    void insertOrUpdateUserOnlineStataus(List<String> list, UserOnLineStatus userOnLineStatus);

    void changeLoginStatusNotInSet(Set<String> userNames, UserOnLineStatus userOnLineStatus);

    UserLoginAccountDTO getUserByUserEmail(String email);

    void updateUserForgetPasswordEmail(UserForgetPwdUpdateDTO userForgetPwdUpdateDTO);

}

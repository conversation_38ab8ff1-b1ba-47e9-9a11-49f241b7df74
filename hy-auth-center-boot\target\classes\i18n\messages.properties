AccountStatusProviderer.result.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
AccountStatusProviderer.result.msg2=\u3011\u65E0\u6CD5\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u3002
AccountStatusProviderer.result.msg3=\u8D26\u53F7\u5DF2\u88AB\u9501\u5B9A
AccountStatusProviderer.result.msg4=\u8D26\u53F7\u5DF2\u88AB\u7981\u7528
AccountStatusProviderer.result.msg5=\u8D26\u53F7\u5DF2\u8FC7\u671F
AccountStatusProviderer.result.msg6=\u8D26\u53F7\u51ED\u636E\u5DF2\u8FC7\u671F
ExternalAuthServiceImpl.result.msg1=\u627E\u4E0D\u5230\u5916\u90E8\u8BF7\u6C42\u5730\u5740\uFF1A
ExternalAuthServiceImpl.result.msg2=\u83B7\u53D6\u7B2C\u4E09\u65B9\u8BF7\u6C42 token \u5931\u8D25\uFF0C\u53C2\u6570\uFF1A
ExternalAuthServiceImpl.result.msg3=\u8FD4\u56DE\u7ED3\u679C\uFF1A\u3010
ExternalAuthServiceImpl.result.msg4=\u8C03\u7528\u7B2C\u4E09\u65B9\u63A5\u53E3\u67E5\u8BE2 TOKEN \u5931\u8D25!
ExternalAuthServiceImpl.result.msg5=\u627E\u4E0D\u5230\u7528\u6237\u4FE1\u606F\u5904\u7406 bean
ExternalAuthServiceImpl.result.msg6=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u7B2C\u4E09\u65B9\u3010
ExternalAuthServiceImpl.result.msg7=\u3011\u914D\u7F6E\u4FE1\u606F\uFF0C\u4E0D\u652F\u6301\u8BE5\u65B9\u5F0F\u767B\u5F55!
ExternalAuthServiceImpl.result.msg8=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u7B2C\u4E09\u65B9\u914D\u7F6E\u4FE1\u606F\uFF0C\u4E0D\u652F\u6301\u8BE5\u65B9\u5F0F\u767B\u5F55!
ExternalAuthServiceImpl.result.msg9=\u8BE5\u5E94\u7528\u8BA4\u8BC1\u914D\u7F6E\u4FE1\u606F\u4E0D\u5B58\u5728!
ExternalAuthServiceImpl.result.msg10=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528\u4FE1\u606F\u5931\u8D25\uFF1A
HySocialPreAuthenticationProvider.result.msg1=\u83B7\u53D6\u7B2C\u4E09\u65B9\u7528\u6237\u767B\u5F55\u8D26\u53F7\u4FE1\u606F\u5931\u8D25\uFF01
HySocialPreAuthenticationProvider.result.msg2=\u7CFB\u7EDF\u6267\u884C\u9519\u8BEF\u3002connectionFactories is null
HySocialPreAuthenticationProvider.result.msg3=\u7CFB\u7EDF\u6267\u884C\u9519\u8BEF\u3002\u6CA1\u6709\u627E\u5230\u5339\u914D\u7684 ConnectionFactory
OkHttpTools.result.msg1=\u65E0\u6CD5\u8BC6\u522B\u7684\u8BF7\u6C42\u53C2\u6570\u4F53\u7C7B\u578B
MiniProgramProviderTokenImpl.result.msg1=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528 token \u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
AuthSdkValidUtil.result.msg1=\u5185\u90E8\u9519\u8BEF\uFF1A%s
OpenIdAuthenticationProvider.result.msg1=\u65E0\u6CD5\u83B7\u53D6\u7528\u6237\u4FE1\u606F
AuthContextFilter.result.msg1=\u89E3\u6790\u79DF\u6237\u5E94\u7528\u4FE1\u606F\u5931\u8D25\u3002
AuthContextFilter.result.msg2=\u5E94\u7528\u72B6\u6001\u4E3A\u5DF2\u505C\u7528\uFF0C\u65E0\u6CD5\u767B\u5F55\u8BBF\u95EE\u3002\u79DF\u6237\u7F16\u7801:
AuthContextFilter.result.msg3=; \u5E94\u7528\u7F16\u7801:
AuthContextFilter.result.msg4=\u5E94\u7528\u5DF2\u5378\u8F7D\uFF0C\u65E0\u6CD5\u767B\u5F55\u8BBF\u95EE\u3002\u79DF\u6237\u7F16\u7801:
AuthApiServeiceDubboImpl.result.msg1=\u6682\u4E0D\u652F\u6301 dubbo \u65B9\u5F0F\uFF0C\u8BF7\u901A\u8FC7 http \u65B9\u5F0F\u8BF7\u6C42 OnlineUserIds
OpenApiConnectionServiceImpl.result.msg1=\u79DF\u6237\uFF1A
OpenApiConnectionServiceImpl.result.msg2=,\u5E94\u7528\uFF1A
OpenApiConnectionServiceImpl.result.msg3=,\u5143\u6570\u636E\u8868\u4E0D\u5B58\u5728\uFF1A
OpenApiConnectionServiceImpl.result.msg4=\u4EBA\u8138\u8BA4\u8BC1\u5931\u8D25,\u539F\u56E0\uFF1A
OpenApiConnectionServiceImpl.result.msg5=A0401, \u8BE5\u4EBA\u5458\u3010
OpenApiConnectionServiceImpl.result.msg6=\u3011\u5DF2\u88AB\u7981\u7528\uFF0C\u8BF7\u542F\u7528\u4EBA\u5458\u518D\u5C1D\u8BD5\u767B\u5F55\uFF01
WechatProviderTokenImpl.result.msg1=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528 token \u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
DingTalkProviderTokenImpl.result.msg1=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528 token \u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
UserAccountDubboService.result.msg1=\u975E 3.x \u6709\u6548\u7684 token.
OauthLogoutController.result.msg1=\u83B7\u53D6token\u5931\u8D25
DingTalkProviderInfoImpl.result.msg1=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u9489\u9489\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
DingTalkProviderInfoImpl.result.msg2=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
DingTalkProviderInfoImpl.result.msg3=\u8BE5\u5E94\u7528\u8BA4\u8BC1\u914D\u7F6E\u4FE1\u606F\u4E0D\u5B58\u5728!
DingTalkProviderInfoImpl.result.msg4=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528\u4FE1\u606F\u5931\u8D25\uFF1A
WechatUserProviderImpl.result.msg1=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u7528\u6237\u4FE1\u606F\u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
AuthenticateAppInfoParser.result.msg1=\u7F3A\u5C11[Authorization]\u4FE1\u606F
HyFacePreAuthenticationProvider.result.msg1=\u5F53\u524D\u4EBA\u5458\u3010
HyFacePreAuthenticationProvider.result.msg2=\u3011\u4EBA\u5458\u4FE1\u606F\u6570\u636E\u4E0D\u5B58\u5728\uFF0C\u8BF7\u6838\u5BF9\u662F\u5426\u5B58\u5728\u672A\u8F6C\u6362\u8D85\u7EA7\u8868\u6216\u57FA\u7840\u6570\u636E\u7F3A\u5931\uFF01
HyFacePreAuthenticationProvider.result.msg3=\u3011\u8D26\u53F7\u5173\u8054\u4FE1\u606F\u6570\u636E\u4E0D\u5B58\u5728\uFF0C\u8BF7\u6838\u5BF9\uFF01
HyFacePreAuthenticationProvider.result.msg4=\u4EBA\u8138\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u6838\u5BF9\u4EBA\u50CF\u4FE1\u606F
HyFacePreAuthenticationProvider.result.msg5=\u7CFB\u7EDF\u6267\u884C\u9519\u8BEF\u3002\u6CA1\u6709\u627E\u5230\u5339\u914D\u7684\u3010HyFacePartyTypeEnum\u3011
MiniProgramProviderInfoImpl.result.msg1=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
MiniProgramProviderInfoImpl.result.msg2=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
MiniProgramProviderInfoImpl.result.msg3=\u8BE5\u5E94\u7528\u8BA4\u8BC1\u914D\u7F6E\u4FE1\u606F\u4E0D\u5B58\u5728!
MiniProgramProviderInfoImpl.result.msg4=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528\u4FE1\u606F\u5931\u8D25\uFF1A
DingTalkUserProviderImpl.result.msg1=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u7528\u6237\u4FE1\u606F\u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
DingTalkUserProviderImpl.result.msg2=\u67E5\u8BE2\u9489\u9489\u7528\u6237\u4FE1\u606F\u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
UserAccountLockAuthenticationProviderer.result.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
UserAccountLockAuthenticationProviderer.result.msg2=\u60A8\u8F93\u5165\u9519\u8BEF\u6B21\u6570\u8D85\u8FC7\u9650\u5236\uFF0C\u8D26\u53F7\u5DF2\u88AB\u9501\u5B9A\uFF0C\u65E0\u6CD5\u767B\u5F55\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458
UserAccountLockAuthenticationProviderer.result.msg3=\u60A8\u8F93\u5165\u9519\u8BEF\u6B21\u6570\u8D85\u8FC7\u9650\u5236\uFF0C\u8D26\u53F7\u5DF2\u88AB\u6C38\u4E45\u9501\u5B9A\uFF0C\u65E0\u6CD5\u767B\u5F55\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
UserAccountLockAuthenticationProviderer.result.msg4=\u60A8\u8F93\u5165\u9519\u8BEF\u6B21\u6570\u8D85\u8FC7\u9650\u5236\uFF0C\u8D26\u53F7\u5DF2\u88AB\u9501\u5B9A\uFF0C
UserAccountLockAuthenticationProviderer.result.msg5=\u5206\u949F\u5185\u65E0\u6CD5\u767B\u5F55\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458
UserAccountLockAuthenticationProviderer.result.msg6=\u60A8\u8F93\u5165\u9519\u8BEF\u6B21\u6570\u8D85\u8FC7\u9650\u5236\uFF0C\u8D26\u53F7\u5DF2\u88AB\u9501\u5B9A\uFF0C\u672C\u65E5\u5185\u65E0\u6CD5\u767B\u5F55\uFF0C\u8BF7\u6B21\u65E5\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458
PwdExpirationAndForceModifyServiceImpl.result.msg1=\u9519\u8BEF\u7684\u5BC6\u7801\u5F3A\u5236\u66F4\u65B0\u7B56\u7565\u6A21\u5F0F,\u8BF7\u68C0\u67E5\u914D\u7F6E
PwdExpirationAndForceModifyServiceImpl.result.msg2=\u65F6\u95F4\u8F6C\u6362\u9519\u8BEF,\u65F6\u95F4\u5B57\u7B26\u4E32\uFF1A%s
PwdExpirationAndForceModifyServiceImpl.result.msg3=\u6570\u636E\u9519\u8BEF, input \u4E0D\u80FD\u4E3A null
PwdExpirationAndForceModifyServiceImpl.result.msg4=\u9519\u8BEF\u7684\u65F6\u95F4\u95F4\u9694\u5355\u4F4D%s\uFF0C\u8BF7\u68C0\u67E5
AbstractPwdPolicyProvider.result.msg1=\u7528\u6237\u8F93\u5165\u7684 clientId \u4E0D\u6B63\u786E:
AbstractPwdPolicyProvider.result.msg2=\u7528\u6237\u8F93\u5165\u7684 clientSecret \u4E0D\u6B63\u786E:
MiniProgramUserProviderImpl.result.msg1=\u83B7\u53D6openid\u9519\u8BEF
MiniProgramUserProviderImpl.result.msg2=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u7528\u6237\u4FE1\u606F\u5931\u8D25\uFF1A
MiniProgramUserProviderImpl.result.msg3=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u7528\u6237\u624B\u673A\u53F7\u4FE1\u606F\u5931\u8D25, \u63A5\u53E3\u8FD4\u56DE\uFF1A
MiniProgramUserProviderImpl.result.msg4=\u67E5\u8BE2\u7528\u6237\u624B\u673A\u53F7\u4FE1\u606F\u5931\u8D25\uFF1A
LinkLoginServiceImpl.result.msg1=\u8BE5\u4EBA\u5458\u57FA\u7840\u6570\u636E\u3010
LinkLoginServiceImpl.result.msg2=\u3011\u672A\u7ED1\u5B9A\u8D26\u53F7\u4FE1\u606F\uFF0C\u8BF7\u5148\u7ED1\u5B9A\uFF01
LinkLoginServiceImpl.result.msg3=\u7CFB\u7EDF\u6267\u884C\u9519\u8BEF\u3002providerId:
LinkLoginServiceImpl.result.msg4=,\u627E\u5230
LinkLoginServiceImpl.result.msg5=\u6761\u6570\u636E\u8BB0\u5F55!
WechatProviderInfoImpl.result.msg1=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u4F01\u4E1A\u5FAE\u4FE1\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
WechatProviderInfoImpl.result.msg2=\u8BA4\u8BC1\u914D\u7F6E\u8868\u4E2D\u7F3A\u5C11\u5FAE\u5E94\u7528\u914D\u7F6E\u4FE1\u606F\uFF0C\u8BF7\u524D\u5F80\u8865\u5145!
WechatProviderInfoImpl.result.msg3=\u8BE5\u5E94\u7528\u8BA4\u8BC1\u914D\u7F6E\u4FE1\u606F\u4E0D\u5B58\u5728!
WechatProviderInfoImpl.result.msg4=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u5E94\u7528\u4FE1\u606F\u5931\u8D25\uFF1A
SmsCodeAuthenticationProvider.result.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
SmsCodeAuthenticationProvider.result.msg2=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
PwdExpirationAndForceModifyProviderer.result.msg1=\u767B\u5F55\u7528\u6237\u4FE1\u606F\u7F3A\u5931
PwdExpirationAndForceModifyProviderer.result.msg2=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
HyAuthenticationSuccessHandler.result.msg1=\u7528\u6237\u8F93\u5165\u7684 clientId \u4E0D\u6B63\u786E:
HyAuthenticationSuccessHandler.result.msg2=\u7528\u6237\u8F93\u5165\u7684 clientSecret \u4E0D\u6B63\u786E:
SmsCodeMapServiceImpl.result.msg1=\u624B\u673A\u53F7\u548C\u9A8C\u8BC1\u7801\u4E0D\u5339\u914D
SmsCodeMapServiceImpl.result.msg2=\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F
SmsCodeMapServiceImpl.result.msg3=%s\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8F93\u5165\u624B\u673A\u53F7
SmsCodeMapServiceImpl.result.msg4=%s\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801
AppLoginServiceImpl.result.msg1=\u7CFB\u7EDF\u6267\u884C\u9519\u8BEF\u3002providerId:
AppLoginServiceImpl.result.msg2=,\u627E\u5230
AppLoginServiceImpl.result.msg3=\u6761\u6570\u636E\u8BB0\u5F55!
FkyUserInfoAdapter.result.msg1=\u627E\u4E0D\u5230\u5916\u90E8\u8BF7\u6C42\u5730\u5740\uFF1A
FkyUserInfoAdapter.result.msg2=\u8C03\u7528\u7B2C\u4E09\u65B9\u63A5\u53E3\u67E5\u8BE2\u7528\u6237\u4FE1\u606F\u5931\u8D25!
FkyUserInfoAdapter.result.msg3=\u67E5\u8BE2\u7B2C\u4E09\u65B9\u67E5\u8BE2\u7528\u6237\u4FE1\u606F\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u3010
OkHttpUtils.result.msg1=\u53D1\u9001\u8BF7\u6C42\u5931\u8D25:
IamAuthServiceImpl.result.msg1=\u83B7\u53D6\u7B2C\u4E09\u65B9\u7528\u6237\u4FE1\u606F\uFF0C\u53C2\u6570\uFF1A
IamAuthServiceImpl.result.msg2=\u8FD4\u56DE\u7ED3\u679C\uFF1A\u3010
IamAuthServiceImpl.result.msg3=\u83B7\u53D6\u7B2C\u4E09\u65B9\u7528\u6237\u4FE1\u606F\u5931\u8D25!
PwdAuthenticationProviderer.result.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
PwdAuthenticationProviderer.result.msg2=\u60A8\u5DF2\u8F93\u5165\u9519\u8BEF\u5BC6\u7801
PwdAuthenticationProviderer.result.msg3=\u6B21\uFF0C\u8FD8\u53EF\u4EE5\u8F93\u5165
PwdAuthenticationProviderer.result.msg4=\u6B21\u3002\u9519\u8BEF\u6B21\u6570\u8D85\u9650\u540E\u5C06\u9501\u5B9A\u8BE5\u8D26\u53F7\u3002
WechatOAuth2Template.result.msg1=\u83B7\u53D6 access token \u5931\u8D25, errcode:
LoginStateInitFilter.assertUtil.msg1=\u53C2\u6570
LoginStateInitFilter.assertUtil.msg2=\u4E0D\u80FD\u4E3A\u7A7A
LoginStateInitFilter.assertUtil.msg3=\u53C2\u6570[
LoginStateInitFilter.assertUtil.msg4=]\u4E0D\u5408\u6CD5\u3002
AuthContextFilter.assertUtil.msg1=\u89E3\u6790\u79DF\u6237\u5E94\u7528\u4FE1\u606F\u5931\u8D25\u3002
AuthContextFilter.assertUtil.msg2=\u7F3A\u5C11\u79DF\u6237\u7F16\u7801\u53C2\u6570\u3002
AuthContextFilter.assertUtil.msg3=\u7F3A\u5C11\u5E94\u7528\u7F16\u7801\u53C2\u6570\u3002
AuthContextFilter.assertUtil.msg4=\u5E94\u7528\u4FE1\u606F\u672A\u767B\u8BB0\uFF0C\u65E0\u6CD5\u767B\u5F55\u8BBF\u95EE\u3002\u79DF\u6237\u7F16\u7801:
AuthContextFilter.assertUtil.msg5=; \u5E94\u7528\u7F16\u7801:
AuthApiServiceImpl.assertUtil.msg1=\u83B7\u53D6\u8BA4\u8BC1\u4E2D\u5FC3\u5730\u5740\u5931\u8D25,\u5730\u5740\u4FE1\u606F\u4E3A\u7A7A
AuthApiServiceImpl.assertUtil.msg2=\u83B7\u53D6\u8BA4\u8BC1\u4E2D\u5FC3\u5F53\u524D\u7528\u6237\u5730\u5740\u5931\u8D25,\u5730\u5740\u4FE1\u606F\u4E3A\u7A7A
AuthApiServiceImpl.assertUtil.msg3=\u83B7\u53D6\u8BA4\u8BC1\u4E2D\u5FC3\u6700\u540E\u767B\u5F55\u4FE1\u606F\u5730\u5740\u5931\u8D25,\u5730\u5740\u4FE1\u606F\u4E3A\u7A7A
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg1=\u5BC6\u7801\u5F3A\u5236\u66F4\u65B0\u7B56\u7565\u6A21\u5F0F\u4E3A\u7A7A
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg2=\u5BC6\u7801\u5F3A\u5236\u66F4\u65B0\u7B56\u7565\uFF1Auser \u6A21\u5F0F\uFF0C\u914D\u7F6E\u4FE1\u606F\u4E3A\u7A7A
PwdExpirationAndForceModifyServiceImpl.assertUtil.msg3=\u5BC6\u7801\u5F3A\u5236\u66F4\u65B0\u7B56\u7565\uFF1Aapp \u6A21\u5F0F\uFF0C\u914D\u7F6E\u4FE1\u606F\u4E3A\u7A7A
UserAccountServiceImpl.assertUtil.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
UserAccountServiceImpl.assertUtil.msg2=\u4E3B\u952E:[%d],\u67E5\u8BE2\u8D26\u53F7\u8868\u6570\u636E\u4E0D\u5B58\u5728
UserAccountServiceImpl.assertUtil.msg3=\u7528\u6237\u8D26\u53F7\u3010userAccountName\u3011\u4E0D\u80FD\u4E3A\u7A7A
UserAccountServiceImpl.assertUtil.msg4=\u7B2C\u4E09\u65B9\u7C7B\u578B\u3010providerId\u3011\u4E0D\u80FD\u4E3A\u7A7A
UserAccountServiceImpl.assertUtil.msg5=\u7B2C\u4E09\u65B9\u7528\u6237ID\u3010providerUserId\u3011\u4E0D\u80FD\u4E3A\u7A7A
UserAccountServiceImpl.assertUtil.msg6=\u5F53\u524D\u8D26\u53F7\u6CA1\u6709\u7ED1\u5B9A\u4EBA\u5458\uFF0C\u8BF7\u5148\u524D\u5F80\u57FA\u7840\u6570\u636E\u7ED1\u5B9A\u4EBA\u5458\uFF01
UserAccountServiceImpl.assertUtil.msg7=\u8BE5\u8D26\u53F7\u3010
UserAccountServiceImpl.assertUtil.msg8=\u3011\u4E0D\u5B58\u5728\uFF01
UserAccountServiceImpl.assertUtil.msg9=\u5F53\u524D\u7B2C\u4E09\u65B9\u7528\u6237 ID \u5DF2\u88AB\u5E94\u7528\u5176\u4ED6\u8D26\u53F7\u7ED1\u5B9A\uFF0C\u8BF7\u5148\u89E3\u7ED1\uFF01
UserAccountServiceImpl.assertUtil.msg10=\u5F53\u524D\u5E94\u7528\u8D26\u53F7\u5DF2\u88AB\u5176\u4ED6\u7B2C\u4E09\u65B9\u7528\u6237\u7ED1\u5B9A\uFF0C\u8BF7\u9009\u62E9\u5176\u4ED6\u8D26\u53F7\uFF01
UserAccountServiceImpl.assertUtil.msg11=\u5BC6\u7801\u7531 6-18 \u4E2A\u6570\u5B57\u3001\u5B57\u6BCD\u6216\u7279\u6B8A\u5B57\u7B26\u7EC4\u6210
RequestIdentifyServiceImpl.assertUtil.msg1=\u65E0\u6CD5\u8BC6\u522B\u7684\u5916\u90E8\u7CFB\u7EDF\uFF1A
AuthRelationUtil.assertUtil.msg1=consul \u5730\u5740\u4E3A\u7A7A
AuthRelationUtil.assertUtil.msg2=consul \u7AEF\u53E3\u4E3A\u7A7A
PwdAuthenticationProviderer.assertUtil.msg1=\u53C2\u6570
PwdAuthenticationProviderer.assertUtil.msg2=\u4E0D\u80FD\u4E3A\u7A7A
PwdAuthenticationProviderer.assertUtil.msg3=\u4E0D\u652F\u6301\u7684\u52A0\u5BC6\u7C7B\u578B\uFF1A\u3010
HySocialPreAuthenticationProvider.assert.msg1=\u4E0D\u652F\u6301\u7684\u767B\u5F55\u65B9\u5F0F:
HyFaceAuthenticationFilter.assert.msg1=\u4EBA\u8138\u7167\u7247\u53C2\u6570[facePic]\u4E0D\u80FD\u4E3A\u7A7A\uFF1A
HyFaceAuthenticationFilter.assert.msg2=\u79DF\u6237\u7F16\u7801\u53C2\u6570[lessCode]\u4E0D\u80FD\u4E3A\u7A7A\uFF1A
HyFaceAuthenticationFilter.assert.msg3=\u5E94\u7528\u7F16\u7801\u53C2\u6570[appCode]\u4E0D\u80FD\u4E3A\u7A7A\uFF1A
HyFaceAuthenticationFilter.assert.msg4=\u4EBA\u8138\u5E93\u7C7B\u578B\u53C2\u6570[loginType]\u4E0D\u80FD\u4E3A\u7A7A\uFF1A
InitDeploy.assert.msg1=yml\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg2=MYSQL_HOST\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg3=MYSQL_PORT\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg4=DATABASE_NAME\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg5=DATABASE_USER\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg6=DATABASE_PWD\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg7=consul host\u4E0D\u80FD\u4E3A\u7A7A\uFF01
InitDeploy.assert.msg8=consul port\u4E0D\u80FD\u4E3A\u7A7A\uFF01
OpenApiConnectionServiceImpl.assert.msg1=\u4EBA\u8138\u670D\u52A1\u5730\u5740\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5 consul \u914D\u7F6E\uFF1Athird.proxy.faceServer
HyPasswordEncoder.assert.msg1=\u52A0\u5BC6\u5BC6\u7801\u4E0D\u5141\u8BB8\u4E3A\u7A7A
HyPasswordEncoder.assert.msg2=\u52A0\u5BC6\u540E\u7684\u5BC6\u7801\u4E0D\u5141\u8BB8\u4E3A\u7A7A
AppInfoStatusController.controllerMap.msg1=\u8BF7\u6C42\u53C2\u6570\u7F3A\u5931: \u79DF\u6237\u53F7\u4E0D\u80FD\u4E3A\u7A7A.
AppInfoStatusController.controllerMap.msg2=\u8BF7\u6C42\u53C2\u6570\u7F3A\u5931: \u5E94\u7528\u53F7\u4E0D\u80FD\u4E3A\u7A7A.
AppInfoStatusController.controllerMap.msg3=\u7528\u6237\u8BF7\u6C42\u53C2\u6570\u9519\u8BEF: status \u53EA\u80FD\u662F 0\u30011 \u6216\u8005 2, 0\uFF1A\u505C\u6B62\uFF0C1\uFF1A\u8FD0\u884C\uFF0C2\uFF1A\u5DF2\u5378\u8F7D
AppInfoStatusController.controllerMap.msg4=\u8BF7\u6C42\u5904\u7406\u6210\u529F
AppInfoStatusController.controllerMap.msg5=\u7CFB\u7EDF\u6267\u884C\u51FA\u9519
MetaDataCacheController.controllerMap.msg1=\u8BF7\u6C42\u5904\u7406\u6210\u529F
OauthLogoutController.controllerMap.msg1=\u767B\u51FA\u6210\u529F
AuthErrorCodeEnum.SUCCESS=\u8BF7\u6C42\u5904\u7406\u6210\u529F
AuthErrorCodeEnum.A0101=\u8BF7\u6C42\u53C2\u6570\u7F3A\u5931
AuthErrorCodeEnum.A0102=\u79DF\u6237\u5E94\u7528\u4FE1\u606F\u7F3A\u5931
AuthErrorCodeEnum.A0103=\u975E\u6CD5\u8BF7\u6C42
AuthErrorCodeEnum.A0342=access_token \u4E0D\u5B58\u5728
AuthErrorCodeEnum.A0343=refresh_token \u4E0D\u5B58\u5728
AuthErrorCodeEnum.A0311=\u7528\u6237\u767B\u9646\u5DF2\u8FC7\u671F
AuthErrorCodeEnum.A0120=\u65E7\u5BC6\u7801\u4E0D\u6B63\u786E
AuthErrorCodeEnum.A0123=\u65B0\u5BC6\u7801\u4E0D\u80FD\u548C\u65E7\u5BC6\u7801\u4E00\u6837
AuthErrorCodeEnum.A0124=\u4E0D\u5141\u8BB8\u4FEE\u6539\u975E\u672C\u8D26\u53F7\u5BC6\u7801
AuthErrorCodeEnum.A0125=\u52A0\u5BC6\u65B9\u5F0F\u5E10\u53F7\u540D\u660E\u6587+\u5BC6\u7801\u660E\u6587\u52A0\u5BC6\u6821\u9A8C\u5F02\u5E38
AuthErrorCodeEnum.A0201=\u7528\u6237\u8D26\u6237\u4E0D\u5B58\u5728
AuthErrorCodeEnum.A0202=\u66F4\u65B0\u7528\u6237\u4FE1\u606F\u5FC5\u987B\u6307\u5B9A\u7528\u6237\u4E3B\u952E
AuthErrorCodeEnum.A0203=\u7528\u6237\u8D26\u6237\u5F02\u5E38
AuthErrorCodeEnum.A0204=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
AuthErrorCodeEnum.A0205=\u9700\u8981\u9A8C\u8BC1\u7801
AuthErrorCodeEnum.A0300=\u8BBF\u95EE\u6743\u9650\u5F02\u5E38
AuthErrorCodeEnum.A0242=\u5BC6\u7801\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u4FEE\u6539
AuthErrorCodeEnum.A0243=\u8DF3\u8F6C\u5230\u4FEE\u6539\u9875\u9762\u4E4B\u524D\u83B7\u53D6 token \u5931\u8D25
AuthErrorCodeEnum.A0245=\u60A8\u7684\u5BC6\u7801\u4E3A\u9ED8\u8BA4\u5BC6\u7801\uFF0C\u8BF7\u7ACB\u5373\u4FEE\u6539
AuthErrorCodeEnum.A0246=\u4E00\u952E\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u767B\u5F55\u7ED1\u5B9A\u8D26\u53F7
AuthErrorCodeEnum.A0422=\u5730\u5740\u4E0D\u5728\u670D\u52A1\u8303\u56F4
AuthErrorCodeEnum.A0400=\u7528\u6237\u8BF7\u6C42\u53C2\u6570\u9519\u8BEF
AuthErrorCodeEnum.A0427=\u8BF7\u6C42 JSON \u89E3\u6790\u5931\u8D25
AuthErrorCodeEnum.B0300=\u7CFB\u7EDF\u8D44\u6E90\u5F02\u5E38
AuthErrorCodeEnum.B0301=\u52A0\u8F7D\u5E94\u7528\u8BA4\u8BC1\u89C4\u5219\u5931\u8D25
AuthErrorCodeEnum.C0319=\u6570\u636E\u4E0D\u5B58\u5728
AuthErrorCodeEnum.C0001=\u8C03\u7528\u7B2C\u4E09\u65B9\u670D\u52A1\u51FA\u9519
AuthErrorCodeEnum.B0001=\u7CFB\u7EDF\u6267\u884C\u51FA\u9519
AppAuthStrategyManagerImpl.other.msg1=\u8868\u4E0D\u5B58\u5728:
AppAuthStrategyManagerImpl.other.msg2=\u5E94\u7528\u7F16\u7801\u9519\u8BEF\uFF0C\u65E0\u6CD5\u52A0\u8F7D\u5E94\u7528\u7684\u8BA4\u8BC1\u914D\u7F6E\u4FE1\u606F,\u79DF\u6237\u7F16\u7801\uFF1A\u3010
AppAuthStrategyManagerImpl.other.msg3=\u3011\uFF0C\u5E94\u7528\u7F16\u7801\uFF1A\u3010
ReturnCode.SUCCESS=\u8BF7\u6C42\u5904\u7406\u6210\u529F
ReturnCode.SUCCESS_SAAS=\u8BF7\u6C42\u5904\u7406\u6210\u529F
ReturnCode.A0200=\u7528\u6237\u8D26\u53F7\u6216\u5BC6\u7801\u9519\u8BEF
ReturnCode.A0202=\u9A8C\u8BC1\u7801\u9519\u8BEF
ReturnCode.A0204=\u9700\u8981\u9A8C\u8BC1\u7801
ReturnCode.A0300=\u8BBF\u95EE\u6743\u9650\u5F02\u5E38
ReturnCode.A0301=\u8BBF\u95EE\u672A\u6388\u6743
ReturnCode.A0311=\u6388\u6743\u5DF2\u8FC7\u671F
ReturnCode.A0201=\u7528\u6237\u8D26\u6237\u4E0D\u5B58\u5728
ReturnCode.A0203=\u8D26\u53F7\u5DF2\u8FC7\u671F
ReturnCode.A0400=\u7528\u6237\u8BF7\u6C42\u53C2\u6570\u9519\u8BEF
ReturnCode.A0420=\u8BF7\u6C42\u53C2\u6570\u503C\u8D85\u51FA\u5141\u8BB8\u7684\u8303\u56F4
ReturnCode.A0422=url\u5730\u5740\u4E0D\u5728\u670D\u52A1\u8303\u56F4
ReturnCode.A0430=\u7528\u6237\u8F93\u5165\u5185\u5BB9\u975E\u6CD5
ReturnCode.A0427=\u8BF7\u6C42 JSON \u89E3\u6790\u5931\u8D25
ReturnCode.A0600=\u7528\u6237\u8D44\u6E90\u5F02\u5E38
ReturnCode.C0300=\u6570\u636E\u5E93\u670D\u52A1\u51FA\u9519
ReturnCode.C0311=\u8868\u4E0D\u5B58\u5728
ReturnCode.C0312=\u5217\u4E0D\u5B58\u5728
ReturnCode.C0313=\u8868\u5B58\u5728\u6570\u636E
ReturnCode.C0314=\u5217\u5B58\u5728\u6570\u636E
ReturnCode.C0315=\u5B58\u5728\u8868\u88AB\u5176\u4ED6\u8868\u5F15\u7528
ReturnCode.C0316=\u5B58\u5728\u5217\u88AB\u88AB\u5176\u4ED6\u8868\u5F15\u7528
ReturnCode.C0317=\u7CFB\u7EDF\u5B57\u6BB5\u4E0D\u5141\u8BB8\u5220\u9664
ReturnCode.C0318=\u7CFB\u7EDF\u8868\u4E0D\u5141\u8BB8\u5220\u9664
ReturnCode.C0319=\u6570\u636E\u4E0D\u5B58\u5728
ReturnCode.C0320=\u6570\u636E\u5DF2\u5B58\u5728
ReturnCode.C0321=\u4E0B\u8F7D\u6587\u4EF6\uFF0C\u6587\u4EF6\u5B8C\u6574\u8DEF\u5F84\u4E0D\u5141\u8BB8\u4E3A\u7A7A\u3002
ReturnCode.C0322=\u4E0B\u8F7D\u6587\u4EF6\uFF0C\u6587\u4EF6\u540D\u4E0D\u5141\u8BB8\u4E3A\u7A7A\u3002
ReturnCode.C0323=\u4E0B\u8F7D\u6587\u4EF6\uFF0C\u6587\u4EF6\u4E0D\u5B58\u5728\u3002
ReturnCode.C0324=\u76EE\u5F55\u521B\u5EFA\u5931\u8D25\u3002
ReturnCode.C0325=\u6587\u4EF6\u683C\u5F0F\u4E0D\u5408\u6CD5\u3002
ReturnCode.C0326=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25\u3002
ReturnCode.C0327=\u8868\u5143\u6570\u636E\u5BFC\u5165\u5931\u8D25\u3002
ReturnCode.C0328=\u5BFC\u5165\u5BFC\u51FA\u6A21\u677F\u4E0D\u5B58\u5728\u3002
ReturnCode.C0329=\u4E1A\u52A1\u5EFA\u6A21API\u4E0D\u5B58\u5728\u3002
ReturnCode.C0340=\u5B58\u5728\u5173\u8054
ReturnCode.C0330=\u8868\u7684\u4E1A\u52A1\u5B57\u6BB5\u4E3A\u7A7A\uFF0C\u4E0D\u521B\u5EFA\u6A21\u677F\u3002
ReturnCode.C0331=\u5BFC\u5165\u5BFC\u51FA\u5355\u5143\u683C\u4FE1\u606F\u6784\u5EFA\u5931\u8D25\u3002
ReturnCode.C0332=\u5BFC\u5165\u5BFC\u51FA\u6A21\u677FSheet\u540D\u79F0\u4E0D\u5141\u8BB8\u91CD\u590D\u3002
ReturnCode.C0333=\u8D85\u7EA7\u8868\u4E0D\u662F\u8F6C\u6362\u800C\u6765\uFF0C\u65E0\u6CD5\u56DE\u9000
ReturnCode.C0001=\u8C03\u7528\u7B2C\u4E09\u65B9\u670D\u52A1\u51FA\u9519
ReturnCode.B0001=\u7CFB\u7EDF\u6267\u884C\u51FA\u9519
DynamicUserAccountMapper.result.msg1=\u6839\u636E\u7528\u6237\u4FE1\u606F\u3010%s\u3011\u5E94\u8BE5\u6700\u591A\u53EA\u80FD\u67E5\u8BE2\u5230\u4E00\u6761\u8BB0\u5F55\uFF0C\u4F46\u73B0\u5728\u67E5\u8BE2\u5230\u3010%s\u3011\u6761\u3002

AuthErrorCodeEnum.A0247=\u5BC6\u7801\u5DF2\u5230\u8FBE\u6307\u5B9A\u4FEE\u6539\u65F6\u95F4\uFF0C\u8BF7\u4FEE\u6539
AuthErrorCodeEnum.A0248=\u60A8\u7684\u767B\u5F55IP\u4E0E\u7ED1\u5B9A\u7684IP\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u68C0\u67E5
SmsCodeMapServiceImpl.result.msg5=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SmsCodeMapServiceImpl.result.msg6=\u624B\u673A\u53F7\u6216\u9A8C\u8BC1\u7801\u9519\u8BEF
SmsCodeMapServiceImpl.result.msg7=\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F
SmsCodeMapServiceImpl.result.msg8=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SmsCodeMapServiceImpl.result.msg9=\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF\uFF0C\u60A8\u5DF2\u9519\u8BEF %S \u6B21\uFF0C\u518D\u9519\u8BEF %s \u6B21\u540E\u5C06\u9501\u5B9A\u8D26\u53F7\u3002
SmsCodeMapServiceImpl.result.msg10=\u9A8C\u8BC1\u78011\u5206\u949F\u5185\u6709\u6548\uFF0C\u8BF7\u52FF\u91CD\u590D\u53D1\u9001
SmsController.result.msg1=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SmsController.result.msg2=\u5F53\u524D\u7CFB\u7EDF\u6CA1\u6709\u8BE5\u624B\u673A\u53F7\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
SmsCodeLogPrintSender.result.msg1=1\u5C0F\u65F6\u5185\u77ED\u4FE1\u53D1\u9001\u6B21\u6570\u5DF2\u8FBE\u4E0A\u9650
SmsCodeLogPrintSender.result.msg2=\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u77ED\u4FE1\u670D\u52A1\u914D\u7F6E
SmsCodeLogPrintSender.result.msg3=\u3010\u5E7F\u897F\u53F8\u6CD5\u3011\u9A8C\u8BC1\u7801\uFF1A%s\uFF0C\u60A8\u6B63\u5728\u8FDB\u884C\u624B\u673A\u53F7\u767B\u5F55\uFF08\u82E5\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5220\u9664\u672C\u77ED\u4FE1\uFF09
SmsCodeLogPrintSender.result.msg4=\u3010\u5E7F\u897F\u53F8\u6CD5\u3011\u9A8C\u8BC1\u7801\uFF1A%s\uFF0C\u60A8\u6B63\u5728\u901A\u8FC7\u624B\u673A\u53F7\u8FDB\u884C\u5BC6\u7801\u627E\u56DE\uFF08\u82E5\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5220\u9664\u672C\u77ED\u4FE1\uFF09
SmsCodeLogPrintSender.result.msg5=\u3010\u624B\u673A\u53F7\u767B\u5F55\u3011\u9A8C\u8BC1\u7801\uFF1A%s\uFF0C\u60A8\u6B63\u5728\u8FDB\u884C\u624B\u673A\u53F7\u767B\u5F55\uFF08\u82E5\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5220\u9664\u672C\u77ED\u4FE1\uFF09
SmsCodeLogPrintSender.result.msg6=\u3010\u624B\u673A\u53F7\u9A8C\u8BC1\u3011\u9A8C\u8BC1\u7801\uFF1A%s\uFF0C\u60A8\u6B63\u5728\u901A\u8FC7\u624B\u673A\u53F7\u8FDB\u884C\u5BC6\u7801\u627E\u56DE\uFF08\u82E5\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5220\u9664\u672C\u77ED\u4FE1\uFF09
SmsServiceImpl.resule.msg1=\u624B\u673A\u53F7\u6216\u9A8C\u8BC1\u7801\u9519\u8BEF
SmsServiceImpl.resule.msg2=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SmsServiceImpl.resule.msg3=\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F
SmsServiceImpl.resule.msg4=\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF\uFF0C\u60A8\u5DF2\u9519\u8BEF%s\u6B21\uFF0C\u518D\u9519\u8BEF%s\u6B21\u540E\u9501\u5B9A\u8D26\u53F7
SmsServiceImpl.resule.msg5=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SmsServiceImpl.resule.msg6=\u9A8C\u8BC1\u78011\u5206\u949F\u5185\u6709\u6548\uFF0C\u8BF7\u52FF\u91CD\u590D\u53D1\u9001
SmsServiceImpl.resule.msg7=\u77ED\u4FE1\u53D1\u9001\u6210\u529F\uFF01
SecurityQuestionServiceImpl.result.msg1=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
SecurityQuestionServiceImpl.result.msg2=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
SecurityQuestionServiceImpl.result.msg3=\u9A8C\u8BC1\u6210\u529F
SecurityQuestionServiceImpl.result.msg4=\u5BC6\u4FDD\u7B54\u6848\u8F93\u5165\u9519\u8BEF\uFF0C\u60A8\u5DF2\u9519\u8BEF%s\u6B21\uFF0C\u518D\u9519\u8BEF%s\u6B21\u540E\u9501\u5B9A\u8D26\u53F7
SecurityQuestionServiceImpl.result.msg5=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
UserAccountController.result.msg1=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
UserAccountController.result.msg2=\u5F53\u524D\u7CFB\u7EDF\u6CA1\u6709\u8BE5\u624B\u673A\u53F7\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
UserAccountController.result.msg3=\u4E0D\u5B58\u5728\u7684\u627E\u56DE\u5BC6\u7801\u7C7B\u578B
UserAccountController.result.msg4=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7
UserAccountController.result.msg5=\u4E0D\u5B58\u5728\u7684\u627E\u56DE\u5BC6\u7801\u7C7B\u578B
UserAccountController.result.msg6=\u8BE5\u8D26\u53F7\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406
UserAccountController.result.msg7=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
UserAccountController.result.msg8=\u5F53\u524D\u7CFB\u7EDF\u6CA1\u6709\u8BE5\u624B\u673A\u53F7\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
UserAccountController.result.msg9=\u5165\u53C2\u5F02\u5E38
UserAccountController.result.msg10=\u65B0\u5BC6\u7801\u548C\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4
UserAccountController.result.msg11=\u4F1A\u8BDD\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u64CD\u4F5C
UserAccountController.result.msg12=\u4FEE\u6539\u6210\u529F

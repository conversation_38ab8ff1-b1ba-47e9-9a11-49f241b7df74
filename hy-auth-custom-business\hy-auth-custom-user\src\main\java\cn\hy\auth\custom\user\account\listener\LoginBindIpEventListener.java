package cn.hy.auth.custom.user.account.listener;

import cn.hutool.core.util.StrUtil;
import cn.hy.auth.common.security.core.authentication.mobile.event.LoginBindIpEvent;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.domain.UserPwdForceModifyResult;
import cn.hy.auth.custom.user.account.enums.UsmAppParamEnum;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.account.service.UserSecurityManageService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title: LoginBindIpEventListener
 * @description: 绑定登陆ip事件监听
 * @date 2024/4/28
 */
@Slf4j
@Component
public class LoginBindIpEventListener implements ApplicationListener<LoginBindIpEvent> {

    private final UserAccountService userAccountService;
    private final UserDetailsService userDetailsService;
    private final UserSecurityManageService userSecurityManageService;

    public LoginBindIpEventListener(UserAccountService userAccountService, UserDetailsService userDetailsService, UserSecurityManageService userSecurityManageService) {
        this.userAccountService = userAccountService;
        this.userDetailsService = userDetailsService;
        this.userSecurityManageService = userSecurityManageService;
    }

    @Override
    public void onApplicationEvent(LoginBindIpEvent loginBindIpEvent) {
        String ip = loginBindIpEvent.getIp();
        String mobile = loginBindIpEvent.getMobile();
        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(mobile, mobile);
        String userName = userByUserNameOrPhone.getUserAccountName();
        HyUserDetails account = (HyUserDetails) userDetailsService.loadUserByUsername(mobile);
        if (StrUtil.isBlank(ip)) {
            log.warn("用户【{}】的登录IP为空，无法校验IP限制", userName);
            return;
        }
        if (
                isLoginBindIp(account, ip)
        ) {
            loginBindIpEvent.setCheckPass(false);
            loginBindIpEvent.setMsg(AuthErrorCodeEnum.A0248.msg());
            loginBindIpEvent.setCode(AuthErrorCodeEnum.A0248.code());
            UserPwdForceModifyResult result = new UserPwdForceModifyResult(AuthErrorCodeEnum.A0248.code(), AuthErrorCodeEnum.A0248.msg(), null);
            loginBindIpEvent.setResult(JSON.toJSONString(result));
            log.info("绑定用户登录ip检查-->验证用户【{}】的登录IP【{}】与绑定的IP不一致，请检查！", userName, ip);
        } else {
            loginBindIpEvent.setCheckPass(true);
        }
    }

    private boolean isLoginBindIp(HyUserDetails account, String ip) {
        try {
            return userSecurityManageService.existsEnableParamCfg(UsmAppParamEnum.LOGIN_BIND_IP.getCode())
                    && userSecurityManageService.ipRestrict(account, ip);
        } catch (Exception e) {
            log.warn("绑定登陆ip校验异常：{}", e.getMessage(), e);
        }
        return false;
    }

}

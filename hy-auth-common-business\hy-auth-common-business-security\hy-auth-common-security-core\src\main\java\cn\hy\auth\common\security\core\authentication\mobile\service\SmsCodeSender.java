package cn.hy.auth.common.security.core.authentication.mobile.service;

import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 手机短信发送者
 *
 * <AUTHOR>
 * @date 2020/11/24
 */
public interface SmsCodeSender {
    /**
     * 发送手机短信
     *
     * @param mobile  手机号
     * @param code    验证码
     * @param request 请求封装体,用于业务自定义扩展
     */
    String send(String mobile, String code, String appCode , String lesseeCode, SmsLoginVO smsLoginVO, String appParamKey);

}
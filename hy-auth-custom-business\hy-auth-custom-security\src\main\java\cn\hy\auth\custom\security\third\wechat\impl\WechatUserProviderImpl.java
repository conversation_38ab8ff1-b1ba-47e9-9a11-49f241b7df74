package cn.hy.auth.custom.security.third.wechat.impl;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.service.HySocialUserInfoApi;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.third.wechat.domain.WechatProviderUser;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 企业微信用户信息获取
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class WechatUserProviderImpl extends AbstractOAuth2ApiBinding implements HySocialUserInfoApi {

    @Value("${third.proxy.qywechat.url:https://prod-proxy-qyapi.haoyuntech.com/wxproxy}")
    private String baseUrl;

    /**
     * 响应码
     */
    private static final String ERROR_CODE_PARAMS = "errcode";

    @Override
    public WechatProviderUser getProviderUserInfo(String lesseeCode, String appCode, String code, ProviderAppAccessToken providerAppAccessToken,
                                                  ProviderInfo providerInfo, Map<String, Object> requestParamMap) {
        WechatProviderUser talkProviderUser = new WechatProviderUser();
        String url = baseUrl+providerInfo.getAppUserInfoUrl()+"?code=" + code+"&access_token="+providerAppAccessToken.getAccessToken();
        String response = getRestTemplate().getForObject(url, String.class);
        if (response != null) {
            JSONObject resObject = JSONObject.parseObject(response);
            if (resObject.getInteger(ERROR_CODE_PARAMS) == 0) {
                //JSONObject resultInfo = resObject.getJSONObject("result");
                //String name = resultInfo.getString("name");
                String userid = resObject.getString("userid");
                String user_ticket = resObject.getString("user_ticket");
                talkProviderUser.setProviderId(providerInfo.getProviderId());
                talkProviderUser.setProviderUserid(userid);
                talkProviderUser.setUserInfoJson(resObject.toJSONString());
                talkProviderUser.setUserDisplayName(null);
                talkProviderUser.setUnionId(user_ticket);
                return talkProviderUser;
            }
        }
        log.error("查询钉钉用户信息失败!");
        throw new AuthBusinessException("500", LocaleUtil.getMessage("WechatUserProviderImpl.result.msg1", null)+response);
    }

    @Override
    public ProviderUserInfo getProviderUserInfo(String code,ProviderAppAccessToken appAccessToken, ProviderInfo providerInfo) {
        return getProviderUserInfo(null, null, code, appAccessToken, providerInfo, null);
    }
}

package cn.hy.auth.custom.security.third.wechat.impl;

import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class HyWechatConnectionFactory extends HyConnectionFactory {

    protected HyWechatConnectionFactory(HyWechatServiceProvider serviceProvider) {
        super(ProviderType.WECHAT.getCode(), serviceProvider);
    }
}

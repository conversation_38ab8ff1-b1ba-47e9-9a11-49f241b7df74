package cn.hy.auth.custom.multi.systable;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/1 15:44
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/appInfo")
@Slf4j
public class AppInfoStatusController {
    private final AppInvolvedTableServiceImpl AppInvolvedTableServiceImpl;
    @PutMapping("/status")
    public Map<String,Object> updateAppInfoStatus(@RequestParam(value = "less_code",required = false) String lessCode, @RequestParam(value = "app_code",required = false)String appCode, Integer status){
        Map<String,Object> resultMap = new HashMap<>();
        if (StringUtils.isBlank(lessCode)){
            resultMap.put("code",AuthErrorCodeEnum.A0101.code());
            resultMap.put("msg", LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg1", null));
            return resultMap;
        }
        if (StringUtils.isBlank(appCode)){
            resultMap.put("code",AuthErrorCodeEnum.A0101.code());
            resultMap.put("msg", LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg2", null));
            return resultMap;
        }
        if (!(status==0||status==1||status==2)){
            resultMap.put("code",AuthErrorCodeEnum.A0400.code());
            resultMap.put("msg", LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg3", null));
            return resultMap;
        }

        try {
            int i = AppInvolvedTableServiceImpl.updateAppInfoStatus(lessCode, appCode, status);
            resultMap.put("code",AuthErrorCodeEnum.SUCCESS.code());
            resultMap.put("msg",LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg4", null));
            resultMap.put("result",i);
            return resultMap;
        }catch (Exception e){
            log.error("{}_{} 修改应用状态为：{},失败了.",lessCode,appCode,status,e);
            resultMap.put("code",AuthErrorCodeEnum.B0001.code());
            resultMap.put("msg",LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg5", "")+","+e.getMessage());
            return resultMap;
        }

    }
}

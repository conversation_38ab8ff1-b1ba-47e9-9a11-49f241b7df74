package cn.hy.auth.custom.common.constant;

import cn.hy.auth.custom.common.enums.EncryptTypeEnum;

/**
 * 登录业务的参数名常量
 *
 * <AUTHOR>
 * @date 2020-11-27 09:28
 **/
public class LoginParamterConts {

    /**
     * 用户名
     */
    public static final String USER_NAME = "username";

    public static final String PASSWORD = "password";

    public static final String CLIENT_ID = "client_id";
    public static final String CLIENT_SECRET = "client_secret";

    public static final String CLIENT_TYPE = "client_type";
    public static final String LESSEE_CODE = "lessee_code";
    public static final String APP_CODE = "app_code";
    public static final String LOGIN_IP = "ip";
    public static final String LOGIN_MAC = "mac";
    /**
     * 密码加密类型
     *
     * @see EncryptTypeEnum
     */
    public static final String PWD_ENCRYPTION_TYPE = "pwd_encryption_type";

    /**
     * 忽略密码状态检查
     *
     * @see EncryptTypeEnum
     */
    public static final String IGNORE_PWD_STATUS = "ignore_pwd_status";

    /**
     * 客户端名，CLIENT_TYPE=4时有用
     */
    public static final String CLIENT_NAME = "client_name";
    public static final String USER_INFO = "user_info";
    /**
     * oauth接口参数
     */
    public static final String GRANT_TYPE = "grant_type";

    /**
     * 验证码id
     */
    public static final String VER_CODE_ID = "ver_code_Id";
}

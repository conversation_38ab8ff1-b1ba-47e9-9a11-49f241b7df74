package cn.hy.auth.common.security.oauth2.config;

import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationProviderChain;
import cn.hy.auth.common.security.core.properties.WebIgnoreProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.List;

/**
 * 安全配置
 *
 * <AUTHOR>
 * @date 2018/12/25 0025 10:53
 */
@Configuration
@EnableWebSecurity
public class HyWebSecurityConfig extends WebSecurityConfigurerAdapter {
    /**
     * 自定义登录业务验证链
     */
    @Autowired(required = false)
    private HyAuthenticationProviderChain hyAuthenticationProviderChain;
    // copy from oauth2里的 AuthorizationServerSecurityConfigurer
    /**
     * 用户信息查询的服务,必须由调用方实现
     */
    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 忽略验证的url或者是资源
     */
    @Autowired(required = false)
    private WebIgnoreProperties webIgnoreConfig;

    /**
     * 用户自定义加载用户详情接口。
     * 会覆盖了userDetailsServiceBean()返回的结果
     */
    @Override
    public UserDetailsService userDetailsService() {
        return userDetailsService;
    }

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 这一步的配置是必不可少的，否则SpringBoot会自动配置一个AuthenticationManager,覆盖掉内存中的用户
     * 必须注入 AuthenticationManager，不然oauth无法处理四种授权方式
     * 初始化 bean：AuthenticationManager用作安全认证
     * <p>
     * return
     * throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * 数据库验证模式
     *
     * @return DaoAuthenticationProvider
     */
    private DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);
        daoAuthenticationProvider.setUserDetailsService(userDetailsService());
        return daoAuthenticationProvider;
    }

    /**
     * 为oauth2登录方式创建安全验证的Authentication对象。不符合可以直接抛异常
     *
     * @param auth .
     * @throws Exception .
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        //自动注入所有自定义provider
        if (this.hyAuthenticationProviderChain != null) {
            this.hyAuthenticationProviderChain.getAuthenticationProviders().forEach(
                    auth::authenticationProvider
            );
        }
        // 父类注入的是DaoAuthenticationProvider。
        auth.authenticationProvider(daoAuthenticationProvider());
    }

    /**
     * 4、配置HttpSecurity,对http请求url的安全约束
     * 4.0 url放行或者是限制，放行/oauth/**
     * 4.1 登录方面：url、用户名和密码字段名、登录失败和成功的handler
     * 4.2 登出方面：url、登出成功的handler
     * 4.3 异常处理：authenticationEntryPoint 和 accessDeniedHandler
     * 4.4 session管理设置
     * 4.5 跨域安全等设置
     *
     * @param http .
     */
    @Override
    protected void configure(HttpSecurity http) {
        // 资源服务器拦截的路径 注意此路径不要拦截主过滤器（spring security）放行的URL
    }

    /**
     * 配置忽略的url
     * 常配置的有静态文件，一般在配置文件中配置
     *
     * @param web .
     */
    @Override
    public void configure(WebSecurity web) {
        if (webIgnoreConfig != null && webIgnoreConfig.getPattern() != null) {
            List<String> pattern = webIgnoreConfig.getPattern();
            web.ignoring().antMatchers(pattern.toArray(new String[0]));
        }
    }

}

package cn.hy.auth.custom.user.account.service;

import cn.hutool.core.map.MapUtil;
import cn.hy.auth.custom.user.cache.service.KickOutTokenStoreService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.stereotype.Component;
import java.util.HashMap;

/**
 * 
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/11
 */
@Slf4j
@Aspect
@Component
public class CheckTokenKickOutAop {
    private  final KickOutTokenStoreService kickOutTokenStoreService;

    public CheckTokenKickOutAop(KickOutTokenStoreService kickOutTokenStoreService) {
        this.kickOutTokenStoreService = kickOutTokenStoreService;
    }

    @Pointcut("execution(* org.springframework.security.oauth2.provider.endpoint.CheckTokenEndpoint.checkToken(..))")
    public void checkTokenMethod(){}

    @Around("checkTokenMethod()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        try {
            return point.proceed();
        }catch (InvalidTokenException e){
            if (e.getMessage().contains("Token was not recognised")){
                // 获取方法参数
                Object[] args = point.getArgs();
                // 由于我们知道checkToken方法有一个String类型的参数，我们可以直接获取它
                String token = (String) args[0];
                // 判断是否被踢出了
                KickOutEnum kickOutTokenEnum = kickOutTokenStoreService.getKickOutTokenMsg(token);
                if (kickOutTokenEnum != null){
                    HashMap<String, String> map =
                            MapUtil.of("code", kickOutTokenEnum.getCode());
                    map.put("msg",kickOutTokenEnum.getMsg());
                    throw new InvalidTokenException(JSON.toJSONString(map));
                }
            }
            throw e;
        }
    }
}

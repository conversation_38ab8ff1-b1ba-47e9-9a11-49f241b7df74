package cn.hy.auth.common.security.core.authentication.mobile.event;

import org.springframework.context.ApplicationEvent;

public class CheckUserAccountLockEvent extends ApplicationEvent {

    private String mobile;

    private Boolean isLock;

    private Boolean userExist;

    private String email;

    public CheckUserAccountLockEvent(String mobile) {
        super(mobile);
        this.mobile = mobile;
    }

    public CheckUserAccountLockEvent(String mobile, String email) {
        super(email);
        this.email = email;
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public boolean isLock() {
        return isLock;
    }

    public void setLock(boolean lock) {
        isLock = lock;
    }

    public Boolean getUserExist() {
        return userExist;
    }

    public void setUserExist(Boolean userExist) {
        this.userExist = userExist;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}

package cn.hy.auth.custom.security.oauth2.environment.service;

import cn.hy.auth.custom.security.oauth2.environment.dao.OauthSysMMapper;
import cn.hy.auth.custom.security.oauth2.environment.domain.OauthSysMDO;
import cn.hy.dataengine.context.DataEngineContextProxy;
import cn.hy.id.IdWorker;
import cn.hy.metadata.engine.common.MetaDataEngineContext;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

/**
 * 系统环境标识服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/11
 */
@Service
@Slf4j
public class OauthSysMServiceImpl implements OauthSysMService {

    private final OauthSysMMapper oauthSysMMapper;

    private final IdWorker idWorker;
    private final Cache<String, String> cache = Caffeine.newBuilder()
            .maximumSize(1)  // 最大缓存1条数据
            .recordStats()
            .build();

    public OauthSysMServiceImpl(OauthSysMMapper oauthSysMMapper, IdWorker idWorker) {
        this.oauthSysMMapper = oauthSysMMapper;
        this.idWorker = idWorker;
    }

    @Override
    public void initializeSystemEnvironmentSymbol() {
        // 判断表中是否有数据，有数据则判定为已有环境标识记录，不做处理
        if (oauthSysMMapper.count() == 0) {
            String lesseeCodeBefore = MetaDataEngineContext.getContext().getLesseeCode();
            String appCodeBefore = MetaDataEngineContext.getContext().getAppCode();
            try {
                // 清空lesseeCode和appCode
                MetaDataEngineContext.getContext().clearLesseeCodeAndAppCodeInAttachments();
                DataEngineContextProxy.getContext().clearLesseeCodeCopy().clearAppCodeCopy();
                // 没有数据，则插入一条记录
                OauthSysMDO oauthSysM = new OauthSysMDO();
                oauthSysM.setId(idWorker.nextId());
                oauthSysM.setGmtCreate(new Date());
                oauthSysM.setGmtModified(new Date());
                // 生成16位uuid（不包含-）
                String environmentSymbol = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
                if (log.isInfoEnabled()){
                    log.info(" auth 初始化环境标识完成，环境标识为: {}", environmentSymbol);
                }
                oauthSysM.setUuid(environmentSymbol);
                oauthSysMMapper.insert(oauthSysM);
            } finally {
                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);
                DataEngineContextProxy.getContext().setLesseeCodeCopy(lesseeCodeBefore).setAppCodeCopy(appCodeBefore);
            }
        }else {
            if (log.isInfoEnabled()){
                log.info(" auth 该环境已有记录环境标识，不需要重复初始化环境标记。具体环境标识可见oauth_sys_m表中的uuid字段数据");
            }
        }
    }

    @Override
    public String getSystemEnvironmentSymbol() {
        // 从缓存中获取数据
        String cachedUuid = cache.getIfPresent("systemEnvironmentSymbol");
        if (cachedUuid == null) {
            String lesseeCodeBefore = MetaDataEngineContext.getContext().getLesseeCode();
            String appCodeBefore = MetaDataEngineContext.getContext().getAppCode();
            try {
                // 清空lesseeCode和appCode
                MetaDataEngineContext.getContext().clearLesseeCodeAndAppCodeInAttachments();
                DataEngineContextProxy.getContext().setLesseeCode(null);
                DataEngineContextProxy.getContext().setAppCode(null);
                // 如果缓存中没有数据，则查询数据库
                cachedUuid = oauthSysMMapper.selectFirstUuid();
                // 更新缓存
                cache.put("systemEnvironmentSymbol", cachedUuid);
            } finally {
                MetaDataEngineContext.getContext().putLessCodeToAttachments(lesseeCodeBefore).putAppCodeToAttachments(appCodeBefore);
                DataEngineContextProxy.getContext().setLesseeCode(lesseeCodeBefore);
                DataEngineContextProxy.getContext().setAppCode(appCodeBefore);
            }
        }
        return cachedUuid;
    }

    @Override
    public String clearCache() {
        // 打印缓存信息
        if (log.isInfoEnabled()){
            log.info("Cache stats: {}", cache.stats());
        }
        // 清空缓存
        cache.invalidateAll();
        return "Cache cleared";
    }
}

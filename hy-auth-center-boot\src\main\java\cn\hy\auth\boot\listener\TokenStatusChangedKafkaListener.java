package cn.hy.auth.boot.listener;

import cn.hy.auth.common.security.oauth2.token.store.HyRedisLocalCacheTokenStore;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 类描述：监听更新token配置信息，用于刷新更新缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2024/6/13 15:49
 **/
@Slf4j
@Service
@ConditionalOnProperty(
        prefix = "auth.token.store",
        name = "type",
        havingValue = "redisLocalCache"
)
public class TokenStatusChangedKafkaListener {
    private final HyRedisLocalCacheTokenStore hyRedisLocalCacheTokenStore;
    public TokenStatusChangedKafkaListener(TokenStore hyRedisLocalCacheTokenStore) {
        this.hyRedisLocalCacheTokenStore = (HyRedisLocalCacheTokenStore) hyRedisLocalCacheTokenStore;
    }

    @KafkaListener(topics = "${hy.saas.token.status.topic:hy-auth-sys-token-status}",
            groupId = "#{authKafkaGroupIdUtil.buildSaasGroupId()}"
    )
    public void processAsyncTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional<Object> message = Optional.ofNullable(record.value());
        try {
            if (message.isPresent()) {
                String msg = (String)message.get();
                log.debug("消费了： Topic:{},Message:{}",record.topic(),msg);
                JSONObject jsonObject = JSON.parseObject(msg);
                String type = (String) jsonObject.get("type");
                String token = (String) jsonObject.get("token");
                String refreshToken = (String) jsonObject.get("refreshToken");
                try {
                    if ("revoked".equals(type)){
                        hyRedisLocalCacheTokenStore.onTokenRevoked(token,refreshToken);
                    } else if ("refreshToken".equals(type)){
                        hyRedisLocalCacheTokenStore.onTokenRefreshed(token,refreshToken);
                    }
                } catch (Exception e) {
                    log.warn("处理Kafka的token 状态变化消息报错，可以忽略不处理。{}",e.getMessage());
                }
            }
        }finally {
            ack.acknowledge();
        }
    }
}

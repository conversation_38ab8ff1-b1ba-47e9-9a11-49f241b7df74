package cn.hy.auth.custom.security.third.iam;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hy.auth.common.security.core.authentication.iam.bean.IamProviderUser;
import cn.hy.auth.common.security.core.authentication.iam.def.IamAuthService;
import cn.hy.auth.common.security.oauth2.util.OkHttpTools;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.security.common.SqlCreateUtil;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 佛科院认证相关业务逻辑
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Service
@Slf4j
public class IamAuthServiceImpl implements IamAuthService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TableRelocateUtil tableRelocateUtil;

    @Autowired
    private OkHttpTools okHttpTools;

    @Value("${IAM_auth_center:http://*************:86}")
    private String iamHost;

    @Value("${third.openapi.iam.user_url:/ims-idms/usersession/getUserInfoByCode?clientId=wlwpt_system}")
    private String iamUserUrl;

    @Override
    public IamProviderUser getUserInfo(String lesseeCode, String appcode, String code) {
        String url = iamHost + iamUserUrl;
        log.info("请求获取url地址:{}", url);
        FormBody responseBody = new FormBody.Builder()
                .add("code", code).build();
        IamProviderUser iamProviderUser = new IamProviderUser();
        try {
            String postResult = okHttpTools.postForm(url, responseBody);
            //String postResult = "{\"code\": 200,\"success\": true,\"data\":{\"userInfo\": {\"id\": \"dfd3cfd2044fb94f9974f12fc4928828\",\"companyId\": \"1\",\"orgId\": \"660\",\"loginName\": \"admin\",\"password\": \"\",\"no\": \"\",\"name\": \"cesq1\",\"email\": \"\",\"phone\": \"\",\"mobile\": \"\",\"userType\": \"\",\"photo\": \"\",\"loginIp\": \"\",\"loginDate\": \"\",\"loginFlag\": \"1\",\"createBy\": \"\",\"createDate\": \"2020-04-08 11:36:51\",\"updateBy\": \"\",\"updateDate\": \"2020-04-08 11:36:51\",\"remarks\": \"\",\"delFlag\": \"0\",\"sort\": -1,\"posCode\": \"\",\"token\": \"d95ca281-2c0d-45c7-903f-dc0f220c0422\",\"partyCost\": -1,\"gender\": \"\",\"birthday\": \"\",\"education\": \"\",\"residence\": \"\",\"nation\": \"\",\"userId\": 1247730094483439618,\"idCard\": \"\",\"userStatus\": \"0\",\"entranceFactory\": \"\",\"leaveFactory\": \"\",\"secondaryPassword\": \"\",\"directSuperior\":\"\",\"commonToken\": \"\"}}}";
            log.info("获取第三方用户信息,接口返回结果：{}", postResult);
            if (postResult != null) {
                JSONObject resObj = JSONObject.parseObject(postResult);
                if (resObj.containsKey("code") && !resObj.getInteger("code").equals(200)) {
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("IamAuthServiceImpl.result.msg1", null) + responseBody + "," +
                            LocaleUtil.getMessage("IamAuthServiceImpl.result.msg2", null) + resObj.getString("msg") + "】");
                }
                JSONObject data = resObj.getJSONObject("data");
                if (data != null && data.size() > 0) {
                    JSONObject userInfo = data.getJSONObject("userInfo");
                    String id = userInfo.getString("id");
                    String companyId = userInfo.getString("companyId");
                    String orgId = userInfo.getString("orgId");
                    String loginName = userInfo.getString("loginName");
                    String name = userInfo.getString("name");
                    String token = userInfo.getString("token");
                    String userId = userInfo.getString("userId");
                    iamProviderUser.setCompanyId(companyId);
                    iamProviderUser.setId(id);
                    iamProviderUser.setUserAccountName(loginName);
                    iamProviderUser.setUserName(name);
                    iamProviderUser.setUserId(userId);
                    iamProviderUser.setToken(token);
                    iamProviderUser.setOrgId(orgId);
                    iamProviderUser.setCompanyId(companyId);
                    iamProviderUser.setLesseeCode(lesseeCode);
                    iamProviderUser.setAppCode(appcode);
                    iamProviderUser.setUserInfoJson(userInfo.toJSONString());
                    return iamProviderUser;
                } else {
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("IamAuthServiceImpl.result.msg1", null) + responseBody + "," +
                            LocaleUtil.getMessage("IamAuthServiceImpl.result.msg2", null) + resObj.getString("msg") + "】");
                }
            } else {
                throw new AuthBusinessException("500", LocaleUtil.getMessage("IamAuthServiceImpl.result.msg3", null));
            }
        } catch (Exception e) {
            throw new AuthBusinessException("500", "" + e.getMessage() + "");
        }
    }

    @Override
    public Map<String, Object> getByUserAccount(IamProviderUser userInfo) {
        List<Map<String, Object>> resultMap = findUserAccountName(userInfo.getLesseeCode(), userInfo.getAppCode(), userInfo.getUserAccountName());
        if (!CollectionUtils.isEmpty(resultMap)) {
            return resultMap.get(0);
        }
        return null;
    }

    @Override
    public Boolean regirstUser(IamProviderUser userInfo) {
        Map<String, Object> parseResult = new HashMap<>();
        parseResult.put("id", BigDecimal.valueOf(UUIDRandomUtils.getSnowUuid()));
        parseResult.put("password", "Ziykbgi8BMoadAYSAyiAxg==");
        parseResult.put("old_id", userInfo.getId());
        parseResult.put("user_account_name", userInfo.getUserAccountName());
        parseResult.put("username", userInfo.getUserName());
        parseResult.put("create_user_id", 1);
        parseResult.put("last_update_user_id", 1);
        parseResult.put("is_system_recode", "0");
        parseResult.put("sequence", 1);
        parseResult.put("data_version", "1");
        parseResult.put("create_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        parseResult.put("last_update_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        log.info("组装用户参数：{}", parseResult);
        String insertSql = SqlCreateUtil.createInsertSql(getTableName(userInfo.getLesseeCode(), userInfo.getAppCode(), "user_account"), parseResult, true);
        jdbcTemplate.execute(insertSql);
        return Boolean.TRUE;
    }


    /**
     * 修改表名
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @param tableCode  表编码
     * @return
     */
    @Override
    public String getTableName(String lesseeCode, String appCode, String tableCode) {
        Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(tableCode);
        if (tableMeta.isPresent()) {
            TableMData tableMData = tableMeta.get();
            lesseeCode = tableMData.getLessCode();
            appCode = tableMData.getAppCode();
        }
        StringBuilder builder = new StringBuilder(128);
        return builder.append(lesseeCode).append("_").append(appCode).append("_").append(tableCode).toString();
    }


    private List<Map<String, Object>> findUserAccountName(String lesseeCode, String appCode, String user_account) {
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append(" select * from " + getTableName(lesseeCode, appCode, "user_account ") + " where user_account_name = ? ");
        return jdbcTemplate.queryForList(sqlBuilder.toString(), user_account);
    }
}

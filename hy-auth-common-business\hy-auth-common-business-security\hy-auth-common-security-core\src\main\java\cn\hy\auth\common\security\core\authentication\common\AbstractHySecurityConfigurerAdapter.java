package cn.hy.auth.common.security.core.authentication.common;

import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;

/**
 * 类描述：公共的配置类，仅用于区分类，实现仅扫描注入HyAbstractSecurityConfigurerAdapter的配置
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/24 20:47
 **/
public abstract class AbstractHySecurityConfigurerAdapter extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
}

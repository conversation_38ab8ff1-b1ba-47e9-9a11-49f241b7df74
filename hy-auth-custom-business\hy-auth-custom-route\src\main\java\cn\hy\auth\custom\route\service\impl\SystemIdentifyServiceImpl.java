package cn.hy.auth.custom.route.service.impl;

import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.common.script.ScriptEngine;
import cn.hy.auth.custom.route.service.RequestIdentifyService;
import cn.hy.auth.custom.route.service.SystemIdentifyService;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 9:55
 **/
@Service
@Slf4j
public class SystemIdentifyServiceImpl implements SystemIdentifyService {
    private final ScriptEngine scriptEngine;
    private final RouteProperties routeProperties;
    private org.springframework.security.oauth2.provider.authentication.TokenExtractor tokenExtractor;
    private final RequestIdentifyService requestIdentifyService;

    public SystemIdentifyServiceImpl(ScriptEngine scriptEngine, RouteProperties routeProperties, RequestIdentifyService requestIdentifyService) {
        this.scriptEngine = scriptEngine;
        this.routeProperties = routeProperties;
        this.requestIdentifyService = requestIdentifyService;
        this.tokenExtractor = new BearerTokenExtractor();
    }

    @Override
    public String getRequestSystemAreaIdentify(HttpServletRequest request) {
        Object token = getToken(request);
        log.debug("收到请求token:{}",token);
        return requestIdentifyService.getRequestSystemAreaIdentify(token.toString());
//        Object result = scriptEngine.run(requestSystemAreaRecognizerScript, ImmutableMap.<String, Object>builder()
//                .put("tokenExtractor", tokenExtractor)
//                .put("token", token)
//                .put("servletRequest", request)
//                .build());
//        if (result != null) {
//            return result.toString();
//        }
//        return null;
    }

    @Override
    public Object getToken(HttpServletRequest request) {
        Object token = request.getParameter("token");
        if (token != null) {
            return token;
        }
        token = request.getParameter("refresh_token");
        if (token != null) {
            return token;
        }
        Authentication authentication = this.tokenExtractor.extract(request);
        if (authentication != null) {
            return authentication.getPrincipal();
        }
        return "";
    }
}

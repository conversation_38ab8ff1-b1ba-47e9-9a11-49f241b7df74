package cn.hy.auth.custom.user.account.dao;

import cn.hy.auth.custom.user.account.domain.SysdevUserOnlineStatusDTO;
import cn.hy.auth.custom.user.account.domain.ThirdUserRelationDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户账号信息数据库访问类
 *
 * <AUTHOR>
 * @date 2020-11-25 11:07
 **/
public interface UserAccountDao {
    /**
     * 查询返回单行数据,sql语句最好增加limit 1,避免查询出错
     *
     * @param sql sql语句
     * @return 结果
     */
    //Map<String, Object> queryForMap(@Param("sql") String sql);

    /**
     * 查询返回多条数据
     *
     * @param sql sql语句
     * @return 结果集
     */
    List<Map<String, Object>> queryForList(@Param("sql") String sql);

    /**
     * 查询返回多条数据
     * @param tableName 表名
     * @param fieldCodes 字段集合
     * @param value .
     * @return 结果集
     */
    List<Map<String, Object>> queryUserInfo(@Param("tableName") String tableName,
                                            @Param("list") List<String> fieldCodes,
                                            @Param("val") Object value);

    /**
     * 查询返回多条数据
     *
     * @param tableName 表名
     * @param uidField uid的字段名
     * @param uid uid值
     * @return 结果集
     */
    List<Map<String, Object>> queryByUid(@Param("tableName") String tableName,
                                         @Param("uidField")String uidField,
                                         @Param("userId")String uid);

    /**
     * 执行更新语句
     *
     * @param sql sql语句
     */
    void update(@Param("sql") String sql);

    /**
     * 执行更新语句
     *
     * @param tableName 表名
     * @param pwdField 密码字段名称
     * @param password 密码
     * @param uidField uid字段名称
     * @param uid  uid值
     */
    void updatePwd(@Param("tableName") String tableName,
                   @Param("pwdField")String pwdField,
                   @Param("password")Object password,
                   @Param("uidField")String uidField,
                   @Param("userId")String uid);

    /**
     * 执行更新语句
     *
     * @param tableName 表名
     * @param field 密码字段名称
     * @param time 密码
     * @param uidField uid字段名称
     * @param uid  uid值
     */
    void updatePwdModifyTime(@Param("tableName") String tableName,
                   @Param("field")String field,
                   @Param("time")Object time,
                   @Param("uidField")String uidField,
                   @Param("userId")String uid);

    /**
     * 查询记录
     * @param field .
     * @param uid .
     * @return .
     */
    Map<String,Object> queryAllByUid(@Param("field") String field,@Param("uid") String uid);

    /**
     * 查询记录
     * @param id .
     * @return .
     */
    Map<String,Object> queryThirdById(@Param("id") String id);

    /**
     * 查询记录
     * @param id .
     * @return .
     */
    Map<String,Object> queryThirdUserById(@Param("id") String id);

    /**
     * 插入第三方关系信息
     * @param  relationDTO
     * @return .
     */
    Integer saveThirdRelation(ThirdUserRelationDTO relationDTO);

    /**
     * 删除第三方关系信息
     * @param  providerId
     * @param  providerUserId
     * @param  userAccountName
     * @return .
     */
    void deleteThirdRelation(@Param("providerId")String providerId, @Param("providerUserId")String providerUserId,
                             @Param("userAccountName") String userAccountName);

    /**
     * 查询第三方关系信息
     * @param  providerId
     * @param  userAccountName
     * @param providerUserId
     * @return .
     */
    List<Map<String, Object>> findUserAccount(@Param("providerId") String providerId, @Param("userAccountName") String userAccountName,
                                        @Param("providerUserId")  String providerUserId);

    /**
     * 查询用户账号信息
     * @param
     * @param  userAccountName
     * @return .
     */
    Map<String, Object> selectOneByAccout(@Param("userAccountName") String userAccountName);

    /**
     * 查询第三方关系信息
     * @param  providerId
     * @param  userAccountName
     * @param providerUserId
     * @return .
     */
    List<Map<String, Object>> findProviderUser(@Param("providerId") String providerId, @Param("userAccountName") String userAccountName,
                                               @Param("providerUserId")  String providerUserId);
    Integer doSome();

    UserLoginAccountDTO selectUserIdByUserNameOrMobile(@Param("userName") String userName, @Param("phoneNumber") String phoneNumber);

    List<Map<String, Object>> queryUserInfoByJoinTable(@Param("val") String value, @Param("mobileLogin") boolean mobileLogin, @Param("appCode") String appCode, @Param("lesseeCode") String lesseeCode);

    void changeLoginInfoByUserNameList(@Param("userNames") List<String> userNames, @Param("status") String status, @Param("statusName") String statusName);

    Integer checkOnLineStatusTableIsExist(@Param("targetTableName") String targetTableName);

    void changeLoginStatusNotInSet(@Param("userNames") Set<String> userNames, @Param("status") String status, @Param("statusName") String statusName);

    UserLoginAccountDTO getUserByUserNameOrPhoneLimitOne(@Param("userName") String accountName, @Param("phoneNumber") String mobile);

    SysdevUserOnlineStatusDTO selectRecodeByUserName(@Param("userAccountName") String userAccountName);

    void insertUserNameStatus(@Param("statusDTO") SysdevUserOnlineStatusDTO statusDTO);

    void updateUserNameStatus(@Param("statusDTO") SysdevUserOnlineStatusDTO statusDTO);

    UserLoginAccountDTO selectUserByEmail(@Param("email") String email);

}

package cn.hy.auth.common.security.core.authentication.mobile.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @title: LoginBindIpEvent
 * @description: 绑定登陆ip事件
 * @date 2024/4/28
 */
@Getter
public class LoginBindIpEvent extends ApplicationEvent {

    /**
     * ip
     */
    private String ip;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否校验通过
     */
    private boolean checkPass;

    /**
     * 信息
     */
    private String msg;

    /**
     * 编码
     */
    private String code;

    /**
     * 结果
     */
    private String result;

    public LoginBindIpEvent(String ip, String mobile) {
        super(ip);
        this.ip = ip;
        this.mobile = mobile;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setCheckPass(boolean checkPass) {
        this.checkPass = checkPass;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setResult(String result) {
        this.result = result;
    }

}

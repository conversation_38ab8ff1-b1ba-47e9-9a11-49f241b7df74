package cn.hy.auth.custom.route.factory;

import cn.hy.auth.custom.route.common.TokenIdentifyConstant;
import cn.hy.auth.custom.route.indentify.ThirdIndentifyClient;
import cn.hy.auth.custom.route.indentify.impl.IAMThirdIndectifyServiceImpl;
import cn.hy.auth.custom.route.indentify.impl.SecThirdIndectifyServiceImpl;
import cn.hy.metadata.engine.common.utils.SpringUtil;
import org.springframework.context.ApplicationContext;

/**
 * 请求路由数据对象工厂
 * *
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public class ThirdIndentifyFactory {

    public static ThirdIndentifyClient getThirdIndentifyClient(String requestType) {
        ApplicationContext applicationContext = SpringUtil.getApplicationContext();
        switch (requestType) {
            //IAM系统
            case TokenIdentifyConstant.IAM:
                return applicationContext.getBean(IAMThirdIndectifyServiceImpl.class);
            //2.X系统
            case TokenIdentifyConstant.X2:
                return applicationContext.getBean(SecThirdIndectifyServiceImpl.class);
            default:
                return null;
        }
    }
}

package cn.hy.auth.custom.security.oauth2.listener;

import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.common.security.oauth2.event.LoginFailureEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEvent;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.security.utils.BaseH2Test;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import cn.hy.auth.custom.user.history.dao.UserLoginInfoMapper;
import cn.hy.auth.custom.user.history.service.impl.AuthLoginInfoServiceImpl;
import cn.hy.id.IdWorker;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class LoginLogEventListenerTest extends BaseH2Test {

    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    ApplicationContext applicationContext;

    @SpyBean
    AuthLoginInfoServiceImpl authLoginInfoService;

    @MockBean
    UserCacheTokenService userCacheTokenService;
    @MockBean
    UserCacheService userCacheService;
    @MockBean
    IdWorker idWorker;
    @MockBean
    UserLoginInfoMapper userLoginInfoMapper;

    UserAccountDTO userAccountDTO;
    private final AtomicBoolean isFirst = new AtomicBoolean(true);

    @Before
    public void setUp() throws Exception {
        AuthContext.getContext()
                .setLesseeCode("hy")
                .setAppCode("iot")
                .loginState()
                .setLoginType(LoginTypeEnum.USERNAME_PASSWORD);

        userAccountDTO = new UserAccountDTO();
               /* UserAccountDTO.builder()
                .id(1L)
                .userAccountName("test_user")
                .password("123")
                .mobile("***********")
                .build();*/
        doReturn(userAccountDTO).when(userCacheService).getUserAccountByLoginName(eq("test_user"));
        doReturn(1111L).when(idWorker).nextId();

        doReturn(1L).when(authLoginInfoService).getMaxSequence();
    }

    @Test
    public void onLoginSuccessLogEvent() {
        LoginSuccessEvent event = mock(LoginSuccessEvent.class);
        Authentication authentication = mock(Authentication.class);
        Map<String, Object> details = new HashMap<>();
        details.put("client_id", "client_test");
        HyUserDetails hyUserDetails = HyUserDetails.builderOf(userAccountDTO);

        doReturn(authentication).when(event).getAuthentication();
        doReturn(details).when(authentication).getDetails();
        doReturn(hyUserDetails).when(authentication).getPrincipal();

        applicationContext.publishEvent(event);

        verify(userLoginInfoMapper, times(1)).insert(any());
    }

    @Test
    public void onLoginFailureLogEvent() {
        LoginFailureEvent event = mock(LoginFailureEvent.class);
        Authentication authentication = mock(Authentication.class);
        Map<String, Object> details = new HashMap<>();
        details.put("client_id", "client_test");

        doReturn(authentication).when(event).getAuthentication();
        doReturn(details).when(authentication).getDetails();
        doReturn("test_user").when(authentication).getPrincipal();
        doReturn(new BadCredentialsException("xx")).when(event).getException();

        applicationContext.publishEvent(event);

        verify(userLoginInfoMapper, times(1)).insert(any());
    }
}
package cn.hy.auth.custom.common.properties;

import cn.hy.auth.custom.common.constant.AuthenticateUriConst;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * 请求信息分类，用于按不同方式提取租户、应用编码
 *
 * <AUTHOR>
 * @date 2012-11-12 17:21:26
 */
@Getter
@Component
@ConfigurationProperties(prefix = "hy.security.request-type")
public class RequestTypeProperties {
    /**
     * 需要忽略的 URL 格式
     */
    private Set<String> nonBusiness = Sets.newHashSet();
    /**
     * 登录的URL格式，从参数中提取租户、应用编码
     */
    private Set<String> login = Sets.newHashSet(AuthenticateUriConst.LOGIN_USERNAME_PASSWORD,
            AuthenticateUriConst.LOGIN_SMS_CODE,
            AuthenticateUriConst.LOGIN_OAUTH2,
            AuthenticateUriConst.LOGIN_RE_AUTH);
    /**
     * 认证相关URL格式，从Header的Authorization中的token提取租户、应用编码
     */
    private Set<String> authenticate = Sets.newHashSet(AuthenticateUriConst.LOGOUT, AuthenticateUriConst.LOGIN_OAUTH2,
            AuthenticateUriConst.AUTH_CHECK_TOKEN,AuthenticateUriConst.LOGIN_COPY_TOKEN);

    public void setNonBusiness(Set<String> nonBusiness) {
        this.nonBusiness = nonBusiness;
    }

    public void setLogin(Set<String> login) {
        this.login = Sets.newHashSet(AuthenticateUriConst.LOGIN_USERNAME_PASSWORD, AuthenticateUriConst.LOGIN_SMS_CODE,
                AuthenticateUriConst.LOGIN_OAUTH2);

        if (Objects.nonNull(login)) {
            this.login.addAll(login);
        }
    }

    public void setAuthenticate(Set<String> authenticate) {
        this.authenticate = Sets.newHashSet(AuthenticateUriConst.LOGOUT, AuthenticateUriConst.LOGIN_OAUTH2,
                AuthenticateUriConst.AUTH_CHECK_TOKEN);

        if (Objects.nonNull(authenticate)) {
            this.authenticate.addAll(authenticate);
        }
    }
}

package cn.hy.auth.custom.security.oauth2.userdetails;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthInfoParser;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/19 10:50
 **/
@Service
@Slf4j
public class MyUserDetailsServiceImpl implements UserDetailsService {

    private final UserAccountService userAccountService;

    public MyUserDetailsServiceImpl(UserAccountService userAccountService) {
        this.userAccountService = userAccountService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return getUserDetail(username);
    }

    private HyUserDetails loadUserDetails(String username){
        List<String> fields = AppAuthInfoParser.INSTANCE.parseLoginFields(username);
        log.debug("匹配到的登录字段列表：{}", fields);
        UserAccountDTO account;
        if (CollectionUtils.isNotEmpty(fields)) {
            account = userAccountService.getUserByMultiField(fields, username);
        } else {
            //如果不配置登录字段列表，则默认采用用户帐号登录
            account = userAccountService.getUserByUserName(username);
        }
        if (Objects.isNull(account)) {
            return null;
        }

        return HyUserDetails.builderOf(account);
    }
    private HyUserDetails getUserDetail(String userName){
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();
        String key = lesseeCode+appCode+userName;
        Optional<Object> accountOptional = AuthContext.getContext().get(key);
        HyUserDetails account;
        if (accountOptional.isPresent()){
            account = (HyUserDetails) accountOptional.get();
        } else {
            account = loadUserDetails(userName);
            // 注意：必须在tomcat的线程里进行。异步线程池里调用会有问题。因为没有合适的地方进行清空上下文的信息，可能会导致用户信息错乱！！！
            AuthContext.getContext().set(key,account);
        }
        return account;
    }
}

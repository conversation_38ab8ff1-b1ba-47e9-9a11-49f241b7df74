package cn.hy.auth.custom.user.account.domain;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * user_login_failure_info
 * 用户登录失败信息表
 * <AUTHOR>
 * @date   2022/04/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserLoginFailure {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 登录失败次数
     */
    private Long loginFailureCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 用户主键
     */
    private BigDecimal userId;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;
}
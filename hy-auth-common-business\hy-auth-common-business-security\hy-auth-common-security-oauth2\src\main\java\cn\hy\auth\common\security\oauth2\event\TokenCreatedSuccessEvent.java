package cn.hy.auth.common.security.oauth2.event;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

/**
 * 类描述：token 成功生成事件
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 10:56
 **/
@ToString
public class TokenCreatedSuccessEvent extends ApplicationEvent {


    private OAuth2AccessToken accessToken;

    public TokenCreatedSuccessEvent(OAuth2Authentication authentication, OAuth2AccessToken accessToken) {
        super(authentication);
        this.accessToken = accessToken;
    }

    /**
     * 认证信息
     */
    public OAuth2Authentication getAuthentication() {
        return (OAuth2Authentication) super.getSource();
    }

    /**
     * 生成的token对象
     */
    public OAuth2AccessToken getAccessToken() {
        return accessToken;
    }
}

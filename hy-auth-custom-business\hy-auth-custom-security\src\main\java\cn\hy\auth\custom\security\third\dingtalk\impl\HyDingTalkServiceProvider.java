package cn.hy.auth.custom.security.third.dingtalk.impl;

import cn.hy.auth.common.security.core.authentication.social.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class HyDingTalkServiceProvider implements HyServiceProvider {

    @Autowired
    private DingTalkProviderTokenImpl dingTalkProviderToken;

    @Autowired
    private DingTalkUserProviderImpl dingTalkUserProvider;

    @Autowired
    private DingTalkProviderInfoImpl dingTalkProviderInfo;

    @Override
    public HyOauth2Operations getOauth2Operations() {
        return dingTalkProviderToken;
    }

    @Override
    public HySocialUserInfoApi getSocialUserInfoApi() {
        return dingTalkUserProvider;
    }

    @Override
    public HyProviderInfoService getProviderInfoService() {
        return  dingTalkProviderInfo;
    }
}

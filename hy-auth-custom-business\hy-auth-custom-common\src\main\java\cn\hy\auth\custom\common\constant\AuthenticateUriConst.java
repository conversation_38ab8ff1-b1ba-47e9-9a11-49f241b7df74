package cn.hy.auth.custom.common.constant;

/**
 * 认证相关uri常量
 *
 * <AUTHOR>
 * @date 2020-12-10 11:50
 **/
public class AuthenticateUriConst {
    /**
     * 用户名密码登录地址
     */
    public static final String LOGIN_USERNAME_PASSWORD = "/**/login";
    /**
     * 手机验证码登录地址
     */
    public static final String LOGIN_SMS_CODE = "/**/login/mobile";
    /**
     * OAUTH2方式登录地址
     */
    public static final String LOGIN_OAUTH2 = "/**/oauth/token";
    /**
     * 二次认证登录地址
     */
    public static final String LOGIN_RE_AUTH = "/**/login/re-auth";

    /**
     * 通过token复制新的一组token
     */
    public static final String LOGIN_COPY_TOKEN = "/**/login/copy-token";

    /**
     * 校验token地址
     */
    public static final String AUTH_CHECK_TOKEN = "/**/oauth/check_token";

    /**
     * 登出
     */
    public static final String LOGOUT = "/**/oauth/revoke";
}

package cn.hy.auth.common.security.oauth2.token.store;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
/**
 *  缓存，可以自定义每个key的过期时间
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/1/11 11:16
 **/
public class ExpiringCache<K, V> {
    /**
     * 限制size，避免溢出问题
     */
    private final Cache<K, CacheEntry<V>> cache = Caffeine.newBuilder()
            //cache的初始容量值
            .initialCapacity(100)
            //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
            .maximumSize(40000)
            .expireAfterWrite(7200, TimeUnit.SECONDS).build();

    public V get(K key) {
        CacheEntry<V> entry = cache.getIfPresent(key);
        if (entry != null && !isExpired(entry)) {
            return entry.getValue();
        }
        // 如果条目已过期，从缓存中移除
        cache.invalidate(key);

        return null;
    }

    public void put(K key, V value, long ttl, TimeUnit unit) {
        long expirationTime = System.currentTimeMillis() + unit.toMillis(ttl);
        CacheEntry<V> entry = new CacheEntry<>(value, expirationTime);
        cache.put(key,entry);
    }


    public void clear(K key){
        cache.invalidate(key);
    }
    private boolean isExpired(CacheEntry<V> entry) {
        return System.currentTimeMillis() > entry.getExpirationTime();
    }

    @Getter
    private static class CacheEntry<V> {
        private final V value;
        private final long expirationTime;

        public CacheEntry(V value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }

    }


}
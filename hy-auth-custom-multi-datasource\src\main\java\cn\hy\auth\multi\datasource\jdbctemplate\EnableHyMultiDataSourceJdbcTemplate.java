package cn.hy.auth.multi.datasource.jdbctemplate;

import cn.hy.paas.multi.datasource.jdbctemplate.HyMultiDataSourceJdbcTemplateConfig;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;


/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/5/30 20:12
 **/
@Configuration
@Import(HyMultiDataSourceJdbcTemplateConfig.class)
public class EnableHyMultiDataSourceJdbcTemplate {

    public EnableHyMultiDataSourceJdbcTemplate(){
    }
}

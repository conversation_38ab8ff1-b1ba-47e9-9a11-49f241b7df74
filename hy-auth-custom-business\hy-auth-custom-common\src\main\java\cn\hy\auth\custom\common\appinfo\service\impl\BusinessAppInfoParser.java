package cn.hy.auth.custom.common.appinfo.service.impl;

import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.BaseAppInfoParser;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 业务请求参数解析器
 *
 * <AUTHOR>
 * @date 2020-12-13 13:13
 **/
@Service
@Slf4j
public class BusinessAppInfoParser extends BaseAppInfoParser {
    /**
     * 排序值，越小越靠前
     *
     * @return 排序值
     */
    @Override
    protected int order() {
        //不符合其他的类型，都当作业务请求解析
        return 99;
    }

    /**
     * 支持的uri模板列表
     *
     * @return 支持的uri模板列表
     */
    @Override
    protected @NonNull List<String> uriPatterns() {
        return new ArrayList<>();
    }

    /**
     * 是否支持该处理
     *
     * @param request 请求信息
     * @return 当前实例是否支持对该请求处理
     */
    @Override
    protected boolean supports(HttpServletRequest request) {
        return true;
    }

    /**
     * 解析得到租户、应用编码信息已经请求分类
     *
     * @param request 请求信息
     * @return 第一个属性：租户、应用编码；第二个属性：请求类型
     */
    @Override
    public ImmutablePair<AppInfoDTO, RequestTypeEnum> parse(HttpServletRequest request) {
        AppInfoDTO info = parseOfBusiness(request);
        return Optional.ofNullable(info).map(i -> ImmutablePair.of(i, RequestTypeEnum.BUSINESS)).orElse(null);
    }

    /**
     * 解析请求类型信息
     *
     * @param request 请求信息
     * @return 请求分类
     */
    @Override
    public RequestTypeEnum parseType(HttpServletRequest request) {
        //前面解析器都匹配不到，到这里都是业务接口
        return RequestTypeEnum.BUSINESS;
    }

    /**
     * 从业务类型的请求解析
     *
     * @param request 请求信息
     */
    private AppInfoDTO parseOfBusiness(HttpServletRequest request) {
        String lesseeCode = obtainParameter(request, LoginParamterConts.LESSEE_CODE);
        String appCode = obtainParameter(request, LoginParamterConts.APP_CODE);
        //先尝试从参数中解析，参数中没有相关信息，再从uri解析
        if (StringUtils.isNotBlank(lesseeCode) && StringUtils.isNotBlank(appCode)) {
            return AppInfoDTO.builder().lesseeCode(lesseeCode).appCode(appCode).build();
        }

        String uri = request.getRequestURI();
        if (uri.indexOf(PATH_SEPARATOR) == 0) {
            //去除第一个斜杠
            uri = uri.substring(1);
        }

        String[] uriPart = uri.split(PATH_SEPARATOR);
        if (uriPart.length < MIN_URI_PART) {
            log.error("uri内容不合法，解析租户和应用信息失败。uri：【{}】", uri);
            return null;
        }
        //第一段是项目编码，第二段是租户编码，第三段是应用编码
        return AppInfoDTO.builder().lesseeCode(uriPart[1]).appCode(uriPart[2]).build();
    }
}

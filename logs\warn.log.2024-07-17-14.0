2024-07-17 14:04:25.682 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. <PERSON> already defined with the same name!
2024-07-17 14:04:25.682 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. <PERSON> already defined with the same name!
2024-07-17 14:04:25.682 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. <PERSON> already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.685 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:04:25.753 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 14:04:28.033 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 14:04:34.184 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:04:53.093 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@568c71de]
2024-07-17 14:04:56.209 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:04:57.006 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 14:05:01.272 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 14:05:03.389 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:05:03.389 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:06:15.093 [paas,sys,/login,,74a128cfd54b428eb3237909cb7c8efb] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-17 14:06:16.501 [paas,sys,/login,,74a128cfd54b428eb3237909cb7c8efb] [http-nio-6060-exec-1] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.9abdc3f8-f837-43b0-b6b9-f34225688788]获取用户主键失败,获取为空
2024-07-17 14:11:07.745 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=57s500ms600µs700ns).
2024-07-17 14:12:11.185 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s439ms649µs200ns).
2024-07-17 14:22:36.272 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.272 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.272 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.272 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:22:36.273 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 14:22:37.134 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 14:22:39.865 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:22:51.481 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@295c834b]
2024-07-17 14:22:53.107 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:22:53.543 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 14:22:55.182 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 14:22:56.458 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:22:56.458 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.601 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.602 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:25:01.603 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 14:25:02.429 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 14:25:04.984 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:25:14.715 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@3de9aa8d]
2024-07-17 14:25:16.296 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:25:16.705 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 14:25:18.345 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 14:25:19.479 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:25:19.479 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:36:15.720 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 14:36:15.732 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 14:36:17.210 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 14:36:20.559 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:36:35.129 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@39d80c30]
2024-07-17 14:36:37.158 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 14:36:37.719 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 14:36:40.972 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 14:36:43.099 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 14:36:43.099 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********

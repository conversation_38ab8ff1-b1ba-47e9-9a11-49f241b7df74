<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-authentication-center</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-center-boot</artifactId>
    <version>1.0.0</version>

    <name>hy-auth-center-boot</name>
    <url>http://www.example.com</url>
    <description>3.0认证中心启动项目</description>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <docker.image.name>${project.name}</docker.image.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-business-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <!--普罗米修斯-->
        <!-- Micrometer Prometheus registry -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!--配置中心-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>consul-api</artifactId>
                    <groupId>com.ecwid.consul</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>consul-api</artifactId>
            <groupId>com.ecwid.consul</groupId>
            <version>1.4.5</version>
        </dependency>
        <!--服务注册与发现-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hy.license</groupId>
            <artifactId>hy-license-sdk-start</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-multi-datasource</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>cn.hy.health</groupId>
            <artifactId>hy-take-version-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->

        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${build.plugins.springboot.version}</version>
                <configuration>
                    <mainClass>cn.hy.auth.boot.AuthApplication</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.10</version>
                <executions>
                    <execution>
                        <id>default</id>
                        <goals>
                            <goal>build</goal>
                            <goal>tag</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skip>false</skip>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <repository>${docker.repository}/${docker.project.name}/${docker.image.name}</repository>
                    <!-- <tag>${project.version}</tag> -->
                    <buildArgs>
                        <JAR_FOLDER>/target/</JAR_FOLDER>
                        <JAR_FILE>${project.name}-${project.version}.jar</JAR_FILE>
                        <EXPOSE_PORT>6060</EXPOSE_PORT>
                    </buildArgs>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/target/classes/</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/../</directory>
                                    <includes>
                                        <include>.version.json</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources/config</directory>
                <includes>
                    <include>application.yml</include>
                    <include>application-*.yml</include>
                    <include>bootstrap.yml</include>
                </includes>
                <filtering>true</filtering>
                <targetPath>config</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>src/main/resources/config</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <!--dev 本地开发调试代码的环境-->
            <id>dev</id>
            <properties>
                <ENV>dev</ENV>
                <CONSUL_ENABLE>false</CONSUL_ENABLE>
                <CONSUL_HOST>**************</CONSUL_HOST>
                <CONSUL_PORT>8500</CONSUL_PORT>
                <AUTH_HOST>127.0.0.1</AUTH_HOST>
                <MYSQL_HOST>*********</MYSQL_HOST>
                <MYSQL_PORT>13306</MYSQL_PORT>
                <DATABASE_NAME>hyap</DATABASE_NAME>
                <DATABASE_USER>iotmp</DATABASE_USER>
                <DATABASE_PWD>Hy@21nbHh</DATABASE_PWD>
                <APP_NAME>HY-AUTH-DEV</APP_NAME>
                <X2_AUTH_CENTER>http://************:8900</X2_AUTH_CENTER>
                <ENABLE_LICENSE>0</ENABLE_LICENSE>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!--cloud 通过Jenkins打镜像包到服务器，上云的环境-->
            <id>cloud</id>
            <properties>
                <ENV>prod</ENV>
                <CONSUL_ENABLE>true</CONSUL_ENABLE>
                <CONSUL_HOST>**************</CONSUL_HOST>
                <CONSUL_PORT>8500</CONSUL_PORT>
                <AUTH_HOST>127.0.0.1</AUTH_HOST>
                <MYSQL_HOST>*********</MYSQL_HOST>
                <MYSQL_PORT>13306</MYSQL_PORT>
                <DATABASE_NAME>hyap</DATABASE_NAME>
                <DATABASE_USER>iotmp</DATABASE_USER>
                <DATABASE_PWD>Hy@21nbHh</DATABASE_PWD>
                <APP_NAME>HY-AUTH</APP_NAME>
                <X2_AUTH_CENTER>http://************:8900</X2_AUTH_CENTER>
                <ENABLE_LICENSE>0</ENABLE_LICENSE>
            </properties>

        </profile>

        <profile>
            <!--prod 打镜像包，提测给私有部署的环境-->
            <id>prod</id>
            <properties>
                <ENV>prod</ENV>
                <CONSUL_ENABLE>true</CONSUL_ENABLE>
                <CONSUL_HOST>**************</CONSUL_HOST>
                <CONSUL_PORT>8500</CONSUL_PORT>
                <AUTH_HOST>127.0.0.1</AUTH_HOST>
                <MYSQL_HOST>*********</MYSQL_HOST>
                <MYSQL_PORT>13306</MYSQL_PORT>
                <DATABASE_NAME>hyap</DATABASE_NAME>
                <DATABASE_USER>iotmp</DATABASE_USER>
                <DATABASE_PWD>Hy@21nbHh</DATABASE_PWD>
                <APP_NAME>HY-AUTH</APP_NAME>
                <X2_AUTH_CENTER>http://************:8900</X2_AUTH_CENTER>
                <ENABLE_LICENSE>1</ENABLE_LICENSE>
            </properties>

        </profile>



    </profiles>
    
</project>

package cn.hy.auth.custom.route.service;

import javax.servlet.http.HttpServletRequest;

/**
 * 类描述：系统标识服务
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 9:54
 **/
public interface SystemIdentifyService {
    /**
     *  获取请求所属领域领域的标识
     * @param request .
     * @return  .
     */
    String getRequestSystemAreaIdentify(HttpServletRequest request);

    /**
     *  获取请求TOKEN
     * @param request .
     * @return  .
     */
    Object getToken(HttpServletRequest request);
}

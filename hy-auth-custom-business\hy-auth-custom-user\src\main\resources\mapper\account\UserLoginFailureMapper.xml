<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.user.account.dao.UserLoginFailureDao">
  <resultMap id="BaseResultMap" type="cn.hy.auth.custom.user.account.domain.UserLoginFailure">
    <id column="id" jdbcType="DECIMAL" property="id" />
    <result column="create_user_id" jdbcType="DECIMAL" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="sequence" jdbcType="BIGINT" property="sequence" />
    <result column="login_failure_count" jdbcType="BIGINT" property="loginFailureCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="user_id" jdbcType="DECIMAL" property="userId" />
    <result column="data_version" jdbcType="VARCHAR" property="dataVersion" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="last_update_user_id" jdbcType="DECIMAL" property="lastUpdateUserId" />
    <result column="last_update_user_name" jdbcType="VARCHAR" property="lastUpdateUserName" />
  </resultMap>
  <delete id="deleteByUid" >
    delete from user_login_failure_info
    where user_id = #{uid}
  </delete>
  <insert id="insert" parameterType="cn.hy.auth.custom.user.account.domain.UserLoginFailure">
    insert into user_login_failure_info (id, create_user_id, last_update_time, 
      sequence, login_failure_count, create_time, 
      create_user_name, user_id, data_version, 
      login_time, last_update_user_id, last_update_user_name
      )
    values (#{id,jdbcType=DECIMAL}, #{createUserId,jdbcType=DECIMAL}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{sequence,jdbcType=BIGINT}, #{loginFailureCount,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUserName,jdbcType=VARCHAR}, #{userId,jdbcType=DECIMAL}, #{dataVersion,jdbcType=VARCHAR}, 
      #{loginTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=DECIMAL}, #{lastUpdateUserName,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.hy.auth.custom.user.account.domain.UserLoginFailure">
    update user_login_failure_info
    set create_user_id = #{createUserId,jdbcType=DECIMAL},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      sequence = #{sequence,jdbcType=BIGINT},
      login_failure_count = #{loginFailureCount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=DECIMAL},
      data_version = #{dataVersion,jdbcType=VARCHAR},
      login_time = #{loginTime,jdbcType=TIMESTAMP},
      last_update_user_id = #{lastUpdateUserId,jdbcType=DECIMAL},
      last_update_user_name = #{lastUpdateUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=DECIMAL}
  </update>
    <update id="resetFailCount">
      update user_login_failure_info set login_failure_count = 0 where user_id = #{userId}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    select id, create_user_id, last_update_time, sequence, login_failure_count, create_time, 
    create_user_name, user_id, data_version, login_time, last_update_user_id, last_update_user_name
    from user_login_failure_info
    where id = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectByUid" resultMap="BaseResultMap">
    select id, create_user_id, last_update_time, sequence, login_failure_count, create_time,
    create_user_name, user_id, data_version, login_time, last_update_user_id, last_update_user_name
    from user_login_failure_info where user_id = #{uid} order by id limit 1
  </select>
</mapper>
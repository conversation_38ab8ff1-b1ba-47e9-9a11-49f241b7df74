package cn.hy.auth.boot.listener;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.multi.systable.AppInvolvedTableServiceImpl;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @title: AuthAppStatusKafkaListener
 * @description: 监听应用状态变更情况，清除缓存
 * @date 2024/7/1
 */
@Service
@Slf4j
public class AuthAppStatusKafkaListener {

    @Value("${hy.saas.cluster-cache.app-status-topic:saas-app-status}")
    private String appStatusTopic;

    private final AppInvolvedTableServiceImpl AppInvolvedTableServiceImpl;

    public AuthAppStatusKafkaListener(cn.hy.auth.custom.multi.systable.AppInvolvedTableServiceImpl appInvolvedTableServiceImpl) {
        AppInvolvedTableServiceImpl = appInvolvedTableServiceImpl;
    }

    @KafkaListener(topics = "${hy.saas.cluster-cache.app-status-topic:saas-app-status}",
            groupId = "#{authKafkaGroupIdUtil.buildSaasGroupId()}"
    )
    public void processAsyncTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional<Object> message = Optional.ofNullable(record.value());
        try {
            if (message.isPresent()) {
                String msg = (String) message.get();
                log.info("消费了app-status-topic；{}，Message：{}", record.topic(), msg);
                AuthAppStatusEventMsg eventMsg = JSON.parseObject(msg, AuthAppStatusEventMsg.class);
                if (null == eventMsg.getAppStatus()) {
                    log.warn("应用状态为空，无法通知更新认证中心应用状态");
                    return;
                }
                String lessCode = eventMsg.getLesseeCode();
                String appCode = eventMsg.getAppCode();
                Integer appStatus = eventMsg.getAppStatus();
                try {
                    AppInvolvedTableServiceImpl.clearAppStatusCache(lessCode, appCode);
                    log.info("通知更新认证中心应用状态完成：msg：{}", LocaleUtil.getMessage("AppInfoStatusController.controllerMap.msg4", null));
                } catch (Exception e) {
                    log.warn("处理Kafka的应用安装消息{}_{},报错，可以忽略不处理。{}", lessCode, appCode, e.getMessage());
                    log.error("{}_{} 修改应用状态为：{}，失败了。", lessCode, appCode, appStatus, e);
                }
            } else {
                log.warn("kafka消息内容为空，无法通知更新认证中心应用状态");
            }
        } finally {
            ack.acknowledge();
        }
    }

}

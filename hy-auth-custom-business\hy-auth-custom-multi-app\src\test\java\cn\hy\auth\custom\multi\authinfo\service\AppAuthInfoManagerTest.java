package cn.hy.auth.custom.multi.authinfo.service;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.authinfo.*;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.multi.authinfo.dao.AppAuthStrategyDao;
import cn.hy.auth.custom.multi.utils.BaseH2Test;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.mockito.Mockito.*;

public class AppAuthInfoManagerTest extends BaseH2Test {
    private static final String CACHE_NAME = "auth_info";
    private static final String CACHE_KEY = "rule";

    @MockBean
    private UserDetailsService userDetailsService;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private AppAuthStrategyManager appAuthInfoManager;
    @MockBean
    private AppAuthStrategyDao appAuthInfoDao;

    private ApplicationContext applicationContext;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final AtomicBoolean isFirst = new AtomicBoolean(true);

    @Before
    public void setUp() throws Exception {
        applicationContext = Mockito.mock(ApplicationContext.class);
        ReflectionTestUtils.setField(appAuthInfoManager, "applicationContext", applicationContext);
        //屏蔽事件发送的影响
        doNothing().when(applicationContext).publishEvent(any());

    }

    /**
     * 验证缓存是否成功
     */
    @Test
    public void get() {
        Assertions.assertThat(Objects.requireNonNull(cacheManager.getCache(CACHE_NAME)).get(CACHE_KEY)).isNull();
        doReturn(mockAuthInfoJson()).when(appAuthInfoDao).getAppAuthStrategyJson();

        AuthContext.getContext().setLesseeCode("hy").setAppCode("iot");
        appAuthInfoManager.get();
        //第一次访问，会从库中加载
        Mockito.verify(appAuthInfoDao, times(1)).getAppAuthStrategyJson();

        //第一次访问之后，缓存里已经存在数据
        Cache cache = cacheManager.getCache(CACHE_NAME);
        Optional<AppAuthStrategyDTO> opData = Optional.ofNullable(cache).map(c -> c.get(CACHE_KEY, AppAuthStrategyDTO.class));
        AppAuthStrategyDTO info = opData.orElse(null);
        Assertions.assertThat(info).isNotNull();

        //后续访问，不再从库中获取
        appAuthInfoManager.get();
        Mockito.verify(appAuthInfoDao, times(1)).getAppAuthStrategyJson();
    }

    @Test(expected = AuthBusinessException.class)
    public void testInfoIsNull() {
        doReturn("").when(appAuthInfoDao).getAppAuthStrategyJson();

        AuthContext.getContext().setLesseeCode("hy").setAppCode("iot");
        appAuthInfoManager.get();
    }

    @Test(expected = AuthBusinessException.class)
    public void testInfoIsError() {
        doReturn("{sss").when(appAuthInfoDao).getAppAuthStrategyJson();

        AuthContext.getContext().setLesseeCode("hy").setAppCode("iot");
        appAuthInfoManager.get();
    }

    private String mockAuthInfoJson() {
        AppAuthStrategyDTO appAuthInfoDTO = new AppAuthStrategyDTO();
        appAuthInfoDTO.setLesseeAccessName("hy");
        appAuthInfoDTO.setAppAccessName("iot");
        appAuthInfoDTO.setLoginRule(new AppAuthLoginRuleDTO());
        appAuthInfoDTO.getLoginRule().setCompetitionPolicy(new AppAuthCompetitionPolicyDTO());
        appAuthInfoDTO.getLoginRule().getCompetitionPolicy().setSameClientTypePolicy("forceLogin").setEnable(true);
        appAuthInfoDTO.getLoginRule()
                .setLoginSupportType(Lists.newArrayList(LoginTypeEnum.OAUTH2_PASSWORD, LoginTypeEnum.USERNAME_PASSWORD,
                        LoginTypeEnum.SMS_CODE, LoginTypeEnum.OAUTH2_CLIENT));
        appAuthInfoDTO.getLoginRule()
                .setLoginField(Lists.newArrayList(new AppAuthLoginFieldDTO("user_account_name", "", 1),
                        new AppAuthLoginFieldDTO("mobile", "", 2)));
        appAuthInfoDTO.setLogoutRule(new AppAuthLogoutRuleDTO());
        appAuthInfoDTO.setLicenseRule(new AppAuthLicenseRuleDTO());
        appAuthInfoDTO.setPwdRule(new AppAuthPwdRuleDTO());
        appAuthInfoDTO.setUserAccountMapping(new AppAuthUserAccountMapping());
        appAuthInfoDTO.getUserAccountMapping().setTableName("user_account");
        appAuthInfoDTO.getUserAccountMapping().setUid("id");
        appAuthInfoDTO.getUserAccountMapping().setPassword("password");
        appAuthInfoDTO.getUserAccountMapping().setUsername("user_account_name");
        appAuthInfoDTO.getUserAccountMapping().setMobile("mobile");
        appAuthInfoDTO.getUserAccountMapping().setEmail("email");
        appAuthInfoDTO.setUserInfoMapping(new ArrayList<>());

        AppAuthUserInfoMapping appAuthUserInfoMapping = new AppAuthUserInfoMapping();
        appAuthUserInfoMapping.setTableName("user_account");
        appAuthUserInfoMapping.setUid("id");
        appAuthUserInfoMapping.setIdentifyCode("user");
        appAuthInfoDTO.getUserInfoMapping().add(appAuthUserInfoMapping);
        return JSONObject.toJSONString(appAuthInfoDTO);
    }
}
package cn.hy.auth.common.security.oauth2.exception;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.util.Map;

/**
 * 自定义序列化逻辑
 * <AUTHOR>
 * @date 2021-01-07 19:44
 **/
public class HyOAuth2ExceptionSerializer extends StdSerializer<HyOAuth2Exception> {

	private static final long serialVersionUID = -1349821064535878286L;

	public HyOAuth2ExceptionSerializer() {
		super(HyOAuth2Exception.class);
	}

	@Override
	public void serialize(HyOAuth2Exception value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
		jsonGenerator.writeStartObject();
		jsonGenerator.writeObjectField("error", value.getOAuth2ErrorCode());
		jsonGenerator.writeObjectField("error_description", value.getMessage());
		if (value.getAdditionalInformation()!=null) {
			for (Map.Entry<String, String> entry : value.getAdditionalInformation().entrySet()) {
				String key = entry.getKey();
				String add = entry.getValue();
				jsonGenerator.writeStringField(key, add);
			}
		}
		jsonGenerator.writeEndObject();
	}
}

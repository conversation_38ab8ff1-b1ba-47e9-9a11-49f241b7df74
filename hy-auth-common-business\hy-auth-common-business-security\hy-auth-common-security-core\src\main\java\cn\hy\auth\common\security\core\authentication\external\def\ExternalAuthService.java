package cn.hy.auth.common.security.core.authentication.external.def;


import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAccessToken;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAuthConfig;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalProviderUser;

import java.util.Map;

/**
 * 佛科院认证相关业务逻辑
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
public interface ExternalAuthService {

    /**
     * 获取token
     * @param code
     * @return
     */
    ExternalAccessToken getAccessToke(String code, ExternalAuthConfig authConfig);

    /**
     * 获取token
     * @param accessToken
     * @return
     */
    ExternalProviderUser getUserInfo(ExternalAccessToken accessToken);

    /**
     * 根据用户账号查询用户信息
     * @param userInfo
     * @return
     */
    Map<String,Object> getByUserAccount(ExternalProviderUser userInfo);

    /**
     * 用户注册
     * @param userInfo
     * @return
     */
    Boolean regirstUser(ExternalProviderUser userInfo);

    /**
     * 获取授权用户信息
     * @param lesseeCode
     * @param appCode
     * @return
     */
    ExternalAuthConfig getAuthConfig(String providerId, String lesseeCode, String appCode);

    /**
     * 查询表名
     * @param lesseeCode
     * @param appCode
     * @param tableCode
     * @return
     */
    String getTableName(String lesseeCode, String appCode, String tableCode);
}

package cn.hy.auth.custom.common.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * 类描述: 操作类型
 *
 * <AUTHOR>
 * @date ：创建于 2022/6/1
 */
@AllArgsConstructor
@Getter
public enum OperationTypeEnum {

    /**
     * 未知
     */
    UNKNOWN("未知"),
    /**
     * 登录
     */
    LOGIN("登录"),
    /**
     * 登出
     */
    LOGOUT("登出"),
    /**
     * 修改密码
     */
    CHANGE_PWD("修改密码"),
    /**
     * 踢人
     */
    KICK_OUT("踢人"),

    /**
     * 查询
     */
    LIST("查询"),

    /**
     * 其他
     */
    OTHER("其他");
    /**
     * 类型描述
     */
    private final String msg;

    public static Optional<OperationTypeEnum> codeOf(String msg) {
        if (StringUtils.isNotBlank(msg)) {
            for (OperationTypeEnum e : OperationTypeEnum.values()) {
                if (e.getMsg().equals(msg)) {
                    return Optional.of(e);
                }
            }
        }
        return Optional.empty();
    }
}

package cn.hy.auth.custom.security.verifycode;

import cn.hutool.core.util.StrUtil;
import cn.hy.auth.custom.security.verifycode.domain.SliderCaptchaTrackDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 登陆验证码
 * hy-paas-cn.hy.paas.user.account.controller
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/2/17 14:28
 */
@RestController
@RequestMapping("/verifyCode")
@AllArgsConstructor
@Slf4j
public class VerifyCodeController {

    private VerifyCodeCreator verifyCodeCreator;

    /**
     * 获到滑动条验证码
     * @return ..
     */
    @RequestMapping("/sliderCaptcha/generate")
    public DefaultResponse sliderCaptcha(@RequestParam("loginName") String loginName){
        return DefaultResponse.successSaas(verifyCodeCreator.generateSliderCaptcha(loginName));
    }

    /**
     * 滑动条验证
     * @param sliderCaptchaTrackDTO 入参
     * @return 验证结果
     */
    @PostMapping("/sliderCaptcha/verify")
    public DefaultResponse sliderCaptchaVerify(@RequestBody SliderCaptchaTrackDTO sliderCaptchaTrackDTO,
                                               @RequestParam("lessee_code") String lesseeCode,
                                               @RequestParam("app_code") String appCode) {
        if (StrUtil.isBlank(sliderCaptchaTrackDTO.getLesseeCode())) {
            sliderCaptchaTrackDTO.setLesseeCode(lesseeCode);
        }
        if (StrUtil.isBlank(sliderCaptchaTrackDTO.getAppCode())) {
            sliderCaptchaTrackDTO.setAppCode(appCode);
        }
        return DefaultResponse.successSaas(verifyCodeCreator.sliderCaptchaVerify(sliderCaptchaTrackDTO));
    }

    /**
     * 显示缓存所有验证码
     * @return 验证码
     */
    @GetMapping("/verify/listallv4test")
    public DefaultResponse listAllVerify(){
        return DefaultResponse.successSaas(verifyCodeCreator.getAllCacheJson());
    }
}

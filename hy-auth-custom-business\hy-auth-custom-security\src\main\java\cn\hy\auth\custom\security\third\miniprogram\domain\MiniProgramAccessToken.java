package cn.hy.auth.custom.security.third.miniprogram.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import lombok.Setter;

import java.util.Date;

/**
 * 微信小程序用户信息
 *
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Setter
public class MiniProgramAccessToken implements ProviderAppAccessToken {

    private String accessToken;
    private int errCode;
    private String errorMsg;
    private Date expiresTime;
    private int expiresIn;

    @Override
    public int getErrCode() {
        return errCode;
    }

    @Override
    public String getErrMsg() {
        return errorMsg;
    }

    @Override
    public String getAccessToken() {
        return accessToken;
    }

    @Override
    public Date getExpiresTime() {
        return expiresTime;
    }

    @Override
    public int getExpiresIn() {
        return expiresIn;
    }
}

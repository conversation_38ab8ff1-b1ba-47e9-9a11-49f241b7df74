package cn.hy.auth.custom.common.filter;

import cn.hy.auth.common.security.oauth2.exception.HyWebResponseExceptionTranslator;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.IbatisException;
import org.springframework.dao.DataAccessException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;

/**
 * 类描述: 公共过滤器
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/17
 */
@Slf4j
public abstract class AbstractAuthFilter extends OncePerRequestFilter {

	private Oauth2SecurityExceptionHandler handler;

	public AbstractAuthFilter() {
		handler = new Oauth2SecurityExceptionHandler();
		handler.setExceptionTranslator(new HyWebResponseExceptionTranslator());
	}

	@Override
	public void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
	                             FilterChain filterChain) throws ServletException, IOException {
		try {
			doMyFilter(request, response, filterChain);
		} catch (AuthBusinessException e) {
			SecurityContextHolder.clearContext();
			log.error("{}",e.getMessage(), e);
			try {
				handler.commence(request, response, e);
			}catch (Exception e2){
			}
		}catch (Exception e){
			log.error(e.getMessage(), e);
			String msg = createMsg(e);
			// 捕获业务bug，转为AuthBusinessException，交由auth2处理异常,处理bug QX202302070008
			throw new AuthBusinessException(AuthErrorCodeEnum.B0001.code(),AuthErrorCodeEnum.B0001.msg()+","+msg);
		}
	}

	private String createMsg(Exception e) {
		// fix QX202404090015
		if (e instanceof IbatisException || e instanceof SQLException || e instanceof DataAccessException){
			return "数据库层错误.";
		}
		return e.getMessage();
	}

	/**
	 * 子类过滤器逻辑
	 *
	 * @param request     .
	 * @param response    .
	 * @param filterChain .
	 * @throws IOException      .
	 * @throws ServletException .
	 */
	protected abstract void doMyFilter(HttpServletRequest request, HttpServletResponse response,
	                                   FilterChain filterChain) throws ServletException, IOException;
}

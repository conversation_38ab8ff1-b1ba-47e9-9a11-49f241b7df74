package cn.hy.auth.custom.route.service.route;

import org.apache.http.HttpResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 13:43
 **/
public interface SimpleHostRoutingService {
    /**
     * 转发请求
     * @param request .
     * @param hostUrl .
     * @param uri .
     * @return .
     * @throws Exception .
     */
     HttpResponse run(HttpServletRequest request, String hostUrl, String uri) throws Exception;
}

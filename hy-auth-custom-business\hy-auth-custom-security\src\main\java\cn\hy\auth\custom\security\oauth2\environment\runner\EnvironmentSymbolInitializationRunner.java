package cn.hy.auth.custom.security.oauth2.environment.runner;

import cn.hy.auth.custom.security.oauth2.environment.service.OauthSysMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 系统环境标识符初始化
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/11
 */
@Component
@Slf4j
public class EnvironmentSymbolInitializationRunner implements CommandLineRunner {

    private final OauthSysMService oauthSysMService;

    public EnvironmentSymbolInitializationRunner(OauthSysMService oauthSysMService) {
        this.oauthSysMService = oauthSysMService;
    }

    @Override
    public void run(String... args) throws Exception {
        if (log.isInfoEnabled()){
            log.info("auth 环境标识符初始化开始");
        }
        try {
            // 启动时检查并初始化环境标识数据
            oauthSysMService.initializeSystemEnvironmentSymbol();
        } catch (Exception e) {
            if (log.isWarnEnabled()) {
                log.warn("【此处异常可忽略，不影响服务正常启动】auth 环境标识符初始化失败，异常信息为：{}", e.getMessage());
                e.printStackTrace();
            }
        }
        if (log.isInfoEnabled()){
            log.info("auth 环境标识符初始化完成");
        }
    }
}

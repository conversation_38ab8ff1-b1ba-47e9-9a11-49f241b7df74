package cn.hy.auth.common.security.oauth2.util;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;

import javax.servlet.http.HttpServletRequest;

/**
 * 类描述: 获取tokenId工具类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/27
 */
public class TokenUtil {
    private static final String TOKEN_KEY = "Authorization";
    private static final String TOKEN_THIRD_KEY = "Blade-Auth";
    private static final String TOKEN_PR = "Bearer ";
    private static final String TOKEN_PR_LOW = "bearer ";

    private TokenUtil() {
    }

    /**
     * 先从请求头中获取 tokenId,没有则从上下文中获取
     *
     * @return tokenId
     */
    public static String getTokenID() {
        String tokenId = fromHeader();
        if (StringUtils.isNotBlank(tokenId)) {
            return tokenId;
        }
        if (StringUtils.isNotBlank(tokenId)) {
            return tokenId;
        }
        return fromContent();
    }
    /**
     * 先从请求头中获取 tokenId,没有则从上下文中获取
     *
     * @return tokenId
     */
    public static String getTokenId(HttpServletRequest request) {
        String tokenId = fromHeader(request);
        if (StringUtils.isNotBlank(tokenId)) {
            return tokenId;
        }
        tokenId = fromRequest(request);
        if (StringUtils.isNotBlank(tokenId)) {
            return tokenId;
        }
        return fromContent();
    }

    private static String fromRequest(HttpServletRequest request) {
        if (request == null) {
            return StringUtils.EMPTY;
        }
        return request.getParameter("token");
    }


    public static String getRequestTokenID() {
        return fromHeader();
    }

    /**
     * 从Header中获取tokenId
     *
     * @return 返回tokenId;
     */
    public static String fromHeader(HttpServletRequest request) {
        if (request == null) {
            return StringUtils.EMPTY;
        }
        String authorization = request.getHeader(TOKEN_KEY)==null? request.getHeader(TOKEN_THIRD_KEY):
                request.getHeader(TOKEN_KEY);
        if (StringUtils.isBlank(authorization)) {
            return StringUtils.EMPTY;
        }
        //替换前缀(大写)
        authorization = StringUtils.replace(authorization, TOKEN_PR, StringUtils.EMPTY);
        //替换前缀(小写)
        authorization = StringUtils.replace(authorization, TOKEN_PR_LOW, StringUtils.EMPTY);

        return authorization;
    }

    /**
     * 从Header中获取tokenId
     *
     * @return 返回tokenId;
     */
    public static String fromHeader() {
        return fromHeader(HttpContextUtil.getHttpServletRequest());
    }

    /**
     * 从OAuth2上下文中获取tokenId
     *
     * @return 返回tokenId;
     */
    public static String fromContent() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (ObjectUtil.isNull(authentication)) {
            return StringUtils.EMPTY;
        }
        Object details = authentication.getDetails();
        if (details instanceof OAuth2AuthenticationDetails) {
            return ((OAuth2AuthenticationDetails) details).getTokenValue();
        }
        return StringUtils.EMPTY;
    }

}

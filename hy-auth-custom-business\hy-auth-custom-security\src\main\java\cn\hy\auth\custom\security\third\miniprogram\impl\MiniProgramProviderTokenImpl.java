package cn.hy.auth.custom.security.third.miniprogram.impl;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.service.HyOauth2Operations;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.third.miniprogram.domain.MiniProgramAccessToken;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 *
 * 微信小程序用户token获取
 *
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Component
@Slf4j
public class MiniProgramProviderTokenImpl extends AbstractOAuth2ApiBinding implements HyOauth2Operations {

    @Value("${third.proxy.miniprogram.url:https://api.weixin.qq.com}")
    private String baseUrl;

    /**
     * 响应码
     */
    private static final String ERROR_CODE_PARAMS = "errcode";

    @Override
    public ProviderAppAccessToken getProviderAppAccessToken(String lesseeCode, String appCode, ProviderInfo providerInfo, Map<String, Object> requestParamMap) {
        String appKey= providerInfo.getAppKey();
        String appSecret= providerInfo.getAppSecret();
        String url = baseUrl+providerInfo.getAppAccessTokenUrl() + "?grant_type=client_credential&appid="+appKey+"&secret="+appSecret;
        String response = getRestTemplate().getForObject(url, String.class);
        MiniProgramAccessToken token=new MiniProgramAccessToken();
        if (response != null) {
            JSONObject resObject = JSONObject.parseObject(response);
            if (resObject.get(ERROR_CODE_PARAMS)==null) {
                String access_token = resObject.getString("access_token");
                Integer expires_in = resObject.getInteger("expires_in");
                Calendar nowTime = Calendar.getInstance();
                nowTime.add(Calendar.SECOND, expires_in);
                Date expiresTime=nowTime.getTime();
                token.setAccessToken(access_token);
                token.setExpiresTime(expiresTime);
            }else {
                String errmsg = resObject.getString("errmsg");
                token.setErrCode(resObject.getInteger(ERROR_CODE_PARAMS));
                token.setErrorMsg(errmsg);
            }
            return token;
        }
        log.error("查询第三方应用token失败,{}",response);
        throw new AuthBusinessException("500", LocaleUtil.getMessage("MiniProgramProviderTokenImpl.result.msg1", null)+response);
    }
}

package cn.hy.auth.common.security.oauth2.extend;

import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.event.CopyTokenSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.LoginFailureEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEventType;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.common.security.oauth2.token.DefaultTokenServicesWrapper;
import cn.hy.auth.common.security.oauth2.util.RequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.IbatisException;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.util.CollectionUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;

/**
 * 当用户登录成功之后做的处理。目前是表单账号密码和手机验证码的默认处理器
 *
 * <AUTHOR>
 * @date 2020/11/11  10:06
 */
@Slf4j
public class HyAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    private AuthorizationServerTokenServices authorizationServerTokenServices;

    private ObjectMapper objectMapper;

    private ApplicationContext applicationContext;

    private ClientDetailsService clientDetailsService;

    private SecurityProperties securityProperties;

    private PasswordEncoder passwordEncoder;

    public HyAuthenticationSuccessHandler(TokenStore tokenStore,
                                          TokenServicesProperties tokenServicesProperties,
                                          ApplicationContext applicationContext,
                                          ClientDetailsService clientDetailsService,
                                          ObjectMapper objectMapper,
                                          PasswordEncoder passwordEncoder,
                                          SecurityProperties securityProperties) {
        this.objectMapper = objectMapper;
        this.applicationContext = applicationContext;
        this.clientDetailsService = clientDetailsService;
        this.securityProperties = securityProperties;
        this.passwordEncoder = passwordEncoder;
        this.authorizationServerTokenServices = createTokenService(tokenStore, tokenServicesProperties, applicationContext, clientDetailsService);
    }


    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        if (securityProperties.getResponse().isJsonFormat()) {
            response.setContentType("application/json;charset=UTF-8");
            try {
                // 获取 ClientDetails
                ClientDetails clientDetails = getClientDetails(request);
                // 校验clientDetails里的grantType
                validateGrantType(getRquestGrantType(request, securityProperties), clientDetails);
                //  获取Oauth2 的token
                OAuth2AccessToken token = getoauth2Accesstoken(authentication, clientDetails);
                // 发送登录成功信息
                if (!isOnlyToken(request)) {
                    publishSuccessEvent(authentication);
                }
                if (isCopyToken(request)){
                    publishCopyEvent(authentication,token);
                }
                // 响应结果
                response.getWriter().write(objectMapper.writeValueAsString(token));
            } catch (AuthenticationException e) {
                // 发出认证失败事件
                log.warn("认证client和clientSecret发生错误,判定是非用户原因导致的失败，不发出登录失败事件");
                //publishFailureEvent(authentication, request, e);
                responseError(request, response, e);
            } catch (Exception e) {
                // 非认证方面的错误问题处理。
                responseError(request, response, e);
            }
        } else {
            publishSuccessEvent(authentication);
            super.onAuthenticationSuccess(request, response, authentication);
        }
    }

    private void publishCopyEvent(Authentication authentication,OAuth2AccessToken token) {
        applicationContext.publishEvent(new CopyTokenSuccessEvent(authentication,token));
    }

    private boolean isCopyToken(HttpServletRequest request) {
        String requestUrl = RequestUtil.getRequestUrl(request);
        return  requestUrl.endsWith("/login/copy-token");
    }

    private void responseError(HttpServletRequest request, HttpServletResponse response, Exception e) throws IOException {
        log.error("{},{}", RequestUtil.getRequestUrl(request), RequestUtil.getAllRequestParam(request, securityProperties.getFormAuth().getPasswordParameter(), "password"), e);
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        try {
            response.setHeader("WWW-Authenticate", String.format("%s %s", OAuth2AccessToken.BEARER_TYPE, createMsg(e)));
        } catch (Exception e1){
            log.info("添加 WWW-Authenticate head发生错误,忽略不理。{}",e1.getMessage());
        }

        Map<String, String> result = new HashMap<>(4);
        result.put("error", OAuth2Exception.INVALID_REQUEST);
        result.put("error_description", createMsg(e));
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
    private String createMsg(Exception e) {
        // fix QX202404090015
        if (e instanceof IbatisException || e instanceof SQLException || e instanceof DataAccessException){
            return "数据库层错误.";
        }
        return e.getMessage();
    }
    /**
     * 获取Oauth2 的token
     *
     * @param authentication 认证信息
     * @param clientDetails  客户端信息
     * @return Oauth2 的token
     */
    private OAuth2AccessToken getoauth2Accesstoken(Authentication authentication, ClientDetails clientDetails) {

        TokenRequest tokenRequest = new TokenRequest(Collections.emptyMap(), clientDetails.getClientId(), clientDetails.getScope(), "custom");

        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);

        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);

        return authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
    }

    /**
     * 获得clientDetails
     *
     * @param request .
     * @return ClientDetails
     */
    private ClientDetails getClientDetails(HttpServletRequest request) {
        String clientId = request.getParameter("client_id");

        String clientSecret = request.getParameter("client_secret");
        if (StringUtils.isBlank(clientId)) {
            throw new BadCredentialsException("Bad client credentials");
        }
        if (clientSecret == null) {
            clientSecret = "";
        }
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        if (null == clientDetails) {
            throw new UnapprovedClientAuthenticationException(LocaleUtil.getMessage("HyAuthenticationSuccessHandler.result.msg1", null) + clientId);
        } else if (!StringUtils.equals(clientDetails.getClientSecret(), passwordEncoder.encode(clientSecret))) {
            throw new UnapprovedClientAuthenticationException(LocaleUtil.getMessage("HyAuthenticationSuccessHandler.result.msg2", null) + clientSecret);
        }
        return clientDetails;
    }

    private String getRquestGrantType(HttpServletRequest request, SecurityProperties securityProperties) {
        String requestUrl = RequestUtil.getRequestUrl(request);
        if (StringUtils.equals(securityProperties.getFormAuth().getLoginProcessingUrl(), requestUrl)) {
            // 表单密码登录
            return CustomGrantType.FORM_LOGIN_GRANT_TYPE;
        } else if (StringUtils.equals(securityProperties.getSmsAuth().getLoginUrl(), requestUrl)) {
            // 手机短信登录
            return CustomGrantType.SMS_LOGIN_GRANT_TYPE;
        }
        log.info("未知的登录方式,默认的grantType是all_custom,放行通过登录类型校验，{}", requestUrl);
        return CustomGrantType.ALL_CUSTOM_LOGIN_GRANT_TYPE;
    }

    /**
     * 校验客户端的grantType
     *
     * @param grantType     .
     * @param clientDetails .
     */
    private void validateGrantType(String grantType, ClientDetails clientDetails) {
        Collection<String> authorizedGrantTypes = clientDetails.getAuthorizedGrantTypes();
        if (authorizedGrantTypes != null && !authorizedGrantTypes.isEmpty()
                && !authorizedGrantTypes.contains(grantType)
                && !authorizedGrantTypes.contains(CustomGrantType.ALL_CUSTOM_LOGIN_GRANT_TYPE)) {
            throw new InvalidClientException("Unauthorized grant type: " + grantType);
        }
    }

    /**
     * 发送登录验证成功的时间
     *
     * @param authentication .
     */
    private void publishSuccessEvent(Authentication authentication) {
        LoginSuccessEvent loginSuccessEvent = new LoginSuccessEvent(authentication, LoginSuccessEventType.SPRING_SECURITY_LOGIN);
        applicationContext.publishEvent(loginSuccessEvent);
    }

    /**
     * 发送登录验证失败的事件
     *
     * @param exception .
     */
    private void publishFailureEvent(Authentication authentication, HttpServletRequest request, AuthenticationException exception) {
        LoginFailureEvent loginFailureEvent = new LoginFailureEvent(exception);
        loginFailureEvent.setAuthentication(authentication);
        loginFailureEvent.setRequest(request);
        applicationContext.publishEvent(loginFailureEvent);
    }

    /**
     * 创建tokenService,用于生成token
     * DefaultTokenServicesWrapper 和HyAuthorizationServerConfig里的HyTokenServices 保持一致的配置
     *
     * @param tokenStore              .
     * @param tokenServicesProperties .
     * @param applicationContext      .
     * @param clientDetailsService    .
     */
    private AuthorizationServerTokenServices createTokenService(TokenStore tokenStore,
                                                                TokenServicesProperties tokenServicesProperties,
                                                                ApplicationContext applicationContext,
                                                                ClientDetailsService clientDetailsService) {
        DefaultTokenServicesWrapper tokenServices = new DefaultTokenServicesWrapper(applicationContext, tokenServicesProperties.isReuseRefreshToken());
        tokenServices.setTokenStore(tokenStore);
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setClientDetailsService(clientDetailsService);
        Map<String, TokenEnhancer> tokenEnhancerMap = applicationContext.getBeansOfType(TokenEnhancer.class);
        if (!CollectionUtils.isEmpty(tokenEnhancerMap)) {
            TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
            tokenEnhancerChain.setTokenEnhancers(new ArrayList<>(tokenEnhancerMap.values()));
            tokenServices.setTokenEnhancer(tokenEnhancerChain);
        }
        // access_token默认有效时长为12个小时,优先使用client设置的有效期
        tokenServices.setAccessTokenValiditySeconds(tokenServicesProperties.getAccessTokenValiditySeconds());
        // refresh_token默认时长为30天,优先使用client设置的有效期
        tokenServices.setRefreshTokenValiditySeconds(tokenServicesProperties.getRefreshTokenValiditySeconds());
        return tokenServices;
    }

    private Boolean isOnlyToken(HttpServletRequest request){
        String requestUrl = RequestUtil.getRequestUrl(request);
        if (StringUtils.equals(securityProperties.getReAuthenticationProperties().getAuthProcessingUrl(), requestUrl)) {
            String onlyToken = request.getParameter(securityProperties.getReAuthenticationProperties().getOnlyTokenParameter());
            return "1".equals(onlyToken);
        }
        if (requestUrl.endsWith("/login/copy-token")){
            return true;
        }
        return false;
    }

    private class CustomGrantType {
        /**
         * 表单登录的授权类型
         */
        static final String FORM_LOGIN_GRANT_TYPE = "form_login";
        /**
         * 表单登录的授权类型
         */
        static final String SMS_LOGIN_GRANT_TYPE = "sms_login";
        /**
         * 所有在oauth2 扩展自定义新增的授权类型
         */
        static final String ALL_CUSTOM_LOGIN_GRANT_TYPE = "all_custom";
    }
}

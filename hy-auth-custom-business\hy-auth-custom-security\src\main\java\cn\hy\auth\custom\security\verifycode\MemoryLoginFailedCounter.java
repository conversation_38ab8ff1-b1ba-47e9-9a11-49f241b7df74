package cn.hy.auth.custom.security.verifycode;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.security.verifycode.util.VerifyCodeUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 本地内存记录登陆失败次数
 * hy-authentication-center-cn.hy.auth.custom.security.verifycode
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/4/15 11:10
 */
@Component
public class MemoryLoginFailedCounter implements LoginFailedCounter {

    /**
     * 0
     */
    private static final Long ZERO = 0L;
    /**
     * 1
     */
    private static final Long ONE = 1L;

    /**
     * 缓存key
     */
    private static final String CACHE_KEY = "auth_captcha_failed_count";

    private final CacheManager cacheManager;

    public MemoryLoginFailedCounter(@Qualifier("hyAuthAutoCacheManager") CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    @Override
    public Long addFailed(String login) {
        if(StringUtils.isEmpty(login)){
            return ZERO;
        }
        String fullLoginNameKey = VerifyCodeUtil.getFullLoginNameKey(login);
        Long failedCount = ZERO;
        // 失败次数，不做客户端的隔离，即不区分移动、PC等客户端，相同的key共用失败次数
        ClientTypeEnum originClientType = AuthContext.getContext().loginState().getClientType();
        try {
            AuthContext.getContext().loginState().setClientType(null);
            // 加入缓存
            Cache cache = this.getCache();
            if (Objects.nonNull(cache)) {
                failedCount = cache.get(fullLoginNameKey, Long.class);
                failedCount = (failedCount == null ? ZERO : failedCount) + ONE;
                cache.put(fullLoginNameKey, failedCount);
            }
        }finally {
            AuthContext.getContext().loginState().setClientType(originClientType);
        }
        return failedCount;
    }

    @Override
    public Long getFailedCount(String login) {
        if(StringUtils.isEmpty(login)){
            return ZERO;
        }
        String fullLoginNameKey = VerifyCodeUtil.getFullLoginNameKey(login);
        Cache cache = this.getCache();
        if (Objects.nonNull(cache)) {
            Long failedCount = cache.get(fullLoginNameKey, Long.class);
            return Objects.isNull(failedCount) ? ZERO : failedCount;
        }
        return ZERO;
    }

    @Override
    public void cleanIfNeed(String login) {
        // 清除当前登录人失败记录次数
        String fullLoginNameKey = VerifyCodeUtil.getFullLoginNameKey(login);
        Cache cache = cacheManager.getCache(CACHE_KEY);
        if (Objects.nonNull(cache)) {
            cache.evict(fullLoginNameKey);
        }
    }

    /**
     * 获取缓存
     * @return 缓存
     */
    private Cache getCache() {
        return cacheManager.getCache(CACHE_KEY);
    }
}

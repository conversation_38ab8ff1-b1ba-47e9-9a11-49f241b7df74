package cn.hy.auth.custom.security.verifycode;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.resource.ImageCaptchaResourceManager;
import cloud.tianai.captcha.resource.ResourceStore;
import cloud.tianai.captcha.resource.common.model.dto.Resource;
import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.store.impl.LocalCacheStore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 从本地初始加载验证码背景图
 * hy-paas-cn.hy.paas.user.account.service.impl.verifycode
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/3/24 17:31
 */
@Slf4j
@Component
public class VerifyBgImgInitFromLocal implements VerifyBgImgInit {
    /**
     *    自定义图库相对路径
     */
    private static final String IMG_FILE_PATH =  "verifycode" + File.separator + "imgs";
    /**
     * 错误提示信息
     */
    private static final String ERROR_MSG = "从本地初始加载验证码背景图->获取图片绝对路径出错";
    /**
     * 资源加载器
     */
    private static final ResourceLoader resourceLoader = new DefaultResourceLoader();
    /**
     * 关键字classpath
     */
    private static final String CLASS_PATH = "classpath";

    @Value("${auth.cache.type:caffeine}")
    private String authCacheType;

    private final ImageCaptchaApplication imageCaptchaApplication;

    private final ImageCaptchaResourceManager imageCaptchaResourceManager;

    public VerifyBgImgInitFromLocal(ImageCaptchaApplication imageCaptchaApplication, ImageCaptchaResourceManager imageCaptchaResourceManager) {
        this.imageCaptchaApplication = imageCaptchaApplication;
        this.imageCaptchaResourceManager = imageCaptchaResourceManager;
    }

    @Override
    public void init() {
        //取到背影图片名
        List<String> fileNames = new ArrayList<>();
        try {
            org.springframework.core.io.Resource[] imgPathResources = ResourcePatternUtils.getResourcePatternResolver(resourceLoader).getResources("classpath:/verifycode/imgs/*.*");
            Arrays.stream(imgPathResources).forEach(resource -> fileNames.add(resource.getFilename()));
            if(CollUtil.isNotEmpty(fileNames)){
                log.info("从本地初始加载验证码背景图->从{}加载验证码背景图", imgPathResources[0].getURI());
            }
            log.info("从本地初始加载验证码背景图->加载了{}个验证码背景图", imgPathResources.length);
        } catch (IOException e) {
            log.error(ERROR_MSG,e);
        }

        //验证码资源管理器
        ResourceStore resourceStore = imageCaptchaResourceManager.getResourceStore();
        // 清除内置的背景图片
        resourceStore.clearAllResources();
        // 自定义的背景图加入到图库
        for (String fileName : fileNames) {
            String fileRelativePath = IMG_FILE_PATH + File.separator + fileName;
            Resource resource = new Resource(CLASS_PATH, fileRelativePath);
            resourceStore.addResource(CaptchaTypeConstant.SLIDER, resource);
        }

        // 是否启用redis缓存，如果 authCacheType = redis，则走默认Bean装载即可
        if (!StrUtil.equals(authCacheType, "redis")) {
            // 强制使用本地缓存
            imageCaptchaApplication.setCacheStore(new LocalCacheStore());
        }
        log.info("从本地初始加载验证码背景图，加载完成");
    }
}

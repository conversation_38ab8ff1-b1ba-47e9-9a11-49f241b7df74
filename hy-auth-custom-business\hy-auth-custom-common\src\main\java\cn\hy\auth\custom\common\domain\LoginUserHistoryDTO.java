package cn.hy.auth.custom.common.domain;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 描述: 用户基本信息与登录信息对象
 *
 * <AUTHOR>
 * @date ：2022/3/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "用户基本信息与登录信息对象")
public class LoginUserHistoryDTO implements Serializable {

    /**
     * 账号基本信息
     */
    private UserAccountDTO userAccountDTO;

    /**
     * 账号基本信息
     */
    private UserLoginInfoDTO userLoginInfoDTO;
}

package cn.hy.auth.common.security.core.authentication.copytoken;

import cn.hy.auth.common.security.core.authentication.common.AbstractHySecurityConfigurerAdapter;
import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationDetailsSource;
import cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationFilter;
import cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationPreProvider;
import cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationProvider;
import cn.hy.auth.common.security.core.properties.ReAuthenticationProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 复制token安全配置
 *
 * <AUTHOR>
 * @date 2024/6/7 10:53
 */
@RequiredArgsConstructor
@Component
public class CopyTokenAuthenticationSecurityConfig extends AbstractHySecurityConfigurerAdapter {

    private final AuthenticationSuccessHandler myAuthenticationSuccessHandler;
    private final AuthenticationFailureHandler myAuthenticationFailureHandler;
    private final TokenStore tokenStore;
    @Override
    public void configure(HttpSecurity http) throws Exception {
        CopyTokenAuthenticationFilter reAuthenticationFilter = new CopyTokenAuthenticationFilter();
        reAuthenticationFilter.setAuthenticationDetailsSource(new HyAuthenticationDetailsSource("refresh_token"));
        // 设置共享现有的AuthenticationManager(带有自定义的authenticationProvider)
        reAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        // 设置成功失败处理器
        reAuthenticationFilter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        reAuthenticationFilter.setAuthenticationFailureHandler(myAuthenticationFailureHandler);

        CopyTokenAuthenticationProvider reAuthenticationProvider = new CopyTokenAuthenticationProvider(tokenStore);

        http.authenticationProvider(reAuthenticationProvider)
                .addFilterAfter(reAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
}

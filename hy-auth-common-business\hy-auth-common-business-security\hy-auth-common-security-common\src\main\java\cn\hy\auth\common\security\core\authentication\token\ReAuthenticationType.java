package cn.hy.auth.common.security.core.authentication.token;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/6/7 17:22
 */
@Getter
@RequiredArgsConstructor
public enum ReAuthenticationType {
    /**
     * .
     */
    PASSWORD("password"),
    USB_KEY("usbkey"),
    ;

    private final String code;

    public static ReAuthenticationType match(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}

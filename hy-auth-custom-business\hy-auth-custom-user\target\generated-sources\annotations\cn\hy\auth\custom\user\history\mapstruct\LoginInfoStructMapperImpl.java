package cn.hy.auth.custom.user.history.mapstruct;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.user.history.domain.UserLoginInfoDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-04-15T10:57:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_402 (Amazon.com Inc.)"
)
public class LoginInfoStructMapperImpl implements LoginInfoStructMapper {

    @Override
    public UserLoginInfoDO dtoTodo(UserLoginInfoDTO userLoginInfoDTO) {
        if ( userLoginInfoDTO == null ) {
            return null;
        }

        UserLoginInfoDO userLoginInfoDO = new UserLoginInfoDO();

        userLoginInfoDO.setId( userLoginInfoDTO.getId() );
        userLoginInfoDO.setClientId( userLoginInfoDTO.getClientId() );
        userLoginInfoDO.setClientType( userLoginInfoDTO.getClientType() );
        userLoginInfoDO.setLoginType( userLoginInfoDTO.getLoginType() );
        userLoginInfoDO.setUserId( userLoginInfoDTO.getUserId() );
        userLoginInfoDO.setUserAccountName( userLoginInfoDTO.getUserAccountName() );
        userLoginInfoDO.setIp( userLoginInfoDTO.getIp() );
        userLoginInfoDO.setMac( userLoginInfoDTO.getMac() );
        userLoginInfoDO.setType( userLoginInfoDTO.getType() );
        userLoginInfoDO.setLoginCount( userLoginInfoDTO.getLoginCount() );
        userLoginInfoDO.setErrorCode( userLoginInfoDTO.getErrorCode() );
        userLoginInfoDO.setErrorMessage( userLoginInfoDTO.getErrorMessage() );
        userLoginInfoDO.setDataVersion( userLoginInfoDTO.getDataVersion() );
        userLoginInfoDO.setCreateUserId( userLoginInfoDTO.getCreateUserId() );
        userLoginInfoDO.setCreateTime( userLoginInfoDTO.getCreateTime() );
        userLoginInfoDO.setCreateUserName( userLoginInfoDTO.getCreateUserName() );
        userLoginInfoDO.setLastUpdateUserId( userLoginInfoDTO.getLastUpdateUserId() );
        userLoginInfoDO.setLastUpdateTime( userLoginInfoDTO.getLastUpdateTime() );
        userLoginInfoDO.setLastUpdateUserName( userLoginInfoDTO.getLastUpdateUserName() );
        userLoginInfoDO.setSequence( userLoginInfoDTO.getSequence() );

        return userLoginInfoDO;
    }

    @Override
    public UserLoginInfoDTO doToDto(UserLoginInfoDO loginInfoDO) {
        if ( loginInfoDO == null ) {
            return null;
        }

        UserLoginInfoDTO userLoginInfoDTO = new UserLoginInfoDTO();

        userLoginInfoDTO.setId( loginInfoDO.getId() );
        userLoginInfoDTO.setClientId( loginInfoDO.getClientId() );
        userLoginInfoDTO.setClientType( loginInfoDO.getClientType() );
        userLoginInfoDTO.setLoginType( loginInfoDO.getLoginType() );
        userLoginInfoDTO.setUserId( loginInfoDO.getUserId() );
        userLoginInfoDTO.setUserAccountName( loginInfoDO.getUserAccountName() );
        userLoginInfoDTO.setIp( loginInfoDO.getIp() );
        userLoginInfoDTO.setMac( loginInfoDO.getMac() );
        userLoginInfoDTO.setType( loginInfoDO.getType() );
        userLoginInfoDTO.setLoginCount( loginInfoDO.getLoginCount() );
        userLoginInfoDTO.setErrorCode( loginInfoDO.getErrorCode() );
        userLoginInfoDTO.setErrorMessage( loginInfoDO.getErrorMessage() );
        userLoginInfoDTO.setDataVersion( loginInfoDO.getDataVersion() );
        userLoginInfoDTO.setCreateUserId( loginInfoDO.getCreateUserId() );
        userLoginInfoDTO.setCreateTime( loginInfoDO.getCreateTime() );
        userLoginInfoDTO.setCreateUserName( loginInfoDO.getCreateUserName() );
        userLoginInfoDTO.setLastUpdateUserId( loginInfoDO.getLastUpdateUserId() );
        userLoginInfoDTO.setLastUpdateTime( loginInfoDO.getLastUpdateTime() );
        userLoginInfoDTO.setLastUpdateUserName( loginInfoDO.getLastUpdateUserName() );
        userLoginInfoDTO.setSequence( loginInfoDO.getSequence() );

        return userLoginInfoDTO;
    }
}

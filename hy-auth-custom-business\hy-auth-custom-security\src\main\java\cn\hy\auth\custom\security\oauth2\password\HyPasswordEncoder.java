package cn.hy.auth.custom.security.oauth2.password;

import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.Assert;

/**
 * 类描述: 密码加密默认实现
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/13
 */
@Slf4j
public class HyPasswordEncoder implements PasswordEncoder {

    @Override
    public String encode(CharSequence rawPassword) {

        Assert.notNull(rawPassword, LocaleUtil.getMessage("HyPasswordEncoder.assert.msg1", null));

        return AesUtil.encrypt(rawPassword.toString(), AesUtil.DEFAULT_KEY);
    }


    /**
     * @param rawPassword     密码明文
     * @param encodedPassword 密码密文(数据库获取)
     * @return
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {

        Assert.notNull(rawPassword, LocaleUtil.getMessage("HyPasswordEncoder.assert.msg1", null));
        Assert.notNull(encodedPassword, LocaleUtil.getMessage("HyPasswordEncoder.assert.msg2", null));

        String encode = AesUtil.encrypt(rawPassword.toString(), AesUtil.DEFAULT_KEY);

        log.debug("encodeRawPassword:{}, encodedPassword:{}", encode, encodedPassword);

        return encode.equals(encodedPassword);
    }
}
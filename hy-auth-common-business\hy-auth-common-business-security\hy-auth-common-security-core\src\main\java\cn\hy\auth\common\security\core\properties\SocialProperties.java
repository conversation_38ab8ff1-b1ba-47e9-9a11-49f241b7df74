package cn.hy.auth.common.security.core.properties;

/**
 * 社交配置
 *
 * <AUTHOR>
 * @date 2019/1/5 12:49
 */
public class SocialProperties {


    private WechatProperties wechat = new WechatProperties();
    //默认的地址
    private String filterProcessesUrl = "/auth";
    //注册地址
    private String signUpUrl;

    public WechatProperties getWechat() {
        return wechat;
    }

    public void setWechat(WechatProperties wechat) {
        this.wechat = wechat;
    }

    public String getFilterProcessesUrl() {
        return filterProcessesUrl;
    }

    public void setFilterProcessesUrl(String filterProcessesUrl) {
        this.filterProcessesUrl = filterProcessesUrl;
    }

    public String getSignUpUrl() {
        return signUpUrl;
    }

    public void setSignUpUrl(String signUpUrl) {
        this.signUpUrl = signUpUrl;
    }
}

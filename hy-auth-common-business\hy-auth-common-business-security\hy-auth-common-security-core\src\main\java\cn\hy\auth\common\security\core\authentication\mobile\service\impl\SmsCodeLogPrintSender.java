package cn.hy.auth.common.security.core.authentication.mobile.service.impl;


import cn.hutool.json.JSONObject;
import cn.hy.auth.common.security.core.authentication.mobile.KafkaSmsCodeMessage;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.id.IdWorker;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：控制台打印出手机验证码的方式，模拟实现发送验证码
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/25 11:05
 **/
@Slf4j
public class SmsCodeLogPrintSender implements SmsCodeSender {

    public Cache<String, Integer> smsSendNumberCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    @Autowired
    private AppConfigParamUtil appConfigParamUtil;

    @Autowired
    private KafkaTemplate kafkaTemplate;

    @Autowired
    private IdWorker idWorker;

    @Value("${:auth_sms_message_topic}")
    private String smsCodeKafkaTopic;

    @Override
    public String send(String mobile, String code, String appCode , String lesseeCode, SmsLoginVO smsLoginVO, String appParamKey) {
        Integer number = smsSendNumberCache.getIfPresent(appParamKey + mobile);
        number = number == null ? 0 : number;
        Object limitobj = appConfigParamUtil.getByKey(lesseeCode, appCode, appParamKey);
        Integer limit = limitobj == null ? 10 : Integer.parseInt(limitobj.toString());
        if (limit <= number) {
            return LocaleUtil.getMessage("SmsCodeLogPrintSender.result.msg1", "");
        }

        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("smsCode", code);
        paramMap.put("mobile", mobile);
        Map<String, Object> smsTemplateData = smsLoginVO.getSmsTemplateData();
        paramMap.put("smsTemplateData", smsTemplateData == null ? new HashMap<>() : smsTemplateData);
        KafkaSmsCodeMessage msg = KafkaSmsCodeMessage.getDefaultMessage(idWorker.nextId()+"");
        msg.setPayload(paramMap);
        String eventMsg = JSON.toJSONString(msg);
        kafkaTemplate.send(smsCodeKafkaTopic, eventMsg)
                .addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                    @Override
                    public void onFailure(Throwable ex) {
                        log.error("发送短信验证码的kafka消息失败，手机号{}，验证码{}", mobile, code);
                    }
                    @Override
                    public void onSuccess(SendResult<String, String> result) {
                        log.info("发送短信验证码的kafka消息成功，手机号{}，验证码{}", mobile, code);
                    }
                });
        log.info("成功发送手机验证码: mobile = {},code = {}", mobile, code);
        smsSendNumberCache.put(appParamKey + mobile, number == null ? 0 : (Integer)number + 1);
        return "";
    }

    public String getRequestBody(String code, String mobile, SmsLoginVO smsLoginVO) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("ecName", smsLoginVO.getEcName());
        jsonObject.set("apId", smsLoginVO.getApId());
        jsonObject.set("mobiles", mobile);
        Integer smsType = smsLoginVO.getSmsType();
        String content = "";
        if (smsType == 1) {
            content = String.format(LocaleUtil.getMessage("SmsCodeLogPrintSender.result.msg5", ""), code);
        } else {
            content = String.format(LocaleUtil.getMessage("SmsCodeLogPrintSender.result.msg6", ""), code);
        }
        jsonObject.set("content", content);
        jsonObject.set("sign", smsLoginVO.getSign());
        jsonObject.set("addSerial", smsLoginVO.getAddSerial());
        jsonObject.set("secretKey", smsLoginVO.getSecretKey());
        jsonObject.set("tel", mobile);
        jsonObject.set("code", code);
        jsonObject.set("send_msg", content);

        return jsonObject.toString();
    }

}

package cn.hy.auth.custom.security.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.CacheManager;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import javax.servlet.*;
/**
 *  拦截checktoken，实现用户无操作自动下线
 *  暂时注释，不实现该功能
 * <AUTHOR>
 * @date  2022-01-11 17:00
 **/
@Slf4j
//@WebFilter(filterName = "autoOfflineFilter", urlPatterns = "/oauth/check_token")
public class AutoOfflineFilter implements Filter {

	/**
	 * token在参数列表中的属性名
	 */
	private static final String TOKEN_ON_PARAM_KEY = "token";

	/**
	 * 程序操作属性名
	 */
	private static final String MECHINE_OPERATION = "machineOpt";

	public static final String CACHE_KEY = "auth-user-survival-time";

	private CacheManager cacheManager;

	public AutoOfflineFilter(@Qualifier("autoOfflineCacheManager")CacheManager cacheManager) {
		this.cacheManager = cacheManager;
	}

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
						 FilterChain filterChain) throws IOException, ServletException {
		filterChain.doFilter(servletRequest, servletResponse);

		//checktoken有效，才刷新时间
		if (((HttpServletResponse) servletResponse).getStatus() != 200) {
			return;
		}

		HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
		String machineOpt = httpRequest.getParameter(MECHINE_OPERATION);
		if (machineOpt != null && Boolean.getBoolean(machineOpt)){
			// 机器操作，不记录为用户操作
			log.debug("机器操作，不记录为用户操作。{}",httpRequest.getRequestURI());
			return;
		}
		String token = httpRequest.getParameter(TOKEN_ON_PARAM_KEY);
		if (StringUtils.isBlank(token)){
			log.debug("机器操作，不记录为用户操作。{}",httpRequest.getRequestURI());
			return;
		}
		if (StringUtils.isNotBlank(token)) {
			cacheManager.getCache(CACHE_KEY).put(token, System.currentTimeMillis());
		}

	}
}
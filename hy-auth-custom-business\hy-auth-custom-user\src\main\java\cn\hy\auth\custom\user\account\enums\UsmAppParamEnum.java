package cn.hy.auth.custom.user.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @title: UsmAppParamEnum
 * @description: 用户安全管理参数枚举
 * @date 2024/1/17
 */
@Getter
@AllArgsConstructor
public enum UsmAppParamEnum {

    PWD_CHANGE_INTERVAL("user_change_pwd_switch", "用户更换密码开关"),

    LOGIN_BIND_IP("restrict_user_ip_switch", "限制用户ip访问开关");

    private final String code;

    private final String name;

}

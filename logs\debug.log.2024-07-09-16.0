2024-07-09 16:00:00.034 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-09 15:47:21,833 to 2024-07-09 16:00:00,017
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.04| 44.00%|            25|            11|             0|             0|       14.6|         80
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-09 16:00:59.915 [,,,,Not Auth Request!] [Thread-51] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-09 16:00:59.915 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.919 [,,,,Not Auth Request!] [Thread-67] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=13236&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.919 [,,,,Not Auth Request!] [Thread-67] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=13236&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.920 [,,,,Not Auth Request!] [Thread-67] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=13236&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.927 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-09 16:00:59.928 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.935 [,,,,Not Auth Request!] [Thread-67] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=13236&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.936 [,,,,Not Auth Request!] [Thread-67] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /***********:20881, dubbo version: 2.7.8, current host: **********
2024-07-09 16:00:59.937 [,,,,Not Auth Request!] [Thread-67] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /***********:20881, dubbo version: 2.7.8, current host: **********

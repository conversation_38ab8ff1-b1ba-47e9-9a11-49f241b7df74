package cn.hy.auth.boot.init;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/11
 */
@Slf4j
@Component
public class ConsulRegisterHandler {

    private final RestTemplateBuilder restTemplateBuilder;
    private final RestTemplate restTemplate;
    private final String consulUrl;
    private final String serviceId;
    private final String serviceName;
    private final String serviceAddress;
    private final String aclToken;

    public ConsulRegisterHandler(RestTemplateBuilder restTemplateBuilder,
                                 @Value("${spring.cloud.consul.host}") String consulHost,
                                 @Value("${spring.cloud.consul.port}") int consulPort,
                                 @Value("${spring.application.name}") String serviceName,
                                 @Value("${spring.cloud.consul.discovery.instanceId}") String serviceId,
                                 @Value("${spring.cloud.consul.discovery.acl-token}") String aclToken,
                                 @Value("${spring.cloud.consul.discovery.hostname}") String serviceAddress) {
        this.consulUrl = "http://" + consulHost + ":" + consulPort;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.restTemplateBuilder = restTemplateBuilder;
        this.restTemplate = restTemplateBuilder.build();
        this.serviceAddress = serviceAddress;
        this.aclToken = aclToken;
        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                ConsulRegisterHandler.this.registerService();
            }
        });
    }

    @PostConstruct
    @PreDestroy
    public void registerService() {
        try {
            // 节点数
            int nodes = consulNodes();
            log.info("####################开始执行conusl注册信息校验删除####################");
            while (true){
                List<Map<String, Object>> services = getServicesByServiceId(serviceName);
                if (services == null || services.size() == 0){
                    break;
                }
                List<Map<String,Object>> serviceIdList = services.stream().filter(item -> item.get("ServiceID").equals(serviceId)).collect(Collectors.toList());
                if (serviceIdList.isEmpty()){
                    break;
                }
                for (Map<String, Object> map : serviceIdList) {
                    // 集群轮询 所以多次执行
                    for (int i = 0; i < nodes; i++) {
                        deregisterService(map.get("ServiceID").toString());
                        log.info("删除conusl注册信息成功:{}", JSON.toJSONString(map));
                    }
                }
            }
        }catch (Exception e){
            log.warn("删除conusl多节点注册信息失败:",e);
        }
    }


    // 获取所有与serviceId相关的服务实例
    public List<Map<String,Object>> getServicesByServiceId(String serviceId) throws IOException {
        // 拼接请求URL
        String urlStr = consulUrl+ "/v1/catalog/service/"+ serviceId;
        HttpEntity<String> entity = getHttpEntity();
        // 发起 GET 请求并携带请求头，返回对象类型
        ResponseEntity<String> response = restTemplate.exchange(urlStr, HttpMethod.GET, entity, String.class);
        // 使用ObjectMapper解析JSON响应
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(response.getBody(), new TypeReference<List<Map<String, Object>>>() {});
    }

    // 根据instanceId注销服务
    public void deregisterService(String instanceId) {
        String urlStr = consulUrl + "/v1/agent/service/deregister/"+instanceId;
        HttpEntity<String> entity = getHttpEntity();
        restTemplate.exchange(urlStr, HttpMethod.PUT,entity,String.class);
    }

    // 查询conusl节点数
    private int consulNodes() throws IOException {
        // 拼接请求URL
        String urlStr = consulUrl+ "/v1/catalog/nodes";
        HttpEntity<String> entity = getHttpEntity();
        // 发起 GET 请求并携带请求头，返回对象类型
        ResponseEntity<String> response = restTemplate.exchange(urlStr, HttpMethod.GET, entity, String.class);
        // 使用ObjectMapper解析JSON响应
        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, Object>> list = objectMapper.readValue(response.getBody(), new TypeReference<List<Map<String, Object>>>() {});
        return list.size();
    }

    // 请求头获取
    private HttpEntity<String> getHttpEntity() {
        // 创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Consul-Token", aclToken);
        // 封装请求头到 HttpEntity（请求体为 null）
        return new HttpEntity<>(null, headers);
    }

}

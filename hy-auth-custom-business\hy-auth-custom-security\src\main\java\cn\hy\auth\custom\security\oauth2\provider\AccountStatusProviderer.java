package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.common.security.core.authentication.mobile.exception.ErrorSmsCodeException;
import cn.hy.auth.common.security.core.authentication.mobile.exception.UserNotExistException;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 用户状态校验
 *
 * <AUTHOR>
 * @date 2020-11-27 15:41
 **/
@Component
@Slf4j
public class AccountStatusProviderer extends AbstractAuthenticationProvider {

    private final UserDetailsService myUserDetailsService;

    public AccountStatusProviderer(UserDetailsService myUserDetailsService) {
        this.myUserDetailsService = myUserDetailsService;
    }

    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return Authentication 认证信息封装类
     */
    @Override
    protected Authentication doAuthenticate(Authentication authentication) {
        String userName = (String)authentication.getPrincipal();
        log.debug("账号状态检查-->验证用户【{}】账号的状态。", userName);
        HyUserDetails account = getUserDetail(userName);
        if (Objects.isNull(account)) {
            throw new BadCredentialsException(LocaleUtil.getMessage("AccountStatusProviderer.result.msg1", null));
        }

        if (!account.isAccountNonLocked()) {
            throw new LockedException(LocaleUtil.getMessage("AccountStatusProviderer.result.msg3", null));
        }

        if (!account.isEnabled()) {
            throw new DisabledException(LocaleUtil.getMessage("AccountStatusProviderer.result.msg4", null));
        }

        if (!account.isAccountNonExpired()) {
            throw new AccountExpiredException(LocaleUtil.getMessage("AccountStatusProviderer.result.msg5", null));
        }

        if (!account.isCredentialsNonExpired()) {
            throw new CredentialsExpiredException(LocaleUtil.getMessage("AccountStatusProviderer.result.msg6", null));
        }

        log.debug("账号状态检查-->验证用户【{}】账号状态正常。", userName);
        return null;
    }

    private HyUserDetails getUserDetail(String userName){
        return (HyUserDetails) myUserDetailsService.loadUserByUsername(userName);
    }
    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    @Override
    protected int order() {
        return 15;
    }


    /**
     * 判断是否是用户密码登录模式
     *
     * @param authentication .
     * @return .
     */
    @Override
    protected boolean isSupport(Authentication authentication) {
        List<LoginTypeEnum> supports = Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD,
                LoginTypeEnum.SMS_CODE,
                LoginTypeEnum.OAUTH2_PASSWORD,
                LoginTypeEnum.USB_KEY);

        return supports.contains(AuthContext.getContext().loginState().getLoginType());

    }
}

2025-04-15 10:57:19.136 [, , , , ] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://*************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2025-04-15 10:57:19.143 [, , , , ] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2025-04-15 10:57:19.390 [, , , , ] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2025-04-15 10:57:25.525 [, , , , ] [main] INFO  c.h.l.a.LogbackSourceContextInitializer - LogbackSourceInitializer - Initialize
2025-04-15 10:57:25.529 [, , , , ] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2025-04-15 10:57:28.322 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-15 10:57:28.322 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-15 10:57:28.523 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 190ms. Found 12 repository interfaces.
2025-04-15 10:57:28.692 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2025-04-15 10:57:28.694 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2025-04-15 10:57:28.695 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2025-04-15 10:57:28.696 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-04-15 10:57:28.698 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2025-04-15 10:57:29.218 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-15 10:57:29.220 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-15 10:57:29.477 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2025-04-15 10:57:29.478 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2025-04-15 10:57:29.478 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2025-04-15 10:57:29.480 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2025-04-15 10:57:29.480 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2025-04-15 10:57:29.482 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2025-04-15 10:57:29.483 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2025-04-15 10:57:29.483 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2025-04-15 10:57:29.483 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2025-04-15 10:57:29.484 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2025-04-15 10:57:29.486 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2025-04-15 10:57:29.487 [, , , , ] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2025-04-15 10:57:29.502 [, , , , ] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 270ms. Found 0 repository interfaces.
2025-04-15 10:57:29.895 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-04-15 10:57:29.895 [, , , , ] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2025-04-15 10:57:29.895 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-04-15 10:57:29.895 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-04-15 10:57:29.896 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-04-15 10:57:30.485 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2025-04-15 10:57:30.485 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2025-04-15 10:57:30.618 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApiV2, dubbo version: 2.7.8, current host: *********
2025-04-15 10:57:30.620 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2025-04-15 10:57:30.621 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 2 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboNewService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboNewService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboNewService.class], Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2025-04-15 10:57:30.624 [, , , , ] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-04-15 10:57:31.165 [, , , , ] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=660913c5-2df2-38cc-8d2b-ed2a1fb75ee1
2025-04-15 10:57:32.187 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$e6ca4448] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:32.578 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$8dbcaea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:32.855 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$ebe92809] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:33.571 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1f7ff5dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:33.683 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9e863b54] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:33.758 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$131832c5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:33.950 [, , , , ] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2f3235c2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:57:34.998 [, , , , ] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2025-04-15 10:57:35.029 [, , , , ] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2025-04-15 10:57:35.050 [, , , , ] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 10:57:35.050 [, , , , ] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-04-15 10:57:35.223 [, , , , ] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 10:57:35.223 [, , , , ] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9596 ms
2025-04-15 10:57:35.582 [, , , , ] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2025-04-15 10:57:35.603 [, , , , ] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2025-04-15 10:57:36.084 [, , , , ] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-15 10:57:36.109 [, , , , ] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@55fc6344
2025-04-15 10:57:36.434 [, , , , ] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2025-04-15 10:57:38.243 [, , , , ] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2025-04-15 10:57:39.922 [, , , , ] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-15 10:57:43.018 [, , , , ] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2025-04-15 10:57:43.341 [, , , , ] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2025-04-15 10:57:43.347 [, , , , ] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2025-04-15 10:57:44.187 [, , , , ] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2025-04-15 10:57:44.771 [, , , , ] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-15 10:57:45.828 [, , , , ] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2025-04-15 10:57:45.871 [, , , , ] [main] INFO  o.h.engine.jdbc.env.internal.LobCreatorBuilderImpl - HHH000422: Disabling contextual LOB creation as connection was null
2025-04-15 10:57:47.984 [, , , , ] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-04-15 10:57:48.970 [, , , , ] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2025-04-15 10:57:53.430 [, , , , ] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2025-04-15 10:57:54.182 [, , , , ] [main] INFO  cn.hy.auth.boot.init.ConsulRegisterHandler - ####################开始执行conusl注册信息校验删除####################
2025-04-15 10:57:54.253 [, , , , ] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:5, workerId:29
2025-04-15 10:57:54.513 [, , , , ] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 10:57:56.691 [, , , , ] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2025-04-15 10:57:56.691 [, , , , ] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2025-04-15 10:57:57.888 [, , , , ] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2025-04-15 10:57:57.888 [, , , , ] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2025-04-15 10:58:00.186 [, , , , ] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2025-04-15 10:58:00.186 [, , , , ] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendEmail'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2025-04-15 10:58:04.104 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2025-04-15 10:58:04.105 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/websocket/**'], []
2025-04-15 10:58:04.738 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@32c3f813, org.springframework.security.web.context.SecurityContextPersistenceFilter@62785afd, org.springframework.security.web.header.HeaderWriterFilter@39a80226, org.springframework.security.web.authentication.logout.LogoutFilter@336e1bcf, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@26d6399e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1ef15e13, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65b636a9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f527fd5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6657c199, org.springframework.security.web.session.SessionManagementFilter@60d4e243, org.springframework.security.web.access.ExceptionTranslationFilter@12ccc0d3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3fdef83]
2025-04-15 10:58:04.848 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@61bf6c81, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bf42283, org.springframework.security.web.context.SecurityContextPersistenceFilter@77c5ba4, org.springframework.security.web.header.HeaderWriterFilter@16a7a6a, org.springframework.security.web.authentication.logout.LogoutFilter@6ea70999, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@40eeb885, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@5145512c, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@42e454c5, cn.hy.auth.common.security.core.authentication.copytoken.CopyTokenAuthenticationFilter@2267faa3, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@3826510a, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@423c8cb7, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@398b69ed, cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationFilter@24a42e9d, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@2e008502, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2e055d52, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c6edbd2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@87fed41, org.springframework.security.web.session.SessionManagementFilter@5e698640, org.springframework.security.web.access.ExceptionTranslationFilter@3ca2be5f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4d97c8d7]
2025-04-15 10:58:04.879 [, , , , ] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@419a26f, org.springframework.security.web.context.SecurityContextPersistenceFilter@235bf36f, org.springframework.security.web.header.HeaderWriterFilter@46b57a3d, org.springframework.security.web.csrf.CsrfFilter@7f0e9c4c, org.springframework.security.web.authentication.logout.LogoutFilter@7825e68f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ed7d39, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d648b8b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f760360, org.springframework.security.web.session.SessionManagementFilter@33f4c40c, org.springframework.security.web.access.ExceptionTranslationFilter@43ec0f22]
2025-04-15 10:58:05.324 [, , , , ] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-15 10:58:06.149 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-15 10:58:08.766 [, , , , ] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-15 10:58:09.017 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2025-04-15 10:58:09.995 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2025-04-15 10:58:10.137 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application hostname="hy" qosEnable="false" name="HY-AUTH-DUBBO-PROVIDER" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2025-04-15 10:58:10.155 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry simplified="true" address="consul://*************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://*************:18500, simplified=true}]
2025-04-15 10:58:10.163 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2025-04-15 10:58:10.179 [, , , , ] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2025-04-15 10:58:10.197 [, , , , ] [main] INFO  c.h.a.c.u.account.dubbo.UserAccountDubboNewService - 创建了UserAccountDubboService
2025-04-15 10:58:10.212 [, , , , ] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2025-04-15 10:58:11.534 [, , , , ] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-04-15 10:58:12.123 [, , , , ] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2025-04-15 10:58:12.123 [, , , , ] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2025-04-15 10:59:13.275 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'configWatchTaskScheduler'
2025-04-15 10:59:13.277 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'catalogWatchTaskScheduler'
2025-04-15 10:59:13.344 [, , , , ] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-04-15 10:59:13.360 [, , , , ] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-15 10:59:13.374 [, , , , ] [main] INFO  cn.hy.auth.boot.init.ConsulRegisterHandler - ####################开始执行conusl注册信息校验删除####################
2025-04-15 10:59:13.384 [, , , , ] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-04-15 10:59:13.577 [, , , , ] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2025-04-15 10:59:13.578 [, , , , ] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-04-15 10:59:13.621 [, , , , ] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.

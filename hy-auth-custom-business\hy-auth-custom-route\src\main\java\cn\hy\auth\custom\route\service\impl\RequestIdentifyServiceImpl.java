package cn.hy.auth.custom.route.service.impl;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.script.ScriptEngine;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.route.factory.ThirdIndentifyFactory;
import cn.hy.auth.custom.route.indentify.ThirdIndentifyClient;
import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.route.service.RequestIdentifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.stereotype.Service;

/**
 * 类描述：用户获取的token标识
 *
 * <AUTHOR>
 * @date 2022/11/17 9:55
 **/
@Service
@Slf4j
public class RequestIdentifyServiceImpl implements RequestIdentifyService {
    private final ScriptEngine scriptEngine;
    private final RouteProperties routeProperties;
    private org.springframework.security.oauth2.provider.authentication.TokenExtractor tokenExtractor;

    public RequestIdentifyServiceImpl(ScriptEngine scriptEngine, RouteProperties routeProperties) {
        this.scriptEngine = scriptEngine;
        this.routeProperties = routeProperties;
        this.tokenExtractor = new BearerTokenExtractor();
    }

    @Override
    public String getRequestSystemAreaIdentify(String token) {
        if (token == null || token.length() == 0) {
            return null;
        }
        if (token.split("\\.").length > 3) {
            log.debug("收到3.X认证请求：-->");
            return "3.X";
        } else {
            //外部系统
            log.debug("收到非3.X认证请求：-->{}",routeProperties.getEnableThird());
            ThirdIndentifyClient thirdIndentifyClient =
                    ThirdIndentifyFactory.getThirdIndentifyClient(routeProperties.getEnableThird());
            AuthAssertUtils.notNull(thirdIndentifyClient,"500", LocaleUtil.getMessage("RequestIdentifyServiceImpl.assertUtil.msg1", null)+routeProperties.getEnableThird());
            return thirdIndentifyClient.getThirdIndentify(token);
        }
    }
}

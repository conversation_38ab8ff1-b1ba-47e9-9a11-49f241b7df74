version: "2.3"
services:
  hy-auth-center:
    container_name: hy-auth-center-boot
    image: "{{ env.hy_auth.image }}"
    networks:
      - hy-cloud-network
    ports:
      - "{{ hy_auth.host }}:6060"
    healthcheck:
      test: ["CMD","curl", "-f", "http://localhost:{{ hy_auth.host }}/actuator/health"]
      interval: 5s
      timeout: 60s
      retries: 12
    volumes:
      - {{ global.app_home }}/auth/logs:/app/logs
      - {{ global.app_home }}/auth/license:/app/bin/license
    restart: always
    environment:
      CONSUL_HOST: "{{ consul.host }}"
      CONSUL_PORT: "{{ consul.port }}"
      AUTH_HOST: "{{ hy_auth.host }}"
      MYSQL_HOST: "{{ mysql.host }}"
      MYSQL_PORT: "{{ mysql.port }}"
      DATABASE_NAME: "{{ mysql.dbname }}"
      DATABASE_USER: "{{ mysql.user }}"
      DATABASE_PWD: "{{ mysql.pwd }}"
      APP_NAME: "{{ hy_auth.appname }}"
      X2_AUTH_CENTER: "{{ 2.x_auth.center }}"
      ACL_TOKEN: "{{ acl_token }}"
    command: deploy init-yml  init-database
networks:
  hy-cloud-network:
    external: true
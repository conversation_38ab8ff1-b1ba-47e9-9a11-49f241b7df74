package cn.hy.auth.common.security.oauth2.util;


import org.springframework.lang.Nullable;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/10 16:11
 **/

public class RequestUtil {
    /**
     * 获得请求url
     *
     * @param request 请求体
     * @return .
     */
    public static String getRequestUrl(final HttpServletRequest request) {
        return request.getRequestURI();
    }

    /**
     * 获取客户端请求参数中所有的信息
     *
     * @param request     .
     * @param ignoreParam 忽略的请求参数
     */
    public static Map<String, String> getAllRequestParam(final HttpServletRequest request, @Nullable String... ignoreParam) {

        Map<String, String> res = new HashMap<>(4);
        if (ignoreParam == null) {
            ignoreParam = new String[]{};
        }
        Set<String> ignoreParamSet = new HashSet<>(ignoreParam.length);
        Collections.addAll(ignoreParamSet, ignoreParam);
        Enumeration<?> temp = request.getParameterNames();
        if (null != temp) {
            while (temp.hasMoreElements()) {
                String en = (String) temp.nextElement();
                String value = request.getParameter(en);
                res.put(en, value);
                //如果字段的值为空，判断若值为空，则删除这个字段>
                if (null == res.get(en) || "".equals(res.get(en)) || ignoreParamSet.contains(en)) {
                    res.remove(en);
                }
            }
        }
        return res;
    }

    private RequestUtil() {
    }

}

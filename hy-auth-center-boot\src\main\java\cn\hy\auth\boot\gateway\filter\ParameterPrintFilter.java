package cn.hy.auth.boot.gateway.filter;

import cn.hy.auth.custom.common.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述: 请求参数打印过滤器
 *
 * <AUTHOR>
 * @date ：创建于 2021/01/12
 */
@Slf4j
@Component
@Order(value = 0)
public class ParameterPrintFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        RequestWrapper requestWrapper = null;
        try {
            requestWrapper = new RequestWrapper(request);
            log.debug("请求方IP:{},请求地址:{},方式:{},请求参数:{}", IpUtils.getIpAddress(request), request.getRequestURI(), request.getMethod(), requestWrapper.getRequestParam());

        } catch (Exception e) {
            log.error("请求参数打印过滤器异常", e);
        }
        //因为从输入流中把参数都拿了出来,导致二次获取流中数据的时候为空了,所有这边必须是使用封装的对象requestWrapper传才行
        filterChain.doFilter(requestWrapper, response);
    }
}

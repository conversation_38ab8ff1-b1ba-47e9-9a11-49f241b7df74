package cn.hy.auth.custom.route.service.route.impl;

import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.route.service.route.SendResponseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.HashSet;
import java.util.Set;


/**
 * Post {@link } that writes responses from proxied requests to the current
 * response.
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Slf4j
@Component
public class SendResponseServiceImpl implements SendResponseService {


    private boolean useServlet31 = true;

    private ThreadLocal<byte[]> buffers;
    private Set<String> ignoreHeader =  new HashSet<>();


    public SendResponseServiceImpl(RouteProperties routeProperties) {
        // To support Servlet API 3.1 we need to check if setContentLengthLong exists
        // minimum support in Spring 5 is 3.0 so we need to keep tihs
        try {
            HttpServletResponse.class.getMethod("setContentLengthLong", long.class);
        } catch (NoSuchMethodException e) {
            useServlet31 = false;
        }
        buffers = ThreadLocal
                .withInitial(() -> new byte[routeProperties.getInitialStreamBufferSize()]);
        ignoreHeader.addAll(routeProperties.getIgnoreHeaders());
    }

    /* for testing */ boolean isUseServlet31() {
        return useServlet31;
    }


    @Override
    public void sendResponse(HttpServletResponse servletResponse, HttpResponse httpResponse) {
        try {
            addResponseHeaders(servletResponse, httpResponse);
            writeResponse(servletResponse, httpResponse);
        } catch (Exception ex) {
            rethrowRuntimeException(ex);
        }
    }

    private void addResponseHeaders(HttpServletResponse servletResponse, HttpResponse httpResponse) {
        Header[] allHeaders = httpResponse.getAllHeaders();
        for (Header header : allHeaders) {
            if (ignoreHeader.contains(header.getName())){
                continue;
            }
            servletResponse.addHeader(header.getName(), header.getValue());
        }

        servletResponse.setContentLengthLong(httpResponse.getEntity().getContentLength());
        servletResponse.setStatus(httpResponse.getStatusLine().getStatusCode());

    }

    private void writeResponse(HttpServletResponse servletResponse, HttpResponse httpResponse) throws Exception {
        // there is no body to send
        if (httpResponse.getEntity() == null
                || httpResponse.getEntity().getContent() == null) {
            return;
        }
        // only set if not set
        if (servletResponse.getCharacterEncoding() == null) {
            servletResponse.setCharacterEncoding("UTF-8");
        }

        OutputStream outStream = servletResponse.getOutputStream();
        InputStream is = null;
        try {

            is = httpResponse.getEntity().getContent();

            if (is != null) {
                writeResponse(is, outStream);
            }
        } finally {
            /**
             * We must ensure that the InputStream provided by our upstream pooling
             * mechanism is ALWAYS closed even in the case of wrapped streams, which are
             * supplied by pooled sources such as Apache's
             * PoolingHttpClientConnectionManager. In that particular case, the underlying
             * HTTP connection will be returned back to the connection pool iif either
             * close() is explicitly called, a read error occurs, or the end of the
             * underlying stream is reached. If, however a write error occurs, we will end
             * up leaking a connection from the pool without an explicit close()
             *
             * <AUTHOR> Edmeier
             */
            if (is != null) {
                try {
                    is.close();
                } catch (Exception ex) {
                    log.warn("Error while closing upstream input stream", ex);
                }
            }

            // cleanup ThreadLocal when we are all done
            if (buffers != null) {
                buffers.remove();
            }

            try {

                if (httpResponse instanceof Closeable) {
                    ((Closeable) httpResponse).close();
                }
                outStream.flush();
                // The container will close the stream for us
            } catch (IOException ex) {
                log.warn("Error while sending response to client: " + ex.getMessage());
            }
        }
    }


    private void writeResponse(InputStream zin, OutputStream out) throws Exception {
        byte[] bytes = buffers.get();
        int bytesRead = -1;
        while ((bytesRead = zin.read(bytes)) != -1) {
            out.write(bytes, 0, bytesRead);
        }
    }

    public static void rethrowRuntimeException(Throwable ex) {
        if (ex instanceof RuntimeException) {
            throw (RuntimeException) ex;
        }
        if (ex instanceof Error) {
            throw (Error) ex;
        }
        throw new UndeclaredThrowableException(ex);
    }

}

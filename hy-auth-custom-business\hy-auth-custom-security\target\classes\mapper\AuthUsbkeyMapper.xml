<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.security.reauthentication.dao.AuthUsbkeyMapper">
    <resultMap id="BaseResultMap" type="cn.hy.auth.custom.security.reauthentication.domain.AuthUsbkeyDO">
        <id column="id" property="id" jdbcType="DECIMAL" />

        <result column="hr_code" property="hrCode" jdbcType="VARCHAR" />
        <result column="random_number" property="randomNumber" jdbcType="VARCHAR" />
        <result column="public_key" property="publicKey" jdbcType="VARCHAR" />
        <result column="sn" property="sn" jdbcType="VARCHAR" />

        <result column="data_version" property="dataVersion" jdbcType="VARCHAR" />
        <result column="create_user_id" property="createUserId" jdbcType="DECIMAL" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
        <result column="last_update_user_id" property="lastUpdateUserId" jdbcType="DECIMAL" />
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
        <result column="last_update_user_name" property="lastUpdateUserName" jdbcType="VARCHAR" />
        <result column="sequence" property="sequence" jdbcType="INTEGER" />
    </resultMap>

    <sql id="base_column_list">
        id, hr_code, random_number, public_key, sn,
        create_user_name, last_update_user_name, create_user_id, last_update_user_id, create_time, last_update_time
    </sql>

    <select id="selectByHrCode" resultMap="BaseResultMap">
        SELECT <include refid="base_column_list"/>
        FROM auth_usbkey
        WHERE hr_code = #{hrCode}
    </select>

    <update id="revokeNonce">
        UPDATE auth_usbkey
        SET random_number = ''
        WHERE id = #{id}
    </update>
</mapper>
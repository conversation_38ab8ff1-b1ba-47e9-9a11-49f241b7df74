package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 策略参数基类
 *
 * <AUTHOR>
 * @date 2020-12-03 16:28
 **/
@Data
public class BasePolicyDTO implements Serializable {
    private static final long serialVersionUID = -8190073242997437865L;
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 属性描述信息
     */
    private String remark;

    public boolean isEnable() {
        return Optional.ofNullable(enable).orElse(Boolean.FALSE);
    }

    /**
     * 解析出时间戳值
     *
     * @param data
     * @param key
     * @return
     */
    protected Long parseTimestamp(Map<String, Object> data, String key) {
        if (!isEnable() || MapUtils.isEmpty(data)) {
            return null;
        }

        Object value = MapUtils.getObject(data, key);
        if (Objects.isNull(value)) {
            return null;
        }

        if (value instanceof Date) {
            return ((Date) value).getTime();
        } else if (value instanceof Long) {
            return (Long) value;
        }
        return null;
    }
}

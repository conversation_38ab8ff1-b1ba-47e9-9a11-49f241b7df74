package cn.hy.auth.custom.security.third.gxfl;

import com.szgx.scmp.gateway.sdk.client.ApacheHttpClient;
import com.szgx.scmp.gateway.sdk.constant.SdkConstant;
import com.szgx.scmp.gateway.sdk.enums.Scheme;
import com.szgx.scmp.gateway.sdk.model.ApiRequest;
import com.szgx.scmp.gateway.sdk.model.ApiResponse;
import com.szgx.scmp.gateway.sdk.model.HttpClientBuilderParams;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date 2024/3/13
 */
public class ZgtWstRestClient extends ApacheHttpClient {

    private final Scheme SCHEME = Scheme.HTTP;
    private final String HOST = "*************:8880";
    private final String CONTEXT_PATH = "/scmp-gateway";

    public ZgtWstRestClient(String appKey, String appSecret, String host) {
        HttpClientBuilderParams httpClientBuilderParams = new HttpClientBuilderParams();
        httpClientBuilderParams.setAppKey(appKey);
        httpClientBuilderParams.setAppSecret(appSecret);
        httpClientBuilderParams.setScheme(SCHEME);
        httpClientBuilderParams.setHost(host);
        httpClientBuilderParams.setContextPath(CONTEXT_PATH);

        super.init(httpClientBuilderParams);
    }




    public String sendRequestReString(ApiRequest request) {
        request.setIsEncrypt(SdkConstant.IS_ENCRYPT_FALSE);
        ApiResponse apiResponse = this.sendSyncRequest(request);
        String response = new String(apiResponse.getBody(), StandardCharsets.UTF_8);
        return response;
    }

}

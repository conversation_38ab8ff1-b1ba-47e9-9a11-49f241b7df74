package cn.hy.auth.custom.security.log.controller;

import cn.hy.auth.custom.security.log.service.LogService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/8 15:24
 **/
@RestController
@RequestMapping("/log/cache/clear")
public class LogController {
    private final LogService logService;

    public LogController(LogService logService) {
        this.logService = logService;
    }

    @GetMapping("")
    public String clearCache(){
        logService.clearCache();
        return "success";
    }

}

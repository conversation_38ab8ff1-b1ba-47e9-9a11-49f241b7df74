package cn.hy.auth.common.security.core.authentication.mobile;

import cn.hy.auth.common.security.core.authentication.mobile.exception.ValidateCodeException;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeService;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.filter.OncePerRequestFilter;


import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 手机短信验证过滤器
 *
 * <AUTHOR>
 * @date 2020-11-24 10:42
 */
@Slf4j
public class SmsCodeValidateFilter extends OncePerRequestFilter implements InitializingBean {

    /**
     * 验证码校验失败处理器
     */
    private AuthenticationFailureHandler authenticationFailureHandler;
    /**
     * 需要拦截的url
     */
    private Set<String> urls = new HashSet<>();
    /**
     * 安全框架参数配置
     */
    private SecurityProperties securityProperties;
    /**
     * 手机验证码service组件
     */
    private SmsCodeService smsCodeService;

    private ApplicationContext applicationContext;

    private AuthenticationDetailsSource<HttpServletRequest, Map<String, String>> hyAuthenticationDetailsSource;
    /**
     * 验证请求url与配置的url是否匹配的工具类
     */
    private AntPathMatcher pathMatcher = new AntPathMatcher();

    public SmsCodeValidateFilter(AuthenticationFailureHandler authenticationFailureHandler,
                                 SecurityProperties securityProperties,
                                 SmsCodeService smsCodeService,
                                 AuthenticationDetailsSource<HttpServletRequest, Map<String, String>> hyAuthenticationDetailsSource,
                                 ApplicationContext applicationContext) {
        this.authenticationFailureHandler = authenticationFailureHandler;
        this.securityProperties = securityProperties;
        this.smsCodeService = smsCodeService;
        this.hyAuthenticationDetailsSource = hyAuthenticationDetailsSource;
        this.applicationContext = applicationContext;
    }


    /**
     * 初始化要拦截的url配置信息
     */
    @Override
    public void afterPropertiesSet() throws ServletException {
        super.afterPropertiesSet();
        String[] configUrls = StringUtils.splitByWholeSeparatorPreserveAllTokens(
                securityProperties.getSmsAuth().getLoginUrl(), ",");
        urls.addAll(Arrays.asList(configUrls));
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        if (doAction(request)) {
            try {
                validateCode(new ServletWebRequest(request));
            } catch (ValidateCodeException e) {
                publishAuthenticationFailureEvent(request, e);
                authenticationFailureHandler.onAuthenticationFailure(request, response, e);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    private boolean doAction(HttpServletRequest request) {
        boolean action = false;
        for (String url : urls) {
            if (pathMatcher.match(url, request.getRequestURI())) {
                action = true;
            }
        }
        return action;
    }

    private void validateCode(ServletWebRequest request) {
        String mobile = request.getParameter(securityProperties.getSmsAuth().getMobileParameter());
        //从请求中拿到imageCode这个参数
        String codeInRequest = request.getParameter(securityProperties.getSmsAuth().getCodeParameter());
        String mobileCipherText = request.getParameter(securityProperties.getSmsAuth().getMobileCipherText());
        log.debug("mobileInRequest = {}, codeInRequest = {} ", mobile, codeInRequest);
        smsCodeService.validateCode(mobile, Objects.toString(mobileCipherText, ""),codeInRequest,
                securityProperties.getSmsAuth().getMobileParameter(), securityProperties.getSmsAuth().getCodeParameter(), request);

    }


    private void publishAuthenticationFailureEvent(HttpServletRequest request, ValidateCodeException e) {
        SmsCodeAuthenticationToken smsCodeAuthenticationToken = new SmsCodeAuthenticationToken(request.getParameter(securityProperties.getSmsAuth().getMobileParameter()));
        smsCodeAuthenticationToken.setDetails(this.hyAuthenticationDetailsSource.buildDetails(request));
        AuthenticationFailureBadCredentialsEvent badCredentialsEvent = new AuthenticationFailureBadCredentialsEvent(smsCodeAuthenticationToken, e);
        applicationContext.publishEvent(badCredentialsEvent);

    }


}

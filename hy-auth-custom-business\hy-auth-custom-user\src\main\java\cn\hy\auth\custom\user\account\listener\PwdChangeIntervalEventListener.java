package cn.hy.auth.custom.user.account.listener;

import cn.hy.auth.common.security.core.authentication.mobile.event.PwdChangeIntervalEvent;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.enums.UsmAppParamEnum;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.account.service.UserSecurityManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title: PwdChangeIntervalEventListener
 * @description: 密码更换时间间隔事件监听
 * @date 2024/4/28
 */
@Slf4j
@Component
public class PwdChangeIntervalEventListener implements ApplicationListener<PwdChangeIntervalEvent> {

    private final UserAccountService userAccountService;
    private final UserDetailsService userDetailsService;
    private final UserSecurityManageService userSecurityManageService;

    public PwdChangeIntervalEventListener(UserAccountService userAccountService, UserDetailsService userDetailsService, UserSecurityManageService userSecurityManageService) {
        this.userAccountService = userAccountService;
        this.userDetailsService = userDetailsService;
        this.userSecurityManageService = userSecurityManageService;
    }

    @Override
    public void onApplicationEvent(PwdChangeIntervalEvent pwdChangeIntervalEvent) {
        String mobile = pwdChangeIntervalEvent.getMobile();
        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(mobile, mobile);
        String userName = userByUserNameOrPhone.getUserAccountName();
        HyUserDetails account = (HyUserDetails) userDetailsService.loadUserByUsername(mobile);
        if (
                isPwdChangeInterval(account)
        ) {
            pwdChangeIntervalEvent.setCheckPass(false);
            pwdChangeIntervalEvent.setMsg(AuthErrorCodeEnum.A0247.msg());
            log.info("更换密码时间间隔检查-->验证用户【{}】的密码已到达指定修改时间，请修改！", userName);
        } else {
            pwdChangeIntervalEvent.setCheckPass(true);
        }
    }

    private boolean isPwdChangeInterval(HyUserDetails account) {
        try {
            return userSecurityManageService.existsEnableParamCfg(UsmAppParamEnum.PWD_CHANGE_INTERVAL.getCode())
                    && !userSecurityManageService.existsIgnoreWhiteList(account)
                    && userSecurityManageService.changeTimeReached(account);
        } catch (Exception e) {
            log.warn("密码更换时间间隔校验异常：{}", e.getMessage(), e);
        }
        return false;
    }

}

package cn.hy.auth.multi.datasource.metadata;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.metadata.engine.common.utils.SpringUtil;
import cn.hy.paas.multi.datasource.service.DataSourceChooser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 类描述：针对修改操作起作用.修改是从事务管理器里取的dataSource
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/6/12 18:36
 **/
@Aspect
@Component
@Slf4j
public class MultiDataSourceDataEngineDaoAspect {
  
    @Pointcut("execution(* cn.hy.metadata.engine.api.*.dao.*.*(..))")
    public void pointcut() {
        // 定义切点表达式，匹配指定包下的所有方法
    }
  
    @Before("pointcut()")
    public void beforeAdvice() {
        DataSourceChooser customDataSourceChooser = SpringUtil.getBean(DataSourceChooser.class);
        if (customDataSourceChooser != null && customDataSourceChooser.isUseDataSource()){
            // 进行多数据源选择
            // 为元数据的jpa选择数据源
            log.debug("DataEngineDaoAspect 为jpa选择数据源");
            customDataSourceChooser.chooseDataSourceByLessCodeAndAppCode(getLessCodeFromContext(),getAppCodeFromContext());
        }
    }



    private String getLessCodeFromContext() {
        return AuthContext.getContext().getLesseeCode();
    }
    private String getAppCodeFromContext() {
        return AuthContext.getContext().getAppCode();
    }

}
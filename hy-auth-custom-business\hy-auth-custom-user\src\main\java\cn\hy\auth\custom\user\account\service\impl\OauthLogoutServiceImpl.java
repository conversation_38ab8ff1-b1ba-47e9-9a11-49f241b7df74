package cn.hy.auth.custom.user.account.service.impl;

import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.user.account.service.KickOutEnum;
import cn.hy.auth.custom.user.account.service.OauthLogoutService;
import cn.hy.auth.custom.user.cache.service.KickOutTokenStoreService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 类描述: 退出登录
 *
 * <AUTHOR>
 * @date ：创建于 2020/10/29
 */
@AllArgsConstructor
@Service
@Slf4j
public class OauthLogoutServiceImpl implements OauthLogoutService {

    private final ConsumerTokenServices consumerTokenServices;

    private final TokenStore tokenStore;

    private final KickOutTokenStoreService kickOutTokenStoreService;
    @Override
    public void revokeToken(String tokenId) {
        //登出
        consumerTokenServices.revokeToken(tokenId);
    }

    @Override
    public void kickOut(String lessCode, String userName, KickOutEnum kickOutEnum) {
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName("client_hy_web", StringUtils.isNotBlank(lessCode)?lessCode+"_"+userName:userName);
        for (OAuth2AccessToken oAuth2AccessToken : accessTokens) {
            if (oAuth2AccessToken.isExpired()) {
                continue;
            }
            log.info("kick out userAccountName:【{}】, token:【{}】,踢人者的ip:{}", userName,oAuth2AccessToken.getValue(), IpUtils.getIpAddress());
            revokeToken(oAuth2AccessToken.getValue());
            // 记录踢出
            kickOutTokenStoreService.putKickOutToken(oAuth2AccessToken.getValue(),kickOutEnum);
        }
    }

}

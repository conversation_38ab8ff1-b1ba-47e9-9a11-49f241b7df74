package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 密码强制更新策略,有应用级别和用户级别的2种模式
 *
 * <AUTHOR>
 * @date 2022-06-13 16:33
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthPwdUpdatePolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -555347941404361347L;

    /**
     * 字段映射用
     */
    private String pwdLastChangeTimeFiled;

    private PasswordUpdateMode pwdUpdateMode;

}

package cn.hy.auth.custom.common.context;

import cn.hy.auth.custom.common.domain.LoginStateDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.dataengine.context.DataEngineContextProxy;
import cn.hy.metadata.engine.common.MetaDataEngineContext;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 认证中心业务服务上下文
 *
 * <AUTHOR>
 * @date 2020-11-23 13:36
 **/
@Slf4j
@Accessors(chain = true)
public class AuthContext {
    private static final ThreadLocal<AuthContext> LOCAL = ThreadLocal.withInitial(AuthContext::new);

    /**
     * 获取当前上下文
     *
     * @return 当前上下文
     */
    public static AuthContext getContext() {
        return LOCAL.get();
    }

    /**
     * 租户编码
     */
    @Getter
    private String lesseeCode;

    /**
     * 应用编码
     */
    @Getter
    private String appCode;

    /**
     * 认证规则信息
     */
    @Setter
    private AppAuthStrategyDTO appAuthStrategy;
    /**
     * 登录状态信息
     */
    @Setter
    private LoginStateDTO loginState;


    /**
     * 自由属性
     */
    private final Map<Object, Object> map = new HashMap<>();

    public AuthContext setLesseeCode(String lesseeCode) {
        this.lesseeCode = lesseeCode;
        MetaDataEngineContext.getContext().setLesseeCode(lesseeCode);
        DataEngineContextProxy.getContext().setLesseeCode(lesseeCode);
        return this;
    }

    public AuthContext setAppCode(String appCode) {
        this.appCode = appCode;
        MetaDataEngineContext.getContext().setAppCode(appCode);
        DataEngineContextProxy.getContext().setAppCode(appCode);
        return this;
    }

    /**
     * 认证规则信息
     *
     * @return 认证规则信息
     */
    public AppAuthStrategyDTO appAuthStrategy() {
        if (this.appAuthStrategy == null) {
            this.appAuthStrategy = new AppAuthStrategyDTO();
        }
        return this.appAuthStrategy;
    }

    /**
     * 登录状态信息
     *
     * @return 登录状态信息
     */
    public LoginStateDTO loginState() {
        if (this.loginState == null) {
            this.loginState = new LoginStateDTO();
        }
        return loginState;
    }

    /**
     * 添加属性到上下文中
     *
     * @param key   属性键名
     * @param value 属性键值
     * @return AuthContext，链式调用支持
     */
    public AuthContext set(Object key, Object value) {
        map.put(key, value);
        return this;
    }

    public void reset() {
        setLesseeCode(StringUtils.EMPTY);
        setAppCode(StringUtils.EMPTY);
        appAuthStrategy = null;
        loginState = null;
        map.clear();
    }

    /**
     * 获取指定属性
     *
     * @param key 属性键名
     * @return 属性键值空值安全类
     */
    public <T> Optional<T> get(Object key) {
        return Optional.ofNullable((T) (map.get(key)));
    }
}

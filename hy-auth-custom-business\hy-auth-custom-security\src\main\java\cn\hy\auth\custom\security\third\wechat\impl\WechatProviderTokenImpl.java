package cn.hy.auth.custom.security.third.wechat.impl;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.service.HyOauth2Operations;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.third.wechat.domain.WechatAccessToken;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * 企业微信用户token获取
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class WechatProviderTokenImpl extends AbstractOAuth2ApiBinding implements HyOauth2Operations {

    @Value("${third.proxy.qywechat.url:http://10.0.3.32:9000/wxproxy/qyapi}")
    private String baseUrl;

    /**
     * 响应码
     */
    private static final String ERROR_CODE_PARAMS = "errcode";

    @Override
    public WechatAccessToken getProviderAppAccessToken(String lesseeCode, String appCode, ProviderInfo providerInfo, Map<String, Object> requestParamMap) {
        String appKey= providerInfo.getAppKey();
        String appSecret= providerInfo.getAppSecret();
        String url = baseUrl+providerInfo.getAppAccessTokenUrl() + "?corpid="+appKey+"&corpsecret="+appSecret;
        String response = getRestTemplate().getForObject(url, String.class);
        WechatAccessToken token=new WechatAccessToken();
        if (response != null) {
            JSONObject resObject = JSONObject.parseObject(response);
            if (resObject.getInteger(ERROR_CODE_PARAMS) == 0) {
                String access_token = resObject.getString("access_token");
                Integer expires_in = resObject.getInteger("expires_in");
                String errmsg = resObject.getString("errmsg");
                Calendar nowTime = Calendar.getInstance();
                nowTime.add(Calendar.SECOND, expires_in);
                Date expiresTime=nowTime.getTime();
                token.setAccessToken(access_token);
                token.setErrCode(resObject.getInteger(ERROR_CODE_PARAMS));
                token.setExpiresTime(expiresTime);
                token.setErrorMsg(errmsg);
                return token;
            }
        }
        log.error("查询第三方应用token失败,{}",response);
        throw new AuthBusinessException("500", LocaleUtil.getMessage("WechatProviderTokenImpl.result.msg1", null)+response);
    }
}

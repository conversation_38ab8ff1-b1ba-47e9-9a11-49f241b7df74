package cn.hy.auth.custom.user.account.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;


/**
 * 类描述:第三方用户账号
 *
 * <AUTHOR>
 * @date ：创建于 2022/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "第三方用户账号")
public class ThirdUserBindDTO {

    /**
     * 第三方用户ID
     */
    @ApiModelProperty(value = "第三方用户表的Id")
    private String providerId;

    /**
     * 第三方用户ID
     */
    @ApiModelProperty(value = "第三方用户ID")
    private String thirdUserId;

    /**
     * 第三方用户ID
     */
    @ApiModelProperty(value = "第三方用户ID")
    private String providerUserId;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    @NotBlank(message = "用户账号不允许为空")
    private String userAccountName;

}

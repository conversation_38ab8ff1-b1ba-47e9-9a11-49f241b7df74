package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.common.security.core.authentication.token.ReAuthenticationToken;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: zqb
 * @Description: 所有验证通过后成功返回
 * @CreateDate: 2019/8/13 20:09
 */
@Slf4j
@Component
public class LastAuthenticationProviderer extends AbstractAuthenticationProvider {

    @Autowired
    UserDetailsService userDetailsService;

    @Override
    public Authentication doAuthenticate(Authentication authentication) {
        // 二次认证在 ReAuthenticationProvider 中处理
        if (authentication instanceof ReAuthenticationToken) {
            return null;
        }
        Map<String, Object> params = (Map<String, Object>) authentication.getDetails();
        String encryptType = MapUtils.getString(params, LoginParamterConts.PWD_ENCRYPTION_TYPE, StringUtils.EMPTY);
        if (StringUtils.isBlank(encryptType) || EncryptTypeEnum.NOT_ENCRYPTION.getCode().toString().equals(encryptType)) {
            return null;
        }
        String username = (String)authentication.getPrincipal();
        log.debug("登录所有验证成功，登录名：{}", username);
        UserDetails userDetails = getUserDetail(username);
        return new UsernamePasswordAuthenticationToken(userDetails, authentication.getCredentials(), authentication.getAuthorities());
    }
    private HyUserDetails getUserDetail(String userName){
        return (HyUserDetails) userDetailsService.loadUserByUsername(userName);
    }
    @Override
    public int order() {
        //最后执行的provider
        return 99;
    }
}

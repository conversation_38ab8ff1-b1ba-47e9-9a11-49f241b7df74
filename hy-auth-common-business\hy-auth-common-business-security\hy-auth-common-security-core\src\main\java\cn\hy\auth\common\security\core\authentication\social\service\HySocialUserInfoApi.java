package cn.hy.auth.common.security.core.authentication.social.service;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;

import java.util.Map;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/9 16:21
 **/
public interface HySocialUserInfoApi {
    /**
     *  获取第三方服务提供商的用户信息
     * @param lesseeCode 租户号
     * @param appCode 应用号
     * @param code .前端传入的auth code
     * @param providerAppAccessToken 在第三方平台创建应用的access_token
     * @param providerInfo 第三方平台服务商信息
     * @param requestParamMap 请求入参的参数
     * @return .
     */
    ProviderUserInfo getProviderUserInfo(String lesseeCode, String appCode,
                                         String code,ProviderAppAccessToken providerAppAccessToken,
                                         ProviderInfo providerInfo, Map<String,Object> requestParamMap);

    /**
     *  获取第三方服务提供商的用户信息, aouth2 的code授权模式
     * @param code .前端传入的auth code
     * @return .
     */
    ProviderUserInfo getProviderUserInfo(String code, ProviderAppAccessToken appAccessToken,ProviderInfo providerInfo);
}

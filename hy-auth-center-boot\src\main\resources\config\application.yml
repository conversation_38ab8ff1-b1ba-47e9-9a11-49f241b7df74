#数据库服务ip地址，例如：**************
db_url_ip: ${MYSQL_HOST:dev-mysql-004037.hydevops.com}
#数据库服务端口号，例如：3306
db_port: ${MYSQL_PORT:3306}
#数据库db名称，例如：hyap
db_url_name: ${DATABASE_NAME:hyap}
#数据库账号，例如：iotmp
db_user: ${DATABASE_USER:iotmp}
#数据库密码
db_pwd: ${DATABASE_PWD:Hy@21nbHh}
#2.x认证中心服务完整地址，例如：http://*********:8900
x_2_auth_center: ${X2_AUTH_CENTER:*******}
#IAM认证中心服务完整地址，例如：http://*********:8900
IAM_auth_center: ${IAM_AUTH_CENTER:http://*************:86}

server:
  port: ${PORT:6060}

# mybatis
mybatis:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
spring:
  profiles:
    active: dev
  # jackson
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8h
    serialization:
      write-dates-as-timestamps: true
    # 驼峰格式
    property-naming-strategy: LOWER_CAMEL_CASE
  # --- DataSource ---
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${db_url_ip}:${db_port}/${db_url_name}?serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&useSSL=false&rewriteBatchedStatements=true
    username: ${db_user}
    # spring 在内存查看时，会自动隐藏密码
    password: ${db_pwd}
  #-----启用默认的banner------
  banner:
    location: classpath:config/banner.txt
  #redis配置
  redis:
    database: 10
    host: 127.0.0.1
    port: 6379
    password: Hy123456
    #因为需要使用健康检测,但是redis又是可选的，顾排除掉redis的健康检测,如果使用redis可以放开（屏蔽）
  autoconfigure:
    exclude:
      - org.springframework.boot.actuate.autoconfigure.redis.RedisHealthIndicatorAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.redis.RedisReactiveHealthIndicatorAutoConfiguration
  #应用国际化
  messages:
    basename: i18n/messages
    falback-to-system-locale: false
    country: ${TRANSLATE_COUNTRY:zh}
    variant: ${TRANSLATE_VARIANT:CN}
#--consul discovery---
endpoints:
  health:
    sensitive: false
  restart:
    enabled: true
  shutdown:
    enabled: true
#---actuator 配置 url 为：/actuator-----
management:
  health:
    redis:
      enabled: false
  security:
    enabled: false
  endpoints:
    shutdown:
      enabled: false
    web:
      exposure:
        include: health,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
#----logging-----
logging:
  level:
    root: info
    cn.hy.dataengine: info
    cn.hy.auth: debug
#---snow id--
id:
  generator:
    data-zone-id: 1
    worker-id: 7

#不需要过滤的表规则,值多个的时候使用逗号分隔，允许正则匹配
metadata:
  redirect:
    exclude: oauth_request_routing
    superTableGeneralization: true
    whiteTableCode: oauth_request_routing
hy:
  security:
    oauth2:
      route:
        enableThird: 2.X
        filterPattern:
          - /user/current
          - /**/user/checkUserAccount
          - /history/login/last
          - /oauth/check_token
          - /user/currentLoginUser
          - /user/bingThirdAccount
          - /user/birdThirdMember
          - /user/removeThirdBing
          - /user/checkUserAccount
          - /oauth/revoke
        serverHosts:
          2.X: ${x_2_auth_center}
          IAM_CODE: ${IAM_auth_center}
          IAM_TOKEN: ${IAM_auth_center}
        hostConnection:
          maxTotalConnections: 500
          maxPerRouteConnections: 200
          connectionRequestTimeoutMillis: 60000
      token:
        # 是否重复使用RefreshToken
        reuseRefreshToken: false
        # 是否启用内存模式的client，false则使用jdbc模式
      clientInMemory: false
      clients[0]:
        clientId: client_hy_web
        clientSecret: 3LfsJMVt/chTqNgo85Gl/w==
        accessTokenValiditySeconds: 3600
    web:
      ignore:
        pattern:
          - /code/sms
          - /**/user/checkUserAccount
          - /dingTalk/**
          - /**/actuator/**
          - /route/cache/clear
          - /log/cache/clear
          - /license/*
          - /verifyCode/sliderCaptcha/**
          - /verifyCode/**
          - /meta/data/cache/clear
          - /**/user/checkUserAccount
    #请求信息分类，用于按不同方式提取租户、应用编码
    request-type:
      authenticate:
        - /**/user/current
        - /**/user/account/current
        - /**/user/association/current
        - /**/free/account/online/list
        - /**/user/pwd
        - /**/history/login/last
        - /**/user/currentLoginUser
        - /**/user/birdThirdMember
        - /**/user/bingThirdAccount
        - /**/user/removeThirdBing
      non-business:
        - /**/actuator/**
        - /route/cache/clear
        - /log/cache/clear
        - /license/*
        - /verifyCode/sliderCaptcha/**
        - /meta/data/cache/clear
        - /**/user/checkUserAccount
    verifyCode:
      maxFailedCount: 1
      # 验证码设置容错，越大代表容错率越高
      tolerant: 0.03f
  license:
    excludesPattern: /**/actuator/**,/license/*
    filterOrder: -********
  # 库表mybatis拦截占位符开关，true代表开，false代表关，目前只有saas需要打开
  dataEngine:
    switch:
      placeholder: false
auth:
  cache:
    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)
    type: caffeine
    #过期时间(s)
    expire_time: 6000
  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)
  token:
    store:
      type: jvm

# 滑块验证码配置， 详细请看 cloud.tianai.captcha.autoconfiguration.ImageCaptchaProperties 类
captcha:
  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider
  prefix: captcha
  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整
  expire:
    # 默认缓存时间 2分钟
    default: 60000
    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些
    WORD_IMAGE_CLICK: 20000
  # 使用加载系统自带的资源， 默认是 false
  init-default-resource: true
  cache:
    # 缓存控制， 默认为false不开启
    enabled: true
    # 验证码会提前缓存一些生成好的验证数据， 默认是20
    cacheSize: 20
    # 缓存拉取失败后等待时间 默认是 5秒钟
    wait-time: 1000
    # 缓存检查间隔 默认是2秒钟
    period: 5000
    secondary:
      # 二次验证， 默认false 不开启
      enabled: false

#第三方代理配置
third:
  proxy:
    dingtalk:
      # 外网：https://oapi.dingtalk.com
      url: http://*********:9000/ddproxy/oapi
    qywechat:
      # 外网：https://qyapi.weixin.qq.com
      url: http://*********:9000/wxproxy/qyapi

dubbo:
  application:
    id: HY-AUTH-DUBBO-PROVIDER
    name: HY-AUTH-DUBBO-PROVIDER
  registry:
    #挂载到CONSUL注册
    address: consul://${CONSUL_HOST:127.0.0.1}:${CONSUL_PORT:8500}
    timout: 10000
    parameters:
       token: ${ACL_TOKEN:}
  protocol:
    # -1 表示从从 20880 开始,有冲突递增，其他服务不需要关心所以-1即可
    port: -1
    name: dubbo
  consumer:
    ## 不检查provider是否在注册中心有，服务没有的话消费者无法启动。
    check: false
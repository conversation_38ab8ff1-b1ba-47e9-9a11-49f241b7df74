package cn.hy.auth.custom.user.account.listener;

import cn.hutool.core.util.StrUtil;
import cn.hy.auth.common.security.core.authentication.mobile.event.CheckUserAccountLockEvent;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Map;

@AllArgsConstructor
@Component
public class CheckUserAccountLockEventListener implements ApplicationListener<CheckUserAccountLockEvent> {

    @Autowired
    private UserAccountLockService userAccountLockService;

    @Override
    public void onApplicationEvent(CheckUserAccountLockEvent logEvent) {
        // 查询是否有锁定记录
        if (StrUtil.isNotBlank(logEvent.getMobile())) {
            Map<String, Object> resultMap = userAccountLockService.isUserAccountLockByUserNameOrMobile(logEvent.getMobile());
            logEvent.setLock((Boolean) resultMap.get("judge"));
            logEvent.setUserExist((Boolean) resultMap.get("userExist"));
            return;
        }
        Map<String, Object> resultMap = userAccountLockService.isUserAccountLockByEmail(logEvent.getEmail());
        logEvent.setLock((Boolean) resultMap.get("judge"));
        logEvent.setUserExist((Boolean) resultMap.get("userExist"));
    }
}

package cn.hy.auth.common.security.oauth2.event;

/**
 * 登录类型常量
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 13:39
 */
public enum LoginSuccessEventType {
    /**
     * springSecurity的登录方式（表单账号密码，手机验证码，社交登录）
     */
    SPRING_SECURITY_LOGIN("spring_security_login"),
    /**
     * oauth2登录方式
     */
    OAUTH2_LOGIN("oauth2_login");

    /**
     * @param type 登录类型
     */
    LoginSuccessEventType(String type) {
        this.type = type;
    }

    /**
     * 登录类型
     */
    private String type;

    public String getType() {
        return type;
    }
}
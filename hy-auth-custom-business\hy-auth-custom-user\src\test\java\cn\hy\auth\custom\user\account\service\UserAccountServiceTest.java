package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.user.account.domain.UserPwdUpdateDTO;
import cn.hy.auth.custom.user.account.mapper.DynamicUserAccountMapper;
import cn.hy.auth.custom.user.account.service.impl.UserAccountServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UserAccountServiceImpl.class})
public class UserAccountServiceTest {

    @Autowired
    private UserAccountService userAccountService;

    @MockBean
    private DynamicUserAccountMapper userAccountMapper;

    @MockBean
    private OauthLogoutService authBaseService;


    @Test
    public void getUserByUserName() {
        UserAccountDTO accountDTO = new UserAccountDTO();
        accountDTO.setId(1L);
        when(userAccountMapper.selectByUserName(any())).thenReturn(accountDTO);
        when(userAccountMapper.selectById(any())).thenReturn(accountDTO);
        String userName = "admin";
        userAccountService.getUserByUserName(userName);
    }

    @Test
    public void getUserByUserId() {
        UserAccountDTO accountDTO = new UserAccountDTO();
        accountDTO.setId(1L);
        when(userAccountMapper.selectById(1L)).thenReturn(accountDTO);
        when(userAccountMapper.getUserAllInfoById(1L)).thenReturn(new HashMap<>());
        userAccountService.getUserAllInfoByUserId(1L);
    }

    @Test
    public void getUserIdByUserName() {
        UserAccountDTO accountDTO = new UserAccountDTO();
        accountDTO.setId(1L);
        when(userAccountMapper.selectByUserName(any())).thenReturn(accountDTO);
        String userName = "admin";
        //userAccountService.getUserIdByUserName(userName);
    }

    @Test
    public void updateUserPassword() {
        String tokenId = "123456";
        UserPwdUpdateDTO userPwdUpdateDTO = UserPwdUpdateDTO.builder()
                .userName("admin")
                .newPwd(AesUtil.encryptByDefaultKey("123456"))
                .oldPwd(AesUtil.encryptByDefaultKey("234567"))
                .pwdEncryptionType(EncryptTypeEnum.PASSWORD_ENCRYPTION.getCode())
                .build();
        UserAccountDTO accountDTO = new UserAccountDTO();
        accountDTO.setId(1L);
        accountDTO.setPassword(userPwdUpdateDTO.getOldPwd());
        when(userAccountMapper.selectByUserName(userPwdUpdateDTO.getUserName())).thenReturn(accountDTO);

        userAccountService.updateUserPassword(tokenId, userPwdUpdateDTO);
        verify(userAccountMapper, times(1)).updatePassword(anyLong(), any());
        verify(authBaseService, times(1)).revokeToken(tokenId);
    }
}
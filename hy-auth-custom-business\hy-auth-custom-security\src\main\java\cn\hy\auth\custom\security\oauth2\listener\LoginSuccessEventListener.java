package cn.hy.auth.custom.security.oauth2.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.validate.event.ClearErrorNumberEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEvent;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.LoginOutTypeEnum;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.security.oauth2.event.LoginSuccessLogEvent;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

import static cn.hy.auth.custom.common.constant.LoginParamterConts.*;

/**
 * 类描述: 登录成功接收事件
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@Slf4j
@Component
@AllArgsConstructor
public class LoginSuccessEventListener implements ApplicationListener<LoginSuccessEvent> {

    private final UserCacheService userCacheService;
    private final ApplicationContext applicationContext;

    @Override
    public void onApplicationEvent(LoginSuccessEvent event) {
        log.debug("登录验证成功 Principal {}", event.getAuthentication().getPrincipal());
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        String mobile = Objects.toString(paramMap.get("mobile"), "");
        String username = Objects.toString(paramMap.get("username"), "");
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();
        applicationContext.publishEvent(new ClearErrorNumberEvent("", mobile, username, paramMap.get("mobileCipherText"), lesseeCode, appCode));

        logProcess(event);
    }

    private void logProcess(LoginSuccessEvent event) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        String ip = paramMap.get("ip");
        if (StringUtils.isBlank(ip)) {
            ip = IpUtils.getIpAddress();
        }
        //记录登出日志
        UserLoginInfoDTO loginLogDTO = UserLoginInfoDTO.builder().ip(ip).currentIp(ip).type(LoginOutTypeEnum.LOGIN_SUCCESS.getCode()).build();
        Authentication authentication = event.getAuthentication();
        loginLogDTO.setLoginType(AuthContext.getContext().loginState().getLoginTypeString());
        String userAccountName = authentication.getName();
        UserAccountDTO userAccountDTO = userCacheService.getUserAccountByLoginName(userAccountName);
        if (ObjectUtil.isNotNull(userAccountDTO)) {
            loginLogDTO.setUserId(userAccountDTO.getId());
            loginLogDTO.setCreateUserId(userAccountDTO.getId());
            loginLogDTO.setLastUpdateUserId(userAccountDTO.getId());
            loginLogDTO.setUserAccountName(userAccountDTO.getUserAccountName());
        } else {
            // client 客户端登录模式
            loginLogDTO.setUserId(0L);
            loginLogDTO.setCreateUserId(0L);
            loginLogDTO.setLastUpdateUserId(0L);
            loginLogDTO.setUserAccountName("");
        }

        if (authentication.getDetails() instanceof Map) {
            Map<String, Object> detail = (Map<String, Object>) authentication.getDetails();
            if (MapUtils.isNotEmpty(detail)) {
                loginLogDTO.setMac(MapUtils.getString(detail, LOGIN_MAC));
                loginLogDTO.setClientType(MapUtils.getInteger(detail, CLIENT_TYPE));
                loginLogDTO.setClientId(MapUtils.getString(detail, CLIENT_ID));
            }
        }

        //发出登出需要记录日志的事件
        applicationContext.publishEvent(new LoginSuccessLogEvent(loginLogDTO));
    }
}

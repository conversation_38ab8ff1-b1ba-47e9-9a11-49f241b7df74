package cn.hy.auth.custom.security.oauth2.token;

import cn.hy.auth.common.security.core.authentication.copytoken.CopyTokenAuthenticationToken;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.security.oauth2.client.HyClientDetailServiceProvider;
import cn.hy.auth.custom.security.oauth2.environment.service.OauthSysMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.*;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：token生成的增强器
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/10/10 11:59
 **/
@Component
@Slf4j
public class HyTokenEnhancer implements TokenEnhancer {
    private static final String SEPERATE_DOT = ".";

    /**
     * 可以token携带环境标识的应用标识（租户编码$$应用编码,租户编码$$应用编码,....）
     */
    private final String appKeysWithEnvironmentSymbol;
    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;

    private final OauthSysMService oauthSysMService;

    public HyTokenEnhancer(HyClientDetailServiceProvider hyClientDetailServiceProvider,
                           @Value("${hy.security.oauth2.token.withEnvironmentSymbol.appCodes:}") String appKeysWithEnvironmentSymbol, OauthSysMService oauthSysMService) {
        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;
        this.appKeysWithEnvironmentSymbol = appKeysWithEnvironmentSymbol;
        this.oauthSysMService = oauthSysMService;
    }

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        //  修改access_token和refresh_token的值，格式:租户访问号.应用访问号.clientType.32位UUID$环境标识
        if (accessToken instanceof DefaultOAuth2AccessToken) {
            DefaultOAuth2AccessToken defaultAccessToken = (DefaultOAuth2AccessToken) accessToken;
            addUserInfoToToken(authentication, defaultAccessToken);
            // 添加环境标识到token后
            String environmentSymbolSuffix = getEnvironmentSymbolSuffixIfNeed(authentication);
            if (!StringUtils.isEmpty(environmentSymbolSuffix)){
                remakeAccessTokenWithSuffix(defaultAccessToken, environmentSymbolSuffix);
                remakeRefreshTokenWithSuffix(defaultAccessToken, environmentSymbolSuffix);
            }
            String prefix = getTokenPrefix(authentication);
            String copyTokenSuffix = getTokenCopySuffix(authentication);
            remakeAccessToken(defaultAccessToken, prefix, copyTokenSuffix);
            remakeRefreshToken(defaultAccessToken, prefix, copyTokenSuffix);
        }
        return accessToken;
    }

    private String getTokenCopySuffix(OAuth2Authentication authentication) {
        if (authentication.getUserAuthentication() instanceof CopyTokenAuthenticationToken) {
            return "-copy";
        }
        return null;
    }

    private void remakeAccessToken(DefaultOAuth2AccessToken defaultAccessToken, String prefix, String copyTokenSuffix) {
        if (StringUtils.hasText(prefix)) {
            if (!defaultAccessToken.getValue().contains(SEPERATE_DOT)) {
                defaultAccessToken.setValue(prefix + defaultAccessToken.getValue());
            }
        }
        if (StringUtils.hasText(copyTokenSuffix)) {
            defaultAccessToken.setValue(defaultAccessToken.getValue() + copyTokenSuffix);
        }
    }

    private void remakeRefreshToken(DefaultOAuth2AccessToken defaultAccessToken, String prefix, String copyTokenSuffix) {
        OAuth2RefreshToken defaultRefreshToken = defaultAccessToken.getRefreshToken();
        if (defaultRefreshToken instanceof DefaultExpiringOAuth2RefreshToken) {

            if (StringUtils.hasText(prefix)) {
                DefaultExpiringOAuth2RefreshToken refreshToken = (DefaultExpiringOAuth2RefreshToken) defaultAccessToken.getRefreshToken();
                if (!refreshToken.getValue().contains(SEPERATE_DOT)) {
                    DefaultExpiringOAuth2RefreshToken newRefreshToken = new DefaultExpiringOAuth2RefreshToken(prefix + refreshToken.getValue(), refreshToken.getExpiration());
                    defaultAccessToken.setRefreshToken(newRefreshToken);
                }
            }
            if (StringUtils.hasText(copyTokenSuffix)) {
                DefaultExpiringOAuth2RefreshToken refreshToken = (DefaultExpiringOAuth2RefreshToken) defaultAccessToken.getRefreshToken();
                DefaultExpiringOAuth2RefreshToken newRefreshToken = new DefaultExpiringOAuth2RefreshToken(refreshToken.getValue() + copyTokenSuffix, refreshToken.getExpiration());
                defaultAccessToken.setRefreshToken(newRefreshToken);
            }
        } else if (defaultRefreshToken instanceof DefaultOAuth2RefreshToken) {
            if (StringUtils.hasText(prefix)) {
                DefaultOAuth2RefreshToken refreshToken = (DefaultOAuth2RefreshToken) defaultAccessToken.getRefreshToken();
                if (!refreshToken.getValue().contains(SEPERATE_DOT)) {
                    DefaultOAuth2RefreshToken newRefreshToken = new DefaultOAuth2RefreshToken(prefix + refreshToken.getValue());
                    defaultAccessToken.setRefreshToken(newRefreshToken);
                }
            }
            if (StringUtils.hasText(copyTokenSuffix)) {
                DefaultOAuth2RefreshToken newRefreshToken = new DefaultOAuth2RefreshToken(defaultAccessToken.getRefreshToken().getValue() + copyTokenSuffix);
                defaultAccessToken.setRefreshToken(newRefreshToken);
            }
        }
    }

    private void remakeAccessTokenWithSuffix(DefaultOAuth2AccessToken defaultAccessToken, String suffix) {
        if (StringUtils.isEmpty(defaultAccessToken.getValue()) || StringUtils.isEmpty(suffix)){
            return;
        }
        defaultAccessToken.setValue(defaultAccessToken.getValue() + suffix);
    }

    private void remakeRefreshTokenWithSuffix(DefaultOAuth2AccessToken defaultAccessToken, String suffix) {
        if (StringUtils.isEmpty(suffix)){
            return;
        }
        OAuth2RefreshToken defaultRefreshToken = defaultAccessToken.getRefreshToken();
        if (defaultRefreshToken instanceof DefaultExpiringOAuth2RefreshToken) {
            DefaultExpiringOAuth2RefreshToken refreshToken = (DefaultExpiringOAuth2RefreshToken) defaultAccessToken.getRefreshToken();
            if (!StringUtils.isEmpty(refreshToken.getValue())) {
                DefaultExpiringOAuth2RefreshToken newRefreshToken = new DefaultExpiringOAuth2RefreshToken(refreshToken.getValue() + suffix, refreshToken.getExpiration());
                defaultAccessToken.setRefreshToken(newRefreshToken);
            }
        } else if (defaultRefreshToken instanceof DefaultOAuth2RefreshToken) {
            DefaultOAuth2RefreshToken refreshToken = (DefaultOAuth2RefreshToken) defaultAccessToken.getRefreshToken();
            if (!StringUtils.isEmpty(refreshToken.getValue())) {
                DefaultOAuth2RefreshToken newRefreshToken = new DefaultOAuth2RefreshToken(refreshToken.getValue() + suffix);
                defaultAccessToken.setRefreshToken(newRefreshToken);
            }
        }
    }

    private void addUserInfoToToken(OAuth2Authentication authentication, DefaultOAuth2AccessToken defaultAccessToken) {
        Authentication userAuthentication = authentication.getUserAuthentication();
        if (userAuthentication != null) {
            // 存入用户名称
            HyUserDetails details = (HyUserDetails) userAuthentication.getPrincipal();
            Map<String, Object> map = new HashMap<>(4);
            map.put("username", details.getUsername());
            HashMap<String, Object> additionalInformation = new HashMap<>(4);
            additionalInformation.put("user_info", map);
            defaultAccessToken.setAdditionalInformation(additionalInformation);
        }
        Map<String, String> requestParameters = null;
        if (authentication.getDetails() instanceof Map) {
            requestParameters = (Map<String, String>) authentication.getDetails();
        } else if (authentication.getOAuth2Request() != null) {
            requestParameters = authentication.getOAuth2Request().getRequestParameters();
        }
        if (requestParameters == null || requestParameters.isEmpty()) {
            return ;
        }
         String grantType = requestParameters.get(LoginParamterConts.GRANT_TYPE);
        if ("client_credentials".equals(grantType)){
            ClientDetails clientDetails = hyClientDetailServiceProvider.getClientDetailsService().loadClientByClientId(authentication.getOAuth2Request().getClientId());
            if (clientDetails != null){
                Map<String, Object> additionalInformation = clientDetails.getAdditionalInformation();
                defaultAccessToken.setAdditionalInformation(additionalInformation);
                authentication.setDetails(additionalInformation);
            }
        }
    }

    private String getTokenPrefix(OAuth2Authentication authentication) {
        Map<String, String> requestParameters = getRequestParameters(authentication);
        if (requestParameters == null || requestParameters.isEmpty()) {
            return null;
        }
        String clientType = requestParameters.get(LoginParamterConts.CLIENT_TYPE);
        String grantType = requestParameters.get(LoginParamterConts.GRANT_TYPE);
        if ("client_credentials".equals(grantType) && org.apache.commons.lang3.StringUtils.isBlank(clientType)) {
            clientType = "defaultClient";
        }
        String lesseeCode = requestParameters.get(LoginParamterConts.LESSEE_CODE);
        String appCode = requestParameters.get(LoginParamterConts.APP_CODE);
        String prefixFormat = "%s" + SEPERATE_DOT + "%s" + SEPERATE_DOT;
        String prefix = String.format(prefixFormat, lesseeCode, appCode);
        if (!StringUtils.isEmpty(clientType)) {
            prefix += clientType + SEPERATE_DOT;
        }
        return prefix;
    }

    private Map<String, String> getRequestParameters(OAuth2Authentication authentication) {
        Authentication userAuthentication = authentication.getUserAuthentication();
        Map<String, String> requestParameters = null;
        if (userAuthentication != null && (userAuthentication.getDetails() instanceof Map)) {
            requestParameters = (Map<String, String>) userAuthentication.getDetails();
        } else if (authentication.getOAuth2Request() != null) {
            requestParameters = authentication.getOAuth2Request().getRequestParameters();
        }
        return requestParameters;
    }

    private String getEnvironmentSymbolSuffixIfNeed(OAuth2Authentication authentication) {
        if (StringUtils.isEmpty(appKeysWithEnvironmentSymbol)){
            if (log.isDebugEnabled()){
                log.debug("没有应用的token需要携带环境标识，不添加环境标识后缀");
            }
            return null;
        }

        // 获取应用编码和租户编码
        Map<String, String> requestParameters = getRequestParameters(authentication);
        if (requestParameters == null || requestParameters.isEmpty()) {
            return null;
        }
        String lesseeCode = requestParameters.get(LoginParamterConts.LESSEE_CODE);
        String appCode = requestParameters.get(LoginParamterConts.APP_CODE);
        if (StringUtils.isEmpty(lesseeCode) || StringUtils.isEmpty(appCode)){
            if (log.isDebugEnabled()){
                log.debug("没有对应的租户编码和应用编码，不添加环境标识后缀");
            }
            return null;
        }

        // 判断该应用是否有配置需要环境标识
        if (!Arrays.asList(appKeysWithEnvironmentSymbol.split(",")).contains(lesseeCode + "$$" + appCode)){
            if (log.isDebugEnabled()){
                log.debug("环境标识配置中没有指定该应用需要添加环境标识，故不需要添加环境标识，应用所在租户编码：{}， 应用编码：{}", lesseeCode, appCode);
            }
            return null;
        }

        // 获取环境标识
        String environmentSymbol = oauthSysMService.getSystemEnvironmentSymbol();
        if (StringUtils.isEmpty(environmentSymbol)){
            return null;
        }
        return "$" + environmentSymbol;
    }
}

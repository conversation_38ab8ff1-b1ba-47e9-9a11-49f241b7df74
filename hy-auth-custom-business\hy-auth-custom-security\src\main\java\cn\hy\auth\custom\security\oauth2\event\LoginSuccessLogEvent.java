package cn.hy.auth.custom.security.oauth2.event;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import lombok.Getter;

/**
 * 登录成功日志事件
 *
 * <AUTHOR>
 */
@Getter
public class LoginSuccessLogEvent extends BaseAsyncEvent {

    private static final long serialVersionUID = 3740695100361892503L;

    /**
     * 创建登出日志事件
     *
     * @param loginInfoDTO
     */
    public LoginSuccessLogEvent(UserLoginInfoDTO loginInfoDTO) {
        super(loginInfoDTO);
    }

    public UserLoginInfoDTO getLoginInfo() {
        if (getSource() == null) {
            return null;
        }

        return (UserLoginInfoDTO) getSource();
    }
}

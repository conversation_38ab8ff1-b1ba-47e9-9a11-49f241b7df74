package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.custom.common.domain.HyUserDetails;

/**
 * 类描述：密码状态
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/8/23 13:46
 **/
public interface PwdStatusService {

    /**
     *
     * "pwdStatusPolicy":
     * {
     * 	"enable": false,
     * 	"pwdStatus": "pwd_status",
     * 	"initPwdAction": "初始密码处理方式，mustBeModified：强制修改,不能登录成功，onlyRemind：仅提醒（无，不实现）",
     * 	"remark": "指定用户账号表的某个字段，pwd_status,1重置初始状态, 其他值为正常状态。默认是1。"
     *  }
     * 是否处理初始密码
     * @return 。
     */
    boolean handleInitPwd( HyUserDetails account);

}

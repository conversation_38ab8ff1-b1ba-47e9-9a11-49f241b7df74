package cn.hy.auth.custom.security.oauth2.provider;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.common.security.oauth2.token.DefaultTokenServicesWrapper;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.user.account.service.PwdExpirationAndForceModifyService;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;

/**
 * 类描述：密码相关的策略基类
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/8/23 12:14
 **/
public abstract class AbstractPwdPolicyProvider extends AbstractAuthenticationProvider {

    private final UserDetailsService userDetailsService;

    private final ClientDetailsService clientDetailsService;
    private final PasswordEncoder passwordEncoder;

    private final AuthorizationServerTokenServices authorizationServerTokenServices;

    protected AbstractPwdPolicyProvider(PwdExpirationAndForceModifyService pwdExpirationAndForceModifyService,
                                        UserDetailsService userDetailsService,
                                        TokenStore tokenStore,
                                        TokenServicesProperties tokenServicesProperties,
                                        ApplicationContext applicationContext,
                                        ClientDetailsService clientDetailsService,
                                        PasswordEncoder passwordEncoder) {
        this.userDetailsService = userDetailsService;
        this.clientDetailsService = clientDetailsService;
        this.passwordEncoder = passwordEncoder;
        this.authorizationServerTokenServices = createTokenService(tokenStore, tokenServicesProperties, applicationContext, clientDetailsService);
    }

    protected String getAccessToken(Authentication authentication) {
        Map<String, Object> params = (Map<String, Object>) authentication.getDetails();
        /*
        String encryptType = MapUtils.getString(params, LoginParamterConts.PWD_ENCRYPTION_TYPE, StringUtils.EMPTY);
        if (StringUtils.isBlank(encryptType) || EncryptTypeEnum.NOT_ENCRYPTION.getCode().toString().equals(encryptType)) {
            return null;
        }
        */
        String username = (String)authentication.getPrincipal();

        UserDetails userDetails = getUserDetail(username);
        UsernamePasswordAuthenticationToken authentication2 = new UsernamePasswordAuthenticationToken(userDetails, authentication.getCredentials(), authentication.getAuthorities());
        //补充上details
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String,String> detailMap= Maps.newHashMap();
        detailMap.put("lessee_code",request.getParameter("lessee_code"));
        detailMap.put("pwd_encryption_type",request.getParameter("pwd_encryption_type"));
        detailMap.put("client_type",request.getParameter("client_type"));
        detailMap.put("client_id",request.getParameter("client_id"));
        detailMap.put("username",request.getParameter("username"));
        detailMap.put("app_code",request.getParameter("app_code"));
        authentication2.setDetails(detailMap);

        ClientDetails clientDetails = getClientDetails(request);
        OAuth2AccessToken token = getoauth2Accesstoken(authentication2, clientDetails);

        String value="";
        if (ObjectUtil.isNotNull(token)){
            return token.getValue();
        }
        return value;
    }

    protected HyUserDetails getUserDetail(String userName){
        return (HyUserDetails) userDetailsService.loadUserByUsername(userName);
    }

    /**
     * 获取Oauth2 的token
     *
     * @param authentication 认证信息
     * @param clientDetails  客户端信息
     * @return Oauth2 的token
     */
    private OAuth2AccessToken getoauth2Accesstoken(Authentication authentication, ClientDetails clientDetails) {

        TokenRequest tokenRequest = new TokenRequest(Collections.emptyMap(), clientDetails.getClientId(), clientDetails.getScope(), "custom");

        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);

        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);

        return authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
    }

    /**
     * 创建tokenService,用于生成token
     * DefaultTokenServicesWrapper 和HyAuthorizationServerConfig里的HyTokenServices 保持一致的配置
     *
     * @param tokenStore              .
     * @param tokenServicesProperties .
     * @param applicationContext      .
     * @param clientDetailsService    .
     */
    private AuthorizationServerTokenServices createTokenService(TokenStore tokenStore,
                                                                TokenServicesProperties tokenServicesProperties,
                                                                ApplicationContext applicationContext,
                                                                ClientDetailsService clientDetailsService) {
        DefaultTokenServicesWrapper tokenServices = new DefaultTokenServicesWrapper(applicationContext, tokenServicesProperties.isReuseRefreshToken());
        tokenServices.setTokenStore(tokenStore);
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setClientDetailsService(clientDetailsService);
        Map<String, TokenEnhancer> tokenEnhancerMap = applicationContext.getBeansOfType(TokenEnhancer.class);
        if (!CollectionUtils.isEmpty(tokenEnhancerMap)) {
            TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
            tokenEnhancerChain.setTokenEnhancers(new ArrayList<>(tokenEnhancerMap.values()));
            tokenServices.setTokenEnhancer(tokenEnhancerChain);
        }
        // access_token默认有效时长为12个小时,优先使用client设置的有效期
        tokenServices.setAccessTokenValiditySeconds(tokenServicesProperties.getAccessTokenValiditySeconds());
        // refresh_token默认时长为30天,优先使用client设置的有效期
        tokenServices.setRefreshTokenValiditySeconds(tokenServicesProperties.getRefreshTokenValiditySeconds());
        return tokenServices;
    }

    /**
     * 获得clientDetails
     *
     * @param request .
     * @return ClientDetails
     */
    private ClientDetails getClientDetails(HttpServletRequest request) {
        String clientId = request.getParameter("client_id");

        String clientSecret = request.getParameter("client_secret");
        if (StringUtils.isBlank(clientId)) {
            throw new BadCredentialsException("Bad client credentials");
        }
        if (clientSecret == null) {
            clientSecret = "";
        }
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        if (null == clientDetails) {
            throw new UnapprovedClientAuthenticationException(LocaleUtil.getMessage("AbstractPwdPolicyProvider.result.msg1", null) + clientId);
        } else if (!StringUtils.equals(clientDetails.getClientSecret(), passwordEncoder.encode(clientSecret))) {
            throw new UnapprovedClientAuthenticationException(LocaleUtil.getMessage("AbstractPwdPolicyProvider.result.msg2", null) + clientSecret);
        }
        return clientDetails;
    }


}

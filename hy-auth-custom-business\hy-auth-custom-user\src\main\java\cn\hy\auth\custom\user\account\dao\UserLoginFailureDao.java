package cn.hy.auth.custom.user.account.dao;

import cn.hy.auth.custom.user.account.domain.UserLoginFailure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
/**
 * 用户登录失败信息记录表
 *
 * <AUTHOR>
 * @date 2022-04-14 11:07
 **/
public interface UserLoginFailureDao {

    /**
     * .
     * @param id .
     * @return .
     */
    int deleteByUid(Object id);
    /**
     * .
     * @param record .
     * @return .
     */
    int insert(UserLoginFailure record);
    /**
     * .
     * @param id .
     * @return .
     */
    UserLoginFailure selectByPrimaryKey(Object id);


    /**
     *  根据用户id查询记录
     * @param uid .
     * @return .
     */
    UserLoginFailure selectByUid(@Param("uid") Object uid);
    /**
     * .
     * @param record .
     * @return .
     */
    int updateByPrimaryKey(UserLoginFailure record);

    void resetFailCount(@Param("userId") Long userId);
}
package cn.hy.auth.custom.user.history.service;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;

import javax.validation.constraints.NotBlank;

/**
 * 类描述: 登录信息
 *
 * <AUTHOR>
 * @date :创建于 2020/11/17
 */
public interface AuthLoginInfoService {

    /**
     * 保存登录数据
     *
     * @param record 记录
     * @return 返回插入条数
     */
    int insert(UserLoginInfoDTO record);

    /**
     * 按主键查询登录信息
     *
     * @param id 主键
     * @return 返回对应的数据
     */
    UserLoginInfoDTO selectByPrimaryKey(Long id);

    /**
     * 查询上一次成功登录信息
     *
     * @param clientId 客户端标识
     * @param userName 登录账号
     * @return 返回上一次成功登录信息
     */
    UserLoginInfoDTO getLastSuccessLoginInfo(@NotBlank String clientId, @NotBlank String userName);
    /**
     * 查询上一次成功登录信息
     *
     * @param clientId 客户端标识
     * @param uid 登录账号id
     * @return 返回上一次成功登录信息
     */
    UserLoginInfoDTO getLastSuccessLoginInfo(@NotBlank String clientId, @NotBlank Long uid);
    /**
     * 获取排序号
     *
     * @return 返回最大排序号
     */
    Long getMaxSequence();
}
package cn.hy.auth.custom.common.utils;

import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {LoginTypeMatcher.class})
public class LoginTypeMatcherTest {

    HttpServletRequest request;
    @Autowired
    LoginTypeMatcher loginTypeMatcher;

    @Before
    public void setUp() throws Exception {
        Logger.getGlobal().setLevel(Level.ALL);
        request = Mockito.mock(HttpServletRequest.class);
    }

    private void test(String uri, LoginTypeEnum expected) {
        doReturn(uri).when(request).getRequestURI();

        LoginTypeEnum loginType = loginTypeMatcher.match(request);

        Assertions.assertThat(loginType).isNotNull();
        Assertions.assertThat(loginType).isEqualTo(expected);
    }

    @Test
    public void matchUserNamePassword() {
        test("/login", LoginTypeEnum.USERNAME_PASSWORD);
    }

    @Test
    public void matchMobile() {
        test("/login/mobile", LoginTypeEnum.SMS_CODE);
    }

    @Test
    public void matchOAuthPassword() {
        doReturn("password").when(request).getParameter(eq("grant_type"));
        test("/oauth/token", LoginTypeEnum.OAUTH2_PASSWORD);
    }

    @Test
    public void matchOAuthClient() {
        doReturn("client_credentials").when(request).getParameter(eq("grant_type"));
        test("/oauth/token", LoginTypeEnum.OAUTH2_CLIENT);
    }

    @Test
    public void matchOfErrorGrant() {
        doReturn("password").when(request).getParameter(eq("grant_type"));
        doReturn("oauth/token").when(request).getRequestURI();

        LoginTypeEnum loginType = loginTypeMatcher.match(request);

        Assertions.assertThat(loginType).isNull();
    }

    @Test
    public void matchOfNotLoginUri() {
        doReturn("any/").when(request).getRequestURI();

        LoginTypeEnum loginType = loginTypeMatcher.match(request);

        Assertions.assertThat(loginType).isNull();
    }
}
package cn.hy.auth.custom.multi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Unit test for simple App.
 */
@TestPropertySource(properties = "spring.jpa.hibernate.ddl=update")
@ComponentScan(basePackages = {"cn.hy.auth", "cn.hy.id", "cn.hy.dataengine", "cn.hy.metadata"})
@MapperScan({"cn.hy.auth.custom.multi.**.dao"})
@EnableTransactionManagement
public class AppAuthCustomMultiAppTest {

    public static void main(String[] args) {
        SpringApplication.run(AppAuthCustomMultiAppTest.class, args);
    }
}

package cn.hy.auth.custom.user.account.domain;

import lombok.Data;

import java.util.Date;

@Data
public class SysdevUserOnlineStatusDTO {
    private Long id;
    private String dataVersion;
    private Long createUserId;
    private String createUserName;
    private Date createTime;
    private Long lastUpdateUserId;
    private String lastUpdateUserName;
    private Date lastUpdateTime;
    private Long sequence;
    private String userAccountName;
    private String onlineStatus;
    private String onlineStatusName;

    private Date loginTime;

    private Date lastLoginTime;
}

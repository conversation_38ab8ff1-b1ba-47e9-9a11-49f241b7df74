package cn.hy.auth.common.security.core.authentication.face;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Objects;

/**
 * 社交登录认证token
 *
 * <AUTHOR>
 * @date 2022-09-08 17:32
 */
public class HyFaceAuthenticationToken extends AbstractAuthenticationToken {

    private static final long serialVersionUID = 13213L;
    private Object principal;
    private String lesseeCode;
    private String appCode;
    private String facePic;
    private String loginType;

    public void setLesseeCode(String lesseeCode) {
        this.lesseeCode = lesseeCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getFacePic() {
        return facePic;
    }



    public HyFaceAuthenticationToken(Object principal, String facePic, String lesseeCode,
                                     String appCode, String loginType) {
        //因为刚开始并没有认证，因此用户没有任何权限，并且设置没有认证的信息（setAuthenticated(false)）
        super(null);
        //这里的principal就是code
        this.principal = principal;
        this.facePic = facePic;
        this.lesseeCode = lesseeCode;
        this.appCode = appCode;
        this.loginType = loginType;
        this.setAuthenticated(false);
    }

    public HyFaceAuthenticationToken(Object principal,String lesseeCode,
                                     String appCode, String loginType, String facePic,
                                     Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.facePic = facePic;
        this.lesseeCode = lesseeCode;
        this.appCode = appCode;
        this.loginType = loginType;
        super.setAuthenticated(true);
    }

    public HyFaceAuthenticationToken(Object principal, String facePic,
                                     Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.facePic = facePic;
        super.setAuthenticated(true);
    }


    @Override
    public Object getCredentials() {
        return null;
    }

    public void setPrincipal(Object principal) {
        this.principal = principal;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) {
        if (isAuthenticated) {
            throw new IllegalArgumentException("Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        } else {
            super.setAuthenticated(false);
        }
    }

    public String getLesseeCode() {
        return lesseeCode;
    }

    public String getAppCode() {
        return appCode;
    }


    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        HyFaceAuthenticationToken that = (HyFaceAuthenticationToken) o;
        return Objects.equals(principal, that.principal);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), principal);
    }
}

package cn.hy.auth.common.security.core.properties;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 类描述：
 *
 * <AUTHOR> by <PERSON><PERSON><PERSON><PERSON>
 * @date 2022/9/8 15:49
 **/
@Data
@Component
public class HyExternalProperties {

    /**
     * 登录filter 拦截处理的url
     */
    private String authProcessingUrl = "/external/access";
    /**
     * 验证码参数名称
     */
    private  String codeParameter = "auth_code";
    private  String providerIdParamter = "provider_id";
    private  String lesseeCodeParamter = "lessee_code";
    private  String appCodeParamter = "app_code";

}

2024-07-04 19:27:13.255 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 19:27:13.257 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 19:27:13.440 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 19:27:13.466 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 19:27:32.502 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 19:27:32.505 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 19:27:32.654 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 19:27:32.679 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 19:27:35.207 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 19:27:35.208 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 19:27:35.354 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135ms. Found 12 repository interfaces.
2024-07-04 19:27:35.364 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.364 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.364 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.382 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.383 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 19:27:35.384 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 19:27:35.576 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 19:27:35.577 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 19:27:35.578 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 19:27:35.579 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 19:27:35.580 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 19:27:36.218 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 19:27:36.232 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 19:27:36.523 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 19:27:36.524 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 19:27:36.524 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 19:27:36.525 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 19:27:36.525 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 19:27:36.525 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 19:27:36.525 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 19:27:36.525 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 19:27:36.526 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 19:27:36.526 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 19:27:36.526 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 19:27:36.526 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 19:27:36.532 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 264ms. Found 0 repository interfaces.
2024-07-04 19:27:36.759 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 19:27:36.951 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 19:27:36.951 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 19:27:36.952 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 19:27:36.952 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 19:27:36.952 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 19:27:37.267 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-04 19:27:37.267 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-04 19:27:37.340 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-04 19:27:37.341 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-04 19:27:37.344 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 19:27:37.529 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 19:27:37.779 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$585b9219] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:37.863 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$7a6d18bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:38.043 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$5d7a75da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:38.236 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$911143ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:38.259 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$10178925] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:38.287 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$84a98096] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:38.444 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a0c38393] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 19:27:39.232 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 19:27:39.251 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 19:27:39.265 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 19:27:39.265 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 19:27:39.675 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 19:27:39.675 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6976 ms
2024-07-04 19:27:40.487 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 19:27:40.502 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 19:27:41.626 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 19:27:41.626 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 19:27:41.654 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@5e572b08
2024-07-04 19:27:42.220 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 19:27:44.199 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 19:27:45.409 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 19:27:45.591 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 19:27:45.779 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 19:27:45.916 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 19:27:45.919 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 19:27:46.176 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 19:27:46.448 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 19:27:47.608 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 19:27:48.191 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 19:27:52.016 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 19:27:52.251 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 19:27:52.454 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:22, workerId:10
2024-07-04 19:27:53.582 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 19:27:53.582 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 19:27:54.536 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 19:27:54.536 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 19:27:55.890 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 19:27:55.890 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 19:27:56.059 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@7fe68a5]
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-04 19:27:57.678 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-04 19:27:57.679 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 19:27:57.679 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 19:27:57.679 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-04 19:27:57.679 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 19:27:57.968 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ee17b74, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f028abf, org.springframework.security.web.header.HeaderWriterFilter@791a53c7, org.springframework.security.web.authentication.logout.LogoutFilter@7fbb0f0f, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@1f8dbc5d, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@23a24fea, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f7a6cc9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@50a4e4e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@32cc87e0, org.springframework.security.web.session.SessionManagementFilter@2e5f2387, org.springframework.security.web.access.ExceptionTranslationFilter@75b9bbab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5fbb2225]
2024-07-04 19:27:58.033 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@54978e18, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f6881af, org.springframework.security.web.context.SecurityContextPersistenceFilter@2706cee5, org.springframework.security.web.header.HeaderWriterFilter@2bf065c2, org.springframework.security.web.authentication.logout.LogoutFilter@1c96322c, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@46c2ed8d, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@71ef979e, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4468deb7, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@2f33a2e3, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@473c1e53, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@1c028ae2, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@115cbd9b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@463e66c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e48c5b2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@234a21e9, org.springframework.security.web.session.SessionManagementFilter@30584e91, org.springframework.security.web.access.ExceptionTranslationFilter@427475a2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1189f179]
2024-07-04 19:27:58.048 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e0ccdd6, org.springframework.security.web.context.SecurityContextPersistenceFilter@73372652, org.springframework.security.web.header.HeaderWriterFilter@1053b20d, org.springframework.security.web.csrf.CsrfFilter@27a9fe9e, org.springframework.security.web.authentication.logout.LogoutFilter@544364ba, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@756695e3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78288298, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5f849a79, org.springframework.security.web.session.SessionManagementFilter@37f37699, org.springframework.security.web.access.ExceptionTranslationFilter@164212ad]
2024-07-04 19:27:58.273 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 19:27:58.273 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 19:27:59.096 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-04 19:27:59.211 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-04 19:28:01.197 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-04 19:28:01.364 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-04 19:28:01.784 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-04 19:28:01.808 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-04 19:28:01.878 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-04 19:28:01.890 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://**************:18500}]
2024-07-04 19:28:01.895 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-04 19:28:01.905 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-04 19:28:01.914 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-04 19:28:02.494 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 19:28:02.649 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 19:28:02.649 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 19:28:02.998 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 19:28:03.018 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 19:28:03.024 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 19:28:03.024 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 19:28:03.030 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 19:28:03.038 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 19:28:03.043 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 19:28:03.043 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 19:28:03.044 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 19:28:03.045 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 19:28:03.050 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 19:28:03.051 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 19:28:03.051 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 19:28:03.055 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 19:28:03.056 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:28:03.057 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 19:28:03.057 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:28:03.072 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 19:28:03.072 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:28:03.107 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 19:28:03.107 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:28:03.108 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:28:03.152 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 45
2024-07-04 19:28:03.153 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 19:28:03.154 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.155 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 45
2024-07-04 19:28:03.155 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 45
2024-07-04 19:28:03.156 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 19:28:03.156 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 19:28:03.158 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.158 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.175 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 19:28:03.175 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 19:28:03.175 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 19:28:03.468 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.939 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.941 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.941 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:03.990 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.011 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.011 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.063 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.063 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.104 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.195 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27088&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.195 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27088&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=27088&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.218 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.241 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.298 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.299 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.311 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.316 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.316 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.873 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.901 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27088&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:04.964 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@12bd6e37; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:05.035 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:05.057 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:05.059 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: **********
2024-07-04 19:28:05.072 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-04 19:28:05.138 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-04 19:28:05.166 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 34.69 seconds (JVM running for 35.631)
2024-07-04 19:28:05.202 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-04 19:28:05.202 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-04 19:28:05.208 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-04 19:28:05.208 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-04 19:28:05.234 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：*******************************************************************************************************************************************************************************,username = iotmp
2024-07-04 19:28:06.024 [,,,,Not Auth Request!] [RMI TCP Connection(7)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-04 19:28:06.025 [,,,,Not Auth Request!] [RMI TCP Connection(7)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-04 19:28:06.055 [,,,,Not Auth Request!] [RMI TCP Connection(7)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 30 ms
2024-07-04 19:28:40.200 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：93ms
2024-07-04 19:28:40.450 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_account
2024-07-04 19:28:40.566 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-04 19:28:40.567 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】
2024-07-04 19:28:40.578 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_H23459
2024-07-04 19:28:40.579 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_H23459是否需要验证码，失败次数0
2024-07-04 19:28:40.582 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 19:28:40.582 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 19:28:40.688 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 默认密码检查-->验证用户【hy_H23459】的密码为默认密码，请立即修改！
2024-07-04 19:28:40.729 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-04 19:28:40.732 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_oauth_client_details]的加载时间：2ms
2024-07-04 19:28:40.775 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1]获取用户主键失败,获取为空
2024-07-04 19:28:40.792 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_role
2024-07-04 19:28:40.794 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：role
2024-07-04 19:28:40.860 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] WARN  c.h.a.c.s.c.a.c.AbstractAuthenticationProvider - Provider has error:{"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
	at cn.hy.auth.custom.security.oauth2.provider.PwdExpirationAndForceModifyProviderer.doAuthenticate(PwdExpirationAndForceModifyProviderer.java:80)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 19:28:40.862 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] ERROR o.s.s.w.a.UsernamePasswordAuthenticationFilter - An internal error occurred while trying to authenticate the user.
org.springframework.security.authentication.InternalAuthenticationServiceException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:33)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
	at cn.hy.auth.custom.security.oauth2.provider.PwdExpirationAndForceModifyProviderer.doAuthenticate(PwdExpirationAndForceModifyProviderer.java:80)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	... 109 common frames omitted
2024-07-04 19:28:40.864 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=paas, pwd_encryption_type=3, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_H23459, app_code=sys} 登录失败. {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"} 
2024-07-04 19:28:40.866 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
org.springframework.security.authentication.InternalAuthenticationServiceException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:33)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.d880c05f-1851-4443-adf7-40894d485bc1"}
	at cn.hy.auth.custom.security.oauth2.provider.PwdExpirationAndForceModifyProviderer.doAuthenticate(PwdExpirationAndForceModifyProviderer.java:80)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	... 109 common frames omitted
2024-07-04 19:28:41.013 [paas,sys,/login,,01b600a76b92451584170ae12b368540] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_user_login_info]的加载时间：2ms
2024-07-04 19:28:41.075 [,,,,Not Auth Request!] [task-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：auth_sys_log_custom_handle
2024-07-04 19:29:00.928 [paas,sys,/user/pwd,hy_H23459,fab19de9e03c4769bca10f373a3456e3] [http-nio-6060-exec-2] ERROR c.h.auth.boot.exception.ControllerExceptionHandler - 业务异常:
cn.hy.auth.custom.common.exceptions.AuthBusinessException: 不允许修改非本账号密码
	at cn.hy.auth.custom.common.utils.AuthAssertUtils.isTrue(AuthAssertUtils.java:83)
	at cn.hy.auth.custom.user.account.controller.UserAccountController.updateUserPassword(UserAccountController.java:110)
	at cn.hy.auth.custom.user.account.controller.UserAccountController$$FastClassBySpringCGLIB$$677fbf5f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at cn.hy.auth.custom.common.log.AuthLogAop.doAround(AuthLogAop.java:71)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at cn.hy.auth.custom.user.account.controller.UserAccountController$$EnhancerBySpringCGLIB$$738a7f08.updateUserPassword(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:919)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:663)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.boot.gateway.filter.ParameterPrintFilter.doFilterInternal(ParameterPrintFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:170)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:68)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 19:29:56.201 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m10s482ms656µs400ns).
2024-07-04 19:29:56.201 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 19:29:56.201 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 19:29:56.201 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 19:30:13.405 [,,,,Not Auth Request!] [OkHttp http://**************:18500/...] ERROR com.orbitz.consul.cache.ConsulCache - Error getting response from consul for keyvalue "/dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators", will retry in 10000 MILLISECONDS
java.net.SocketTimeoutException: timeout
	at okio.Okio$4.newTimeoutException(Okio.java:232)
	at okio.AsyncTimeout.exit(AsyncTimeout.java:276)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:243)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.java:358)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:230)
	at okhttp3.internal.http1.Http1ExchangeCodec.readHeaderLine(Http1ExchangeCodec.java:242)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.java:213)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.cache.TimeoutInterceptor.intercept(TimeoutInterceptor.java:53)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.Consul$Builder.lambda$withAclToken$2(Consul.java:419)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
	at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
	at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 19:30:13.406 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:30:13.406 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:30:13.406 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 19:30:13.413 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 19:27:52,000 to 2024-07-04 19:30:13,392
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.24| 36.36%|            22|             8|             0|             0|       16.0|         97
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 19:30:16.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-0cb5c245-e297-476f-a4f5-ad56028a8df4 is not valid.
2024-07-04 19:30:16.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-983d4de2-5d33-4e40-8f91-7ba24527bd82 is not valid.
2024-07-04 19:30:16.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 19:30:16.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 19:30:16.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-c0a745a0-d45c-4ce1-a80b-50b283c91833 is not valid.
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 19:30:16.420 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:30:16.424 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:30:16.424 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 19:30:16.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 48
2024-07-04 19:30:16.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 48
2024-07-04 19:30:16.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 19:30:16.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 19:30:16.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 48
2024-07-04 19:30:16.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 19:30:16.442 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 19:30:16.442 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 19:30:16.442 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 19:31:18.113 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_H23459
2024-07-04 19:31:18.114 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_H23459是否需要验证码，失败次数0
2024-07-04 19:31:18.114 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 19:31:18.114 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 19:31:18.118 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_H23459】的密码状态正常。
2024-07-04 19:31:18.120 [paas,sys,/login,,84a8cb4627604c48962d7c2fbac6682b] [http-nio-6060-exec-4] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.2749a6b3-8868-4a98-95c7-a0ecf032b3bc]获取用户主键失败,获取为空
2024-07-04 19:33:54.764 [,,,,Not Auth Request!] [Thread-57] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-04 19:33:54.764 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.765 [,,,,Not Auth Request!] [Thread-73] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=27088&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.766 [,,,,Not Auth Request!] [Thread-73] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=27088&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.767 [,,,,Not Auth Request!] [Thread-73] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27088&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.775 [,,,,Not Auth Request!] [Thread-73] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27088&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.776 [,,,,Not Auth Request!] [Thread-73] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /**************:20881, dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.778 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 19:33:54.778 [,,,,Not Auth Request!] [Thread-73] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: **********
2024-07-04 19:33:54.779 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********

package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthAccountLockPolicyDTO;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.enums.UserAccountLockType;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.exceptions.BlockedLoginException;
import cn.hy.auth.custom.security.oauth2.event.PwdErrorEvent;
import cn.hy.auth.custom.user.account.domain.UserAccountLock;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 用户账号是否锁定验证
 *
 * <AUTHOR>
 * @date 2022-04-14 16:52
 **/
@Component
@Slf4j
public class UserAccountLockAuthenticationProviderer extends AbstractAuthenticationProvider {

    private final UserDetailsService userDetailsService;
    private final UserAccountLockService userAccountLockService;
    private final ApplicationContext applicationContext;

    public UserAccountLockAuthenticationProviderer(UserDetailsService userDetailsService,
                                                   UserAccountLockService userAccountLockService, ApplicationContext applicationContext) {
        this.userDetailsService = userDetailsService;
        this.userAccountLockService = userAccountLockService;
        this.applicationContext = applicationContext;
    }

    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return 只能return null,否则会截断后续的处理
     */
    @Override
    protected Authentication doAuthenticate(Authentication authentication) {

        String userName = (String)authentication.getPrincipal();
        if (StringUtils.isBlank(userName)) {
            log.error("用户名参数为空，不需要记录验证码次数。");
            throw new BadCredentialsException(LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg1", null));
        }

        log.debug("检查用户账号是否锁定-->验证用户【{}】的锁定状态。", userName);
        HyUserDetails account = getUserDetail(userName);
        if (Objects.isNull(account)) {
            log.error("账号密码检查-->根据用户名【" + userName + "】无法获取用户信息。");
            // 账号错误也需要记录验证码次数
            applicationContext.publishEvent(new PwdErrorEvent(userName));
            throw new BadCredentialsException(LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg1", null));
        }
        UserAccountLock accountLock = userAccountLockService.getUserAccountLock(account.getUserId());
            if(accountLock != null){
            String msg = getAccountLockedTip(accountLock.getLockRecodeType());
            log.debug("检查用户账号是否锁定--> 租户号：{}，应用号：{}，用户账号：{}，提示：{}",
                    AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),userName,msg);
            throw new BlockedLoginException(msg);
        }

        log.debug("检查用户账号是否锁定-->用户账号【{}】没有锁定。", userName);
        return null;
    }

    public static String getAccountLockedTip(){
        // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        String msg = LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg2", "");
        if ("forever".equals(accountLockPolicy.getLockType())){
            msg = LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg3", "");
        } else if ("minuteTime".equals(accountLockPolicy.getLockType())){
            msg = LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg4", "") +accountLockPolicy.getLockMinute()+LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg5", "");
        } else if ("sameDay".equals(accountLockPolicy.getLockType())){
            msg = LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg6", "");
        }
        return msg;
    }

    public static String getAccountLockedTip(String type){
        // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        String msg = LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg7", "您输入错误次数超过限制，账号已被锁定，无法登录，请稍后重试或联系管理员");
        String descByType = UserAccountLockType.getDescByType(type);
        if ("forever".equals(accountLockPolicy.getLockType())){
                msg = descByType + LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg8", "，账号已被永久锁定，无法登录，请联系管理员");
        } else if ("minuteTime".equals(accountLockPolicy.getLockType())){
            msg = descByType + LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg9", "，账号已被锁定，") + accountLockPolicy.getLockMinute() + LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg10", "分钟内无法登录，请稍后重试或联系管理员");
        } else if ("sameDay".equals(accountLockPolicy.getLockType())){
            msg = descByType + LocaleUtil.getMessage("UserAccountLockAuthenticationProviderer.result.msg11", "，账号已被锁定，本日内无法登录，请次日重试或联系管理员");
        }
        return msg;
    }

    private HyUserDetails getUserDetail(String userName){
        return (HyUserDetails) userDetailsService.loadUserByUsername(userName);
    }
    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    @Override
    protected int order() {
        return 0;
    }

    /**
     * 判断是否是用户密码登录模式
     *
     * @param authentication .
     * @return .
     */
    @Override
    protected boolean isSupport(Authentication authentication) {
        List<LoginTypeEnum> supports = Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD,
                LoginTypeEnum.OAUTH2_PASSWORD);

        return supports.contains(AuthContext.getContext().loginState().getLoginType());
    }
}

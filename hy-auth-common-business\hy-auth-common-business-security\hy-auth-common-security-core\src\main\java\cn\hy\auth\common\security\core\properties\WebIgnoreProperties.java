package cn.hy.auth.common.security.core.properties;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 登录忽略的url配置.一般为静态文件
 *
 * <AUTHOR>
 * @date 2012-11-12 17:21:26
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "hy.security.web.ignore")
public class WebIgnoreProperties {
    /**
     * 需要忽略的 URL 格式，不考虑请求方法
     */
    private List<String> pattern = Lists.newArrayList();

}

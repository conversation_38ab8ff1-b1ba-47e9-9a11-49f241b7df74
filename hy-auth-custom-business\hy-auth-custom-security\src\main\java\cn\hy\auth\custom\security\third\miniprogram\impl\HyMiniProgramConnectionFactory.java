package cn.hy.auth.custom.security.third.miniprogram.impl;

import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import cn.hy.auth.common.security.core.authentication.social.service.HyServiceProvider;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Component
public class HyMiniProgramConnectionFactory extends HyConnectionFactory {

    protected HyMiniProgramConnectionFactory(HyMiniProgramServiceProvider serviceProvider) {
        super(ProviderType.MINI_PROGRAM.getCode(), serviceProvider);
    }
}

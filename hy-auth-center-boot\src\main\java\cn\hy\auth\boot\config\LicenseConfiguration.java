package cn.hy.auth.boot.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/5/27 16:14
 **/
@Slf4j
public class LicenseConfiguration {

    public static boolean getLicenseCondition(){
        try {
            InputStream resourceInputStream = getResourceInputStream("/mapper/LicenseConfiguration.xml");
            String content = IOUtils.toString(resourceInputStream, StandardCharsets.UTF_8);
            // 默认都是打开，0标识关闭
            return !content.contains("0");
        } catch (IOException e) {
            log.info("读取配置文件发生错误，强制启用license功能。");
            return true;
        }
    }

    /**
     * 获取项目资源目录下的文件流
     *
     * @param resourceFileFullName resource目录下文件的相对路径全名称。
     * @return .
     */
    public static InputStream getResourceInputStream(String resourceFileFullName) throws IOException {
        if (!StringUtils.isEmpty(resourceFileFullName) && resourceFileFullName.startsWith(File.separator)) {
            // 不需要用"/"
            resourceFileFullName = resourceFileFullName.substring(1);
        }
        // jar包运行时，能正确获得resource目录下的文件 https://cloud.tencent.com/developer/article/1549662
        return new ClassPathResource(resourceFileFullName).getInputStream();
    }

}

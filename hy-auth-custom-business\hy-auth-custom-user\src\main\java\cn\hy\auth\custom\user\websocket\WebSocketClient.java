package cn.hy.auth.custom.user.websocket;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Session;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/6/26 15:12
 */
@Slf4j
public class WebSocketClient {

    /**
     * 默认180秒不在线算死亡
     */
    private static final int MIN_ACTIVE_TIME = 180 * 1000;

    @Getter
    private final Session session;

    /**
     * 客户端发心跳（ping）后，该时间戳会被刷新
     */
    private long lastPingTime = System.currentTimeMillis();

    public WebSocketClient(Session session) {
        this.session = session;
    }

    public void sendMessage(String message) {
        if (!session.isOpen()) {
            return;
        }
        try {
            session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            log.error("Websocket send message error: {}", e.getMessage());
        }
    }

    public void keepAlive() {
        lastPingTime = System.currentTimeMillis();
    }

    public boolean isActive() {
        boolean act = (System.currentTimeMillis() - lastPingTime) <= MIN_ACTIVE_TIME;
        return session.isOpen() && act;
    }
}

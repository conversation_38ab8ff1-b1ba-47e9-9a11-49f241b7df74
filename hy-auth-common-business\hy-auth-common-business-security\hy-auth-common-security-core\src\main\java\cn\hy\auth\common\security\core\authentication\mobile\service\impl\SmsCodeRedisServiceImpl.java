package cn.hy.auth.common.security.core.authentication.mobile.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.ValidateCode;
import cn.hy.auth.common.security.core.authentication.mobile.event.CheckUserAccountLockEvent;
import cn.hy.auth.common.security.core.authentication.mobile.event.LockUserAccountEvent;
import cn.hy.auth.common.security.core.authentication.mobile.event.LoginBindIpEvent;
import cn.hy.auth.common.security.core.authentication.mobile.event.PwdChangeIntervalEvent;
import cn.hy.auth.common.security.core.authentication.mobile.exception.*;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeService;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import lombok.Builder;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 类描述：基于Redis的手机验证码服务实现类
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/25 10:28
 **/
@Slf4j
public class SmsCodeRedisServiceImpl implements SmsCodeService, ValidateErrorService {
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AppConfigParamUtil appConfigParamUtil;

    private final static String LOGIN_CODE_PREFIX = "LOGIN_SMS_";

    private final static String LOGIN_CODE_ERROR_PREFIX = "LOGIN_SMS_ERROR_";

    @Override
    public void validateCode(String mobile, String mobileCipherText, String codeInRequest, String mobileParameter, String codeParameter, ServletWebRequest request) {
        checkParameterNotNull(mobile, codeInRequest, mobileParameter, codeParameter);
        checkUserAccount(StringUtils.isNotBlank(mobileCipherText) ? mobileCipherText : mobile);
        String smsCodeCacheKey = StringUtils.isNotBlank(mobileCipherText) ? mobile + mobileCipherText : mobile;
        ValidateCode validateCode = getCode(smsCodeCacheKey, codeInRequest);
        checkValidataCode(mobile, codeInRequest, validateCode, mobileCipherText);
        removeCode(smsCodeCacheKey, validateCode.getCode());
        checkLoginBindIp(request.getParameter("ip"), mobile);
    }

    private void checkUserAccount(String mobile) {
        CheckUserAccountLockEvent checkUserAccountLockEvent = new CheckUserAccountLockEvent(mobile);
        applicationContext.publishEvent(checkUserAccountLockEvent);
        if (checkUserAccountLockEvent.isLock()) {
            throw new AccountLockException(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg5", ""));
        } else if (!checkUserAccountLockEvent.getUserExist()) {
            throw new UserNotExistException(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg6", ""));
        }
    }

    private void checkPwdChangeInterval(String mobile) {
        PwdChangeIntervalEvent pwdChangeIntervalEvent = new PwdChangeIntervalEvent(mobile);
        applicationContext.publishEvent(pwdChangeIntervalEvent);
        if (!pwdChangeIntervalEvent.isCheckPass()) {
            throw new UserCheckException(pwdChangeIntervalEvent.getMsg());
        }
    }

    private void checkLoginBindIp(String ip, String mobile) {
        LoginBindIpEvent loginBindIpEvent = new LoginBindIpEvent(ip, mobile);
        applicationContext.publishEvent(loginBindIpEvent);
        if (!loginBindIpEvent.isCheckPass()) {
            throw new UserCheckResultException(loginBindIpEvent.getCode(), loginBindIpEvent.getResult());
        }
    }

    @Override
    public String saveCode(String mobile, String code, LocalDateTime expireTime) {
        String cacheKey = getCacheKey(LOGIN_CODE_PREFIX, mobile);
        SmsCode smsCode = SmsCode.builder().mobile(mobile).code(code).expireTime(expireTime).build();
        List<SmsCode> smsCodes = redisTemplate.opsForList().range(cacheKey, 0, -1);
        boolean isSend = smsCodes.stream().anyMatch(mobileSmsCode -> !mobileSmsCode.isExpried());
        if (isSend) {
            return LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg10", "");
        }
        redisTemplate.opsForList().leftPush(cacheKey, smsCode);
        return "";
    }

    public static String getCacheKey(String prefix ,String key) {
        return prefix + key;
    }

    @Override
    public ValidateCode getCode(String mobile, String code) {
        List<SmsCode> smsCodeList = redisTemplate.opsForList().range(getCacheKey(LOGIN_CODE_PREFIX, mobile), 0, -1);
        if (!CollectionUtils.isEmpty(smsCodeList)) {
            ValidateCode validateCode = new ValidateCode();
            boolean hasNoExpired = smsCodeList.stream().anyMatch(item -> !item.isExpried());
            validateCode.setHasNoExpired(hasNoExpired);
            validateCode.setExpireTime(LocalDateTime.now());
            if (!hasNoExpired) {
                return validateCode;
            }
            for (SmsCode smsCode : smsCodeList) {
                if (Objects.equals(smsCode.code, code) && !smsCode.isExpried()) {
                    return new ValidateCode(smsCode.code, true, false, smsCode.expireTime);
                }
            }
            validateCode.setCodeError(true);
            return validateCode;
        }
        return null;
    }

    @Override
    public void removeCode(String mobile, String code) {
        List<SmsCode> smsCodeList = redisTemplate.opsForList().range(getCacheKey(LOGIN_CODE_PREFIX, mobile), 0, -1);;
        if (!CollectionUtils.isEmpty(smsCodeList)) {
            if (smsCodeList.size() == 1) {
                redisTemplate.opsForList().remove(getCacheKey(LOGIN_CODE_PREFIX, mobile), 0, smsCodeList.get(0));
                return;
            }
            Set<SmsCode> inValidateCodeSet = new HashSet<>();
            for (SmsCode smsCode : smsCodeList) {
                // 需要删除的验证码和过期的验证码
                if (Objects.equals(smsCode.code, code) || smsCode.isExpried()) {
                    redisTemplate.opsForList().remove(getCacheKey(LOGIN_CODE_PREFIX, mobile), 0, smsCode);
                }
            }
        }
    }

    private void checkValidataCode(String mobile, String codeInRequest, ValidateCode validateCode, String mobileCipherText) {
        String cacheKey = StringUtils.isNotBlank(mobileCipherText) ? mobile + mobileCipherText : mobile;
        if (validateCode == null || !validateCode.isHasNoExpired()) {
            log.debug("验证码已过期。mobileInRequest = {}, codeInRequest = {},{} ", mobile, codeInRequest, validateCode);
            throw new ErrorSmsCodeException(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg7", ""));
        }
        String codeErrorCacheKey = getCacheKey(LOGIN_CODE_ERROR_PREFIX, cacheKey);
        if (validateCode.isHasNoExpired() && validateCode.isCodeError()) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            Map<String, String> paramMap = ServletUtil.getParamMap(request);
            Object errorLimitObj = appConfigParamUtil.getByKey(paramMap.get("lessee_code"), paramMap.get("app_code"), "sysdevSmsErrorLimit");
            Integer errorLimit = errorLimitObj == null ? 4 : Integer.parseInt(errorLimitObj.toString()) - 1;
            Integer errorTime = (Integer) redisTemplate.opsForValue().get(codeErrorCacheKey);
            if (errorTime == null) {
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                LocalDateTime tomorrowMidnight = LocalDateTime.of(tomorrow, LocalTime.MIDNIGHT);
                Date date = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
                redisTemplate.opsForValue().set(codeErrorCacheKey, 0);
                redisTemplate.expireAt(codeErrorCacheKey, date);
                errorTime = 0;
            }
            if (errorTime >= errorLimit) {
                LockUserAccountEvent lockUserAccountEvent = new LockUserAccountEvent(StringUtils.isNotBlank(mobileCipherText) ? mobileCipherText : mobile);
                redisTemplate.opsForValue().set(codeErrorCacheKey, 0);
                applicationContext.publishEvent(lockUserAccountEvent);
            } else {
                redisTemplate.opsForValue().set(codeErrorCacheKey, errorTime + 1);
            }
            log.debug("验证码错误。mobileInRequest = {}, codeInRequest = {} ", mobile, codeInRequest);
            String lockMessage = LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg8", "");
            String errorMesage = String.format(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg9", ""), errorTime + 1, errorLimit - errorTime);
            throw new ErrorSmsCodeException(errorTime >= errorLimit ? lockMessage : errorMesage);
        }
        redisTemplate.opsForValue().set(codeErrorCacheKey, 0);
    }

    private void checkParameterNotNull(String mobile, String codeInRequest, String mobileParameter, String codeParameter) {
        if (StringUtils.isEmpty(mobile)) {
            log.debug("请输入手机号。mobileInRequest = {}, codeInRequest = {} ", mobile, codeInRequest);
            throw new ValidateCodeException(String.format(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg3", null), mobileParameter));
        }
        if (StringUtils.isEmpty(codeInRequest)) {
            log.debug("请输入验证码。mobileInRequest = {}, codeInRequest = {} ", mobile, codeInRequest);
            throw new ValidateCodeException(String.format(LocaleUtil.getMessage("SmsCodeMapServiceImpl.result.msg4", null), codeParameter));
        }
    }

    @Override
    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {
        String cacheKey = StringUtils.isNotBlank(mobileCipherText) ? mobile + mobileCipherText : mobile;
        redisTemplate.opsForValue().set(getCacheKey(LOGIN_CODE_ERROR_PREFIX, cacheKey), 0);
    }

    @Builder
    @ToString
    public static class SmsCode implements Serializable {
        private String mobile;
        private String code;
        private LocalDateTime expireTime;

        public SmsCode(String mobile, String code, LocalDateTime expireTime) {
            this.mobile = mobile;
            this.code = code;
            this.expireTime = expireTime;
        }

        public boolean isExpried() {
            return LocalDateTime.now().isAfter(expireTime);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof SmsCode)) {
                return false;
            }
            SmsCode smsCode = (SmsCode) o;
            return Objects.equals(mobile, smsCode.mobile) &&
                    Objects.equals(code, smsCode.code) &&
                    Objects.equals(expireTime, smsCode.expireTime);
        }

        @Override
        public int hashCode() {
            return Objects.hash(mobile, code, expireTime);
        }

        public String getCode() {
            return code;
        }
    }
}

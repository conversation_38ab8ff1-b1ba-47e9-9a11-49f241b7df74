package cn.hy.auth.custom.security.third.face;

import cn.hutool.core.map.MapUtil;
import cn.hy.auth.common.security.core.authentication.face.model.HyFaceRecognitionDTO;
import cn.hy.auth.common.security.core.authentication.face.service.OpenApiConnectionService;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.log.util.OkHttpUtils;
import cn.hy.auth.custom.user.account.dao.HrMemberDao;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import cn.hy.saas.commons.exception.BusinessException;
import com.alibaba.fastjson.JSONObject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Optional;


/**
 * 类描述： 表业务管理器
 *
 * <AUTHOR> by hejiawei
 * @date 2023/6/4 17:03
 **/
@Component
@Slf4j
public class OpenApiConnectionServiceImpl implements OpenApiConnectionService {

    private static final String FACE_TABLE_NAME = "auth_user_face";

    @Value("${third.proxy.faceServer.url:http://127.0.0.1:18082/face/faceCompare}")
    private String faceServerUrl;
    @Autowired
    private TableRelocateUtil tableRelocateUtil;
    @Autowired
    private HrMemberDao hrMemberDao;

    @Override
    public JSONObject getUserByFace(HyFaceRecognitionDTO recognitionParams) {
        Assert.notNull(faceServerUrl, LocaleUtil.getMessage("OpenApiConnectionServiceImpl.assert.msg1", null));
        log.info("人脸服务地址：{}", faceServerUrl);
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();
        try {
            Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(FACE_TABLE_NAME);
            if (tableMeta.isPresent()) {
                TableMData tableMData = tableMeta.get();
                recognitionParams.setLesseeCode(tableMData.getLessCode());
                recognitionParams.setAppCode(tableMData.getAppCode());
            } else {
                throw new BusinessException("500", LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg1", null) + lesseeCode + LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg2", null) + appCode + LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg3", null) + FACE_TABLE_NAME);
            }
            //读取第三方服务
            String response = OkHttpUtils.postJson(faceServerUrl, JSONObject.toJSONString(recognitionParams));
            if (response != null) {
                JSONObject resObject = JSONObject.parseObject(response);
                if (resObject.getInteger("code") == 0) {
                    Object data = resObject.get("data");
                    if (data != null) {
                        return JSONObject.parseObject(data.toString());
                    }
                } else {
                    throw new BusinessException("500", LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg4", null) + response);
                }
            }
        } catch (Exception e) {
            throw new BusinessException("500", LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg4", null) + e);
        }
        return null;
    }

    @Override
    public Map<String, Object> getUserAccount(@NonNull String hrCode) {
        Map<String, Object> map = hrMemberDao.queryAccountByCode(hrCode, null);
        if (!MapUtil.isEmpty(map)) {
            if (map.get("enable_disable") != null) {
                String enableDisable = map.get("enable_disable").toString();
                //禁用
                if ("0".equals(enableDisable)) {
                    log.warn("人员状态{}被禁用，不能获取账号信息！{}", hrCode, map);
                    throw new BusinessException("A0401", LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg5", null) + hrCode + LocaleUtil.getMessage("OpenApiConnectionServiceImpl.result.msg6", null));
                }
            }
        }
        return map;
    }

}

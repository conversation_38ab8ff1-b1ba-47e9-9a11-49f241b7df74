package cn.hy.auth.common.security.core.properties;

import lombok.Getter;
import lombok.Setter;

/**
 * 类描述：spring security 类型登录后响应的格式配置
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 15:27
 **/
@Getter
@Setter
public class SecurityResponseFormatProperties {

    private String responseFormat = ResponseFormat.JSON;

    /**
     * @return 响应是否为json的格式
     */
    public boolean isJsonFormat() {
        return ResponseFormat.JSON.equals(this.responseFormat);
    }

    private static class ResponseFormat {
        /**
         * json 格式
         */
        private static final String JSON = "JSON";
        /**
         * HTML 网页格式
         */
        private static final String HTML = "HTML";

        private ResponseFormat() {
        }
    }
}

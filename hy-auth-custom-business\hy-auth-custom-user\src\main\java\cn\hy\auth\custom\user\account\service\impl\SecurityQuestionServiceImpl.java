package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.enums.UserAccountLockType;
import cn.hy.auth.custom.common.enums.UserResetPasswordType;
import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.user.account.dao.UserSecurityQuestionDao;
import cn.hy.auth.custom.user.account.domain.CheckUpdatePasswordDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import cn.hy.auth.custom.user.account.service.ForgetPasswordService;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class SecurityQuestionServiceImpl extends ForgetPasswordService implements ValidateErrorService {
    private final static String prefix = "SECURITY_QUESTION_";

    protected String ERROR_NUMBER_PREFIX_SECURITY = ERROR_NUMBER_PREFIX + "SECURITY_QUESTION_";

    @Autowired
    private UserSecurityQuestionDao userSecurityQuestionDao;

    @Autowired
    private UserAccountLockService userAccountLockService;

    @Autowired
    private AppConfigParamUtil appConfigParamUtil;

    @Autowired
    private ForgetPasswordCacheManage cacheManage;

    @Override
    public Map<String, Object> doCheck(CheckUpdatePasswordDTO checkUpdatePasswordDTO) {
        Map<String, Object> resultMap = new HashMap<>();
        String message = "";
        String tempPermissionIdentification = "";
        String userName = checkUpdatePasswordDTO.getUserName();
        Long securityQuestionId = checkUpdatePasswordDTO.getSecurityQuestionId();
        String answer = checkUpdatePasswordDTO.getAnswer();
        boolean result= false;
        // 查询是否有锁定记录
        Map<String, Object> judgeMap = userAccountLockService.isUserAccountLockByUserNameOrMobile(userName);
        Boolean judge = (Boolean) judgeMap.get("judge");
        Boolean userExist = (Boolean) judgeMap.get("userExist");
        String userId = Objects.toString(judgeMap.get("userId"), "");
        if (!userExist) {
            resultMap.put("tempPermissionIdentification", tempPermissionIdentification);
            resultMap.put("message", LocaleUtil.getMessage("SecurityQuestionServiceImpl.result.msg1", ""));
            resultMap.put("result", result);
            return resultMap;
        } else if (judge) {
            resultMap.put("tempPermissionIdentification", tempPermissionIdentification);
            resultMap.put("message", LocaleUtil.getMessage("SecurityQuestionServiceImpl.result.msg2", ""));
            resultMap.put("result", result);
            return resultMap;
        }
        answer = AesUtil.encryptByDefaultKey(answer);

        String errorNUmberCacheKey = ERROR_NUMBER_PREFIX_SECURITY + userId;
        Object ifPresent = cacheManage.get(errorNUmberCacheKey);
        if (ifPresent == null) {
            LocalDate tomorrow = LocalDate.now().plusDays(1);
            LocalDateTime tomorrowMidnight = LocalDateTime.of(tomorrow, LocalTime.MIDNIGHT);
            Date date = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
            cacheManage.put(errorNUmberCacheKey, 0, date);
        }
        Integer errorNumber = ifPresent == null ? 0 : (Integer) ifPresent;

        int count = userSecurityQuestionDao.selectOne(userName, securityQuestionId, answer);
        result = count == 1;
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        if (result) {
            tempPermissionIdentification = generateTempIdentification(paramMap, userId, userName);
            cacheManage.invalidate(errorNUmberCacheKey);
            message = LocaleUtil.getMessage("SecurityQuestionServiceImpl.result.msg3", "");
        } else {
            Object errorLimitObj = appConfigParamUtil.getByKey(paramMap.get("lessee_code"), paramMap.get("app_code"), "sysdevForgetPassworSecuLimit");
            Integer errorLimit = errorLimitObj == null ? 5 : Integer.parseInt(errorLimitObj.toString());

            int totalErrorNumber = errorNumber + 1;
            cacheManage.put(errorNUmberCacheKey, totalErrorNumber);
            message = String.format(LocaleUtil.getMessage("SecurityQuestionServiceImpl.result.msg4", ""), totalErrorNumber, errorLimit - totalErrorNumber);
            if (totalErrorNumber >= errorLimit) {
                message = LocaleUtil.getMessage("SecurityQuestionServiceImpl.result.msg5", "");
                userAccountLockService.lockUserAccount(userName, "", UserAccountLockType.SECURITY_QUESTION.getType());
                cacheManage.invalidate(errorNUmberCacheKey);
            }
        }
        resultMap.put("tempPermissionIdentification", tempPermissionIdentification);
        resultMap.put("message", message);
        resultMap.put("result", result);
        return resultMap;
    }

    @Override
    public Map<String, Object> sendToThirdService(String type, String account, SmsLoginVO smsLoginVO) {
        return new HashMap<>();
    }

    @Override
    public String getCheckType() {
        return UserResetPasswordType.SECURITY_QUESTION.getType();
    }

    @Override
    public boolean checkTempPermission(UserLoginAccountDTO userLoginAccountDTO, String tempPermissionIdentification) {
        return doCheckTempPermission(userLoginAccountDTO, tempPermissionIdentification);
    }

    @Override
    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {
        cacheManage.invalidate(ERROR_NUMBER_PREFIX_SECURITY + userId);
    }
}

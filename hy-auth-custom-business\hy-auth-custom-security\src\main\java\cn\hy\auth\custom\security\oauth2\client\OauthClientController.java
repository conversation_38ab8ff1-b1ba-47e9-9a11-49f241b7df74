package cn.hy.auth.custom.security.oauth2.client;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：清理缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/8 15:24
 **/
@RestController
@RequestMapping("/oauthClient/cache/clear")
public class OauthClientController {
    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;

    public OauthClientController(HyClientDetailServiceProvider hyClientDetailServiceProvider) {
        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;
    }


    @GetMapping("")
    public String clearCache(){
        hyClientDetailServiceProvider.clearCache();
        return "success";
    }

}

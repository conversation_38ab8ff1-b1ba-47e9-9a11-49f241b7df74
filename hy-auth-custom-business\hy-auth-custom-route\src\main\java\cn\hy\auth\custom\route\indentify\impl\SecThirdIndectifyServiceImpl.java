package cn.hy.auth.custom.route.indentify.impl;

import cn.hy.auth.custom.route.indentify.ThirdIndentifyClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 类描述：用于获取2.X系统TOKEN标识
 *
 * <AUTHOR>
 * @date 2022/11/17 14:12
 **/
@Service
@Slf4j
public class SecThirdIndectifyServiceImpl implements ThirdIndentifyClient {

    @Override
    public String getThirdIndentify(String token) {
         log.info("THIRD REQUEST------> 2.x");
         return "2.X";
    }
}

package cn.hy.auth.custom.security.third.external.fky;

import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAccessToken;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalProviderUser;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.log.util.OkHttpUtils;
import cn.hy.auth.custom.security.third.external.base.ExternalRequestUrl;
import cn.hy.auth.custom.security.third.external.base.GetExternalRequest;
import cn.hy.auth.custom.security.third.external.base.IExternalUserInfo;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 佛科院地址定义
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Component
@Slf4j
public class FkyUserInfoAdapter implements IExternalUserInfo {

    @Override
    public ExternalProviderUser getUserInfo(ExternalAccessToken accessToken) {
        ExternalRequestUrl requestBean = GetExternalRequest.getRequestBean(accessToken.getProviderId());
        if (requestBean == null || requestBean.getUserInfoUrl() == null) {
            throw new AuthBusinessException("500", LocaleUtil.getMessage("FkyUserInfoAdapter.result.msg1", null) + accessToken.getProviderId());
        }
        String userInfoUrl = requestBean.getUserInfoUrl();
        String suffix = userInfoUrl.contains("?") ? "&access_token=" + accessToken.getAccessToken() : "?access_token=" + accessToken.getAccessToken();
        userInfoUrl += suffix;
        ExternalProviderUser externalProviderUser;
        try {
            String postResult = OkHttpUtils.get(userInfoUrl);
            if (postResult != null) {
                externalProviderUser = new ExternalProviderUser();
                JSONObject resObj = JSONObject.parseObject(postResult);
                log.info("调用第三方【{}】接口请求到的用户信息结果：{}", accessToken.getProviderId(), resObj);
                String userId = resObj.getString("id");
                Object attributes = resObj.get("attributes");
                 if(attributes!=null){
                     JSONObject attrObj = JSONObject.parseObject(JSONObject.toJSONString(attributes));
                     externalProviderUser.setUserInfoJson(attrObj.toJSONString());
                     externalProviderUser.setUserName(attrObj.getString("cn"));
                 }
                externalProviderUser.setUserId(userId);
                externalProviderUser.setUserAccountName(userId);
                externalProviderUser.setLesseeCode(accessToken.getLesseeCode());
                externalProviderUser.setAppCode(accessToken.getAppCode());
                return externalProviderUser;
            } else {
                log.error("调用第三方接口查询用户信息失败：{}", postResult);
                throw new AuthBusinessException("500", LocaleUtil.getMessage("FkyUserInfoAdapter.result.msg2", null));
            }
        } catch (Exception e) {
            log.error("调用第三方接口查询用户信息失败：{}", e);
            throw new AuthBusinessException("500", LocaleUtil.getMessage("FkyUserInfoAdapter.result.msg3", null) + e.getMessage() + "】");
        }
    }
}

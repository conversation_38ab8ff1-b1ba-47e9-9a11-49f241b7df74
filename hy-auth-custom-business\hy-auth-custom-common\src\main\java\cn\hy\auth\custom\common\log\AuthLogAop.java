package cn.hy.auth.custom.common.log;

import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.log.annotation.ConfigLog;
import cn.hy.auth.custom.common.log.domain.LogDTO;
import cn.hy.auth.custom.common.log.enums.OperationResultEnum;
import cn.hy.auth.custom.common.log.event.LogEvent;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.common.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * 类描述: 配置平台日志切面工具类
 *
 * <AUTHOR>
 * @date ：创建于 2020/8/4
 */
@Slf4j
@Aspect
@Component
public class AuthLogAop {

    private final ApplicationContext applicationContext;
    private final UserDetailsService userDetailsService;
    /**
     * 切点
     */
    private static final String POINTCUT = "@annotation(cn.hy.auth.custom.common.log.annotation.ConfigLog)";

    public AuthLogAop(ApplicationContext applicationContext, UserDetailsService userDetailsService) {
        this.applicationContext = applicationContext;
        this.userDetailsService = userDetailsService;
    }

    /**
     * 日志切面入库
     *
     * @param joinPoint 切点
     * @return 返回对象
     * @throws Throwable 异常
     */
    @Around(POINTCUT)
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        //请求前打印
        LogDTO logWithBlobDTO = createLogDto(joinPoint);
        Object result;
        try {
            // 执行目标方法,获得返回值
            result = joinPoint.proceed();
            //默认请求成功的
            logWithBlobDTO.setOperationResult(OperationResultEnum.SUCCESS.getMsg());
            logWithBlobDTO.setOperationResultDesc("操作成功");
        } catch (Exception e) {
            //默认请求成功的
            logWithBlobDTO.setOperationResult(OperationResultEnum.FAILURE.getMsg());
            logWithBlobDTO.setOperationResultDesc(e.getMessage());
            //异常不处理，统一异常拦截器处理
            throw e;
        } finally {
            //请求后打印
            try {
                LocalDateTime endTime = LocalDateTime.now();
                //结束时间
                logWithBlobDTO.setEndTime(endTime);
                //耗时
                logWithBlobDTO.setElapsedTime(String.valueOf(Duration.between(logWithBlobDTO.getBeginTime(), endTime).toMillis()));
                ConfigLog passLog = getAnnotation(joinPoint);
                boolean isSave = passLog != null && Optional.of(passLog.isSave()).orElse(true);
                String token = TokenUtil.getTokenID();
                if (StringUtils.isNoneBlank(token)){
                    String[] tokenParts = token.split("\\.");
                    if (tokenParts.length > 3){
                        // 从token中截取到clientType信息
                        logWithBlobDTO.setClientType(tokenParts[2]);
                    }
                }
                applicationContext.publishEvent(new LogEvent(this, logWithBlobDTO,isSave, token));
            } catch (Exception e) {
                log.error("日志记录切面处理响应结果异常，没有发出日志事件，没有成功记录日志。LogDTO：{}",logWithBlobDTO, e);
            }

        }
        return result;
    }

    /**
     * 请求前打印
     *
     * @param joinPoint 切面
     * @return 返回日志对象
     */
    private LogDTO createLogDto(ProceedingJoinPoint joinPoint) {
        LogDTO logWithBlobDTO = new LogDTO();
        logWithBlobDTO.setDataVersion(1L);


        // 获取请求相关信息
        try {
            //业务流水号
            logWithBlobDTO.setTransactionId(getIncreaseUuid());
            //开始时间
            logWithBlobDTO.setBeginTime(LocalDateTime.now());
            //操作人信息
            logWithBlobDTO.setUserAccount(LoginUtil.getLoginName());
            // TODO 这里会请求用户表和角色表，多请求了一次。要优化
            try {
                HyUserDetails hyUserDetails = (HyUserDetails) userDetailsService.loadUserByUsername(LoginUtil.getLoginName());

                    logWithBlobDTO.setCreateUserId(hyUserDetails.getUserId());
                    logWithBlobDTO.setUserId(String.valueOf(hyUserDetails.getUserId()));

            } catch (Exception e){
                logWithBlobDTO.setCreateUserId(9999L);
            }

            //租户
            logWithBlobDTO.setLesseeCode(AuthContext.getContext().getLesseeCode());
            //应用
            logWithBlobDTO.setAppCode(AuthContext.getContext().getAppCode());

            //获取参数对象
            setRequestParam(logWithBlobDTO);

            ConfigLog passLog = getAnnotation(joinPoint);
            if (null != passLog) {
                //操作类型
                logWithBlobDTO.setOperationType(passLog.operationType().getMsg());
                //操作类型
                logWithBlobDTO.setModuleName(passLog.moduleName().getMsg());
                String operationObj = passLog.operationObj();
                //操作对象
                logWithBlobDTO.setOperationObj(operationObj);
            }
        } catch (Exception e) {
            log.error("日志切面错误:", e);
        }

        return logWithBlobDTO;
    }

    /**
     * 请求后打印
     *
     * @param joinPoint      切点
     * @param result         请求返回对象
     * @param logWithBlobDTO 日志对象
     */
    private void analysisResult(ProceedingJoinPoint joinPoint, Object result, LogDTO logWithBlobDTO) {

    }

    /**
     * 获得指定注解类
     *
     * @param joinPoint 切面
     * @return 返回直接类对象
     */
    private <T extends Annotation> T getAnnotation(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        return method != null ? method.getAnnotation((Class<T>) ConfigLog.class) : null;
    }

    /**
     * 获取请求参数
     *
     * @param logWithBlobDTO 日志对象
     */
    private void setRequestParam(LogDTO logWithBlobDTO) {
        // 获取当前的HttpServletRequest对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            // 请求全路径
            logWithBlobDTO.setUrl(request.getRequestURI());
            logWithBlobDTO.setQueryString(request.getQueryString());
            // IP地址
            logWithBlobDTO.setIp(IpUtils.getIpAddress(request));

            logWithBlobDTO.setRequiredMethod(request.getMethod());
            // 设置请求参数,安全考虑，不保存用户的请求参数
            //String requestParam = request.getRequestParam();
            //if (StringUtils.isNotBlank(requestParam)) {
               // logWithBlobDTO.setRequiredParam(requestParam);
           // }
        }
    }

    /***
     *  获取增长式的32位uuid
     * @return 32位uuid
     */
    public static   String getIncreaseUuid() {
        return System.currentTimeMillis() + getUuid(19);
    }

    /**
     * 获取指定长度的UUID
     *
     * @param length 长度
     * @return uuid
     */
    public static   String getUuid(int length) {
        return UUID.randomUUID().toString().replace("-", "").substring(0, length);
    }

}

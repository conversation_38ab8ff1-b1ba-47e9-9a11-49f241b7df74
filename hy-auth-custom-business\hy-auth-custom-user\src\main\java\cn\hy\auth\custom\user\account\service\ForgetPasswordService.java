package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.user.account.domain.CheckUpdatePasswordDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public abstract class ForgetPasswordService {

    public final static String ERROR_NUMBER_PREFIX = "ERROR_NUMBER_";

    public final static String PERMISSION_PREFIX = "TEMP_PERMISSION_";

    public abstract Map<String, Object> doCheck(CheckUpdatePasswordDTO checkUpdatePasswordDTO);

    public abstract Map<String, Object> sendToThirdService(String type, String account, SmsLoginVO smsLoginVO);

    public abstract String getCheckType();

    public abstract boolean checkTempPermission(UserLoginAccountDTO userLoginAccountDTO, String tempPermissionIdentification);

    protected String generateTempIdentification(Map<String, String> paramMap, String userId, String userName) {
        String lesseeCode = paramMap.get("lessee_code");
        String appCode = paramMap.get("app_code");
        Long time = System.currentTimeMillis() + 5 * 60 * 1000;
        return doGenerateTempIdentification(lesseeCode, appCode, userId, userName, time.toString());
    }
    
    protected boolean doCheckTempPermission(UserLoginAccountDTO dto, String tempPermissionIdentification) {
        if (StringUtils.isBlank(tempPermissionIdentification)) {
            return false;
        }
        String decrypt = AesUtil.decrypt(tempPermissionIdentification, AesUtil.DEFAULT_KEY);
        String[] split = decrypt.split("-");
        if (split.length != 5) {
            return false;
        }
        String time = split[4];
        if (Long.valueOf(time) < System.currentTimeMillis()) {
            return false;
        }
        String identification = doGenerateTempIdentification(dto.getLesseeCode(), dto.getAppCode(), dto.getId().toString(), dto.getUserAccountName(), time);
        return tempPermissionIdentification.equals(identification);
    }

    protected String doGenerateTempIdentification(String lesseeCode, String appCode, String userId, String userName, String time) {
        StringBuilder builder = new StringBuilder();
        builder.append(lesseeCode);
        builder.append("-");
        builder.append(appCode);
        builder.append("-");
        builder.append(userId);
        builder.append("-");
        builder.append(userName);
        builder.append("-");
        builder.append(time);
        return AesUtil.encrypt(builder.toString(), AesUtil.DEFAULT_KEY);
    }

}

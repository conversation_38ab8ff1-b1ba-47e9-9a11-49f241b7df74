package cn.hy.auth.common.security.oauth2.event;

import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

/**
 * 类描述：token 注销事件
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 10:56
 **/
@ToString
public class TokenRevokedEvent extends ApplicationEvent {


    /**
     * -- GETTER --
     *   注销结果
     *
     */
    @Getter
    private final boolean revokeToken;

    private final OAuth2Authentication oAuth2Authentication;
    @Getter
    private final OAuth2AccessToken oAuth2AccessToken;

    public TokenRevokedEvent(String tokenValue, boolean revokeToken, OAuth2Authentication oAuth2Authentication, OAuth2AccessToken oAuth2AccessToken) {
        super(tokenValue);
        this.revokeToken = revokeToken;
        this.oAuth2Authentication = oAuth2Authentication;
        this.oAuth2AccessToken = oAuth2AccessToken;
    }

    /**
     * refresh token 请求前的值
     */
    public String getTokenValue() {
        return (String) getSource();
    }

    /**
     * 获取认证信息
     *
     * @return .
     */
    public OAuth2Authentication getOAuth2Authentication() {
        return oAuth2Authentication;
    }

}

package cn.hy.auth.custom.user.account.mapper;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthUserAccountMapping;
import cn.hy.auth.custom.user.utils.BaseH2Test;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.concurrent.atomic.AtomicBoolean;

public class DynamicUserAccountMapperTest extends BaseH2Test {

    @Autowired
    private DynamicUserAccountMapper userAccountMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    //region 为了排除掉的影响
    @MockBean
    private CacheManager cacheManager;
    @MockBean
    private UserDetailsService UserDetailsService;
    //endregion

    private final AtomicBoolean isFirst = new AtomicBoolean(true);

    @Before
    public void setUp() throws Exception {
        String sql = "CREATE TABLE IF NOT EXISTS user_account ( id BIGINT (20) NOT NULL, " +
                "user_account_name VARCHAR (32) DEFAULT NULL, login_name VARCHAR (32) DEFAULT NULL," +
                "password VARCHAR (32) DEFAULT NULL, PRIMARY KEY (id))";
        jdbcTemplate.execute(sql);

        if (isFirst.getAndSet(false)) {
            jdbcTemplate.execute(
                    "Insert into user_account(id,user_account_name,login_name,password) values(1,'张三','zhangsan','1'),(2,'cp','cp1','1')");
        }

        AuthContext.getContext().appAuthStrategy().setUserAccountMapping(new AppAuthUserAccountMapping());
        AuthContext.getContext().appAuthStrategy().getUserAccountMapping().setTableName("user_account");
        AuthContext.getContext().appAuthStrategy().getUserAccountMapping().setUid("id");
        AuthContext.getContext().appAuthStrategy().getUserAccountMapping().setPassword("password");
        AuthContext.getContext().appAuthStrategy().getUserAccountMapping().setUsername("user_account_name");
    }

    @Test
    public void selectById() {
        UserAccountDTO accountDTO = userAccountMapper.selectById(2L);

        Assertions.assertThat(accountDTO).isNotNull();
        Assertions.assertThat(accountDTO.getUserAccountName()).isEqualTo("cp");
    }

    @Test
    public void selectByUserName() {
        UserAccountDTO accountDTO = userAccountMapper.selectByUserName("cp");

        Assertions.assertThat(accountDTO).isNotNull();
        Assertions.assertThat(accountDTO.getId()).isEqualTo(2L);
    }

    @Test
    public void updatePassword() {
        userAccountMapper.updatePassword(2L, "2");

        UserAccountDTO accountDTO = userAccountMapper.selectById(2L);
        Assertions.assertThat(accountDTO).isNotNull();
        Assertions.assertThat(accountDTO.getPassword()).isEqualTo("2");
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.multi.authinfo.dao.AppAuthStrategyDao">

    <select id="getAppAuthStrategyJson" resultType="java.lang.String">
        Select config_content
        from auth_config limit 1
    </select>
    <select id="checkFieldInUserAccount"  resultType="java.lang.String" >
        Select ${field} from user_account WHERE id = 1;
    </select>

</mapper>
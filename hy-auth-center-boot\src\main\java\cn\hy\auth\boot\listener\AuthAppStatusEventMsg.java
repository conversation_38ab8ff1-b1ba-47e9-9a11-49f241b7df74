package cn.hy.auth.boot.listener;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @title: AuthAppStatusEventMsg
 * @description: 认证中心应用状态变更事件消息对象
 * @date 2024/7/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthAppStatusEventMsg {

    /**
     * 消息主键（uuid）
     */
    private String id;

    /**
     * 租户编码
     */
    private String lesseeCode;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 应用状态：0禁用、1启用、2已卸载
     */
    private Integer appStatus;

}

package cn.hy.auth.custom.security.verifycode.domain;

import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: SliderCaptchaTrackDTO
 * @time: 2022-11-08 14:10
 * @desc: 滑块验证码入参DTO
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SliderCaptchaTrackDTO {

    /**
     * 滑动条验证参数
     */
    private ImageCaptchaTrack sliderCaptchaTrack;

    /**
     * 验证码ID
     */
    private String id;

    /**
     * 登陆账号
     */
    private String loginName;

    /**
     * 租户编号
     */
    private String lesseeCode;

    /**
     * 应用编号
     */
    private String appCode;
}

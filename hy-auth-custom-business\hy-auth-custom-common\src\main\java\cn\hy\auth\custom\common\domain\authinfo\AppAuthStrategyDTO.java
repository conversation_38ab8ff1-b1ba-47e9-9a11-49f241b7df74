package cn.hy.auth.custom.common.domain.authinfo;


import lombok.Data;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 认证规则信息
 *
 * <AUTHOR>
 * @date 2020-11-23 15:32
 **/
@Data
public class AppAuthStrategyDTO implements Serializable {
    private static final long serialVersionUID = 6404116616523241881L;
    /**
     * 租户访问号
     */
    @NonNull
    private String lesseeAccessName;
    /**
     * 应用访问号
     */
    @NonNull
    private String appAccessName;
    /**
     * 登录规则
     */
    @NonNull
    private AppAuthLoginRuleDTO loginRule;
    /**
     * 登出规则
     */
    @NonNull
    private AppAuthLogoutRuleDTO logoutRule;
    /**
     * license规则
     */
    @NonNull
    private AppAuthLicenseRuleDTO licenseRule;
    /**
     * 密码规则
     */
    @NonNull
    private AppAuthPwdRuleDTO pwdRule;
    /**
     * 用户账户信息表映射关系
     */
    @NonNull
    private AppAuthUserAccountMapping userAccountMapping;

    /**
     * 用户扩展信息表映射关系
     */
    @NonNull
    private List<AppAuthUserInfoMapping> userInfoMapping;

    public AppAuthStrategyDTO() {
        loginRule = new AppAuthLoginRuleDTO();
        logoutRule = new AppAuthLogoutRuleDTO();
        licenseRule = new AppAuthLicenseRuleDTO();
        pwdRule = new AppAuthPwdRuleDTO();
        userAccountMapping = new AppAuthUserAccountMapping();
        userInfoMapping = new ArrayList<>();
    }

    public void setLoginRule(AppAuthLoginRuleDTO loginRule) {
        if(CollectionUtils.isNotEmpty(loginRule.getLoginField())){
            loginRule.getLoginField().sort(Comparator.comparing(AppAuthLoginFieldDTO::getSort));
        }
        this.loginRule = loginRule;
    }
}

package cn.hy.auth.common.security.core.authentication.social.bean;

import java.util.Date;

/**
 * 类描述：第三方服务提供商中创建应用的访问token
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/9 16:13
 **/
public interface ProviderAppAccessToken {
    /**
     * 响应码
     * @return .
     */
    int getErrCode();

    /**
     * 提示语
     * @return .
     */
    String getErrMsg();

    /**
     *  token
     *  @return .
     */
    String getAccessToken();

    /**
     * 过期时间
     * @return .
     */
    Date getExpiresTime();

    /**
     * 存活时间，秒为单位
     * @return .
     */
    int getExpiresIn();


}

package cn.hy.auth.custom.user.account.service.impl;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthPwdUpdatePolicyDTO;
import cn.hy.auth.custom.common.domain.authinfo.PasswordUpdateMode;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.user.account.service.PwdExpirationAndForceModifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static cn.hy.auth.custom.common.enums.AuthErrorCodeEnum.C0319;

/**
 * <AUTHOR>
 * @date 2022/6/13 17:19
 **/
@Slf4j
@Service
public class PwdExpirationAndForceModifyServiceImpl implements PwdExpirationAndForceModifyService {

    public final static String APP_MODE = "app";
    public final static String USER_MODE = "user";
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";



    @Override
    public boolean isExpiration(HyUserDetails account) {
        AppAuthPwdUpdatePolicyDTO policy = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdUpdatePolicy();
        if (policy==null || ! policy.isEnable()) {
            return false;
        }
        if (policy.getPwdUpdateMode() == null) {
            return false;
        }
        String mode = policy.getPwdUpdateMode().getMode();
        AuthAssertUtils.isNotBlank(policy.getPwdUpdateMode().getMode(), C0319.code(), LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.assertUtil.msg1", null));
        switch (mode) {
            case APP_MODE:
                return appModeHandle(policy, account);
            case USER_MODE:
                return userModeHandle(policy, account);
            default:
                throw new IllegalArgumentException(LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.result.msg1", null));
        }
    }

    private boolean userModeHandle(AppAuthPwdUpdatePolicyDTO policy, HyUserDetails account) {
        PasswordUpdateMode pwdUpdateMode = policy.getPwdUpdateMode();
        PasswordUpdateMode.User user = pwdUpdateMode.getUser();
        AuthAssertUtils.isNotNull(user,C0319.code(),LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.assertUtil.msg2", null));
        String pwdLiftTimeUnit = user.getPwdLiftTimeUnit();

        //查最近修改时间、时间间隔  与当前时间比较
        String pwdLastChangeTimeFiled = policy.getPwdLastChangeTimeFiled();
        String pwdLiftTimeField = user.getPwdLiftTimeField();

        Map<String, Object> returnMap = account.getAccount();
        Object pwdLastChangeTimeFieldObj = returnMap.get(pwdLastChangeTimeFiled);
        if (Objects.isNull(pwdLastChangeTimeFieldObj)){
            log.error("pwdLastChangeTimeFiled查出为null，请检查字段与配置的是否一样以及数据是否null");
            return true;
        }
        String lastUpdateTimeStr = getDateStr(pwdLastChangeTimeFieldObj);
        Object pwdLiftTimeFieldObj = returnMap.get(pwdLiftTimeField);
        int interval = 30;
        if (Objects.isNull(pwdLiftTimeFieldObj)){
            log.debug("pwdLiftTimeField查出为null，设置默认为30");
        }else {
            interval =Integer.parseInt(pwdLiftTimeFieldObj.toString());
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getUpdateDateByTimeStr(lastUpdateTimeStr));
        Date expirationTime = getExpirationTime(calendar, interval, pwdLiftTimeUnit);

        //最近修改时间+间隔时间<当前时间---》过期
        return expirationTime.before(new Date());
    }

    private Date getUpdateDateByTimeStr(String lastUpdateTimeStr) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_TIME_PATTERN);
        try {
            return dateFormatter.parse(lastUpdateTimeStr);
        } catch (ParseException e) {
            throw new IllegalArgumentException(String.format(LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.result.msg2", null), lastUpdateTimeStr));
        }
    }

    private boolean appModeHandle(AppAuthPwdUpdatePolicyDTO policy, HyUserDetails account) {
        String pwdLastChangeTimeFiled = policy.getPwdLastChangeTimeFiled();
        PasswordUpdateMode pwdUpdateMode = policy.getPwdUpdateMode();
        //查最近修改时间  与当前时间比较
        PasswordUpdateMode.App app = pwdUpdateMode.getApp();
        AuthAssertUtils.isNotNull(app,C0319.code(),LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.assertUtil.msg3", null));
        Integer interval = app.getPwdLiftTime();
        String pwdLiftTimeUnit = app.getPwdLiftTimeUnit();
        Integer expireRemindThreshold = app.getExpireRemindThreshold();

        Map<String, Object> timeMap = account.getAccount();
        Object pwdLastChangeTimeFieldObj = timeMap.get(pwdLastChangeTimeFiled);
        if (Objects.isNull(pwdLastChangeTimeFieldObj)){
            log.error("pwdLastChangeTimeFiled查出为null，请检查字段与配置的是否一样以及数据是否null");
            return true;
        }
        String lastUpdateTimeStr = getDateStr(pwdLastChangeTimeFieldObj);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getUpdateDateByTimeStr(lastUpdateTimeStr));
        Date expirationTime = getExpirationTime(calendar, interval, pwdLiftTimeUnit);

        //最近修改时间+间隔时间<当前时间---》过期
        return expirationTime.before(new Date());
    }

    private String getDateStr(Object input) {
        if (input == null) {
            throw new IllegalArgumentException(LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.result.msg3", null));
        }
        if (input instanceof LocalDateTime) {
            return format((LocalDateTime) input);
        } else if (input instanceof Date) {
            return format((Date) input);
        } else {
            log.error(String.format("无法处理的数据类型：%s", String.valueOf(input)));
            return input.toString();
        }
    }
    // 将LocalDateTime对象格式化为字符串
    private static String format(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        return localDateTime.format(formatter);
    }

    // 将Date对象格式化为字符串
    private static String format(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_TIME_PATTERN);
        return formatter.format(date);
    }

    private Date getExpirationTime(Calendar calendar, Integer interval, String pwdLiftTimeUnit) {
        calendar.add(calendarField(pwdLiftTimeUnit), interval);
        return calendar.getTime();
    }

    private int calendarField(String pwdLiftTimeUnit) {
        switch (pwdLiftTimeUnit) {
            case "year":
                return Calendar.YEAR;
            case "month":
                return Calendar.MONTH;
            case "week":
                return Calendar.WEEK_OF_YEAR;
            case "day":
                return Calendar.DAY_OF_YEAR;
            default:
                throw new IllegalArgumentException(String.format(LocaleUtil.getMessage("PwdExpirationAndForceModifyServiceImpl.result.msg4", null), pwdLiftTimeUnit));
        }
    }

}

2024-07-04 15:00:00.034 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 14:59:38,553 to 2024-07-04 15:00:00,014
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.70| 20.00%|             5|             1|             0|             0|       30.0|         58
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 15:15:00.012 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 15:00:00,014 to 2024-07-04 15:15:00,011
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 15:23:12.610 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_hy
2024-07-04 15:23:12.612 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_hy是否需要验证码，失败次数0
2024-07-04 15:23:12.613 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 15:23:12.613 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 15:23:12.658 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_hy】的密码状态正常。
2024-07-04 15:23:12.674 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-04 15:23:12.679 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_oauth_client_details]的加载时间：3ms
2024-07-04 15:23:12.790 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_user_login_info]的加载时间：2ms
2024-07-04 15:24:10.777 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:24:10.779 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:24:10.779 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:25:20.832 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:24.851 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-172fb5bd-0d95-447c-9626-59c7d7c061ec is not valid.
2024-07-04 15:25:25.432 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:25:25.432 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:25:25.433 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:25:27.226 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:27.226 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:44.966 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 6
2024-07-04 15:25:44.966 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:25:44.966 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:25:44.966 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:44.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:44.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:25:44.976 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:44.976 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:44.977 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:25:44.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 8
2024-07-04 15:25:44.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:44.989 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:45.051 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  c.hy.metadata.engine.api.cache.CacheManageProvider - 清除结果：md_table-hy_test151-{"resultCode":"SUCCESS","success":true}
2024-07-04 15:25:45.051 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  c.hy.metadata.engine.api.cache.CacheManageProvider - 清除结果：md_table-hy_test151-{"resultCode":"SUCCESS","success":true}
2024-07-04 15:25:45.052 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 清空了缓存信息,cache hy_test151_auth_info
2024-07-04 15:25:45.052 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 清空了缓存信息,cache hy_test151_auth_info
2024-07-04 15:25:45.053 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:45.062 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:25:45.062 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:25:45.062 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:25:45.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:25:47.992 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:25:47.994 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:47.994 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:25:47.994 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:25:48.000 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 9
2024-07-04 15:25:48.001 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:25:48.002 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 9
2024-07-04 15:25:48.002 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:25:48.004 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:25:48.006 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:25:48.100 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-3e134b50-de38-4f77-a1a1-30c6bdbee65f is not valid.
2024-07-04 15:25:48.101 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:25:48.101 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:25:48.101 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:25:55.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:25:55.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:25:56.407 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:26:07.967 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:26:07.968 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:26:07.968 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:26:09.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 10
2024-07-04 15:26:09.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:26:09.664 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:26:09.665 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:26:09.665 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:26:09.666 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:26:09.666 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:26:10.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:26:19.897 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:26:19.898 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:26:19.897 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:26:21.471 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:26:21.471 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:26:29.533 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 11
2024-07-04 15:26:29.534 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:26:46.092 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:26:46.092 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:26:46.092 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:27:19.287 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=1227972921, epoch=2839) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 15:30:44.535 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:30:44.535 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:30:44.541 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 15:15:00,011 to 2024-07-04 15:30:44,536
cache                         |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_field                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_property             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_clips                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition_relationship|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_custom                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dict                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dynamic_field         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table                      |      0.01| 28.57%|            14|             4|             0|             0|        2.2|          3
md_table_be_fk                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_treeTable_ext              |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 15:30:44.541 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 14
2024-07-04 15:30:44.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:44.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:30:44.544 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:47.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-babe6d8b-f703-4026-a812-c1d5fe90e669 is not valid.
2024-07-04 15:30:47.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-6a58dae3-3864-4f08-9266-325923853460 is not valid.
2024-07-04 15:30:47.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:47.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:47.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:47.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:47.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:30:47.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:30:50.558 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:30:50.559 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:50.559 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:30:50.559 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:30:50.564 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-04 15:30:50.564 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-04 15:30:50.564 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-04 15:30:50.567 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:30:50.567 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:30:50.568 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:30:50.570 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:30:50.571 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 15:30:50.572 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:42:23.374 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:42:23.381 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数0
2024-07-04 15:42:23.381 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 15:42:23.381 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 15:42:26.671 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 默认密码检查-->验证用户【hy_test2】的密码为默认密码，请立即修改！
2024-07-04 15:42:26.704 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=paas, pwd_encryption_type=3, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_test2, app_code=sys} 登录失败. {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"} 
2024-07-04 15:45:00.018 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 15:30:44,536 to 2024-07-04 15:45:00,016
cache                         |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_field                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_property             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_clips                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition_relationship|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_custom                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dict                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dynamic_field         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table                      |      0.01|100.00%|            12|            12|             0|             0|        0.0|          0
md_table_be_fk                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_treeTable_ext              |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 15:48:54.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=961983839, epoch=2017) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 15:48:54.952 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:48:54.952 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:48:54.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=1151758294, epoch=4867) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 15:48:54.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=1384101466, epoch=4851) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 15:48:54.952 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:48:55.015 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:48:55.015 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:48:55.076 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:48:58.033 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-3312d067-56ec-4399-ad4e-66e0daa27774 is not valid.
2024-07-04 15:48:58.034 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-5b8b0241-916b-4105-b4b1-2433ee332c19 is not valid.
2024-07-04 15:48:58.034 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:48:58.034 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:48:58.035 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:48:58.035 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:48:58.035 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:48:58.035 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:48:58.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 17
2024-07-04 15:48:58.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 17
2024-07-04 15:48:58.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:48:58.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:48:58.045 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:48:58.045 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:48:58.080 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-4efb2533-db3c-4d5f-bfd6-7dbb68a29e38 is not valid.
2024-07-04 15:48:58.081 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:48:58.081 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:48:58.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:49:01.056 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:01.061 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 18
2024-07-04 15:49:01.061 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:49:01.061 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 18
2024-07-04 15:49:01.061 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:49:01.061 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 18
2024-07-04 15:49:01.062 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:49:01.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 15:49:01.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:49:01.065 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:49:29.552 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:29.552 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:29.552 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:30.016 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:30.017 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:30.016 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:43.717 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:43.717 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:43.717 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:43.720 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:43.720 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:49:43.830 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:44.220 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:44.220 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:49:46.840 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-4c58cc95-7342-4080-86c7-793e3dc14196 is not valid.
2024-07-04 15:49:46.841 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:49:46.841 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:49:46.841 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:46.847 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 20
2024-07-04 15:49:46.847 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:49:46.851 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-c9576047-6f81-4fca-a940-9ec604833006 is not valid.
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-4abe904d-f9e8-446a-9693-5176fb497527 is not valid.
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:47.224 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:49.862 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:49:49.862 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:49:49.862 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:49:49.862 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:49:49.868 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 21
2024-07-04 15:49:49.869 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 21
2024-07-04 15:49:49.869 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 21
2024-07-04 15:49:49.869 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:49:49.869 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:49:49.870 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:49:49.873 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:49:49.873 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:49:49.873 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 15:50:45.884 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:50:45.884 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数0
2024-07-04 15:50:45.885 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 15:50:45.885 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 15:50:45.894 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=paas, pwd_encryption_type=3, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_test2, app_code=sys} 登录失败. 用户名或密码错误 
2024-07-04 15:50:49.128 [paas,sys,/login,,ddadf718fed7497e8ab2bfe8891c2652] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:50:49.128 [paas,sys,/login,,ddadf718fed7497e8ab2bfe8891c2652] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数1
2024-07-04 15:51:06.496 [paas,sys,/login,,4b696a98b90642a5afb19a06d62a6464] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:51:06.497 [paas,sys,/login,,4b696a98b90642a5afb19a06d62a6464] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数1
2024-07-04 15:51:11.689 [,,,,Not Auth Request!] [Thread-45] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-04 15:51:11.690 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.694 [,,,,Not Auth Request!] [Thread-58] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=5432&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.694 [,,,,Not Auth Request!] [Thread-58] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=5432&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.695 [,,,,Not Auth Request!] [Thread-58] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5432&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.703 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 15:51:11.709 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.713 [,,,,Not Auth Request!] [Thread-58] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5432&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.726 [,,,,Not Auth Request!] [Thread-58] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:11.747 [,,,,Not Auth Request!] [Thread-58] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:25.372 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 15:51:25.375 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 15:51:25.532 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 15:51:25.563 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 15:51:27.699 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 15:51:27.699 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 15:51:27.789 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85ms. Found 12 repository interfaces.
2024-07-04 15:51:27.872 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 15:51:27.874 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 15:51:27.875 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 15:51:27.876 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 15:51:27.877 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 15:51:28.168 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 15:51:28.169 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 15:51:28.356 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 15:51:28.357 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 15:51:28.357 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 15:51:28.358 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 15:51:28.358 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 15:51:28.358 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 15:51:28.358 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 15:51:28.359 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 15:51:28.359 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 15:51:28.359 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 15:51:28.360 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 15:51:28.360 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 15:51:28.366 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 181ms. Found 0 repository interfaces.
2024-07-04 15:51:28.573 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 15:51:28.573 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 15:51:28.573 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 15:51:28.573 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 15:51:28.573 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 15:51:28.801 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:28.801 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:28.843 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:28.844 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:28.847 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 15:51:28.982 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 15:51:29.119 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$5e22e98c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.186 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$8034702e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.249 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$6341cd4d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.359 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$96d89b20] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.380 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$15dee098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.411 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8a70d809] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:29.525 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a68adb06] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 15:51:30.141 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 15:51:30.159 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 15:51:30.169 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 15:51:30.169 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 15:51:30.294 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 15:51:30.295 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4708 ms
2024-07-04 15:51:30.511 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 15:51:30.526 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 15:51:30.842 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 15:51:30.860 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@13da6bc9
2024-07-04 15:51:31.114 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 15:51:32.776 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 15:51:34.552 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 15:51:34.825 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 15:51:34.985 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 15:51:35.119 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 15:51:35.121 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 15:51:35.522 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 15:51:35.773 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 15:51:36.995 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 15:51:37.444 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 15:51:40.016 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 15:51:40.215 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 15:51:40.365 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:8, workerId:6
2024-07-04 15:51:41.745 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 15:51:41.745 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 15:51:42.404 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 15:51:42.404 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 15:51:43.525 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 15:51:43.525 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-04 15:51:45.059 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-04 15:51:45.060 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 15:51:45.284 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@524b1c70, org.springframework.security.web.context.SecurityContextPersistenceFilter@567a2954, org.springframework.security.web.header.HeaderWriterFilter@7e8b070e, org.springframework.security.web.authentication.logout.LogoutFilter@bb6bf68, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@37ca8b4c, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@21a8998c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5dbae6f7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3d741969, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@420cf372, org.springframework.security.web.session.SessionManagementFilter@143a4506, org.springframework.security.web.access.ExceptionTranslationFilter@12c9803d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@11786a43]
2024-07-04 15:51:45.337 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@2f21994d, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ddeada6, org.springframework.security.web.context.SecurityContextPersistenceFilter@1763a8a1, org.springframework.security.web.header.HeaderWriterFilter@17a65cb1, org.springframework.security.web.authentication.logout.LogoutFilter@2988933b, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@23a24fea, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@52f950e2, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@2ee17b74, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@b0e7bde, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@28466003, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@10cd5b30, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@82b778b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2fb37f92, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@a55ed01, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@70832ddb, org.springframework.security.web.session.SessionManagementFilter@bb51961, org.springframework.security.web.access.ExceptionTranslationFilter@21833d72, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56d1efec]
2024-07-04 15:51:45.356 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7963a94b, org.springframework.security.web.context.SecurityContextPersistenceFilter@6337c48d, org.springframework.security.web.header.HeaderWriterFilter@3dacb927, org.springframework.security.web.csrf.CsrfFilter@6e48c5b2, org.springframework.security.web.authentication.logout.LogoutFilter@24ad12ff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44640a84, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c59f6ae, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1db7acc9, org.springframework.security.web.session.SessionManagementFilter@5d7b361, org.springframework.security.web.access.ExceptionTranslationFilter@1006fe59]
2024-07-04 15:51:45.572 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 15:51:46.072 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-04 15:51:47.680 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-04 15:51:47.844 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-04 15:51:48.229 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-04 15:51:48.309 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-04 15:51:48.320 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://**************:18500}]
2024-07-04 15:51:48.326 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-04 15:51:48.337 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-04 15:51:48.346 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-04 15:51:48.736 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 15:51:48.874 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 15:51:48.874 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 15:51:49.153 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 15:51:49.171 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 15:51:49.177 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 15:51:49.177 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 15:51:49.182 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 15:51:49.186 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 15:51:49.191 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 15:51:49.191 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 15:51:49.191 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 15:51:49.192 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 15:51:49.198 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 15:51:49.198 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 15:51:49.198 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 15:51:49.201 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 15:51:49.201 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 15:51:49.202 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:51:49.202 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:51:49.205 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 15:51:49.207 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 15:51:49.207 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:51:49.214 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:51:49.233 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:51:49.241 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 24
2024-07-04 15:51:49.241 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 24
2024-07-04 15:51:49.241 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 24
2024-07-04 15:51:49.243 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:51:49.243 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:51:49.243 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:51:49.252 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.252 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.260 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:51:49.260 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 15:51:49.260 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:51:49.529 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.679 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.705 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.722 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.722 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.767 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.767 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.812 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.878 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.879 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=21128&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.887 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.906 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.957 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.959 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.960 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.355 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.383 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.432 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@24ff6769; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.482 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.493 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.493 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:50.509 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-04 15:51:50.541 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-04 15:51:50.623 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 28.401 seconds (JVM running for 30.512)
2024-07-04 15:51:50.629 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-04 15:51:50.629 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-04 15:51:50.634 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-04 15:51:50.634 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-04 15:51:50.657 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：*******************************************************************************************************************************************************************************,username = iotmp
2024-07-04 15:51:50.986 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-04 15:51:50.987 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-04 15:51:51.089 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 102 ms
2024-07-04 15:52:24.680 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：54ms
2024-07-04 15:52:24.909 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】
2024-07-04 15:52:24.917 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:52:24.918 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数0
2024-07-04 15:52:24.921 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 15:52:24.921 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 15:52:24.985 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_test2】的密码状态正常。
2024-07-04 15:52:24.995 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-04 15:52:24.998 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_oauth_client_details]的加载时间：3ms
2024-07-04 15:52:27.012 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_user_login_info]的加载时间：3ms
2024-07-04 15:53:38.174 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.174 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.174 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:53:38.293 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:38.293 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:38.293 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-1a2ac23f-699e-4ff1-8a0d-5e41d899cb19 is not valid.
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-55ae786e-51cc-403d-90ce-823e9f18dabd is not valid.
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-cfcd122d-0889-4ae7-bb6c-e7c9d4132c11 is not valid.
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:53:41.302 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:53:41.303 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:53:41.303 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:53:41.303 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:53:41.304 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:53:41.306 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:53:41.312 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 27
2024-07-04 15:53:41.312 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 27
2024-07-04 15:53:41.313 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 27
2024-07-04 15:53:41.313 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:53:41.313 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:53:41.313 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:53:41.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:53:41.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 15:53:41.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:53:47.452 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_test2
2024-07-04 15:53:47.452 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_test2是否需要验证码，失败次数0
2024-07-04 15:53:47.452 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-04 15:53:47.452 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-04 15:53:49.050 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_test2】的密码状态正常。
2024-07-04 15:54:25.827 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:54:25.827 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:54:25.828 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:54:25.829 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:54:25.829 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:54:25.829 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:54:25.829 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:54:25.830 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:54:25.830 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:55:21.130 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:21.130 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:21.134 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-8031766a-2751-4c66-a0af-7c716e39f35c is not valid.
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-34e08ac5-5038-402d-b98a-bdb97563e7df is not valid.
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-b57cb445-19f9-4d6f-8f57-7b4d98ed1665 is not valid.
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:55:41.360 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:55:41.376 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:43.446 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:43.446 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:55:43.448 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:55:43.448 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:55:43.448 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:55:43.448 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:55:43.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:55:43.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:55:43.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:55:43.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:55:43.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:55:44.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:56:24.496 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 30
2024-07-04 15:56:24.497 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 30
2024-07-04 15:56:24.497 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 30
2024-07-04 15:56:24.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:56:24.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:56:24.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:57:27.905 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:57:27.910 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 33
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 33
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 33
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:57:27.916 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:57:46.256 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:57:46.722 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:57:47.159 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:57:47.159 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:57:47.159 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:57:47.615 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:58:58.663 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:58:58.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:58:58.663 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:58:58.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:58:58.664 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:58:58.666 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:58:58.666 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 15:58:58.671 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 35
2024-07-04 15:58:58.671 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:58:58.677 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:58:58.779 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:58:58.780 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 15:58:58.780 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 15:58:58.780 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 15:58:58.780 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:59:01.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:59:01.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:59:01.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 15:59:01.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:59:01.685 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 36
2024-07-04 15:59:01.685 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 36
2024-07-04 15:59:01.685 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:59:01.686 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:59:01.688 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:59:01.689 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:59:01.791 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-4f6cf6cf-99e0-4931-a75a-aa4932f23e5c is not valid.
2024-07-04 15:59:01.791 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 15:59:01.791 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 15:59:01.791 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:59:04.696 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:59:04.696 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 15:59:04.696 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 15:59:04.697 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 15:59:04.697 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:59:04.697 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1]
2024-07-04 15:59:04.697 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:59:04.697 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 15:59:04.700 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 37
2024-07-04 15:59:04.701 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 15:59:04.701 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 37
2024-07-04 15:59:04.701 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 37
2024-07-04 15:59:04.701 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 15:59:04.701 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 15:59:04.703 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 15:59:04.705 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 15:59:04.705 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]

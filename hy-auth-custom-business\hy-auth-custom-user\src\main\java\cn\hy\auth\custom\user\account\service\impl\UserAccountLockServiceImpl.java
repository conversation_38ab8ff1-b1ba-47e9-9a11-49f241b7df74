package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthAccountLockPolicyDTO;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.user.account.dao.UserAccountLockDao;
import cn.hy.auth.custom.user.account.dao.UserLoginFailureDao;
import cn.hy.auth.custom.user.account.domain.UserAccountLock;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginFailure;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.id.IdWorker;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类描述：用户账号锁定服务
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/4/14 11:49
 **/
@Service
@AllArgsConstructor
public class UserAccountLockServiceImpl implements UserAccountLockService, ValidateErrorService {
    private final UserAccountLockDao userAccountLockDao;
    private final UserLoginFailureDao userLoginFailureDao;
    private final IdWorker idWorker;
    private final UserAccountService userAccountService;
    private final ConcurrentHashMap<String, Object> parallelLockMap = new ConcurrentHashMap<>();
    @Override
    public UserLoginFailure saveLoginFail(Long userId, String username) {
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){
            return null;
        }
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();
        // 保存登录失败次数和相关信息，
        synchronized (getSerialNoRecordLoadingLock(lesseeCode+appCode+username)){
            UserLoginFailure userLoginFailure = userLoginFailureDao.selectByUid(userId);
            if (userLoginFailure == null){
                userLoginFailure = UserLoginFailure.builder()
                        .id(new BigDecimal(idWorker.nextId()))
                        .createUserId(new BigDecimal(userId))
                        .lastUpdateUserId(new BigDecimal(userId))
                        .createTime(new Date())
                        .lastUpdateTime(new Date())
                        .loginTime(new Date())
                        .userId(new BigDecimal(userId))
                        .dataVersion("1")
                        .loginFailureCount(1L)
                        .build();
                userLoginFailureDao.insert(userLoginFailure);
            } else {
                // 判断是自增，还是从0开始输入
                // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）
                boolean restLoginFailureCount;
                if ("minuteTime".equals(accountLockPolicy.getLockType())){
                    // 指定分钟后自动恢复
                    String loginErrorThreshold = accountLockPolicy.getLoginErrorThreshold();
                    int loginErrorThresholdInt = Integer.parseInt(loginErrorThreshold);
                    restLoginFailureCount = DateUtil.compare(new Date(),DateUtils.addMinutes(userLoginFailure.getLoginTime(),loginErrorThresholdInt)) >= 0;
                } else {
                    // 次日之后可自动恢复
                    restLoginFailureCount = DateUtil.compare(DateUtil.parse(DateUtil.today()),userLoginFailure.getLoginTime()) >= 0;
                }
                updateUserLoginFailure(userLoginFailure,restLoginFailureCount);
            }
            // 判断是否需要锁定账号
            processLockAccount(userLoginFailure,accountLockPolicy);
            return userLoginFailure;
        }
    }

    private Object getSerialNoRecordLoadingLock(String identify) {
         return parallelLockMap.computeIfAbsent(identify, t -> new Object());
    }

    @Override
    public boolean isUserAccountLock(Long userId) {
        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){
            // 未启用
            return false;
        }
        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);
        if (userAccountLock ==null) {
            return false;
        }
        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())>= 0) {
            userAccountLockDao.deleteByUid(userId);
            // 已过锁定有效期
            return false;
        }
        return true;
    }

    @Override
    public UserAccountLock getUserAccountLock(Long userId) {
        // 1、判断是否启用账号锁定功能 2、判断锁定的记录是否已经超过有效期（是的话，清除记录）3、进行判断
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){
            // 未启用
            return null;
        }
        UserAccountLock userAccountLock = userAccountLockDao.selectByUid(userId);
        if (userAccountLock ==null) {
            return null;
        }
        if (DateUtil.compare(DateUtil.date(),userAccountLock.getLockEndTime())>= 0) {
            userAccountLockDao.deleteByUid(userId);
            // 已过锁定有效期
            return null;
        }
        return userAccountLock;
    }

    @Override
    public void clearLoginFail(Long userId, String username) {
        AppAuthAccountLockPolicyDTO accountLockPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy();
        if (!Boolean.TRUE.equals(accountLockPolicy.getEnable())){
            // 未启用
            return ;
        }
        userLoginFailureDao.deleteByUid(userId);

    }

    @Override
    public void lockUserAccount(String userName, String phone, String lockType) {
        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(userName, phone);
        UserAccountLock lock = new UserAccountLock();
        lock.setId(new BigDecimal(idWorker.nextId()));
        lock.setCreateUserId(new BigDecimal(1));
        lock.setCreateUserName("admin");
        lock.setCreateTime(new Date());
        lock.setDataVersion("1");

        lock.setLockEndTime(DateUtil.parse("2099-01-01"));
        lock.setClientType("4");
        lock.setLastUpdateUserId(new BigDecimal(1L));
        lock.setLastUpdateTime(new Date());
        lock.setSequence(1L);
        lock.setUserId(new BigDecimal(userByUserNameOrPhone.getId()));
        lock.setLockStartTime(new Date());
        lock.setLastUpdateUserName("admin");
        lock.setLockRecodeType(lockType);
        userAccountLockDao.insert(lock);
    }

    @Override
    public Map<String, Object> isUserAccountLockByUserNameOrMobile(String phoneNumber) {
        UserLoginAccountDTO userByUserNameOrPhone = userAccountService.getUserByUserNameOrPhone(phoneNumber, phoneNumber);
        Map<String, Object> map = new HashMap<>();
        map.put("userId", "");
        if (userByUserNameOrPhone == null) {
            map.put("userExist", false);
            map.put("judge", false);
            return map;
        }
        boolean result = isUserAccountLock(userByUserNameOrPhone.getId());
        map.put("judge", result);
        map.put("userExist", true);
        map.put("userId", userByUserNameOrPhone.getId());
        map.put("userAccountName", userByUserNameOrPhone.getUserAccountName());
        return map;
    }


    private void processLockAccount(UserLoginFailure userLoginFailure,AppAuthAccountLockPolicyDTO accountLockPolicy){
        if (userLoginFailure.getLoginFailureCount() >= Integer.parseInt(accountLockPolicy.getLoginErrorThreshold())){
            userAccountLockDao.deleteByUid(userLoginFailure.getUserId());
            UserAccountLock userAccountLock = UserAccountLock.builder()
                    .id(new BigDecimal(idWorker.nextId()))
                    .createUserId(userLoginFailure.getUserId())
                    .lastUpdateUserId(userLoginFailure.getUserId())
                    .userId(userLoginFailure.getUserId())
                    .ip(IpUtils.getIpAddress())
                    .lockStartTime(new Date())
                    .createTime(new Date())
                    .lastUpdateTime(new Date())
                    .dataVersion("1")
                    .clientType(AuthContext.getContext().loginState().getClientType().name())
                    .lockEndTime(getLockEndTime(accountLockPolicy))
                    .build();
            userAccountLockDao.insert(userAccountLock);
        }
    }
    private Date getLockEndTime(AppAuthAccountLockPolicyDTO accountLockPolicy) {
        // forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）
        if ("forever".equals(accountLockPolicy.getLockType())){
            // 锁定100年
            return DateUtils.addYears(DateUtil.parse(DateUtil.today()),100);
        }else if ("minuteTime".equals(accountLockPolicy.getLockType())){
            String lockMinute = accountLockPolicy.getLockMinute();
            int lockMinuteInt = Integer.parseInt(lockMinute);
            return DateUtils.addMinutes(new Date(),lockMinuteInt);
        }else {
            // 次日
            return DateUtils.addDays(DateUtil.parse(DateUtil.today()),1);
        }
    }


    private void updateUserLoginFailure(UserLoginFailure userLoginFailure, boolean restLoginFailureCount){
        if (restLoginFailureCount){
            // 可以从1开始了
            userLoginFailure.setLoginFailureCount(1L);
        }else {
            // 自增
            Long loginFailureCount = userLoginFailure.getLoginFailureCount();
            if (loginFailureCount==null){
                loginFailureCount = 0L;
            }
            loginFailureCount++;
            userLoginFailure.setLoginFailureCount(loginFailureCount);
        }
        userLoginFailure.setLoginTime(new Date());
        userLoginFailure.setLastUpdateTime(new Date());
        userLoginFailureDao.updateByPrimaryKey(userLoginFailure);
    }

    @Override
    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {
        userLoginFailureDao.resetFailCount(userId);
    }

    @Override
    public Map<String, Object> isUserAccountLockByEmail(String email) {
        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);
        Map<String, Object> map = new HashMap<>();
        map.put("userId", "");
        if (userLoginAccountDTO == null) {
            map.put("userExist", false);
            map.put("judge", false);
            return map;
        }
        boolean result = isUserAccountLock(userLoginAccountDTO.getId());
        map.put("judge", result);
        map.put("userExist", true);
        map.put("userId", userLoginAccountDTO.getId());
        map.put("userAccountName", userLoginAccountDTO.getUserAccountName());
        return map;
    }

    @Override
    public void lockUserAccountByEmail(String email, String lockType) {
        UserLoginAccountDTO userLoginAccountDTO = userAccountService.getUserByUserEmail(email);
        UserAccountLock lock = new UserAccountLock();
        lock.setId(new BigDecimal(idWorker.nextId()));
        lock.setCreateUserId(new BigDecimal(1));
        lock.setCreateUserName("admin");
        lock.setCreateTime(new Date());
        lock.setDataVersion("1");
        lock.setLockEndTime(DateUtil.parse("2099-01-01"));
        lock.setClientType("4");
        lock.setLastUpdateUserId(new BigDecimal(1L));
        lock.setLastUpdateTime(new Date());
        lock.setSequence(1L);
        lock.setUserId(new BigDecimal(userLoginAccountDTO.getId()));
        lock.setLockStartTime(new Date());
        lock.setLastUpdateUserName("admin");
        lock.setLockRecodeType(lockType);
        userAccountLockDao.insert(lock);
    }

}

package cn.hy.auth.custom.security.log.domain;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * hy_8test8_auth_sys_log_custom_handle
 * 认证中心日志自定义处理配置表
 * <AUTHOR>
 * @date   2022/06/06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AuthSysLogHandle {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    /**
     * 请求地址
     */
    private String uri;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否启用
     */
    private Integer enable;

    /**
     * script
     */
    private String script;
}
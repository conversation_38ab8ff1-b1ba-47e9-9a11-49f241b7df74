package cn.hy.auth.common.security.core.properties;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/11 10:29
 */
@Data
@Component
public class ReAuthenticationProperties {

    private final String authProcessingUrl = "/login/re-auth";

    private final String usernameParameter = "username";
    private final String clientNameParameter = "client_name";
    private final String onlyTokenParameter = "only_token";

    private final String browserId = "browser_id";
    private final String pageId = "page_id";

    /**
     * 口令认证
     */
    private final String passwordParameter = "password";

    /**
     * UsbKey 认证
     */
    private final String signatureParameter = "signature"; // 签名
}

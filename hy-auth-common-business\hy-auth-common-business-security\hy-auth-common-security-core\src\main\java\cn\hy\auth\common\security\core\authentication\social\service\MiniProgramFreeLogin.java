package cn.hy.auth.common.security.core.authentication.social.service;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;

/**
 * <AUTHOR>
 * @Date 2023/7/6
 */
public interface MiniProgramFreeLogin {

    String getPhoneNumber(ProviderAppAccessToken providerAppAccessToken,String code);

    /**
     * 绑定第三方账号
     * @param lesseeCode
     * @param appCode
     * @param providerId
     * @param userAccountName
     * @param provideruserId
     */
    void bingThirdAccount(String lesseeCode, String appCode, String providerId, String userAccountName, String provideruserId);

    /**
     * 添加临时账号
     * @param lesseeCode
     * @param appCode
     * @param userAccountName
     */
    void addUserAccount(String lesseeCode, String appCode, String userAccountName);

    /**
     * 修改账号名
     * @param lesseeCode
     * @param appCode
     * @param newAccountName
     * @param oldAccountName
     * @return
     */
    String updateUserAccountName(String lesseeCode, String appCode ,String newAccountName,String oldAccountName);
}

package cn.hy.auth.custom.route.service.impl;

import cn.hy.auth.custom.route.service.EnvironmentSymbolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 9:55
 **/
@Service
@Slf4j
public class EnvironmentSymbolServiceImpl implements EnvironmentSymbolService {

    /**
     * token主内容分隔符
     */
    private static final String TOKEN_MAIN_CONTENT_SPLIT = "-";

    /**
     * 环境标识前分割符
     */
    private static final String ENVIRONMENT_SYMBOL_PREFIX_SPLIT = "$";

    /**
     * 环境标识前分割符 (正则)
     */
    private static final String ENVIRONMENT_SYMBOL_PREFIX_SPLIT_REGEX = "\\$";

    @Override
    public String getEnvironmentSymbolFromToken(String token) {
        if (StringUtils.isEmpty(token)){
            return null;
        }
        if (token.split("\\.").length < 4) {
            if (log.isDebugEnabled()){
                log.debug("该token不是3.xToken，无法获取token中携带的环境标识，token：{}", token);
            }
            return null;
        }
        // 取出token的主内容
        String mainContent = token.split("\\.")[3];
        // 分割主内容，若>=5个则取第5个字符串，第5个字符串中“&&”后面的内容即为环境标识，若<5个则返回null
        String environmentSymbol = null;
        String[] contents = mainContent.split(TOKEN_MAIN_CONTENT_SPLIT);
        if (contents.length >= 5 && contents[4].contains(ENVIRONMENT_SYMBOL_PREFIX_SPLIT)){
            environmentSymbol = contents[4].split(ENVIRONMENT_SYMBOL_PREFIX_SPLIT_REGEX)[1];
        }
        return environmentSymbol;
    }
}

package cn.hy.auth.custom.user.account.controller;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.exception.Oauth2BusinessException;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.common.log.annotation.ConfigLog;
import cn.hy.auth.custom.common.log.enums.ModuleNameEnum;
import cn.hy.auth.custom.common.log.enums.OperationTypeEnum;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.user.account.service.OauthLogoutService;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述: 退出登录
 *
 * <AUTHOR>
 * @date ：创建于 2020/10/29
 */
@RequestMapping(value = "oauth")
@RestController
@Slf4j
public class OauthLogoutController {

    private final OauthLogoutService authBaseService;

    private final UserAccountService userAccountService;
    @Value("${hy.auth.environment:prod}")
    private String environment;

    public OauthLogoutController(OauthLogoutService authBaseService, UserAccountService userAccountService) {
        this.authBaseService = authBaseService;
        this.userAccountService = userAccountService;
    }

    /**
     * 删除token
     */
    @GetMapping(value = "revoke")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "登出", operationType = OperationTypeEnum.LOGOUT)
    public ResponseEntity<Map> revokeToken(Boolean forceLogout) {
        String tokenId = TokenUtil.getTokenID();
        if (StringUtils.isBlank(tokenId)) {
            throw new Oauth2BusinessException(LocaleUtil.getMessage("OauthLogoutController.result.msg1", null));
        }

        boolean realRevoke = ObjectUtils.notEqual("dev",environment)|| Boolean.TRUE.equals(forceLogout);
        if (realRevoke){
            authBaseService.revokeToken(tokenId);
        } else {
           log.info("开发环境，没有真正退出。tokenId:{},user_account_name:{}",tokenId,LoginUtil.getLoginName());
        }
        String username = LoginUtil.getLoginName();
        userAccountService.changeLoginInfoByUserNameList(Arrays.asList(username), UserOnLineStatus.OFFLINE);
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("result", LocaleUtil.getMessage("OauthLogoutController.controllerMap.msg1", ""));
        return new ResponseEntity(resultMap, HttpStatus.OK);
    }

}

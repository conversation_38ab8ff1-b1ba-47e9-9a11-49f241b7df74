package cn.hy.auth.custom.security.third.external.fky;

import cn.hy.auth.custom.security.third.external.base.ExternalRequestUrl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 佛科院地址定义
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Component
public class FkyIdsRequestUrl implements ExternalRequestUrl {

    @Value("${third.proxy.fky.url:https://authserver.fosu.edu.cn}")
    private String fkyBaseUrl;

    @Override
    public String getAccessTokenUrl() {
        return fkyBaseUrl + "/authserver/oauth2.0/accessToken";
    }

    @Override
    public String getUserInfoUrl() {
        return fkyBaseUrl + "/authserver/oauthApi/user/profile";
    }
}

package cn.hy.auth.custom.security.oauth2.client.dao;

import cn.hy.auth.custom.security.oauth2.client.domain.OauthClientDetailsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * hy_1_oauth_client_details
 * oauth客户端信息表 mapper
 *
 * <AUTHOR>
 * @date 2020/12/08
 */
@Mapper
public interface OauthClientDetailsMapper {

    /**
     * 根据clientId 查询记录
     *
     * @param clientId .
     * @return .
     */
    OauthClientDetailsDO selectByClientId(@Param("clientId") String clientId);

    /**
     *  应用级别的token配置
     * @return .
     */
    List<Map<String,Object>> selectAppTokenConfig();
    /**
     *  租户级别的token配置
     * @return .
     */
    List<Map<String,Object>> selectGlobalTokenConfig();
}
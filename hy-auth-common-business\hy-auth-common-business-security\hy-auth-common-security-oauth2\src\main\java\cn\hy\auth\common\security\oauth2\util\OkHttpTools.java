package cn.hy.auth.common.security.oauth2.util;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.constant.DefaultInitContants;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * @Author: hejiawei
 * @Description: OkHttp操作
 * @Date: 2022/4/24
 * @Version: 1.0
 */
@Component
@Slf4j
@AllArgsConstructor
public class OkHttpTools {

    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded");

    private final OkHttpClient okHttpClient;

    public OkHttpClient getOkHttpClient() {
        return okHttpClient;
    }

    /**
     * get方法请求
     *
     * @return
     */
    public String get(String url) {
        String responseBody = "";
        StringBuffer sb = new StringBuffer(url);
        Request request = new Request.Builder()
                .url(sb.toString())
                .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            int status = response.code();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            log.error("okhttp3 put error >> ex = {}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return responseBody;
    }

    /**
     * 同步post提交表单数据（包含文件）
     *
     * @param url
     * @param params
     * @return
     * @throws IOException
     */
    public  String post(String url, Map<String, Object> params) throws IOException {
        RequestBody body = RequestBody.create(JSON, JSONObject.toJSONString(params));
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Response response = okHttpClient.newCall(request).execute();
        return response.body().string();
    }

    /**
     * 同步post提交表单数据（包含文件）
     *
     * @param url
     * @param formBody
     * @return
     * @throws IOException
     */
    public  String postForm(String url, FormBody formBody) throws IOException {
       // MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        Response response = okHttpClient.newCall(request).execute();
        return response.body().string();
    }


    /**
     * post(Support form,json)
     * 支持delete、put等请求
     *
     * @param url    请求的url
     * @param params post form 提交的参数
     * @param params submitType  提交类型（form  or  json）
     * @return
     */
    public String post(String url, Map<String, Object> params, String submitType,
                       String methodType) {
        String responseBody = "";
        RequestBody body = null;
        if (submitType.equals(DefaultInitContants.FORM_TYPE)) {
            //form-data
            FormBody.Builder builder = new FormBody.Builder();
            if (params != null && params.keySet().size() > 0) {
                for (String key : params.keySet()) {
                    builder.add(key, (String) params.get(key));
                }
            }
            body = builder.build();
        } else if (submitType.equals(DefaultInitContants.JSON_TYPE)) {
            //json
            String jsonParams = JSONObject.toJSONString(params);
            body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonParams);
        } else {
            throw new RuntimeException(LocaleUtil.getMessage("OkHttpTools.result.msg1", null));
        }
        Request request = new Request.Builder()
                .url(url)
                .method(methodType, body)
                .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            log.error("okhttp3 post error >> ex = {}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return responseBody;
    }
}

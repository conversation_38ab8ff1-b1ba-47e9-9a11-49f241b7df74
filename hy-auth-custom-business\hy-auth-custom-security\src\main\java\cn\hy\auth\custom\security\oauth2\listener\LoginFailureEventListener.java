package cn.hy.auth.custom.security.oauth2.listener;

import cn.hy.auth.common.security.oauth2.event.LoginFailureEvent;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.LoginOutTypeEnum;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.security.oauth2.event.LoginFailureLogEvent;
import cn.hy.auth.custom.security.oauth2.userdetails.MyUserDetailsServiceImpl;
import cn.hy.auth.custom.user.account.service.impl.UserAccountServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

import static cn.hy.auth.custom.common.constant.LoginParamterConts.*;

/**
 * 类描述：登录失败事件监听器
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/26 12:19
 **/
@Component
@Slf4j
@AllArgsConstructor
public class LoginFailureEventListener implements ApplicationListener<LoginFailureEvent> {
    private static final String UNKNOWN_USER = "unknown_user";

    private final ApplicationContext applicationContext;
    private MyUserDetailsServiceImpl userDetailsServiceImpl;
    @Override
    public void onApplicationEvent(LoginFailureEvent loginFailureEvent) {
        log.debug("接收到登录失败事件:{}", loginFailureEvent.getAuthentication());
        logProcess(loginFailureEvent);
    }

    private void logProcess(LoginFailureEvent event) {
        String ip = IpUtils.getIpAddress();
        //记录登出日志
        UserLoginInfoDTO loginLogDTO = UserLoginInfoDTO.builder()
                .ip(ip)
                .currentIp(ip)
                .errorMessage(event.getException().getMessage())
                .type(LoginOutTypeEnum.LOGIN_FAILURE.getCode())
                .build();

        Authentication authentication = event.getAuthentication();
        if (authentication != null) {
            loginLogDTO.setUserAccountName(authentication.getName());
        } else {
            loginLogDTO.setUserAccountName(UNKNOWN_USER);
        }
        HttpServletRequest request = event.getRequest();
        if (request != null) {
            loginLogDTO.setMac(request.getParameter(LOGIN_MAC));
            loginLogDTO.setClientType(getClinetType(request));
            loginLogDTO.setClientId(request.getParameter(CLIENT_ID));
        }
        try {
            HyUserDetails userDetails = (HyUserDetails) userDetailsServiceImpl.loadUserByUsername(loginLogDTO.getUserAccountName());
            if (userDetails != null){
                loginLogDTO.setUserId(userDetails.getUserId());
                loginLogDTO.setCreateUserId(userDetails.getUserId());
                loginLogDTO.setLastUpdateUserId(userDetails.getUserId());
            } else {
                loginLogDTO.setUserId(0L);
                loginLogDTO.setCreateUserId(0L);
                loginLogDTO.setLastUpdateUserId(0L);
            }

            loginLogDTO.setLoginType(AuthContext.getContext().loginState().getLoginTypeString());

            //发布之前校验上下文信息
            if (StringUtils.isBlank(AuthContext.getContext().getLesseeCode()) ||
                    StringUtils.isBlank(AuthContext.getContext().getAppCode())) {
                log.error("记录登录日志失败，原因：租户或应用编码为空。日志内容：【{}】；上下文信息：【{}】", loginLogDTO, AuthContext.getContext());
                return;
            }
            //发出登出需要记录日志的事件
            applicationContext.publishEvent(new LoginFailureLogEvent(loginLogDTO));
        }catch (Exception e){
            log.info("记录登录失败日志发生错误，忽略不处理。{}",e.getMessage());
        }

    }

    private Integer getClinetType(HttpServletRequest request) {
        try {
            return request.getParameter(CLIENT_TYPE) != null ? Integer.parseInt(request.getParameter(CLIENT_TYPE)) : null;
        } catch (NumberFormatException e) {
            log.warn("CLIENT_TYPE 参数不正确，{} 无法解析为整数。", request.getParameter(CLIENT_TYPE), e);
        }
        return null;
    }
}

package cn.hy.auth.custom.user.account.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: UserLoginBindIpDTO
 * @description: 用户登陆绑定IP
 * @date 2024/1/17
 */
@Data
public class UserLoginBindIpDTO implements Serializable {

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 账号名
     */
    private String userAccountName;

    /**
     * 限制系统登录地址：0否 1是
     */
    private String restrictLoginAddress;

    /**
     * 用户账号主键
     */
    private BigDecimal userAccountId;

    /**
     * ip列表
     */
    private List<UserLoginBindIpSublistDTO> ipList;

}

db_url_ip: *********
db_port: 13306
db_url_name: hyap
db_ip: *********

server:
  port: 6060
  servlet:
    context-path: /
# mybatis
mybatis:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

spring:
  # jackson
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8h
    serialization:
      write-dates-as-timestamps: true
    # 驼峰格式
    property-naming-strategy: LOWER_CAMEL_CASE
  application:
    name: hy-auth
  # --- DataSource ---
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${db_url_ip}:${db_port}/${db_url_name}?serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&useSSL=false&rewriteBatchedStatements=true
    username: iotmp
    password: Hy@21nbHh
  #-----启用默认的banner------
  banner:
    location: classpath:config/banner.txt
  redis:
    database: 10
    host: 127.0.0.1
    port: 6379
    password: Hy123456
#----logging-----
logging:
  level:
    root: info
    cn.hy.dataengine: info
    cn.hy.auth: debug
#---snow id--
id:
  generator:
    data-zone-id: 1
    worker-id: 7

#不需要过滤的表规则,值多个的时候使用逗号分隔，允许正则匹配
metadata:
  redirect:
    exclude: auth_*
hy:
  security:
    oauth2:
      clientInMemory: true
      clients[0]:
        clientId: app
        clientSecret: 123456
        accessTokenValiditySeconds: 3000
    web:
      ignore:
        pattern:
          - /css/*
          - /hello/free
          - /code/sms
auth:
  cache:
    #缓存类型,caffeine(caffeine内存模式),redis(redis模式)
    type: caffeine
    #过期时间(s)
    expire_time: 6000
  #token缓存方式,jdbc(数据库模式)、jvm(内存模式)、redis(redis模式)、cacheJdbc(数据库+内存模式)
  token:
    store:
      type: jvm
package cn.hy.auth.multi.datasource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 类描述：针对修改操作起作用.修改是从事务管理器里取的dataSource
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/6/12 18:36
 **/
@Configuration
@EnableAspectJAutoProxy
public class TransactionManagersConfig {
    @Autowired
    EntityManagerFactory emf;
    @Autowired
    DataSource dataSource;
    @Bean(name = "transactionManager")
    public PlatformTransactionManager transactionManager() {
        JpaTransactionManager tm =
                new HyMultiDataSourceJpaTransactionManager();
        tm.setEntityManagerFactory(emf);
        tm.setDataSource(dataSource);
        return tm;
    }
}

package cn.hy.auth.custom.user.cache.service;

import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * 类描述: 用户缓存信息
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/27
 */
public interface UserCacheService {

    /**
     * 缓存用户信息
     *
     * @param userId 用户主键
     * @return 返回用户缓存信息
     */
    //UserAccountDTO cacheUser(@NotNull Long userId);

    /**
     * 按用户主键查询用户存数信息，没有找到则从数据库中获取并放入缓存
     *
     * @param userId 用户主键
     * @return 返回用户缓存信息
     */
    //UserAccountDTO getCacheByUserId(@NotNull Long userId);

    /**
     * 按登录账号查询用户信息，没有找到则从数据库中获取并放入缓存
     *
     * @param loginName 登录账号
     * @return 返回用户缓存信息
     */
    UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName);

    /**
     * 按登录账号查询用户主键，没有找到则从数据库中获取并放入缓存
     *
     * @param loginName 登录账号
     * @return 返回用户主键
     */
    //Long getUserIdByLoginName(@NotBlank String loginName);

    /**
     * 按用户主键移除缓存信息
     *
     * @param userId 用户主键
     */
    void removeCacheByUserId(@NotBlank Long userId);

    /**
     * 按登录账号移除缓存信息
     *
     * @param loginName 登录账号
     */
    void removeCacheByLoginName(@NotBlank String loginName);

    /**
     * 查询在线用户
     *
     * @return 返回在线用户的主键信息
     */
    Set<Long> getOnlineUserId();

    /**
     * 按tokenId查询用户登录数据
     *
     * @param tokenId ..
     * @return 返回登录信息
     */
    UserLoginInfoDTO getUserLoginInfoByTokenId(@NotBlank String tokenId);


}

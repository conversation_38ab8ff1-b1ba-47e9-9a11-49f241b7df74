package cn.hy.auth.common.security.oauth2.exception;

import cn.hy.auth.common.security.core.authentication.mobile.exception.AccountLockException;
import cn.hy.auth.common.security.core.authentication.mobile.exception.ErrorSmsCodeException;
import cn.hy.auth.common.security.core.authentication.mobile.exception.UserCheckException;
import cn.hy.auth.common.security.core.authentication.mobile.exception.UserNotExistException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;

/**
 * 自定义异常转换类
 * <AUTHOR>
 * @date 2021-01-06 15:37
 **/
@Slf4j
public class HyWebResponseExceptionTranslator implements WebResponseExceptionTranslator {
	@Override
	public ResponseEntity<OAuth2Exception> translate(Exception exception) throws Exception {
		log.warn(exception.getMessage(),exception);
		if (exception instanceof OAuth2Exception) {
			OAuth2Exception oAuth2Exception = (OAuth2Exception) exception;
			return ResponseEntity
					.status(oAuth2Exception.getHttpErrorCode())
					.body(new HyOAuth2Exception(oAuth2Exception.getMessage()));
		} else if (exception instanceof UserNotExistException
				|| exception instanceof AccountLockException
				|| exception instanceof UsernameNotFoundException
				|| exception instanceof UserCheckException) {
			return ResponseEntity
					.status(HttpStatus.OK)
					.body(new HyOAuth2Exception(exception.getMessage()));
		} else if (exception instanceof ErrorSmsCodeException) {
			ErrorSmsCodeException errorSmsCodeException = (ErrorSmsCodeException) exception;
			return ResponseEntity
					.status(HttpStatus.OK)
					.body(new HyOAuth2Exception(errorSmsCodeException.getMessage()));
		} else if(exception instanceof AuthenticationException){
			AuthenticationException authenticationException = (AuthenticationException) exception;
			return ResponseEntity
					.status(HttpStatus.UNAUTHORIZED)
					.body(new HyOAuth2Exception(authenticationException.getMessage()));
		}
		return ResponseEntity
				.status(HttpStatus.OK)
				.body(new HyOAuth2Exception(exception.getMessage()));
	}
}

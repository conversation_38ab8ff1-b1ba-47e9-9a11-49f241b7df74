package cn.hy.auth.custom.multi.authinfo.service;

import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * 应用的认证规则管理器
 *
 * <AUTHOR>
 * @date 2020-11-25 09:33
 **/
public interface AppAuthStrategyManager {

    /**
     * 获取应用对应的认证规则对象
     *
     * @return 认证规则对象
     */
    AppAuthStrategyDTO get();

    /**
     * 清除缓存
     * @return .
     */
    boolean removeFromCache(String lesseeCode,String appCode);

    /**
     *  清除租户下的所有应用的缓存
     * @param lessCode .
     */
    void clearCache(String lessCode);
}

package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.custom.user.account.domain.UserAccountLock;
import cn.hy.auth.custom.user.account.domain.UserLoginFailure;

import java.util.Map;

/**
 * 类描述：用户账号锁定
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/4/14 10:37
 **/
public interface UserAccountLockService {
    /**
     * .
     * @param userId .
     * @param username .
     * @return .
     */
    UserLoginFailure saveLoginFail(Long userId, String username);

    /**
     *  检查用户是否被锁定
     * @param userId .
     * @return true is locked
     */
    boolean isUserAccountLock(Long userId);

    /**
     *  清理登录失败记录
     * @param userId .
     * @param username .
     */
    void clearLoginFail(Long userId, String username);

    void lockUserAccount(String userName, String phone, String lockType);

    Map<String, Object> isUserAccountLockByUserNameOrMobile(String phoneNumber);

    UserAccountLock getUserAccountLock(Long userId);

    Map<String, Object> isUserAccountLockByEmail(String email);

    void lockUserAccountByEmail(String email, String lockType);

}

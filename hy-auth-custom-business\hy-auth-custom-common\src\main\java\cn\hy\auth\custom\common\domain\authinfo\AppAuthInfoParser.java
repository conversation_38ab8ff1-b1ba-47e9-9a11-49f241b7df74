package cn.hy.auth.custom.common.domain.authinfo;

import cn.hy.auth.custom.common.context.AuthContext;
import com.google.common.collect.Lists;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 认证规则解析器
 * 基于上下文AuthContext中的AppAuthInfoDTO对象，解析相关信息
 *
 * <AUTHOR>
 * @date 2020-12-03 17:45
 **/
public enum AppAuthInfoParser {
    /**
     * 模拟单例对象
     */
    INSTANCE;


    private AppAuthLoginRuleDTO getLoginRule() {
        return AuthContext.getContext().appAuthStrategy().getLoginRule();
    }

    /**
     * 登录竞争策略
     *
     * @return 登录竞争策略
     */
    @NonNull
    public AppAuthCompetitionPolicyDTO getLoginCompetitionPolicy() {
        return Optional.ofNullable(this.getLoginRule().getCompetitionPolicy())
                .orElse(new AppAuthCompetitionPolicyDTO());
    }

    /**
     * 支持登录的字段信息列表
     *
     * @return 登录字段信息列表
     */
    @NonNull
    public List<AppAuthLoginFieldDTO> getLoginFields() {
        return Optional.ofNullable(this.getLoginRule().getLoginField()).orElse(new ArrayList<>());
    }

    /**
     * 用户人员信息表描述信息
     *
     * @return 人员信息表描述信息
     */
    @NonNull
    public List<AppAuthUserInfoMapping> getUserInfoMapping() {
        return AuthContext.getContext().appAuthStrategy().getUserInfoMapping();
    }

    /**
     * 根据登录字段的正则表达式，匹配登录时传入的登录帐号，返回符合条件的字段列表
     *
     * @param accountName 用户输入的帐号
     * @return 符合条件的字段编码列表
     */
    public List<String> parseLoginFields(String accountName) {
        List<AppAuthLoginFieldDTO> loginFields = getLoginFields();
        if (StringUtils.isBlank(accountName) || CollectionUtils.isEmpty(loginFields)) {
            return Lists.newArrayList();
        }
        List<String> collect = loginFields.stream()
                .filter(f -> StringUtils.isBlank(f.getMatchRegExp()) ||
                        Pattern.matches(f.getMatchRegExp(), accountName))
                .sorted(Comparator.comparing(AppAuthLoginFieldDTO::getSort))
                .map(AppAuthLoginFieldDTO::getFiled)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
            return collect;
        }
        // fixQX202304070063 当为中文的账号名时，没有匹配的，默认选第一个
        return Collections.singletonList(loginFields.get(0).getFiled());
    }
}

package cn.hy.auth.custom.common.domain.authinfo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 用于登录的字段描述
 *
 * <AUTHOR>
 * @date 2020-12-03 14:02
 **/
@Data
@AllArgsConstructor
public class AppAuthLoginFieldDTO implements Serializable {
    private static final long serialVersionUID = 2649024367225381532L;
    /**
     * 字段编码
     */
    private String filed;
    /**
     * 用于匹配的正则表达式
     * 登录时，用登录的帐号匹配这个正则表达式，匹配成功，就说明这个帐号对应filed这个字段。
     */
    private String matchRegExp;
    /**
     * 排序
     */
    private Integer sort;
}

package cn.hy.auth.boot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;
import cn.hy.auth.custom.security.oauth2.client.HyClientDetailServiceProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 类描述：监听更新token配置信息，用于刷新更新缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2024/6/13 15:49
 **/
@Service
@Slf4j
public class TokenConfigChangedKafkaListener {
    private final HyClientDetailServiceProvider hyClientDetailServiceProvider;
    public TokenConfigChangedKafkaListener(HyClientDetailServiceProvider hyClientDetailServiceProvider) {
        this.hyClientDetailServiceProvider = hyClientDetailServiceProvider;
    }

    @KafkaListener(topics = "${hy.saas.tokenConfig.cluster-cache.topic:saas-app-token-config}",
            groupId = "#{authKafkaGroupIdUtil.buildSaasGroupId()}"
    )
    public void processAsyncTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional<Object> message = Optional.ofNullable(record.value());
        try {
            if (message.isPresent()) {
                String msg = (String)message.get();
                log.debug("消费了： Topic:{},Message:{}",record.topic(),msg);
                JSONObject jsonObject = JSON.parseObject(msg);
                String lessCode = (String) jsonObject.get("lessCode");
                String appCode = (String) jsonObject.get("appCode");
                try {
                    // 清除各种缓存
                    if (ObjectUtil.isEmpty(appCode)|| "appmanage".equals(appCode)){
                        hyClientDetailServiceProvider.clearCache(lessCode);
                    } else {
                        hyClientDetailServiceProvider.clearCache(lessCode,appCode);
                    }
                } catch (Exception e) {
                    log.warn("处理Kafka的token 配置變化消息{}_{},报错，可以忽略不处理。{}",lessCode,appCode,e.getMessage());
                }
            }
        }finally {
            ack.acknowledge();
        }
    }
}

package cn.hy.auth.custom.common.utils;

import com.mysql.cj.conf.ConnectionUrlParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import javax.annotation.Nonnull;

/**
 * 动态获取参数配置工具类
 *
 * <AUTHOR> by unknown
 * @date 2020/11/12 12:25
 **/
@Slf4j
public class PropertyUtil {

    public enum PropertyKey {
        /**
         * 数据库连接属性
         */
        DATA_SOURCE("spring.datasource.url");

        /**
         *
         */


        private String code;

        private Object defaultValue;

        PropertyKey(String code) {
            this.code = code;
            this.defaultValue = StringUtils.EMPTY;
        }

        public String getCode() {
            return code;
        }

        public Object getDefaultValue() {
            return defaultValue;
        }
    }

    public static String getPropertyValue(@Nonnull PropertyKey propertyKey) {
        return getPropertyValue(propertyKey, String.class);
    }

    public static <T> T getPropertyValue(@Nonnull PropertyKey propertyKey, Class<T> targetType) {
        Environment environment;

        try {
            environment = AuthSpringUtil.getBean(Environment.class);
            if (environment != null) {
                return environment.getProperty(propertyKey.getCode(), targetType, (T) propertyKey.getDefaultValue());
            }
        } catch (Exception e) {
            log.warn("Spring's Environment haven't been injected yet, will return default value");
        }
        return (T) propertyKey.getDefaultValue();
    }

    public static String getDbName() {
        String dataSourceUrl = PropertyUtil.getPropertyValue(PropertyUtil.PropertyKey.DATA_SOURCE);
        ConnectionUrlParser connectionUrlParser = ConnectionUrlParser.parseConnectionString(dataSourceUrl);
        return connectionUrlParser.getPath();
    }

}

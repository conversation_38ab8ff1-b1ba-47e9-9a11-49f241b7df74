2024-07-04 14:16:53.934 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:16:53.936 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:16:54.093 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:16:54.118 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:16:58.876 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:16:58.876 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:16:58.998 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108ms. Found 12 repository interfaces.
2024-07-04 14:16:59.007 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.007 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.007 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.009 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.010 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.010 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:16:59.010 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:16:59.184 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:16:59.185 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:16:59.186 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:16:59.187 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:16:59.188 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:16:59.646 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:16:59.648 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:16:59.893 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:16:59.894 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:16:59.894 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:16:59.894 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:16:59.894 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:16:59.895 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:16:59.895 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:16:59.895 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:16:59.895 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:16:59.896 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:16:59.896 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:16:59.897 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:16:59.902 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 235ms. Found 0 repository interfaces.
2024-07-04 14:17:00.085 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:17:00.220 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:17:00.220 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:17:00.220 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:17:00.220 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:17:00.221 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:17:00.522 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:17:00.522 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:17:00.581 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:17:00.582 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:17:00.584 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:17:00.732 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:17:00.850 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$6b184a26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:00.912 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$8d29d0c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:01.009 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$70372de7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:01.265 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a3cdfbba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:01.286 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$22d44132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:01.309 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$976638a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:01.411 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b3803ba0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:17:02.053 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:17:02.071 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:17:02.090 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:17:02.090 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:17:02.305 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:17:02.305 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 8137 ms
2024-07-04 14:17:02.527 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:17:02.541 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:17:02.870 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:17:02.870 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:17:02.885 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@1c175d38
2024-07-04 14:17:03.120 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:17:04.183 [,,,,Auth is starting] [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.BeanCreationException. Message: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
2024-07-04 14:17:04.226 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-04 14:17:04.457 [,,,,Auth is starting] [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.23/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.23/java.util.concurrent.locks.LockSupport.park(LockSupport.java:194)
 java.base@11.0.23/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2081)
 java.base@11.0.23/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:433)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.23/java.lang.Thread.run(Thread.java:829)
2024-07-04 14:17:04.458 [,,,,Auth is starting] [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2024-07-04 14:17:04.460 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-04 14:17:04.468 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-04 14:17:04.489 [,,,,Auth is starting] [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:157)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at cn.hy.auth.boot.AuthApplication.main(AuthApplication.java:41)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:125)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:86)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:427)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:181)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:154)
	... 6 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1303)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:235)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:193)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:188)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:170)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:89)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:261)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:234)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:54)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5139)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:831)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:423)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:928)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:455)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:106)
	... 11 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:184)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1295)
	... 52 common frames omitted
Caused by: java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:62)
	at org.springframework.security.oauth2.http.converter.jaxb.JaxbOAuth2ExceptionMessageConverter.<init>(JaxbOAuth2ExceptionMessageConverter.java:21)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.geDefaultMessageConverters(DefaultOAuth2ExceptionRenderer.java:117)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.<init>(DefaultOAuth2ExceptionRenderer.java:53)
	at org.springframework.security.oauth2.provider.error.AbstractOAuth2SecurityExceptionHandler.<init>(AbstractOAuth2SecurityExceptionHandler.java:42)
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.<init>(Oauth2SecurityExceptionHandler.java:18)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.<init>(AbstractAuthFilter.java:29)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.<init>(AppAuthInfoEixtFilter.java:31)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:172)
	... 54 common frames omitted
Caused by: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:278)
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:421)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662)
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:59)
	... 66 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader.loadClass(TomcatEmbeddedWebappClassLoader.java:70)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1186)
	at javax.xml.bind.ServiceLoaderUtil.nullSafeLoadClass(ServiceLoaderUtil.java:122)
	at javax.xml.bind.ServiceLoaderUtil.safeLoadClass(ServiceLoaderUtil.java:155)
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:276)
	... 70 common frames omitted
2024-07-04 14:25:07.534 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:25:07.536 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:25:07.596 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: *********
2024-07-04 14:25:07.610 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:45:45.832 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:45:45.835 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:45:45.950 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:45:45.972 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:45:48.389 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:45:48.389 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:45:48.459 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 65ms. Found 12 repository interfaces.
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:45:48.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:45:48.532 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:45:48.533 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:45:48.533 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:45:48.534 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:45:48.535 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:45:48.792 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:45:48.793 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:45:48.995 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:45:48.995 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:45:48.996 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:45:48.996 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:45:48.997 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:45:48.997 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:45:48.998 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:45:48.998 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:45:48.998 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:45:48.998 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:45:48.999 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:45:48.999 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:45:49.006 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 208ms. Found 0 repository interfaces.
2024-07-04 14:45:49.105 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:45:49.167 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:45:49.167 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:45:49.168 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:45:49.168 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:45:49.168 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:45:49.357 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:45:49.357 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:45:49.397 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:45:49.397 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:45:49.399 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:45:49.553 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:45:49.675 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$9169b858] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.718 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b37b3efa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.764 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$96889c19] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.846 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ca1f69ec] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.864 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$4925af64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.886 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bdb7a6d5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:49.977 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d9d1a9d2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:45:50.512 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:45:50.545 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:45:50.580 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:45:50.580 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:45:51.206 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:45:51.206 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5217 ms
2024-07-04 14:45:51.394 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:45:51.407 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:45:51.740 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:45:51.740 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:45:51.750 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4071aa13
2024-07-04 14:45:51.986 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:45:53.076 [,,,,Auth is starting] [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.BeanCreationException. Message: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
2024-07-04 14:45:53.097 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-04 14:45:53.245 [,,,,Auth is starting] [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.23/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.23/java.util.concurrent.locks.LockSupport.park(LockSupport.java:194)
 java.base@11.0.23/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2081)
 java.base@11.0.23/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:433)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.23/java.lang.Thread.run(Thread.java:829)
2024-07-04 14:45:53.247 [,,,,Auth is starting] [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2024-07-04 14:45:53.249 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-04 14:45:53.260 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-04 14:45:53.270 [,,,,Auth is starting] [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:157)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at cn.hy.auth.boot.AuthApplication.main(AuthApplication.java:41)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:125)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:86)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:427)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:181)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:154)
	... 6 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1303)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:235)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:193)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:188)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:170)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:89)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:261)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:234)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:54)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5139)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:831)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:423)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:928)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:455)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:106)
	... 11 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:184)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1295)
	... 52 common frames omitted
Caused by: java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:62)
	at org.springframework.security.oauth2.http.converter.jaxb.JaxbOAuth2ExceptionMessageConverter.<init>(JaxbOAuth2ExceptionMessageConverter.java:21)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.geDefaultMessageConverters(DefaultOAuth2ExceptionRenderer.java:117)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.<init>(DefaultOAuth2ExceptionRenderer.java:53)
	at org.springframework.security.oauth2.provider.error.AbstractOAuth2SecurityExceptionHandler.<init>(AbstractOAuth2SecurityExceptionHandler.java:42)
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.<init>(Oauth2SecurityExceptionHandler.java:18)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.<init>(AbstractAuthFilter.java:29)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.<init>(AppAuthInfoEixtFilter.java:31)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:172)
	... 54 common frames omitted
Caused by: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:278)
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:421)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662)
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:59)
	... 66 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader.loadClass(TomcatEmbeddedWebappClassLoader.java:70)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1186)
	at javax.xml.bind.ServiceLoaderUtil.nullSafeLoadClass(ServiceLoaderUtil.java:122)
	at javax.xml.bind.ServiceLoaderUtil.safeLoadClass(ServiceLoaderUtil.java:155)
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:276)
	... 70 common frames omitted
2024-07-04 14:47:37.134 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:37.148 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:47:37.164 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:37.168 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:47.376 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:47:47.380 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:47:47.507 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:47:47.533 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:47:49.651 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:47:49.651 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:47:49.724 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68ms. Found 12 repository interfaces.
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.730 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:47:49.731 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:47:49.788 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:47:49.788 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:47:49.789 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:47:49.790 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:47:49.790 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:47:50.061 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:47:50.062 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:47:50.190 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:47:50.190 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:47:50.190 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:47:50.191 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:47:50.191 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:47:50.191 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:47:50.192 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:47:50.192 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:47:50.192 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:47:50.192 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:47:50.193 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:47:50.193 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:47:50.197 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 130ms. Found 0 repository interfaces.
2024-07-04 14:47:50.316 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:47:50.396 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:47:50.396 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:47:50.396 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:47:50.396 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:47:50.397 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:47:50.610 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:50.611 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:50.653 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:50.654 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:47:50.656 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:47:50.792 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:47:50.894 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$78b0479f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:50.937 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$9ac1ce41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:50.985 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$7dcf2b60] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:51.065 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b165f933] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:51.083 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$306c3eab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:51.108 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a4fe361c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:51.218 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c1183919] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:47:51.669 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:47:51.711 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:47:51.719 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:47:51.720 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:47:51.853 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:47:51.853 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4304 ms
2024-07-04 14:47:52.033 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:47:52.048 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:47:52.315 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:47:52.315 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:47:52.322 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4a4d59c1
2024-07-04 14:47:52.520 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:47:53.768 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 14:47:54.948 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 14:47:55.232 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 14:47:55.482 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 14:47:55.612 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 14:47:55.615 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 14:47:55.880 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 14:47:56.093 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 14:47:57.206 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:47:57.689 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 14:48:00.429 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 14:48:00.565 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 14:48:00.706 [,,,,Auth is starting] [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInfoStatusController' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInfoStatusController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
2024-07-04 14:48:00.707 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2024-07-04 14:48:00.708 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:48:00.772 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-04 14:48:00.772 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-07-04 14:48:00.779 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-07-04 14:48:00.780 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-04 14:48:00.784 [,,,,Auth is starting] [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [slider-captcha-queue-0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.23/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.23/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:234)
 java.base@11.0.23/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2123)
 java.base@11.0.23/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@11.0.23/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.23/java.lang.Thread.run(Thread.java:829)
2024-07-04 14:48:00.794 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-04 14:48:00.805 [,,,,Auth is starting] [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInfoStatusController' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInfoStatusController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:849)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at cn.hy.auth.boot.AuthApplication.main(AuthApplication.java:41)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1247)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1167)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:627)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:456)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1247)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1167)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	... 31 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:622)
	... 45 common frames omitted
Caused by: java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at cn.hy.id.generator.SnowflakeIdWorker.<clinit>(SnowflakeIdWorker.java:184)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass.createIdWorker(AutoConfigrutionClass.java:35)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$1f7d94a1.CGLIB$createIdWorker$0(<generated>)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$1f7d94a1$$FastClassBySpringCGLIB$$8b58c86.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:363)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$1f7d94a1.createIdWorker(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 46 common frames omitted
Caused by: java.lang.ClassNotFoundException: sun.misc.BASE64Encoder
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:581)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:178)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 58 common frames omitted
2024-07-04 14:51:26.018 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:26.022 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:51:26.024 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:26.025 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:33.837 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:51:33.840 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:51:33.980 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:51:34.010 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:51:35.291 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:51:35.291 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:51:35.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 69ms. Found 12 repository interfaces.
2024-07-04 14:51:35.370 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.370 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.370 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.370 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.370 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:51:35.371 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:51:35.426 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:51:35.427 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:51:35.428 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:51:35.428 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:51:35.429 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:51:35.669 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:51:35.670 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:51:35.793 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:51:35.793 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:51:35.793 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:51:35.794 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:51:35.794 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:51:35.794 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:51:35.794 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:51:35.795 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:51:35.795 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:51:35.795 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:51:35.796 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:51:35.796 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:51:35.801 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 126ms. Found 0 repository interfaces.
2024-07-04 14:51:35.902 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:51:35.986 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:51:35.986 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:51:35.986 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:51:35.986 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:51:35.987 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:51:36.168 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:36.168 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:36.218 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:36.218 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:51:36.220 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:51:36.360 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:51:36.468 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$b7d1c514] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.511 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$d9e34bb6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.557 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$bcf0a8d5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.638 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f08776a8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.657 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6f8dbc20] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.682 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e41fb391] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:36.772 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$39b68e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:51:37.184 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:51:37.198 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:51:37.216 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:51:37.216 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:51:37.346 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:51:37.346 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3315 ms
2024-07-04 14:51:37.519 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:51:37.532 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:51:37.797 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:51:37.797 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:51:37.805 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4d62b6da
2024-07-04 14:51:38.008 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:51:39.089 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 14:51:39.619 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 14:51:39.767 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 14:51:39.874 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 14:51:39.926 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 14:51:39.928 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 14:51:40.049 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 14:51:40.172 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 14:51:40.858 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:51:41.129 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 14:51:43.455 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 14:51:43.562 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 14:51:43.662 [,,,,Auth is starting] [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInfoStatusController' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInfoStatusController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
2024-07-04 14:51:43.662 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2024-07-04 14:51:43.664 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:51:43.688 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-04 14:51:43.689 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-07-04 14:51:43.700 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-07-04 14:51:43.701 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-04 14:51:43.705 [,,,,Auth is starting] [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [slider-captcha-queue-0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.23/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.23/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:234)
 java.base@11.0.23/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2123)
 java.base@11.0.23/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@11.0.23/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.23/java.lang.Thread.run(Thread.java:829)
2024-07-04 14:51:43.715 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-04 14:51:43.727 [,,,,Auth is starting] [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInfoStatusController' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInfoStatusController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:849)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at cn.hy.auth.boot.AuthApplication.main(AuthApplication.java:41)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appInvolvedTableServiceImpl' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\systable\AppInvolvedTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1247)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1167)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'createIdWorker' defined in class path resource [cn/hy/id/autoconfigure/AutoConfigrutionClass.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:627)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:456)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1247)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1167)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	... 31 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.id.IdWorker]: Factory method 'createIdWorker' threw exception; nested exception is java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:622)
	... 45 common frames omitted
Caused by: java.lang.NoClassDefFoundError: sun/misc/BASE64Encoder
	at cn.hy.id.generator.SnowflakeIdWorker.<clinit>(SnowflakeIdWorker.java:184)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass.createIdWorker(AutoConfigrutionClass.java:35)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$5e9f1216.CGLIB$createIdWorker$0(<generated>)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$5e9f1216$$FastClassBySpringCGLIB$$76d634bc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:363)
	at cn.hy.id.autoconfigure.AutoConfigrutionClass$$EnhancerBySpringCGLIB$$5e9f1216.createIdWorker(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 46 common frames omitted
Caused by: java.lang.ClassNotFoundException: sun.misc.BASE64Encoder
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:581)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:178)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 58 common frames omitted
2024-07-04 14:53:54.292 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:53:54.294 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:53:54.297 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: *********
2024-07-04 14:53:54.303 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:54:06.849 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:54:06.851 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:54:06.950 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:54:06.977 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:54:08.279 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:54:08.279 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:54:08.338 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 56ms. Found 12 repository interfaces.
2024-07-04 14:54:08.345 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.345 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.345 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.345 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:54:08.346 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:54:08.403 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:54:08.404 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:54:08.405 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:54:08.405 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:54:08.406 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:54:08.649 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:54:08.649 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:54:08.782 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:54:08.782 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:54:08.783 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:54:08.783 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:54:08.783 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:54:08.783 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:54:08.784 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:54:08.784 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:54:08.784 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:54:08.784 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:54:08.785 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:54:08.785 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:54:08.790 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 136ms. Found 0 repository interfaces.
2024-07-04 14:54:08.899 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:54:08.969 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:54:08.969 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:54:08.969 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:54:08.969 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:54:08.970 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:54:09.159 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:54:09.159 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:54:09.198 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:54:09.198 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:54:09.200 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:54:09.318 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:54:09.417 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$d8aac637] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.461 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$fabc4cd9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.507 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$ddc9a9f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.606 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$116077cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.624 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9066bd43] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.650 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4f8b4b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:09.731 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2112b7b1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:54:10.124 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:54:10.137 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:54:10.155 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:54:10.155 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:54:10.295 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:54:10.295 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3303 ms
2024-07-04 14:54:10.467 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:54:10.481 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:54:10.732 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:54:10.733 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:54:10.741 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@667b3972
2024-07-04 14:54:10.925 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:54:11.774 [,,,,Auth is starting] [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.BeanCreationException. Message: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
2024-07-04 14:54:11.799 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-04 14:54:11.862 [,,,,Auth is starting] [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.23/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.23/java.util.concurrent.locks.LockSupport.park(LockSupport.java:194)
 java.base@11.0.23/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2081)
 java.base@11.0.23/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:433)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.23/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.23/java.lang.Thread.run(Thread.java:829)
2024-07-04 14:54:11.863 [,,,,Auth is starting] [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2024-07-04 14:54:11.864 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-04 14:54:11.872 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-04 14:54:11.881 [,,,,Auth is starting] [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:157)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at cn.hy.auth.boot.AuthApplication.main(AuthApplication.java:41)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:125)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:86)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:427)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:181)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:154)
	... 6 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appAuthInfoEixtFilter' defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\cn\hy\auth\custom\multi\filter\AppAuthInfoEixtFilter.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1303)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:235)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:193)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:188)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:170)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:89)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:261)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:234)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:54)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5139)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:831)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1377)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1367)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:902)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:423)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:928)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:455)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:106)
	... 11 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter]: Constructor threw exception; nested exception is java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:184)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1295)
	... 50 common frames omitted
Caused by: java.lang.RuntimeException: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
 - with linked exception:
[java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory]
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:62)
	at org.springframework.security.oauth2.http.converter.jaxb.JaxbOAuth2ExceptionMessageConverter.<init>(JaxbOAuth2ExceptionMessageConverter.java:21)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.geDefaultMessageConverters(DefaultOAuth2ExceptionRenderer.java:117)
	at org.springframework.security.oauth2.provider.error.DefaultOAuth2ExceptionRenderer.<init>(DefaultOAuth2ExceptionRenderer.java:53)
	at org.springframework.security.oauth2.provider.error.AbstractOAuth2SecurityExceptionHandler.<init>(AbstractOAuth2SecurityExceptionHandler.java:42)
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.<init>(Oauth2SecurityExceptionHandler.java:18)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.<init>(AbstractAuthFilter.java:29)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.<init>(AppAuthInfoEixtFilter.java:31)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:172)
	... 52 common frames omitted
Caused by: javax.xml.bind.JAXBException: Implementation of JAXB-API has not been found on module path or classpath.
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:278)
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:421)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721)
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662)
	at org.springframework.security.oauth2.http.converter.jaxb.AbstractJaxbMessageConverter.<init>(AbstractJaxbMessageConverter.java:59)
	... 64 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.sun.xml.internal.bind.v2.ContextFactory
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader.loadClass(TomcatEmbeddedWebappClassLoader.java:70)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1186)
	at javax.xml.bind.ServiceLoaderUtil.nullSafeLoadClass(ServiceLoaderUtil.java:122)
	at javax.xml.bind.ServiceLoaderUtil.safeLoadClass(ServiceLoaderUtil.java:155)
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:276)
	... 68 common frames omitted
2024-07-04 14:56:08.077 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:56:08.080 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:56:08.082 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: *********
2024-07-04 14:56:08.083 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:31.096 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:57:31.098 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:57:31.220 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:57:31.238 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:57:34.226 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:57:34.226 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:57:34.293 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 63ms. Found 12 repository interfaces.
2024-07-04 14:57:34.300 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.300 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:57:34.301 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:57:34.362 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:57:34.362 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:57:34.363 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:57:34.363 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:57:34.364 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:57:34.619 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:57:34.620 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:57:34.752 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:57:34.752 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:57:34.752 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:57:34.753 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:57:34.753 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:57:34.753 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:57:34.753 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:57:34.753 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:57:34.754 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:57:34.754 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:57:34.754 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:57:34.755 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:57:34.760 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124ms. Found 0 repository interfaces.
2024-07-04 14:57:34.865 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:57:34.941 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:57:34.942 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:57:34.942 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:57:34.942 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:57:34.942 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:57:35.134 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:35.136 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:35.180 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:35.180 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:35.183 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:57:35.306 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:57:35.436 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$a8431718] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.485 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$ca549dba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.565 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$ad61fad9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.680 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e0f8c8ac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.698 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$5fff0e24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.723 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d4910595] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:35.822 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f0ab0892] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:57:36.267 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:57:36.283 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:57:36.293 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:57:36.294 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:57:36.369 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:57:36.369 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5115 ms
2024-07-04 14:57:36.561 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:57:36.576 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:57:36.854 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:57:36.854 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:57:36.864 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7b9f7087
2024-07-04 14:57:37.127 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:57:38.424 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 14:57:38.974 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 14:57:39.058 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 14:57:39.164 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 14:57:39.222 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 14:57:39.224 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 14:57:39.389 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 14:57:39.525 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 14:57:40.321 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:57:40.618 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 14:57:41.914 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 14:57:42.022 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 14:57:42.122 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:24, workerId:4
2024-07-04 14:57:42.647 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 14:57:42.647 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 14:57:43.307 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 14:57:43.307 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 14:57:44.284 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 14:57:44.284 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 14:57:44.416 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@19f03a01]
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-04 14:57:45.642 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 14:57:45.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4c4da7ec, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c584c7f, org.springframework.security.web.header.HeaderWriterFilter@142c0c3e, org.springframework.security.web.authentication.logout.LogoutFilter@45c99f3d, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@18d87207, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@76a756f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b753aa5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@12cd1fe6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d9c3a0e, org.springframework.security.web.session.SessionManagementFilter@80249b0, org.springframework.security.web.access.ExceptionTranslationFilter@6aabacb7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@146eda1b]
2024-07-04 14:57:45.871 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@6da50fd, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@69e91f2b, org.springframework.security.web.context.SecurityContextPersistenceFilter@20f1cf40, org.springframework.security.web.header.HeaderWriterFilter@62f0035a, org.springframework.security.web.authentication.logout.LogoutFilter@188f6f98, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@7f78cca6, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@21282fc6, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4d58d224, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@bc168ac, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@5fc88c91, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@7ef9d004, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@1ecf9602, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@52997ccc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@30114f16, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e4b197f, org.springframework.security.web.session.SessionManagementFilter@1b36701c, org.springframework.security.web.access.ExceptionTranslationFilter@48b745af, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@505f8cf]
2024-07-04 14:57:45.885 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27ae4c06, org.springframework.security.web.context.SecurityContextPersistenceFilter@2fc578cf, org.springframework.security.web.header.HeaderWriterFilter@3e03037f, org.springframework.security.web.csrf.CsrfFilter@18f94d41, org.springframework.security.web.authentication.logout.LogoutFilter@23295bac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@224d6d02, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f38acf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6ec2108c, org.springframework.security.web.session.SessionManagementFilter@7e5b34c1, org.springframework.security.web.access.ExceptionTranslationFilter@692c8530]
2024-07-04 14:57:46.091 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:57:46.091 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:57:46.449 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-04 14:57:46.510 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-04 14:57:47.883 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-04 14:57:48.014 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-04 14:57:48.347 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-04 14:57:48.367 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-04 14:57:48.408 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-04 14:57:48.416 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://**************:18500}]
2024-07-04 14:57:48.422 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-04 14:57:48.430 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-04 14:57:48.438 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-04 14:57:48.812 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:57:48.891 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:57:48.891 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:57:49.088 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:57:49.104 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:57:49.109 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:57:49.109 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:57:49.112 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:57:49.115 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:57:49.120 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:57:49.120 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:57:49.121 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:57:49.121 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:57:49.126 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:57:49.126 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:57:49.126 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:57:49.128 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:57:49.129 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:57:49.131 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:57:49.131 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:57:49.133 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:57:49.134 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:57:49.135 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:57:49.135 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:57:49.135 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:57:49.135 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:57:49.157 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-04 14:57:49.157 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-04 14:57:49.157 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-04 14:57:49.158 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 14:57:49.158 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 14:57:49.158 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 14:57:49.176 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.consumer.internals.Fetcher - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Resetting offset for partition saas-sys-apps-0 to offset 10123.
2024-07-04 14:57:49.176 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.apache.kafka.clients.consumer.internals.Fetcher - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Resetting offset for partition saas-sys-apps-2 to offset 10123.
2024-07-04 14:57:49.176 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.consumer.internals.Fetcher - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Resetting offset for partition saas-sys-apps-1 to offset 10122.
2024-07-04 14:57:49.178 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.181 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.181 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.182 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 14:57:49.182 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 14:57:49.182 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 14:57:49.388 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.561 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.564 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.564 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.585 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.603 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.604 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.657 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.657 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.700 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.751 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=7316&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.751 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=7316&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=7316&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.758 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.779 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.838 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.895 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.904 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.906 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:49.907 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.443 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.463 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=7316&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.513 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@462d0402; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.580 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.593 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.593 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: *********
2024-07-04 14:57:50.606 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-04 14:57:50.637 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-04 14:57:50.652 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 21.245 seconds (JVM running for 22.103)
2024-07-04 14:57:50.656 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-04 14:57:50.656 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-04 14:57:50.676 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-04 14:57:50.676 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-04 14:57:50.689 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：*******************************************************************************************************************************************************************************,username = iotmp
2024-07-04 14:57:51.169 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-04 14:57:51.169 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-04 14:57:51.207 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 38 ms
2024-07-04 14:58:11.266 [paas,sys,/user/account/current,,d08d371453f14a4f9435e653151d4f15] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：46ms
2024-07-04 14:58:11.384 [paas,sys,/user/account/current,,d08d371453f14a4f9435e653151d4f15] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_account
2024-07-04 14:58:11.446 [paas,sys,/user/account/current,,d08d371453f14a4f9435e653151d4f15] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-04 14:58:11.446 [paas,sys,/user/account/current,,d08d371453f14a4f9435e653151d4f15] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】
2024-07-04 14:59:18.822 [,,,,Not Auth Request!] [Thread-37] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-04 14:59:18.822 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.824 [,,,,Not Auth Request!] [Thread-49] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=7316&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.825 [,,,,Not Auth Request!] [Thread-49] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=7316&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.825 [,,,,Not Auth Request!] [Thread-49] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=7316&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.831 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 14:59:18.832 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.836 [,,,,Not Auth Request!] [Thread-49] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=7316&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.837 [,,,,Not Auth Request!] [Thread-49] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:18.838 [,,,,Not Auth Request!] [Thread-49] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:25.264 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://**************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-04 14:59:25.266 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-04 14:59:25.414 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-04 14:59:25.435 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-04 14:59:27.365 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:59:27.365 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:59:27.455 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84ms. Found 12 repository interfaces.
2024-07-04 14:59:27.464 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.464 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.465 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 14:59:27.466 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 14:59:27.547 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-04 14:59:27.548 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-04 14:59:27.549 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-04 14:59:27.549 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-04 14:59:27.550 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-04 14:59:27.827 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-04 14:59:27.828 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-04 14:59:28.012 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-04 14:59:28.012 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-04 14:59:28.012 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-04 14:59:28.013 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-04 14:59:28.013 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-04 14:59:28.013 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-04 14:59:28.014 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-04 14:59:28.014 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-04 14:59:28.014 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-04 14:59:28.014 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-04 14:59:28.014 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-04 14:59:28.015 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-04 14:59:28.019 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174ms. Found 0 repository interfaces.
2024-07-04 14:59:28.147 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 14:59:28.225 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:59:28.225 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-04 14:59:28.225 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:59:28.225 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:59:28.226 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-04 14:59:28.436 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:28.436 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:28.483 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:28.483 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:28.486 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-04 14:59:28.639 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-04 14:59:28.759 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$f3eebf87] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:28.807 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$16004629] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:28.868 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$f90da348] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:28.978 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$2ca4711b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:29.000 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$abaab693] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:29.030 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$203cae04] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:29.137 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3c56b101] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-04 14:59:29.722 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-04 14:59:29.741 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-04 14:59:29.752 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-04 14:59:29.752 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-04 14:59:29.868 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-04 14:59:29.869 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4410 ms
2024-07-04 14:59:30.065 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-04 14:59:30.081 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-04 14:59:30.419 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:59:30.420 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:59:30.443 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3453f79c
2024-07-04 14:59:30.680 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-04 14:59:32.247 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-04 14:59:33.033 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-04 14:59:33.179 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-04 14:59:33.319 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-04 14:59:33.424 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-04 14:59:33.427 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-04 14:59:33.794 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-04 14:59:33.991 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-04 14:59:35.794 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-04 14:59:36.142 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-04 14:59:38.569 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-04 14:59:38.730 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-04 14:59:38.851 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:6, workerId:14
2024-07-04 14:59:39.478 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 14:59:39.478 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-04 14:59:40.233 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 14:59:40.233 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-04 14:59:41.421 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 14:59:41.421 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-04 14:59:41.590 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@611b2857]
2024-07-04 14:59:42.855 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-04 14:59:42.856 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-04 14:59:43.077 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1c49ae29, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d2fc711, org.springframework.security.web.header.HeaderWriterFilter@52c3f893, org.springframework.security.web.authentication.logout.LogoutFilter@87852fb, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@27879b4e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7a282bad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@232fc6a6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ca9fd34, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@646599c6, org.springframework.security.web.session.SessionManagementFilter@3ae2721b, org.springframework.security.web.access.ExceptionTranslationFilter@567a2954, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@125c42e3]
2024-07-04 14:59:43.133 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@70832ddb, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6b5e1db0, org.springframework.security.web.context.SecurityContextPersistenceFilter@5fbb2225, org.springframework.security.web.header.HeaderWriterFilter@17dfa412, org.springframework.security.web.authentication.logout.LogoutFilter@3fb732ee, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@5125159e, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@11b0cf30, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@56d1efec, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@427475a2, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@2bf065c2, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@30584e91, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@2706cee5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c9cc591, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@36891524, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@463e66c2, org.springframework.security.web.session.SessionManagementFilter@32c77e0d, org.springframework.security.web.access.ExceptionTranslationFilter@2324100b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@82b778b]
2024-07-04 14:59:43.150 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c802d25, org.springframework.security.web.context.SecurityContextPersistenceFilter@27e1c83, org.springframework.security.web.header.HeaderWriterFilter@1db7acc9, org.springframework.security.web.csrf.CsrfFilter@6337c48d, org.springframework.security.web.authentication.logout.LogoutFilter@4ee8ba7f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@66b3c476, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46c2ed8d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4468deb7, org.springframework.security.web.session.SessionManagementFilter@4e08c557, org.springframework.security.web.access.ExceptionTranslationFilter@7963a94b]
2024-07-04 14:59:43.324 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 14:59:43.324 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-04 14:59:43.720 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-04 14:59:43.797 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-04 14:59:45.374 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-04 14:59:45.536 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-04 14:59:46.559 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-04 14:59:46.586 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-04 14:59:46.634 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application hostname="hy" qosEnable="false" name="HY-AUTH-DUBBO-PROVIDER" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-04 14:59:46.646 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://**************:18500}]
2024-07-04 14:59:46.655 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-04 14:59:46.667 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-04 14:59:46.675 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-04 14:59:47.072 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:59:47.179 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:59:47.179 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:59:47.443 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:59:47.457 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:59:47.463 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:59:47.463 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:59:47.468 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:59:47.471 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:59:47.476 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:59:47.476 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:59:47.476 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:59:47.477 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-04 14:59:47.482 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-04 14:59:47.482 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-04 14:59:47.483 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-04 14:59:47.490 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:59:47.490 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:59:47.491 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:59:47.491 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:59:47.494 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:59:47.494 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:59:47.494 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:59:47.494 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:59:47.495 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:59:47.495 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:59:47.506 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: J55e1zSXTMeMOetOxZetWA
2024-07-04 14:59:47.506 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: 2147483647 rack: null)
2024-07-04 14:59:47.506 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-04 14:59:47.506 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-04 14:59:47.506 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:59:47.514 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:59:47.514 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 14:59:47.521 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 4
2024-07-04 14:59:47.521 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 4
2024-07-04 14:59:47.522 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 4
2024-07-04 14:59:47.524 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 14:59:47.524 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 14:59:47.524 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 14:59:47.529 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.532 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.532 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.540 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 14:59:47.540 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 14:59:47.540 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 14:59:47.688 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.822 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.823 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.824 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.846 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.862 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.862 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://**************:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.902 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.902 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:47.943 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.000 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5432&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.000 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5432&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=5432&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.006 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.022 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.044 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.044 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.053 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.055 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.056 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.401 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.431 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5432&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.475 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@1d1b5407; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.516 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.533 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.533 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: *********
2024-07-04 14:59:48.545 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-04 14:59:48.570 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-04 14:59:48.592 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 25.787 seconds (JVM running for 26.979)
2024-07-04 14:59:48.595 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-04 14:59:48.595 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-04 14:59:48.599 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-04 14:59:48.599 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-04 14:59:48.623 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：*******************************************************************************************************************************************************************************,username = iotmp
2024-07-04 14:59:48.865 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-04 14:59:48.866 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-04 14:59:48.906 [,,,,Not Auth Request!] [RMI TCP Connection(6)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 40 ms
2024-07-04 14:59:52.949 [paas,sys,/user/account/current,,a2636d0aa6a04c45a0634a01babed875] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：55ms
2024-07-04 14:59:53.120 [paas,sys,/user/account/current,,a2636d0aa6a04c45a0634a01babed875] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_account
2024-07-04 14:59:53.182 [paas,sys,/user/account/current,,a2636d0aa6a04c45a0634a01babed875] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-04 14:59:53.183 [paas,sys,/user/account/current,,a2636d0aa6a04c45a0634a01babed875] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】

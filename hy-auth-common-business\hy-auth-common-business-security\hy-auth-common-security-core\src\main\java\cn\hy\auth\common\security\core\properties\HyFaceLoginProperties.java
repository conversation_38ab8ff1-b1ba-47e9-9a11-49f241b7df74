package cn.hy.auth.common.security.core.properties;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 类描述：人脸登录
 *
 * <AUTHOR> by <PERSON><PERSON><PERSON><PERSON>
 * @date 2022/9/8 15:49
 **/
@Data
@Component
public class HyFaceLoginProperties {

    /**
     * 登录filter 拦截处理的url
     */
    private String authProcessingUrl = "/faceLogin";
    private  String picParameter = "facePic";
    private  String lesseeCodeParamter = "lessee_code";
    private  String appCodeParamter = "app_code";
    private  String loginTypeParamter = "login_type";
}

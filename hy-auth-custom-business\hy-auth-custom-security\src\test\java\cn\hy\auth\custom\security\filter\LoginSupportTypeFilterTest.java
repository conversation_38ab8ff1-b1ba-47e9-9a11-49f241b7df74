package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.utils.LoginTypeMatcher;
import cn.hy.auth.custom.security.exceptions.UnsupportedLoginTypeException;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {LoginSupportTypeFilter.class, LoginTypeMatcher.class})
public class LoginSupportTypeFilterTest {

    HttpServletRequest request;
    HttpServletResponse response;
    FilterChain filterChain;

    @Autowired
    LoginSupportTypeFilter supportTypeFilter;

    @Before
    public void setup() {
        Logger.getGlobal().setLevel(Level.ALL);

        request = Mockito.mock(HttpServletRequest.class);
        response = Mockito.mock(HttpServletResponse.class);
        filterChain = Mockito.mock(FilterChain.class);

        AuthContext.getContext().setLesseeCode("hy").setAppCode("iot");
        AuthContext.getContext()
                .appAuthStrategy()
                .getLoginRule()
                .setLoginSupportType(Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD));
    }

    @Test
    public void testSupport() {
        doReturn("/login").when(request).getRequestURI();

        try {
            supportTypeFilter.doFilterInternal(request, response, filterChain);
            verify(filterChain, times(1)).doFilter(request, response);
            Assertions.assertThat(AuthContext.getContext().loginState().getLoginType())
                    .isEqualTo(LoginTypeEnum.USERNAME_PASSWORD);

        } catch (ServletException | IOException e) {
            throw new RuntimeException();
        }
    }

    @Test(expected = UnsupportedLoginTypeException.class)
    public void testNotSupport() {
        doReturn("/login/mobile").when(request).getRequestURI();

        try {
            supportTypeFilter.doFilterInternal(request, response, filterChain);

        } catch (ServletException | IOException e) {
            throw new RuntimeException();
        }
    }

    @Test
    public void testNotAction() {
        doReturn("/password").when(request).getRequestURI();

        try {
            supportTypeFilter.doFilterInternal(request, response, filterChain);
            verify(filterChain, times(1)).doFilter(request, response);
//            Assertions.assertThat(AuthContext.getContext().loginState().getLoginType()).isNull();

        } catch (ServletException | IOException e) {
            throw new RuntimeException();
        }
    }
}
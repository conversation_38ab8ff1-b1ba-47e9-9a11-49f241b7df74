package cn.hy.auth.custom.user.account.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 类描述:第三方用户账号
 *
 * <AUTHOR>
 * @date ：创建于 2022/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "第三方用户账号")
public class ThirdMemberBindDTO {

    /**
     * 第三方用户ID
     */
    @ApiModelProperty(value = "第三方Id")
    private String providerId;

    /**
     * 第三方用户ID
     */
    @ApiModelProperty(value = "第三方用户ID")
    @NotBlank(message = "第三方用户ID不能为空")
    private String providerUserId;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    @NotBlank(message = "用户账号不允许为空")
    private String userAccountName;


    /**
     * 是否需要绑定第三方账号
     */
    @ApiModelProperty(value = "是否需要绑定第三方账号")
    @NotBlank(message = "是否需要绑定第三方账号")
    private Boolean isBindThird = Boolean.FALSE;
}

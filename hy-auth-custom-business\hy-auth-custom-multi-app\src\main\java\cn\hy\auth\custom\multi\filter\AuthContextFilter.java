package cn.hy.auth.custom.multi.filter;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.util.RequestUtil;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.LoginStateDTO;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.multi.systable.AppInvolvedTableServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 上下文初始化过滤器
 * 认证中心中所有自定义Filter最先执行这个。
 * 1、初始化上下文中的租户编码、应用编码；
 * 2、在调用逻辑处理完成后，重置上下文。
 *
 * <AUTHOR>
 * @date 2020-12-10 11:24
 **/
@Order(-1000)
@Component
@Slf4j
public class AuthContextFilter extends AbstractAuthFilter {
    @Autowired
    @Lazy
    private AppInfoParseProxy appInfoParseProxy;
    @Autowired
    @Lazy
    private AppInvolvedTableServiceImpl appInvolvedTableService;

    @Autowired
    @Lazy
    private TokenStore tokenStore;
    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response,
                              FilterChain filterChain) throws ServletException, IOException {

        ImmutablePair<AppInfoDTO, RequestTypeEnum> parserResult = appInfoParseProxy.parseWithRequestType(request);
        if (Objects.isNull(parserResult)) {
            log.warn("解析租户应用信息失败。请求URL:【{}】,参数：{}", request.getRequestURL(),
                    RequestUtil.getAllRequestParam(request, LoginParamterConts.PASSWORD));
            throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg1", null));
        }

        if (!RequestTypeEnum.NON_BUSINESS.equals(parserResult.getRight())) {
            //没有配置忽略的uri，都会进行租户、应用编码的校验

            if (log.isDebugEnabled()) {
                log.info("登录请求，校验登录扩展参数。URL:【{}】,参数：{}", request.getRequestURL(),
                        RequestUtil.getAllRequestParam(request, LoginParamterConts.PASSWORD));
            }

            AppInfoDTO appInfo = parserResult.getLeft();
            AuthAssertUtils.isNotNull(appInfo, AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg1", null));
            AuthAssertUtils.isNotBlank(appInfo.getLesseeCode(), AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg2", null));
            AuthAssertUtils.isNotBlank(appInfo.getAppCode(), AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg3", null));
            AuthContext.getContext().setLesseeCode(appInfo.getLesseeCode()).setAppCode(appInfo.getAppCode());
            String loginName = LoginUtil.getLoginName();
            String tokenId = TokenUtil.getTokenId(request);
            if (StringUtils.isBlank(loginName) && StringUtils.isNotBlank(tokenId)){
                OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(tokenId);
                if (oAuth2Authentication!=null){
                    loginName = oAuth2Authentication.getName();
                }
            }
            AuthContext.getContext().set("_login_name_", loginName);
            log.debug("租户应用信息解析-->租户编码：【{}】,应用编码：【{}】", appInfo.getLesseeCode(), appInfo.getAppCode());

            //if (!RequestTypeEnum.LOGIN.equals(parserResult.getRight())) {//对于非登录的请求，需要校验租户、应用的合法性
            // 校验租户和应用号是为了确保每个请求的上下文的信息都是准确的
            Map<String, Object> appInfoMap = appInvolvedTableService.getAppInfo(appInfo.getLesseeCode(), appInfo.getAppCode());

            if (RequestTypeEnum.LOGIN.equals(parserResult.getRight())){
                //对于登录的请求，仅处理应用状态
                if (!CollectionUtils.isEmpty(appInfoMap)){
                    Integer status = (Integer) appInfoMap.getOrDefault("status", 1);
                    // 非运行状态
                    if (0 == status){
                        throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.result.msg2", null) + appInfo.getLesseeCode() + LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg5", null) + appInfo.getAppCode());
                    } else if (2 == status){
                        throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.result.msg4", null) + appInfo.getLesseeCode() + LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg5", null) + appInfo.getAppCode());
                    }
                }
            } else {
                //对于非登录的请求，需要校验租户、应用的合法性
                AuthAssertUtils.isTrue(!CollectionUtils.isEmpty(appInfoMap), AuthErrorCodeEnum.A0102.code(),
                        LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg4", null) + appInfo.getLesseeCode() + LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg5", null) + appInfo.getAppCode());
                Integer status = (Integer) appInfoMap.getOrDefault("status", 1);
                if (0 == status){
                    throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.result.msg2", null) + appInfo.getLesseeCode() + LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg5", null) + appInfo.getAppCode());
                } else if (2 == status){
                    throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthContextFilter.result.msg4", null) + appInfo.getLesseeCode() + LocaleUtil.getMessage("AuthContextFilter.assertUtil.msg5", null) + appInfo.getAppCode());
                }
            }
            getClientType(request, AuthContext.getContext());
        }
        filterChain.doFilter(request, response);

    }

    
    /**
     * 获取登录类型
     *
     * @param request     ..
     * @param authContext ..
     */
    private void getClientType(HttpServletRequest request, AuthContext authContext) {
        String token = request.getHeader("Authorization") ==null? request.getHeader("Blade-Auth")
                : request.getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            // 取refresh_token的clientType
            token = request.getParameter("refresh_token");
        }
        if (StringUtils.isBlank(token)) {
            return;
        }
        String[] tokenPart = token.split("\\.");
        if (tokenPart.length < 4) {
            return;
        }
        LoginStateDTO loginStateDTO = new LoginStateDTO();
        loginStateDTO.setClientType(ClientTypeEnum.codeOf(tokenPart[2]));
        authContext.setLoginState(loginStateDTO);
    }
}

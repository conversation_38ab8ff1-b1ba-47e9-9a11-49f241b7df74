package cn.hy.auth.common.security.core.authentication.external;

import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAccessToken;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAuthConfig;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalProviderUser;
import cn.hy.auth.common.security.core.authentication.external.def.ExternalAuthService;
import cn.hy.auth.common.security.core.authentication.gxfl.bean.GxflProviderUser;
import cn.hy.auth.common.security.core.authentication.gxfl.service.GxflAuthService;
import cn.hy.auth.common.security.core.authentication.iam.bean.IamProviderUser;
import cn.hy.auth.common.security.core.authentication.iam.def.IamAuthService;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 外部第三方登录认证提供者
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Data
@Slf4j
@Component
public class HyExternalPreAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService myUserDetailsService;
    protected boolean hideUserNotFoundExceptions = false;
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    @Autowired
    private List<HyConnectionFactory> connectionFactories;
    @Autowired
    private ExternalAuthService externalAuthService;
    @Autowired
    private IamAuthService iamAuthService;
    @Autowired
    private GxflAuthService gxflAuthService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        HyExternalAuthenticationToken authenticationToken = (HyExternalAuthenticationToken) authentication;
        String code = authenticationToken.getCode();
        String lesseeCode = authenticationToken.getLesseeCode();
        String appCode = authenticationToken.getAppCode();
        String providerId = authenticationToken.getProviderId();
        Map<String, Object> details = (Map<String, Object>) authenticationToken.getDetails();
        String userAccountName = null;
        try {
            if (providerId.equalsIgnoreCase("iam")) {
                //iam国标oauth2的认证
                //1. 根据code获取用户信息
                log.info("1.根据code获取用户信息....");
                IamProviderUser userInfo = iamAuthService.getUserInfo(lesseeCode, appCode, code);
                //3. 根据用户ID查询用户账号，如果不存在自动注册账号
                log.info("2.尝试根据用户信息查询基础数据账号信息....");
                Map<String, Object> byUserAccount = iamAuthService.getByUserAccount(userInfo);
                if (byUserAccount != null && byUserAccount.get("user_account_name") != null) {
                    log.info("3.获取到基础数据账号:{}", byUserAccount.get("user_account_name"));
                    userAccountName = (String) byUserAccount.get("user_account_name");
                } else {
                    //自动注册用户
                    log.info("3.基础数据无该用户信息，注册到基础数据账号信息，并得到用户账号：【{}】", userInfo.getUserId());
                    iamAuthService.regirstUser(userInfo);
                    userAccountName = userInfo.getUserAccountName();
                }
            } else if (providerId.equalsIgnoreCase("gxfl")){
                // 广西法律 免登认证
                // 1、根据code获取用户信息
                log.info("1.根据code获取用户信息....");
                GxflProviderUser userInfo = gxflAuthService.getUserInfo(lesseeCode, appCode, code, details);
                // 2、根据登录账号查询用户账号，如果不存在自动注册账号
                log.info("2.尝试根据用户信息查询基础数据账号信息....");
                Map<String, Object> byUserAccount = gxflAuthService.getByUserAccount(userInfo);
                if (byUserAccount != null && byUserAccount.get("user_account_name") != null) {
                    log.info("3.获取到基础数据账号:{}", byUserAccount.get("user_account_name"));
                    userAccountName = (String) byUserAccount.get("user_account_name");
                } else {
                    //自动注册用户
                    log.info("3.基础数据无该用户信息，注册到基础数据账号信息，并得到用户账号：【{}】", userInfo.getUserAccountName());
                    gxflAuthService.registerUser(userInfo);
                    userAccountName = userInfo.getUserAccountName();
                }
            } else {
                //基于客户端的oauth2认证....
                //0. 根据租户和应用获取clientId和clientSecret
                log.info("1.获取工作台配置信息认证配置...");
                ExternalAuthConfig authConfig = externalAuthService.getAuthConfig(providerId, lesseeCode, appCode);
                //1. 根据grant_type、code、client_id、client_secret、redirect_uri 获取accesstoken
                log.info("2.根据客户端配置以及授权码【{}】获取第三方access_token....", code);
                ExternalAccessToken accessToke = externalAuthService.getAccessToke(code, authConfig);
                //2. 根据token获取用户ID
                log.info("3.根据第三方access_token获取用户信息....");
                ExternalProviderUser userInfo = externalAuthService.getUserInfo(accessToke);
                //3. 根据用户ID查询用户账号，如果不存在自动注册账号
                log.info("4.尝试根据用户信息查询基础数据账号信息....");
                Map<String, Object> byUserAccount = externalAuthService.getByUserAccount(userInfo);
                //4. 设置用户账号，并返回3.X token

                if (byUserAccount != null && byUserAccount.get("user_account_name") != null) {
                    log.info("5.获取到基础数据账号:{}", byUserAccount.get("user_account_name"));
                    userAccountName = (String) byUserAccount.get("user_account_name");
                } else {
                    //自动注册用户
                    log.info("5.基础数据无该用户信息，注册到基础数据账号信息");
                    externalAuthService.regirstUser(userInfo);
                    userAccountName = userInfo.getUserAccountName();
                }
            }
            log.info(" 免登访问.......");
            authenticationToken.setPrincipal(userAccountName);
            return authenticationToken;
        } catch (UsernameNotFoundException ex) {
            if (hideUserNotFoundExceptions) {
                throw new BadCredentialsException(messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.badCredentials",
                        "Bad credentials"));
            } else {
                throw ex;
            }
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }

    }

    @Override
    public boolean supports(Class<?> authentication) {
        return HyExternalAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public void setMyUserDetailsService(UserDetailsService myUserDetailsService) {
        this.myUserDetailsService = myUserDetailsService;
    }
}

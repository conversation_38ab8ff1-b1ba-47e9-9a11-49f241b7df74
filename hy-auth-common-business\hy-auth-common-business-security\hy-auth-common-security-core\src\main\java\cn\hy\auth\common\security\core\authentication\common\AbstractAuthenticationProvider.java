package cn.hy.auth.common.security.core.authentication.common;

import cn.hy.auth.common.security.core.authentication.mobile.exception.UserNotExistException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.IbatisException;
import org.springframework.dao.DataAccessException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.sql.SQLException;

/**
 * 自定义登录校验业务的抽象实现，可以实现排序
 *
 * <AUTHOR>
 * @date 2020-11-12 9:26
 */
@Slf4j
public abstract class AbstractAuthenticationProvider implements AuthenticationProvider, Comparable<AbstractAuthenticationProvider> {
    /**
     * 自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return .
     */
    @Override
    public Authentication authenticate(Authentication authentication) {

        log.debug("{}.authenticate>>> {}", this.getClass().getName(), authentication);
        if (this.isSupport(authentication)) {
            try {
                return this.doAuthenticate(authentication);
            } catch (Exception e) {
                log.warn("Provider has error:{}", e.getMessage(),e);
                String msg = createMsg(e);
                throw new InternalAuthenticationServiceException(msg, e);
            }
        } else {
            return null;
        }
    }

    private String createMsg(Exception e) {
        // fix QX202404090015
        if (e instanceof IbatisException || e instanceof SQLException || e instanceof DataAccessException){
            return "数据库层错误.";
        }
        return e.getMessage();
    }
    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return Authentication 认证信息封装类
     * @throws AuthenticationException .
     */
    protected abstract Authentication doAuthenticate(Authentication authentication);

    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    protected abstract int order();

    /**
     * 判断是否需要当前Provider处理，子类可覆盖，实现自己的业务判断逻辑
     *
     * @param authentication .
     * @return .
     */
    protected boolean isSupport(Authentication authentication) {
        return true;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return true;
    }


    @SuppressWarnings("NullableProblems")
    @Override
    public int compareTo(AbstractAuthenticationProvider p) {
        return this.order() - p.order();
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }
}

package cn.hy.auth.custom.user.account.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: UserLoginBindIpSublistDTO
 * @description: 用户登陆绑定IP子表
 * @date 2024/1/17
 */
@Data
public class UserLoginBindIpSublistDTO implements Serializable {

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 外键
     */
    private BigDecimal fid;

    /**
     * 起始地址
     */
    private String initialAddress;

    /**
     * 终止地址
     */
    private String finalAddress;

}

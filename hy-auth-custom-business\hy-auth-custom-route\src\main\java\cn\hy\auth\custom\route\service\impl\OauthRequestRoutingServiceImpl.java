package cn.hy.auth.custom.route.service.impl;

import cn.hy.auth.custom.route.dao.OauthRequestRoutingDao;
import cn.hy.auth.custom.route.domain.OauthRequestRouting;
import cn.hy.auth.custom.route.service.OauthRequestRoutingService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.stereotype.Service;

/**
 * 类描述：.
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 14:12
 **/
@Service
public class OauthRequestRoutingServiceImpl implements OauthRequestRoutingService {

    private final OauthRequestRoutingDao oauthRequestRoutingDao;
    private Cache<String, OauthRequestRouting> cache = Caffeine.newBuilder()
            .maximumSize(200)
            .recordStats().build();

    public OauthRequestRoutingServiceImpl(OauthRequestRoutingDao oauthRequestRoutingDao) {
        this.oauthRequestRoutingDao = oauthRequestRoutingDao;
    }

    @Override
    public OauthRequestRouting selectBySystemAreaIdentify(String systemAreaIdentify, String sourceUrl) {
        String key = systemAreaIdentify+","+sourceUrl;
        return cache.get(key,k-> loadData(systemAreaIdentify, sourceUrl));
    }

    @Override
    public String clearCache() {
        cache.invalidateAll();
        return "success";
    }

    private OauthRequestRouting loadData(String systemAreaIdentify,String sourceUrl){
        return oauthRequestRoutingDao.selectBySystemAreaIdentify(systemAreaIdentify,sourceUrl);
    }
}

package cn.hy.auth.common.business.tool.token.service;

import org.springframework.security.oauth2.provider.OAuth2Authentication;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类描述: token与用户的关系接口
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/28
 */
public interface UserCacheTokenService {

    /**
     * 保存缓存信息
     *
     * @param tokenId              tokenId
     * @param oAuth2Authentication oAuth2Authentication
     * @param <T>                  返回对象
     * @return 返回对象
     */
    <T> T cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication);



    /**
     * 按tokenId获取用户缓存信息,没有则从数据库中获取
     *
     * @param tokenId tokenId
     * @param <T>     返回对象
     * @return 返回对象
     */
    <T> T getUserCacheByTokenId(@NotBlank String tokenId);

    /**
     * 按tokenId获取用户主键,没有则从数据库中获取
     *
     * @param tokenId tokenId
     * @return 返回用户主键
     */
    //Long getUserIdByTokenId(@NotBlank String tokenId);

    /**
     * 按tokenId删除用户缓存信息
     *
     * @param tokenId tokenId
     */
    void deleteUserCacheByTokenId(@NotBlank String tokenId);
}

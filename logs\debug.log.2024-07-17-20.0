2024-07-17 20:00:00.023 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-17 19:58:19,687 to 2024-07-17 20:00:00,008
cache                         |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_field                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_field_property             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_clips                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_condition_relationship|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_custom                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dict                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_json_dynamic_field         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_be_fk                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_fk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_table_pk                   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
md_treeTable_ext              |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
------------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-17 20:02:18.294 [null,null,/login,null,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:18.296 [null,null,/login,null,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:18.296 [null,null,/login,null,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:19.629 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：229ms
2024-07-17 20:02:19.917 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_account
2024-07-17 20:02:20.129 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-17 20:02:20.133 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】
2024-07-17 20:02:20.142 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_performance4445
2024-07-17 20:02:20.144 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_performance4445是否需要验证码，失败次数0
2024-07-17 20:02:20.145 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.167 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-17 20:02:20.168 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-17 20:02:20.168 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.168 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.170 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.170 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.363 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.363 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.364 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.366 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] ERROR c.h.a.c.s.o.provider.PwdAuthenticationProviderer - 账号密码检查-->用户【hy_performance4445】密码错误。
2024-07-17 20:02:20.367 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:20.388 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] WARN  c.h.a.c.s.c.a.c.AbstractAuthenticationProvider - Provider has error:用户名或密码错误
org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at cn.hy.auth.custom.security.oauth2.provider.PwdAuthenticationProviderer.doAuthenticate(PwdAuthenticationProviderer.java:103)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:34)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:52)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:124)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:02:20.408 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] ERROR o.s.s.w.a.UsernamePasswordAuthenticationFilter - An internal error occurred while trying to authenticate the user.
org.springframework.security.authentication.InternalAuthenticationServiceException: 用户名或密码错误
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:38)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:52)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:124)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at cn.hy.auth.custom.security.oauth2.provider.PwdAuthenticationProviderer.doAuthenticate(PwdAuthenticationProviderer.java:103)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:34)
	... 109 common frames omitted
2024-07-17 20:02:20.422 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=paas, pwd_encryption_type=3, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_performance4445, app_code=sys} 登录失败. 用户名或密码错误 
2024-07-17 20:02:20.425 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - 用户名或密码错误
org.springframework.security.authentication.InternalAuthenticationServiceException: 用户名或密码错误
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:38)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:52)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:124)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at cn.hy.auth.custom.security.oauth2.provider.PwdAuthenticationProviderer.doAuthenticate(PwdAuthenticationProviderer.java:103)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:34)
	... 109 common frames omitted
2024-07-17 20:02:20.896 [paas,sys,/login,,1ba5a2b7695d43d9812c5fe5e7d71e37] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_user_login_info]的加载时间：6ms
2024-07-17 20:02:20.969 [,,,,Not Auth Request!] [task-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：auth_sys_log_custom_handle
2024-07-17 20:02:38.762 [null,null,/login,null,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:38.762 [null,null,/login,null,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:38.762 [null,null,/login,null,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:38.808 [paas,sys,/login,,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_performance4445
2024-07-17 20:02:38.808 [paas,sys,/login,,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_performance4445是否需要验证码，失败次数1
2024-07-17 20:02:38.808 [paas,sys,/login,,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:38.879 [paas,sys,/login,,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] ERROR cn.hy.auth.custom.common.filter.AbstractAuthFilter - {"code":"A0205","msg":"需要验证码"}
cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0205","msg":"需要验证码"}
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:63)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:124)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:02:39.695 [paas,sys,/login,,282639be2ea94be5b719c7b521930aa9] [http-nio-6060-exec-2] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - {"code":"A0205","msg":"需要验证码"}
cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler$1: {"code":"A0205","msg":"需要验证码"}
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.commence(Oauth2SecurityExceptionHandler.java:22)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:124)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:02:55.146 [null,null,/login,null,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.146 [null,null,/login,null,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.146 [null,null,/login,null,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_performance4446
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_performance4446是否需要验证码，失败次数0
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.147 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.157 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.158 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.159 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:55.229 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.u.a.s.impl.UserSecurityManageServiceImpl - existsEnableParamCfg 上下文：paas_sys
2024-07-17 20:02:55.229 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.u.a.s.impl.UserSecurityManageServiceImpl - existsEnableParamCfg 上下文：paas_sys
2024-07-17 20:02:55.229 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_performance4446】的密码状态正常。
2024-07-17 20:02:55.358 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-17 20:02:55.366 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_oauth_client_details]的加载时间：6ms
2024-07-17 20:02:55.371 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - paas_sys 使用默認的token有效期配置：360000
2024-07-17 20:02:55.392 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:58.314 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-07-17 20:02:58.343 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-07-17 20:02:59.396 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.c57d1a0b-01e2-439b-9c77-3cbab9b7d3f2]获取用户主键失败,获取为空
2024-07-17 20:02:59.401 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:02:59.433 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_role
2024-07-17 20:02:59.436 [paas,sys,/login,,73f7d3c4913c4ff7aa20d5d034218119] [http-nio-6060-exec-3] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：role
2024-07-17 20:03:17.245 [null,null,/user/account/current,null,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:17.245 [null,null,/user/account/current,null,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:17.245 [null,null,/user/account/current,null,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:17.264 [paas,sys,/user/account/current,hy_performance4446,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:17.264 [paas,sys,/user/account/current,hy_performance4446,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:17.387 [paas,sys,/user/account/current,hy_performance4446,ccd8adb6e1d041ffb02c3ef0ff26f3d4] [http-nio-6060-exec-4] INFO  cn.hy.auth.common.security.core.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:03:24.635 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.637 [,,,,Not Auth Request!] [Thread-131] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-17 20:03:24.638 [,,,,Not Auth Request!] [Thread-147] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=26944&qos.enable=false&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.638 [,,,,Not Auth Request!] [Thread-147] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=26944&qos.enable=false&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.638 [,,,,Not Auth Request!] [Thread-147] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.646 [,,,,Not Auth Request!] [Thread-147] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.647 [,,,,Not Auth Request!] [Thread-147] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.658 [,,,,Not Auth Request!] [Thread-147] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:24.658 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-17 20:03:24.659 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:44.819 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://*************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-17 20:03:44.824 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-17 20:03:45.039 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-17 20:03:45.065 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-17 20:03:49.768 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-17 20:03:49.769 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-17 20:03:49.951 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174ms. Found 12 repository interfaces.
2024-07-17 20:03:49.972 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.973 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.974 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:49.976 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:50.646 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:50.646 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:50.646 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:50.646 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:03:50.646 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 20:03:50.799 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-17 20:03:50.800 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-17 20:03:50.802 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-17 20:03:50.803 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-17 20:03:50.805 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-17 20:03:51.277 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-17 20:03:51.278 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-17 20:03:51.681 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-17 20:03:51.682 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-17 20:03:51.682 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-17 20:03:51.683 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-17 20:03:51.683 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-17 20:03:51.683 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-17 20:03:51.684 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-17 20:03:51.684 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-17 20:03:51.684 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-17 20:03:51.685 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-17 20:03:51.685 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-17 20:03:51.685 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-17 20:03:51.693 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 392ms. Found 0 repository interfaces.
2024-07-17 20:03:51.944 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 20:03:52.173 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:03:52.174 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-17 20:03:52.174 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:03:52.174 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:03:52.175 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:03:52.491 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:52.491 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:52.555 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:52.555 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-17 20:03:52.559 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-17 20:03:52.753 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=8daa72ea-297f-3025-928f-6aa0115f3b82
2024-07-17 20:03:52.908 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$b024a724] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:52.979 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$d2362dc6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:53.058 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$b5438ae5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:53.237 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e8da58b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:53.263 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$67e09e30] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:53.307 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$dc7295a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:53.489 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f88c989e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:03:54.600 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-17 20:03:54.644 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-17 20:03:54.661 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-17 20:03:54.661 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-17 20:03:54.875 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-17 20:03:54.876 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9779 ms
2024-07-17 20:03:55.227 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-17 20:03:55.259 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-17 20:03:55.812 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 20:03:55.812 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-17 20:03:55.830 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@17b9c9ff
2024-07-17 20:03:56.118 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-17 20:03:58.752 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-17 20:04:00.190 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-17 20:04:00.464 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-17 20:04:00.745 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-17 20:04:00.897 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-17 20:04:00.900 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-17 20:04:02.059 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-17 20:04:04.649 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-17 20:04:06.866 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 20:04:07.601 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-17 20:04:11.413 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-17 20:04:11.566 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-17 20:04:11.703 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:25, workerId:2
2024-07-17 20:04:12.355 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-17 20:04:12.356 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-17 20:04:13.300 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-17 20:04:13.300 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-17 20:04:14.800 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-17 20:04:14.801 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-17 20:04:14.946 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@31d07dd9]
2024-07-17 20:04:17.974 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-17 20:04:17.974 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-17 20:04:17.974 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-17 20:04:17.974 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-17 20:04:17.974 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-17 20:04:17.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/websocket/**'], []
2024-07-17 20:04:18.014 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.common.utils.LocaleUtil - 读取国际化信息，country： zhvariant: CN
2024-07-17 20:04:18.303 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7102b480, org.springframework.security.web.context.SecurityContextPersistenceFilter@33df7a87, org.springframework.security.web.header.HeaderWriterFilter@1c89afe, org.springframework.security.web.authentication.logout.LogoutFilter@51c316cb, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@55e85ec8, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6198bac2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@404ea1e4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1a420390, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9628240, org.springframework.security.web.session.SessionManagementFilter@537caf04, org.springframework.security.web.access.ExceptionTranslationFilter@6cf4ac81, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21fba6fd]
2024-07-17 20:04:18.356 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@7094dc0, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@797ed02e, org.springframework.security.web.context.SecurityContextPersistenceFilter@2dacf052, org.springframework.security.web.header.HeaderWriterFilter@6fe8e276, org.springframework.security.web.authentication.logout.LogoutFilter@aa8f19d, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@12a57194, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@4d500865, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1f17fe4c, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@fcdad9a, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@426786ee, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@4d97c12c, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@c9ebdb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35590f53, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d8ab0dc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@620a2725, org.springframework.security.web.session.SessionManagementFilter@59c9df97, org.springframework.security.web.access.ExceptionTranslationFilter@3c148f23, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@50c253b6]
2024-07-17 20:04:18.378 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ac6c956, org.springframework.security.web.context.SecurityContextPersistenceFilter@571010af, org.springframework.security.web.header.HeaderWriterFilter@2495bc30, org.springframework.security.web.csrf.CsrfFilter@3bf572e0, org.springframework.security.web.authentication.logout.LogoutFilter@6d043931, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@12917a56, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bec2008, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@239c4792, org.springframework.security.web.session.SessionManagementFilter@12bc9450, org.springframework.security.web.access.ExceptionTranslationFilter@4ba1ccaf]
2024-07-17 20:04:18.647 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 20:04:18.647 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-17 20:04:21.202 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-17 20:04:21.285 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 20:04:24.723 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-17 20:04:25.026 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-17 20:04:25.469 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-17 20:04:25.499 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 20:04:25.557 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-17 20:04:25.575 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://*************:18500" protocol="consul" port="18500" simplified="true" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://*************:18500, simplified=true}]
2024-07-17 20:04:25.582 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-17 20:04:25.596 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-17 20:04:25.608 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-17 20:04:26.124 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.484 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.485 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.929 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:26.950 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.958 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.959 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.963 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:26.967 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.973 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.974 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.974 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:26.975 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.982 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.982 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.982 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:26.982 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:26.982 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:26.983 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.984 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:26.984 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:26.990 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:26.991 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:26.991 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:26.991 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.991 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.992 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:26.992 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:26.992 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:26.992 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:26.992 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:26.998 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:26.998 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:26.998 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:26.998 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:26.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:26.999 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:26.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:26.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:26.999 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.008 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.009 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.009 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.009 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.010 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.010 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.010 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.013 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.016 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.027 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.027 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.027 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:27.028 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.030 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.034 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.034 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 46
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 46
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 46
2024-07-17 20:04:27.035 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 46
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 46
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.035 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:27.037 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-8, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.037 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.037 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-8, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.037 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.037 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-8, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.043 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:04:27.045 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:27.045 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:27.045 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:27.045 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:27.047 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.048 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.048 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:27.048 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.049 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.049 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-9, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.050 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-9, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.050 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.050 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-9, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.054 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.054 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-10, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.055 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-10, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.055 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.055 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-10, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.058 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.058 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.058 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:27.059 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:04:27.065 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.065 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:04:27.065 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:04:27.065 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:04:27.065 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-11, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.065 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:04:27.066 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-11, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.071 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.071 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-11, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.074 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:04:27.074 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-12, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:04:27.075 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-12, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:27.075 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:27.075 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-12, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:27.665 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:27.671 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:27.672 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://*************:18500" protocol="consul" port="18500" simplified="true" />] will be used as the config center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.230 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.550 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.552 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.552 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.578 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.598 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.599 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://*************:18500" protocol="consul" port="18500" simplified="true" />] will be used as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.639 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.639 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.676 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.730 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5288&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.731 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=5288&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=5288&qos.enable=false&registry=consul&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.738 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.755 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.807 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.807 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.817 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.819 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:28.820 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.601 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.632 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.682 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@2a06eb97; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.722 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.731 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.731 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:29.741 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-17 20:04:29.809 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-17 20:04:29.829 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 49.226 seconds (JVM running for 52.057)
2024-07-17 20:04:30.043 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-17 20:04:30.043 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:04:30.047 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:04:30.050 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-17 20:04:30.050 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-17 20:04:30.079 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：***************************************************************************************************************************************************************************************,username = iotmp
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-11, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.084 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-12, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-9, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-10, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.085 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-11, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-9, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-8, groupId=auth_consumer-*********-6060] Successfully joined group with generation 47
2024-07-17 20:04:30.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-12, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.085 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-10, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-app-token-config-0]
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-8, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:04:30.092 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:04:30.092 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-app-token-config-0]
2024-07-17 20:04:30.770 [,,,,Not Auth Request!] [RMI TCP Connection(43)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-17 20:04:30.770 [,,,,Not Auth Request!] [RMI TCP Connection(43)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-17 20:04:30.803 [,,,,Not Auth Request!] [RMI TCP Connection(43)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 33 ms
2024-07-17 20:04:48.994 [,,,,Not Auth Request!] [Thread-61] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-17 20:04:48.994 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:48.995 [,,,,Not Auth Request!] [Thread-83] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=5288&qos.enable=false&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:48.996 [,,,,Not Auth Request!] [Thread-83] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=5288&qos.enable=false&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:48.996 [,,,,Not Auth Request!] [Thread-83] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:48.997 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-17 20:04:48.998 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:49.005 [,,,,Not Auth Request!] [Thread-83] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:49.007 [,,,,Not Auth Request!] [Thread-83] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:04:49.008 [,,,,Not Auth Request!] [Thread-83] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:20.515 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://*************:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-17 20:11:20.518 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-17 20:11:20.763 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-17 20:11:20.847 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-17 20:11:25.250 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-17 20:11:25.250 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-17 20:11:25.333 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 78ms. Found 12 repository interfaces.
2024-07-17 20:11:25.341 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.341 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.341 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.341 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-17 20:11:25.342 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-17 20:11:25.412 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-17 20:11:25.412 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-17 20:11:25.413 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-17 20:11:25.414 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-17 20:11:25.415 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-17 20:11:25.711 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-17 20:11:25.712 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-17 20:11:25.869 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-17 20:11:25.869 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-17 20:11:25.869 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-17 20:11:25.870 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-17 20:11:25.870 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-17 20:11:25.870 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-17 20:11:25.871 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-17 20:11:25.871 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-17 20:11:25.871 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-17 20:11:25.872 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-17 20:11:25.872 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-17 20:11:25.873 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-17 20:11:25.878 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 159ms. Found 0 repository interfaces.
2024-07-17 20:11:26.033 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-17 20:11:26.131 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:11:26.131 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-17 20:11:26.131 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:11:26.131 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:11:26.131 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-17 20:11:26.331 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:26.331 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:26.378 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:26.378 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:26.381 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-17 20:11:26.520 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=d256ff50-ab64-32b0-afc7-8f3f35b32b34
2024-07-17 20:11:26.659 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$e686a037] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:26.722 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$89826d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:26.778 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$eba583f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:26.896 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1f3c51cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:26.920 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9e429743] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:26.948 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$12d48eb4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:27.069 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2eee91b1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-17 20:11:27.564 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-17 20:11:27.600 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-17 20:11:27.611 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-17 20:11:27.611 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-17 20:11:27.696 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-17 20:11:27.696 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6814 ms
2024-07-17 20:11:27.917 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【不绑定机器码方式】获取机器编码.
2024-07-17 20:11:27.934 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从consul上的配置文件hy.license.content中读取license内容
2024-07-17 20:11:28.298 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 20:11:28.298 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-17 20:11:28.311 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@62966c9f
2024-07-17 20:11:28.567 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-17 20:11:30.154 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-17 20:11:30.817 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-17 20:11:30.927 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-17 20:11:31.042 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-17 20:11:31.115 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-17 20:11:31.118 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-17 20:11:31.301 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-17 20:11:31.456 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-17 20:11:32.394 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 20:11:32.726 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-17 20:11:35.680 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-17 20:11:35.842 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-17 20:11:35.964 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:13, workerId:12
2024-07-17 20:11:36.521 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-17 20:11:36.522 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-17 20:11:37.166 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-17 20:11:37.166 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-17 20:11:39.543 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-17 20:11:39.543 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-17 20:11:39.716 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@26b78c2]
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/dingTalk/**'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear/**'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/**'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-17 20:11:42.831 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-17 20:11:42.832 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-17 20:11:42.832 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/free/log/setLevel'], []
2024-07-17 20:11:42.832 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-17 20:11:42.832 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/websocket/**'], []
2024-07-17 20:11:43.041 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f849a79, org.springframework.security.web.context.SecurityContextPersistenceFilter@a55ed01, org.springframework.security.web.header.HeaderWriterFilter@17a65cb1, org.springframework.security.web.authentication.logout.LogoutFilter@32cc87e0, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@6304ff53, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@78288298, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2324100b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d4f7aab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2cad213d, org.springframework.security.web.session.SessionManagementFilter@1763a8a1, org.springframework.security.web.access.ExceptionTranslationFilter@5d4838fc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c49db66]
2024-07-17 20:11:43.097 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@4a74e70d, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@513156fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d579733, org.springframework.security.web.header.HeaderWriterFilter@3833a045, org.springframework.security.web.authentication.logout.LogoutFilter@5c3e0c92, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@4a094674, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@4fd43b14, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@68955382, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@5b9d7666, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@36bcae97, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@6d618003, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@3030a20, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@282c2bbd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d388775, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@331d7260, org.springframework.security.web.session.SessionManagementFilter@c7ba58d, org.springframework.security.web.access.ExceptionTranslationFilter@f014ff3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c510181]
2024-07-17 20:11:43.113 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@66896ed0, org.springframework.security.web.context.SecurityContextPersistenceFilter@61df99e6, org.springframework.security.web.header.HeaderWriterFilter@40bfd515, org.springframework.security.web.csrf.CsrfFilter@4b10e470, org.springframework.security.web.authentication.logout.LogoutFilter@7b84d8ef, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@165e3835, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@28f74096, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1716da4, org.springframework.security.web.session.SessionManagementFilter@204e6dda, org.springframework.security.web.access.ExceptionTranslationFilter@2d6b53a3]
2024-07-17 20:11:43.294 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-17 20:11:43.294 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-17 20:11:43.712 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-17 20:11:43.801 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 20:11:45.901 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-17 20:11:46.102 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-17 20:11:46.515 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-17 20:11:46.543 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-17 20:11:46.585 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application hostname="hy" qosEnable="false" name="HY-AUTH-DUBBO-PROVIDER" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-17 20:11:46.597 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry simplified="true" address="consul://*************:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, address=consul://*************:18500, simplified=true}]
2024-07-17 20:11:46.606 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-17 20:11:46.621 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-17 20:11:46.631 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-17 20:11:47.119 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.331 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.331 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.608 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.628 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.635 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.635 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.641 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:11:47.646 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.652 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.653 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.653 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:11:47.654 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.659 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.659 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.661 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:11:47.661 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:11:47.662 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.662 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.662 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:11:47.663 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.667 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:11:47.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:47.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:47.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.671 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.671 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.672 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:11:47.672 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [**************:29104]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = auth_consumer-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-17 20:11:47.678 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.678 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:11:47.679 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:47.679 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:47.679 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.680 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-17 20:11:47.680 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-17 20:11:47.680 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-17 20:11:47.686 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: T7tWdfHRQSCVAEpsDPe9Cw
2024-07-17 20:11:47.686 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:11:47.687 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:47.687 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:47.687 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.693 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 50
2024-07-17 20:11:47.693 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 50
2024-07-17 20:11:47.693 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 50
2024-07-17 20:11:47.695 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:47.695 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:47.695 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:47.697 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:11:47.699 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:47.699 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:47.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:11:47.739 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:47.741 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:47.741 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry simplified="true" address="consul://*************:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:47.924 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.069 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.072 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.072 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.102 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.122 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.122 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry simplified="true" address="consul://*************:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.166 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.166 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.208 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.270 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=20404&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.271 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=20404&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://*************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=20404&qos.enable=false&registry=consul&release=2.7.8&simplified=true&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.280 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.299 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.377 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.378 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.389 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.392 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.392 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:48.971 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /*************:20881, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.003 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.070 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@2132d9b; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.124 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.133 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.133 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: *********
2024-07-17 20:11:49.145 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-17 20:11:49.172 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-17 20:11:49.192 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 32.85 seconds (JVM running for 34.333)
2024-07-17 20:11:49.196 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-17 20:11:49.196 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-17 20:11:49.200 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-17 20:11:49.200 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-17 20:11:49.223 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：***************************************************************************************************************************************************************************************,username = iotmp
2024-07-17 20:11:49.637 [,,,,Not Auth Request!] [RMI TCP Connection(32)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-17 20:11:49.639 [,,,,Not Auth Request!] [RMI TCP Connection(32)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-17 20:11:49.700 [,,,,Not Auth Request!] [RMI TCP Connection(32)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 60 ms
2024-07-17 20:11:50.714 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:11:50.714 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:50.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 51
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 51
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 51
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 51
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:11:50.720 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 51
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:50.720 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:50.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:11:50.721 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:11:50.724 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:12:25.262 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_auth_config]的加载时间：88ms
2024-07-17 20:12:25.495 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_account
2024-07-17 20:12:25.669 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-17 20:12:25.672 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【paas】-【sys】
2024-07-17 20:12:25.680 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_performance4446
2024-07-17 20:12:25.681 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_performance4446是否需要验证码，失败次数0
2024-07-17 20:12:25.684 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-17 20:12:25.684 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-17 20:12:25.775 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【hy_performance4446】的密码状态正常。
2024-07-17 20:12:25.788 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-17 20:12:25.793 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_oauth_client_details]的加载时间：3ms
2024-07-17 20:12:26.001 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-07-17 20:12:26.005 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-07-17 20:12:26.261 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.c57d1a0b-01e2-439b-9c77-3cbab9b7d3f2]获取用户主键失败,获取为空
2024-07-17 20:12:29.828 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：user_role
2024-07-17 20:12:29.833 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：role
2024-07-17 20:12:31.786 [paas,sys,/login,,fcfc01bce7d44f62978d6bf87b7d4f90] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[paas_sys_user_login_info]的加载时间：2ms
2024-07-17 20:12:31.838 [,,,,Not Auth Request!] [task-1] ERROR c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 应用：sys，元数据表不存在：auth_sys_log_custom_handle
2024-07-17 20:15:00.019 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-17 20:11:35,673 to 2024-07-17 20:15:00,009
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.12| 22.22%|            18|             4|             0|             0|       16.1|         93
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-17 20:30:00.168 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-17 20:15:00,009 to 2024-07-17 20:30:00,051
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-17 20:45:00.013 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-17 20:30:00,051 to 2024-07-17 20:45:00,012
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-17 20:46:25.034 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Node 0 was unable to process the fetch request with (sessionId=1846979033, epoch=4128): INVALID_FETCH_SESSION_EPOCH.
2024-07-17 20:48:02.832 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:02.832 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:02.871 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:02.871 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:02.832 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:19.159 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:19.168 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:19.179 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:19.193 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:19.159 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:22.579 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-3-c2b61c16-e478-44d8-8a43-c1b53a78a8dc is not valid.
2024-07-17 20:48:22.579 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-5-723acdf5-8926-4fad-8c03-1faa5b0576fc is not valid.
2024-07-17 20:48:36.280 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:22.579 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-2-8cc280cb-ee88-4a27-8e6f-994244f2982e is not valid.
2024-07-17 20:48:36.280 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:36.280 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:36.280 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:22.579 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-6-f093bd63-95ca-482c-8b06-609f0b95a95b is not valid.
2024-07-17 20:48:36.280 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:48:37.148 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:37.176 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:37.176 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:37.176 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:37.194 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:48:37.241 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:48:37.241 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:48:37.241 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:48:37.242 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:48:37.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:48:37.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:48:37.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:48:37.317 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:48:37.336 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:48:37.337 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:48:37.336 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:48:37.336 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 53
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 53
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 53
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:48:38.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 53
2024-07-17 20:48:38.229 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:48:38.242 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:48:38.242 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:48:38.242 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:48:38.257 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:49:15.371 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.371 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.371 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.371 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.371 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:15.418 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:15.418 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:25.074 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:25.074 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:25.074 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:35.895 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:35.895 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:40.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:40.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-4-cf82b625-c58c-4f20-9d7a-9cd7c222922d is not valid.
2024-07-17 20:49:40.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-6-9a9cbd81-ddf0-437a-aaf5-9bad8bc2ec1e is not valid.
2024-07-17 20:49:40.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:40.850 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:49:40.850 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:49:40.850 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:40.852 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-2-dd526922-dd26-4a95-81bc-fe2f1c274d98 is not valid.
2024-07-17 20:49:40.852 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:49:40.852 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:49:40.852 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:40.854 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:40.850 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:49:40.878 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:49:40.879 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:48.234 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:48.234 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-5-11215e1b-ee1f-4db4-8181-186e3c9b4eb2 is not valid.
2024-07-17 20:49:48.234 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:49:48.235 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:49:48.235 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:48.234 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:48.254 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 57
2024-07-17 20:49:48.254 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:49:48.257 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:49:48.261 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 57
2024-07-17 20:49:48.261 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:49:48.261 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:49:48.254 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 57
2024-07-17 20:49:48.263 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:49:48.263 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:49:48.257 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 57
2024-07-17 20:49:48.289 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:49:48.290 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:49:48.234 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-3-ef6d0cdb-2f22-4526-be3c-d5c0fb0fcd2e is not valid.
2024-07-17 20:49:48.298 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:49:48.298 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:49:48.298 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:49:48.368 [,,,,Not Auth Request!] [OkHttp http://*************:18500/...] ERROR com.orbitz.consul.cache.ConsulCache - Error getting response from consul for keyvalue "/dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators", will retry in 10000 MILLISECONDS
java.net.SocketTimeoutException: timeout
	at okio.Okio$4.newTimeoutException(Okio.java:232)
	at okio.AsyncTimeout.exit(AsyncTimeout.java:276)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:243)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.java:358)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:230)
	at okhttp3.internal.http1.Http1ExchangeCodec.readHeaderLine(Http1ExchangeCodec.java:242)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.java:213)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.cache.TimeoutInterceptor.intercept(TimeoutInterceptor.java:53)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.Consul$Builder.lambda$withAclToken$2(Consul.java:419)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
	at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
	at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:49:48.368 [,,,,Not Auth Request!] [OkHttp http://*************:18500/...] ERROR com.orbitz.consul.cache.ConsulCache - Error getting response from consul for keyvalue "/dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators", will retry in 10000 MILLISECONDS
java.net.SocketTimeoutException: timeout
	at okio.Okio$4.newTimeoutException(Okio.java:232)
	at okio.AsyncTimeout.exit(AsyncTimeout.java:276)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:243)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.java:358)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:230)
	at okhttp3.internal.http1.Http1ExchangeCodec.readHeaderLine(Http1ExchangeCodec.java:242)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.java:213)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.cache.TimeoutInterceptor.intercept(TimeoutInterceptor.java:53)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.Consul$Builder.lambda$withAclToken$2(Consul.java:419)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
	at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
	at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:49:58.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:49:58.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:49:58.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.529 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:49:58.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:49:58.529 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:58.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.566 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:58.566 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:49:59.972 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:59.972 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:59.988 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:49:59.988 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:00.057 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:50:00.058 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:50:00.069 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:50:00.069 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:50:00.058 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:50:00.069 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:50:00.081 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:50:00.081 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:50:00.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:50:00.081 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:50:00.082 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:50:00.083 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:50:01.622 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 58
2024-07-17 20:50:01.622 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Successfully joined group with generation 58
2024-07-17 20:50:01.622 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 58
2024-07-17 20:50:01.622 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 58
2024-07-17 20:50:01.622 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 58
2024-07-17 20:50:02.095 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:50:01.640 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:50:02.104 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:50:02.095 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:50:02.104 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:50:02.104 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:50:02.095 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:50:02.552 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:50:02.552 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:50:06.657 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:50:26.151 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.151 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.163 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.162 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.163 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.179 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.179 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:26.203 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:26.180 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:29.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:29.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:29.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:29.679 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:50:29.680 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-5-9c24a069-b18f-4856-9076-9f3436ace9d0 is not valid.
2024-07-17 20:50:31.015 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:50:31.815 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:50:31.815 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:50:33.987 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-3-aeba36e8-8730-4cff-ba0e-2f94f6b994ce is not valid.
2024-07-17 20:50:33.974 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-4-65616460-6574-410a-912e-a584c01afaf2 is not valid.
2024-07-17 20:51:15.984 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:50:33.974 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed for since member id consumer-6-3ec34d57-87b4-4b59-a7a4-9bbce5e90484 is not valid.
2024-07-17 20:51:16.012 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:51:21.308 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:51:21.337 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:51:21.337 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=49s849ms392µs900ns).
2024-07-17 20:51:21.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:21.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Error sending fetch request (sessionId=593195088, epoch=180) to node 0: org.apache.kafka.common.errors.TimeoutException: Failed to send request after 30000 ms..
2024-07-17 20:51:21.337 [,,,,Not Auth Request!] [OkHttp http://*************:18500/...] ERROR com.orbitz.consul.cache.ConsulCache - Error getting response from consul for keyvalue "/dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators", will retry in 10000 MILLISECONDS
java.net.SocketTimeoutException: timeout
	at okio.Okio$4.newTimeoutException(Okio.java:232)
	at okio.AsyncTimeout.exit(AsyncTimeout.java:276)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:243)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.java:358)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:230)
	at okhttp3.internal.http1.Http1ExchangeCodec.readHeaderLine(Http1ExchangeCodec.java:242)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.java:213)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.cache.TimeoutInterceptor.intercept(TimeoutInterceptor.java:53)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.Consul$Builder.lambda$withAclToken$2(Consul.java:419)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
	at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
	at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:51:21.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:51:21.401 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:21.401 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:23.425 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:51:23.441 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:51:25.879 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:51:25.879 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:25.917 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:51:25.947 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Successfully joined group with generation 62
2024-07-17 20:51:25.947 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions []
2024-07-17 20:51:26.598 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:51:26.598 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-17 20:51:26.599 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:51:26.599 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:51:26.599 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:51:35.482 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-17 20:51:37.470 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:51:37.470 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:51:37.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:51:37.580 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:37.602 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:51:37.510 [,,,,Not Auth Request!] [OkHttp http://*************:18500/...] ERROR com.orbitz.consul.cache.ConsulCache - Error getting response from consul for keyvalue "/dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators", will retry in 10000 MILLISECONDS
java.net.SocketTimeoutException: timeout
	at okio.Okio$4.newTimeoutException(Okio.java:232)
	at okio.AsyncTimeout.exit(AsyncTimeout.java:286)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:241)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.java:358)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:230)
	at okhttp3.internal.http1.Http1ExchangeCodec.readHeaderLine(Http1ExchangeCodec.java:242)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.java:213)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.cache.TimeoutInterceptor.intercept(TimeoutInterceptor.java:53)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at com.orbitz.consul.Consul$Builder.lambda$withAclToken$2(Consul.java:419)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
	at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
	at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
	at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket Closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.Okio$2.read(Okio.java:140)
	at okio.AsyncTimeout$2.read(AsyncTimeout.java:237)
	... 30 common frames omitted
2024-07-17 20:59:45.764 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m29s752ms422µs800ns).
2024-07-17 20:51:37.687 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-17 20:59:45.764 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.812 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-17 20:59:45.812 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.860 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.860 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | auth_consumer-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.863 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:45.864 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Group coordinator **************:29104 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-17 20:59:45.966 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] WARN  cn.hy.auth.custom.dubbo.ConsulRegistry -  [DUBBO] fail to check pass for url: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, check id is: a47174d7, dubbo version: 2.7.8, current host: *********
com.ecwid.consul.v1.OperationException: OperationException(statusCode=500, statusMessage='Internal Server Error', statusContent='Unknown check "service:a47174d7"')
	at com.ecwid.consul.v1.agent.AgentConsulClient.agentCheckPass(AgentConsulClient.java:211)
	at com.ecwid.consul.v1.ConsulClient.agentCheckPass(ConsulClient.java:270)
	at cn.hy.auth.custom.dubbo.ConsulRegistry.checkPass(ConsulRegistry.java:232)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2024-07-17 20:59:45.968 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:45.968 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:59:45.969 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Successfully joined group with generation 65
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Successfully joined group with generation 65
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Successfully joined group with generation 65
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-6, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=auth_consumer-*********-6060] Setting newly assigned partitions []
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=auth_consumer-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-4-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:59:45.975 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-17 20:59:45.978 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-17 20:59:45.968 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=auth_consumer-*********-6060] Discovered group coordinator **************:29104 (id: ********** rack: null)
2024-07-17 20:59:46.021 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-5, groupId=auth_consumer-*********-6060] (Re-)joining group
2024-07-17 20:59:46.050 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://*************:20881/cn.hy.auth.custom.common.api.UserAccountApi?application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&release=2.7.8&timestamp=*************, dubbo version: 2.7.8, current host: *********

package cn.hy.auth.common.security.core.authentication.util;

import com.fasterxml.jackson.core.json.async.NonBlockingJsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class AppConfigParamUtil {

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    public Object getByKey(String lesseeCode, String appCode, String key) {
        String getLimitConfig = String.format("select address from %s_%s_bizgw_plugin_driver_business_cfg where bus_cfg_key = '%s'", lesseeCode, appCode, key);
        SqlParameterSource parameters = new MapSqlParameterSource();
        Object value = null;
        try {
            value = jdbcTemplate.queryForObject(getLimitConfig, parameters, Object.class);
        } catch (IncorrectResultSizeDataAccessException e) {
            log.error("{}参数不存在", key);
        }
        return value;
    }

}

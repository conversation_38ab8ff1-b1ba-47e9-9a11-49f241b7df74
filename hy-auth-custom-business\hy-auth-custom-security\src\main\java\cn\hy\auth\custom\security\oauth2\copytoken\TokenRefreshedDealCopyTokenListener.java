package cn.hy.auth.custom.security.oauth2.copytoken;

import cn.hy.auth.common.security.oauth2.event.TokenRefreshedSuccessEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述: 刷新token事件
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/26
 */
@AllArgsConstructor
@Component
@Slf4j
public class TokenRefreshedDealCopyTokenListener implements ApplicationListener<TokenRefreshedSuccessEvent> {
    private final CopyTokenService copyTokenService;
    @Override
    public void onApplicationEvent(TokenRefreshedSuccessEvent event) {
        String oldRefreshTokenValue = event.getRefreshTokenValue();
        if (!oldRefreshTokenValue.endsWith("-copy")){
            return;
        }
        String tokenValue = event.getoAuth2AccessToken().getValue();
        int expiresIn = event.getoAuth2AccessToken().getExpiresIn();
        String[] split = oldRefreshTokenValue.split("\\.");
        String lesseeCode = split[0];
        String appCode = split[1];
        Map<String,Object> userInfo = (Map<String, Object>) event.getoAuth2AccessToken().getAdditionalInformation().getOrDefault("user_info", new HashMap<>());
        String username = (String) userInfo.get("username");
        // copy token刷新时，需要删除缓存；
        String copyAccessToken = copyTokenService.getCopyAccessToken(oldRefreshTokenValue);
        if (StringUtils.isNotBlank(copyAccessToken)){
            copyTokenService.removeCopyToken(lesseeCode, appCode, username, copyAccessToken);
            copyTokenService.removeRefreshCopyAccessToken(oldRefreshTokenValue);
        }
        copyTokenService.cacheCopyToken(lesseeCode, appCode, username, tokenValue,expiresIn);
        copyTokenService.cacheCopyRefreshTokenToAccessToken(event.getoAuth2AccessToken().getRefreshToken().getValue(), tokenValue,expiresIn);
    }
}

package cn.hy.auth.custom.user.cache.service.impl.cache;

import cn.hy.auth.common.business.tool.redis.service.RedisService;
import cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;
import cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;
import cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;
import cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类描述:redis缓存处理
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@AllArgsConstructor
@Service
@NativeCacheHandle(value = NativeCacheEnum.REDIS)
public class RedisCacheServiceImpl implements NativeDiffCacheService {

    private final RedisService redisService;

    @Override
    public Set<Long> handle(NativeCacheDTO nativeCacheDTO) {
        String cacheName = String.format("%s::%s", nativeCacheDTO.getRootCacheName(), UserCacheKeyConst.TOKEN_USER_PREFIX);
        List<Object> cacheUserIds = redisService.getValuesByPrefix(cacheName);
        return cacheUserIds.stream().map(userId -> Long.valueOf(userId.toString())).collect(Collectors.toSet());
    }
}

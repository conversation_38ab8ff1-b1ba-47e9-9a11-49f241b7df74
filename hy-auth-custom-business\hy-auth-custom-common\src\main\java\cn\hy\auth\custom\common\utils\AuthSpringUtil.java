package cn.hy.auth.custom.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @description: 工具类，用于取出在Spring容器中的对象
 * @author: CAIPENG
 * @create: 2019-06-05 16:20
 **/
@Component
@Slf4j
public class AuthSpringUtil implements ApplicationContextAware {
    @Autowired
    private static ApplicationContext applicationContext;

    private static final String APPLICATIONCONTEXT_IS_NULL = "In AuthSpringUtil,applicationContext is null.";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        AuthSpringUtil.setUp(applicationContext);
    }

    public static void setUp(ApplicationContext applicationContext) {
        AuthSpringUtil.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 在Spring容器取对象
     *
     * @param beanId
     * @return
     */
    public static Object getBean(String beanId) {
        if (applicationContext == null) {
            log.warn(APPLICATIONCONTEXT_IS_NULL);
            return null;
        } else {
            return applicationContext.getBean(beanId);
        }
    }

    /**
     * 在Spring容器取对象
     *
     * @param clzzz
     * @return
     */
    public static <T> T getBean(Class<T> clzzz) {
        if (applicationContext == null) {
            log.error(APPLICATIONCONTEXT_IS_NULL);
            return null;
        } else {
            return applicationContext.getBean(clzzz);
        }

    }

    /**
     * 在Spring容器取对象
     *
     * @param beanName
     * @param clazz
     * @return
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        if (applicationContext == null) {
            log.error(APPLICATIONCONTEXT_IS_NULL);
            return null;
        } else {
            return applicationContext.getBean(beanName, clazz);
        }

    }
}

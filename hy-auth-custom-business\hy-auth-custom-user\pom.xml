<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-auth-custom-business</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-custom-user</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>hy-auth-custom-user</name>
    <description>自定义业务用户相关业务实现（账号，密码，关联信息）</description>
    <url>http://www.example.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-common</artifactId>
            <version>1.0.0-i18n-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-multi-app</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-custom-dubbo</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-sdk-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
        </pluginManagement>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <includes>
                    <include>application.yml</include>
                </includes>
            </testResource>
        </testResources>
    </build>
</project>

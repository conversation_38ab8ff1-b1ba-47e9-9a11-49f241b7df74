package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.service.EmailCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeMapServiceImpl;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.enums.UserAccountLockType;
import cn.hy.auth.custom.common.enums.UserResetPasswordType;
import cn.hy.auth.custom.user.account.domain.CheckUpdatePasswordDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.service.ForgetPasswordService;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title: EmailServiceImpl
 * @description: 邮箱
 * @date 2024/10/17
 */
@Slf4j
@Service
public class EmailServiceImpl extends ForgetPasswordService implements ValidateErrorService {

    private final static String EMAIL_PREFIX = "auth_captcha_email:";
    private final static String ERROR_NUMBER_PREFIX_EMAIL = "auth_captcha_email_error:";
    private final static String PERMISSION_PREFIX_EMAIL = "auth_captcha_email_temp_permission:";

    private final UserAccountLockService userAccountLockService;
    private final AppConfigParamUtil appConfigParamUtil;
    private final ForgetPasswordCacheManage cacheManage;
    private final SecurityProperties securityProperties;
    private final EmailCodeSender emailCodeSender;

    @Autowired
    public EmailServiceImpl(UserAccountLockService userAccountLockService, AppConfigParamUtil appConfigParamUtil, ForgetPasswordCacheManage cacheManage, SecurityProperties securityProperties, EmailCodeSender emailCodeSender) {
        this.userAccountLockService = userAccountLockService;
        this.appConfigParamUtil = appConfigParamUtil;
        this.cacheManage = cacheManage;
        this.securityProperties = securityProperties;
        this.emailCodeSender = emailCodeSender;
    }

    @Override
    public Map<String, Object> doCheck(CheckUpdatePasswordDTO checkUpdatePasswordDTO) {
        String email = checkUpdatePasswordDTO.getEmail();
        String emailCode = checkUpdatePasswordDTO.getCaptcha();

        String emailCodeCacheKey = EMAIL_PREFIX + email;
        String errorNUmberCacheKey = ERROR_NUMBER_PREFIX_EMAIL + email;

        SmsCodeMapServiceImpl.SmsCode cacheEmailCode = (SmsCodeMapServiceImpl.SmsCode) cacheManage.get(emailCodeCacheKey);
        Map<String, Object> resultMap = new HashMap<>();
        String message = "";
        String tempPermissionIdentification = "";
        boolean result = false;
        Object ifPresent = cacheManage.get(errorNUmberCacheKey);
        int errorNumber = ifPresent == null ? 0 : (Integer) ifPresent;
        // 查询是否有锁定记录
        Map<String, Object> judgeMap = userAccountLockService.isUserAccountLockByEmail(email);
        Boolean judge = (Boolean) judgeMap.get("judge");
        Boolean userExist = (Boolean) judgeMap.get("userExist");
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        if (!userExist) {
            message = LocaleUtil.getMessage("EmailServiceImpl.doCheck.msg1", "");
        } else if (judge) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg2", "");
        } else if (cacheEmailCode == null || cacheEmailCode.isExpried()) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg3", "");
        } else if (!emailCode.equals(cacheEmailCode.getCode())) {
            // 忘记密码邮箱找回错误上限次数
            Object errorLimitObj = appConfigParamUtil.getByKey(paramMap.get("lessee_code"), paramMap.get("app_code"), "sysdevForgetPwdEmailErrorLimit");
            int errorLimit = errorLimitObj == null ? 5 : Integer.parseInt(errorLimitObj.toString());
            int totalErrorNumber = errorNumber + 1;
            cacheManage.put(errorNUmberCacheKey, totalErrorNumber);
            message = String.format(LocaleUtil.getMessage("SmsServiceImpl.resule.msg4", ""), totalErrorNumber, errorLimit - totalErrorNumber);
            if (totalErrorNumber >= errorLimit) {
                message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg5", "");
                // 锁定账号
                userAccountLockService.lockUserAccountByEmail(email, UserAccountLockType.EMAIL_FORGET_PWD.getType());
                cacheManage.invalidate(errorNUmberCacheKey);
            }
        } else {
            result = true;
            String permissionCacheKey = PERMISSION_PREFIX_EMAIL + email;
            tempPermissionIdentification = generateTempIdentification(paramMap, Objects.toString(judgeMap.get("userId"), ""), Objects.toString(judgeMap.get("userAccountName"), ""));
            cacheManage.put(permissionCacheKey, tempPermissionIdentification);
            cacheManage.invalidate(emailCodeCacheKey);
            cacheManage.invalidate(errorNUmberCacheKey);
        }
        resultMap.put("tempPermissionIdentification", tempPermissionIdentification);
        resultMap.put("message", message);
        resultMap.put("result", result);
        return resultMap;
    }

    @Override
    public Map<String, Object> sendToThirdService(String type, String account, SmsLoginVO smsLoginVO) {
        String emailCodeCacheKey = EMAIL_PREFIX + account;
        SmsCodeMapServiceImpl.SmsCode cacheValue = (SmsCodeMapServiceImpl.SmsCode) cacheManage.get(emailCodeCacheKey);
        Map<String, Object> resultMap = new HashMap<>();
        boolean result = false;
        String message = "";
        if (cacheValue != null) {
            message = LocaleUtil.getMessage("EmailServiceImpl.sendToThirdService.msg1", "");
        } else {
            String emailCodeStr = RandomStringUtils.randomNumeric(securityProperties.getSmsAuth().getLength());
            log.info("忘记密码功能邮箱找回方式：邮箱：{}，生成的验证码：{}", account, emailCodeStr);
            // 邮件验证码5分钟过期
            SmsCodeMapServiceImpl.SmsCode emailCode = new SmsCodeMapServiceImpl.SmsCode(account, emailCodeStr, LocalDateTime.now().plusSeconds(300));

            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            Map<String, String> paramMap = ServletUtil.getParamMap(request);
            smsLoginVO.setSmsType(2);
            String sendResult = emailCodeSender.send(
                    account, emailCodeStr, paramMap.get("app_code"), paramMap.get("lessee_code"), smsLoginVO,
                    "sysdevForgetPwdEmailLimit"
            );
            if (StringUtils.isNotBlank(sendResult)) {
                message = sendResult;
            } else {
                // 邮件验证码5分钟过期
                cacheManage.put(emailCodeCacheKey, emailCode, 5, TimeUnit.MINUTES);
                String errorNUmberCacheKey = ERROR_NUMBER_PREFIX_EMAIL + account;
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                LocalDateTime tomorrowMidnight = LocalDateTime.of(tomorrow, LocalTime.MIDNIGHT);
                Date date = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
                cacheManage.put(errorNUmberCacheKey, 0, date);
                result = true;
                message = LocaleUtil.getMessage("EmailServiceImpl.sendToThirdService.msg2", "");
            }
        }
        resultMap.put("result", result);
        resultMap.put("message", message);
        return resultMap;
    }

    @Override
    public String getCheckType() {
        return UserResetPasswordType.EMAIL_CODE.getType();
    }

    @Override
    public boolean checkTempPermission(UserLoginAccountDTO userLoginAccountDTO, String tempPermissionIdentification) {
        return doCheckTempPermission(userLoginAccountDTO, tempPermissionIdentification);
    }

    @Override
    public void clearErrorTime(String email, String accountName, String emailCipherText, Long userId) {
        cacheManage.invalidate(ERROR_NUMBER_PREFIX_EMAIL + email);
    }

}

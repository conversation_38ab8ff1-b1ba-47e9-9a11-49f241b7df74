package cn.hy.auth.custom.route.dao;

import cn.hy.auth.custom.route.domain.OauthRequestRouting;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 请求转发到第三方系统的配置 Mapper 接口
 * </p>
 *
 *
 CREATE TABLE `oauth_request_routing` (
 `id` BIGINT(20) NOT NULL,
 `system_area_identify` VARCHAR(64) NOT NULL COMMENT '请求所属领域的系统标识，如2.x',
 `source_url` VARCHAR(256) NOT NULL COMMENT '源url 相对地址',
 `target_url` VARCHAR(256) NOT NULL COMMENT '目标url相对地址',
 `pre_route_script` TEXT NULL COMMENT '路由转发前的预处理脚本',
 `success_script` TEXT NULL COMMENT '调用成功后需要执行的groovy脚本 http code 为200',
 `failure_script` TEXT NULL COMMENT '调用失败后需要执行的groovy脚本 http code 非200',
 `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP,
 `remark` VARCHAR(128) NULL DEFAULT NULL COMMENT '备注说明',
 PRIMARY KEY (`id`) USING BTREE
 )
 COMMENT='请求转发到第三方系统的配置'
 COLLATE='utf8_general_ci'
 ENGINE=InnoDB
 ;
 *
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface OauthRequestRoutingDao {
    /**
     *  .
     * @param systemAreaIdentify .
     * @param sourceUrl .源地址
     * @return .
     */
   OauthRequestRouting selectBySystemAreaIdentify(@Param("systemAreaIdentify") String systemAreaIdentify, @Param("sourceUrl") String sourceUrl);
}

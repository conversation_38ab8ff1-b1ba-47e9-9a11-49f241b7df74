package cn.hy.auth.custom.multi;

import cn.hy.auth.custom.common.domain.authinfo.*;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2020-11-30 19:16
 **/
public class MyTest {

    @Test
    public void test() {
        AppAuthStrategyDTO appAuthInfoDTO = new AppAuthStrategyDTO();
        appAuthInfoDTO.setLesseeAccessName("hy");
        appAuthInfoDTO.setAppAccessName("iot");
        appAuthInfoDTO.setLoginRule(new AppAuthLoginRuleDTO());
        appAuthInfoDTO.getLoginRule().setCompetitionPolicy(new AppAuthCompetitionPolicyDTO());
        appAuthInfoDTO.getLoginRule().getCompetitionPolicy().setSameClientTypePolicy("forceLogin").setEnable(true);

        appAuthInfoDTO.getLoginRule()
                .setLoginSupportType(Lists.newArrayList(LoginTypeEnum.OAUTH2_PASSWORD, LoginTypeEnum.USERNAME_PASSWORD,
                        LoginTypeEnum.SMS_CODE, LoginTypeEnum.OAUTH2_CLIENT));

        appAuthInfoDTO.getLoginRule()
                .setLoginField(Lists.newArrayList(new AppAuthLoginFieldDTO("user_account_name", "", 1),
                        new AppAuthLoginFieldDTO("mobile", "", 1)));

        appAuthInfoDTO.getLoginRule().setPwdExpirePolicy(new AppAuthPwdExpirePolicyDTO());
        appAuthInfoDTO.getLoginRule().getPwdExpirePolicy().setPwdLastChangeTime("1111").setExpireRemindThreshold("2222").setEnable(true);

        appAuthInfoDTO.setLogoutRule(new AppAuthLogoutRuleDTO());
        appAuthInfoDTO.setLicenseRule(new AppAuthLicenseRuleDTO());
        appAuthInfoDTO.setPwdRule(new AppAuthPwdRuleDTO());


        appAuthInfoDTO.setUserAccountMapping(new AppAuthUserAccountMapping());
        appAuthInfoDTO.getUserAccountMapping().setTableName("user_account");
        appAuthInfoDTO.getUserAccountMapping().setUid("id");
        appAuthInfoDTO.getUserAccountMapping().setPassword("password");
        appAuthInfoDTO.getUserAccountMapping().setUsername("user_account_name");
        appAuthInfoDTO.getUserAccountMapping().setMobile("mobile");
        appAuthInfoDTO.getUserAccountMapping().setEmail("email");

        appAuthInfoDTO.setUserInfoMapping(new ArrayList<>());

        AppAuthUserInfoMapping appAuthUserInfoMapping = new AppAuthUserInfoMapping();
        appAuthUserInfoMapping.setTableName("user_account");
        appAuthUserInfoMapping.setUid("id");
        appAuthUserInfoMapping.setIdentifyCode("user");
        appAuthInfoDTO.getUserInfoMapping().add(appAuthUserInfoMapping);

        System.out.println(JSON.toJSONString(appAuthInfoDTO));
    }

    @Test
    public void test2() {
        String json = "{\"appAccessName\":\"iot\",\"lesseeAccessName\":\"hy\",\"licenseRule\":{},\"loginRule\":{\"competitionPolicy\":{\"blockLogin\":false,\"enable\":true,\"forceLogin\":true,\"sameClientTypePolicy\":\"forceLogin\"},\"loginField\":[{\"filed\":\"user_account_name\",\"matchRegExp\":\"\"},{\"filed\":\"mobile\",\"matchRegExp\":\"\"}],\"loginSupportType\":[\"OAUTH2_PASSWORD\",\"USERNAME_PASSWORD\",\"SMS_CODE\",\"OAUTH2_CLIENT\"],\"pwdExpirePolicy\":{\"enable\":true,\"expireRemindThreshold\":\"2222\",\"pwdLastChangeTime\":\"1111\"}},\"logoutRule\":{},\"pwdRule\":{},\"userAccountMapping\":{\"email\":\"email\",\"mobile\":\"mobile\",\"password\":\"password\",\"tableName\":\"user_account\",\"uid\":\"id\",\"username\":\"user_name\"},\"userInfoMapping\":{\"sqlView\":\"select * from auth_user_account\",\"uid\":\"id\"}}\n";

        AppAuthStrategyDTO appAuthInfoDTO = JSON.parseObject(json, AppAuthStrategyDTO.class);
        System.out.println(appAuthInfoDTO);
    }
}

package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.user.account.dao.UserSecurityManageDao;
import cn.hy.auth.custom.user.account.domain.*;
import cn.hy.auth.custom.user.account.enums.UsmAppParamEnum;
import cn.hy.auth.custom.user.account.enums.UsmPwdChangeEnum;
import cn.hy.auth.custom.user.account.service.UserSecurityManageService;
import cn.hy.id.IdWorker;
import cn.hy.metadata.engine.common.utils.PropertyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: UserSecurityManageServiceImpl
 * @description: 用户安全管理接口实现
 * @date 2024/1/15
 */
@Slf4j
@Service
public class UserSecurityManageServiceImpl implements UserSecurityManageService {

    /**
     * 每月默认30天
     */
    private static final Integer PER_MONTH_DEFAULT_DAY_NUM = 30;

    private static final ZoneOffset zoneOffset = ZoneOffset.ofHours(8);

    private final IdWorker idWorker;
    private final JdbcTemplate jdbcTemplate;
    private final UserSecurityManageDao userSecurityManageDao;

    @Autowired
    public UserSecurityManageServiceImpl(IdWorker idWorker, JdbcTemplate jdbcTemplate, UserSecurityManageDao userSecurityManageDao) {
        this.idWorker = idWorker;
        this.jdbcTemplate = jdbcTemplate;
        this.userSecurityManageDao = userSecurityManageDao;
    }

    @Override
    public boolean existsEnableParamCfg(String code) {
        // 存在数据，需要启用校验：true
        String lesseeCode = "";
        String appCode = "";
        if (AuthContext.getContext() != null) {
            lesseeCode = AuthContext.getContext().getLesseeCode();
            appCode = AuthContext.getContext().getAppCode();
        }
        String lesseeAppCode = lesseeCode + "_" + appCode;
        log.info("existsEnableParamCfg 上下文：{}", lesseeAppCode);
        if ("paas".equals(lesseeCode) && "sys".equals(appCode)) {
            return false;
        }
        List<BizgwPluginBusinessCfgDTO> list = getBizPluginBusinessCfgList(code);
        return !CollUtil.isEmpty(list);
    }

    @Override
    public List<BizgwPluginBusinessCfgDTO> getBizPluginBusinessCfgList(String code) {
        return userSecurityManageDao.selectParamCfgByCode(code);
    }

    @Override
    public boolean existsIgnoreWhiteList(HyUserDetails account) {
        // 表不存在，设定为无白名单，需要校验：false（外面取反）
        if (tableNotExist("sysdev_user_pwd_change_white_list")) {
            return false;
        }
        // 存在白名单数据，不需要校验：false
        String username = account.getUsername();
        List<UserPwdChangeWhiteListDTO> whiteList = userSecurityManageDao.selectPwdWhiteListByUser(username);
        return CollUtil.isNotEmpty(whiteList);
    }

    @Override
    public void updatePwdChangeStatus(String username, String pwdChangeStatus) {
        userSecurityManageDao.updatePwdChangeStatus(username, pwdChangeStatus);
    }

    @Override
    public boolean changeTimeReached(HyUserDetails account) {
        // 表不存在，无需校验：false
        if (tableNotExist("sysdev_user_pwd_change_interval")) {
            return false;
        }
        // true：需要校验；false：不需要校验
        boolean result = false;

        // 查看当前用户的密码更换时间间隔配置
        String username = account.getUsername();
        // 用户创建时间
        Object createTime = account.getAccount().get("create_time");
        Date userCreateTime = null;
        if (createTime instanceof Date) {
            userCreateTime = ((Date) createTime);
        } else if (createTime instanceof LocalDateTime) {
            LocalDateTime localDateTime = (LocalDateTime) createTime;
            userCreateTime = Date.from(localDateTime.toInstant(zoneOffset));
        }
        // 用户最后修改时间
        Object lastUpdateTime = account.getAccount().get("last_update_time");
        Date userLastUpdateTime = null;
        if (lastUpdateTime instanceof Date) {
            userLastUpdateTime = ((Date) lastUpdateTime);
        } else if (lastUpdateTime instanceof LocalDateTime) {
            LocalDateTime localDateTime = (LocalDateTime) lastUpdateTime;
            userLastUpdateTime = Date.from(localDateTime.toInstant(zoneOffset));
        }

        List<UserPwdChangeIntervalDTO> list = userSecurityManageDao.selectPwdChangeIntervalByUser(username);

        // 当前时间
        Date curDate = new Date();
        // 密码最近修改时间
        Date lastPwdChangeTime;
        // 用户未设置密码更换时间间隔，则以全局配置为主
        UserPwdChangeIntervalDTO dto = new UserPwdChangeIntervalDTO();
        String pwdChangeInterval;
        if (CollUtil.isEmpty(list)) {
            List<BizgwPluginBusinessCfgDTO> cfgList = getBizPluginBusinessCfgList(UsmAppParamEnum.PWD_CHANGE_INTERVAL.getCode());
            pwdChangeInterval = cfgList.get(0).getValue();
            lastPwdChangeTime = userLastUpdateTime;
        } else {
            // 默认取第一条
            dto = list.get(0);
            Date validEndTime = dto.getUserValidEndTime();
            // 有效结束时间跟当前时间比较
            if (ObjectUtil.isNotNull(validEndTime) && validEndTime.compareTo(curDate) < 0) {
                // i为-1 结束时间早于当前时间，此设置已无效
                return false;
            }
            // 计算时间间隔
            pwdChangeInterval = dto.getPwdChangeInterval();
            if (dto.getLastPwdChangeTime() == null) {
                // 如果表中的数据不存在“密码最近修改时间”，那么以用户账号表最后修改时间为主
                log.info("username：{} 密码最近修改时间为空", username);
                lastPwdChangeTime = userLastUpdateTime;
            } else {
                lastPwdChangeTime = dto.getLastPwdChangeTime();
            }
        }

        // 没有设置用户有效开始时间，则默认从用户创建时间开始算；若填写用户有效开始时间，则从有效开始时间起才计算时间间隔
        Date validStartTime = ObjectUtil.isNull(dto.getUserValidStartTime()) ? userCreateTime : dto.getUserValidStartTime();

        if (validStartTime == null || lastPwdChangeTime == null) {
            return false;
        }

        if (validStartTime.compareTo(curDate) > 0) {
            // 开始时间晚于当前时间，此设置暂未生效
            return false;
        }

        if (lastPwdChangeTime.compareTo(curDate) > 0) {
            // 修改的时间晚于当前时间，密码短期内无需修改
            return false;
        }

        // 计算时间间隔
        UsmPwdChangeEnum pwdChangeEnum = UsmPwdChangeEnum.getByCode(pwdChangeInterval);

        if (UsmPwdChangeEnum.NEVER.equals(pwdChangeEnum)) {
            // 设置永不，直接不校验，无需判断时间间隔
            return false;
        }

        // 开始时间
        LocalDateTime startLocalDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(validStartTime.getTime()), zoneOffset);
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        // 密码最近修改时间
        LocalDateTime lastUpdateLocalDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(lastPwdChangeTime.getTime()), zoneOffset);

        // 当前时间 和 密码最近修改时间 比较，看看差距多少天
        long gapDays = ChronoUnit.DAYS.between(lastUpdateLocalDateTime, now);
        log.info("username：{}，startLocalDateTime：{}，now：{}，gapDays：{}，pwdChangeInterval：{}，pwdChangeEnum：{}",
                username, startLocalDateTime, now, gapDays, pwdChangeInterval, pwdChangeEnum);

        if (gapDays <= 0) {
            // 密码最近修改时间 晚于/等于 当前时间
            return false;
        }

        switch (pwdChangeEnum) {
            case ONE_MONTH:
                result = whetherReached(gapDays, pwdChangeEnum, 1L);
                break;
            case TWO_MONTH:
                result = whetherReached(gapDays, pwdChangeEnum, 2L);
                break;
            case THREE_MONTH:
                result = whetherReached(gapDays, pwdChangeEnum, 3L);
                break;
            case HALF_A_YEAR:
                result = whetherReached(gapDays, pwdChangeEnum, 6L);
                break;
            case YEAR:
                result = whetherReached(gapDays, pwdChangeEnum, 12L);
                break;
            default:
                // 默认false
        }

        return result;
    }

    private boolean whetherReached(long gapDays, UsmPwdChangeEnum pwdChangeEnum, Long codeNum) {
        String code = pwdChangeEnum.getCode();
        return gapDays >= codeNum * PER_MONTH_DEFAULT_DAY_NUM;
    }

    @Override
    public boolean ipRestrict(HyUserDetails account, String ip) {
        List<BizgwPluginBusinessCfgDTO> cfgList = getBizPluginBusinessCfgList(UsmAppParamEnum.LOGIN_BIND_IP.getCode());
        BizgwPluginBusinessCfgDTO cfgDTO = cfgList.get(0);
        if ("0".equals(cfgDTO.getValue())) {
            // 未启用ip限制
            return false;
        }

        // 表不存在，无需校验：false
        if (tableNotExist("sysdev_user_login_bind_ip") || tableNotExist("sysdev_user_login_bind_ip_sublist")) {
            return false;
        }

        String username = account.getUsername();
        List<UserLoginBindIpDTO> list = userSecurityManageDao.selectLoginBindIpByUser(username);
        if (CollUtil.isEmpty(list)) {
            return false;
        }

        // 正常一个用户只会有一条
        if ("0".equals(list.get(0).getRestrictLoginAddress())) {
            return false;
        }

        List<BigDecimal> fidList = list.stream().map(UserLoginBindIpDTO::getId).collect(Collectors.toList());
        List<UserLoginBindIpSublistDTO> subList = userSecurityManageDao.selectLoginBindIpListByFid(fidList);
        if (CollUtil.isEmpty(subList)) {
            return false;
        }

        List<UserLoginBindIpSublistDTO> bingoList = subList.stream()
                .filter(bindIp -> StrUtil.equals(ip, bindIp.getInitialAddress()) || StrUtil.equals(ip, bindIp.getFinalAddress()))
                .collect(Collectors.toList());
        // 当前登录IP与绑定的IP一不一致：bingoList为空则不一致 即有限制true
        return CollUtil.isEmpty(bingoList);
    }

    @Override
    public void insertOrUpdatePwdChangeTimeStatus(UserAccountDTO account, String pwdChangeStatus, Date lastPwdChangeTime) {
        // 表不存在，无需校验：false
        if (tableNotExist("sysdev_user_pwd_change_interval")) {
            return;
        }
        // 查询应用设置配置信息
        String code = UsmAppParamEnum.PWD_CHANGE_INTERVAL.getCode();
        if (!existsEnableParamCfg(code)) {
            return;
        }
        List<BizgwPluginBusinessCfgDTO> cfgDTOList = userSecurityManageDao.selectParamCfgByCode(code);
        // 先查询当前用户在更换密码时间间隔表中是否存在数据，如果存在数据
        Long userAccountId = account.getId();
        try {
            List<UserPwdChangeIntervalDTO> list = userSecurityManageDao.selectPwdChangeIntervalByUserId(userAccountId);
            Date curDate = new Date();
            if (CollUtil.isEmpty(list)) {
                UserPwdChangeIntervalDTO userPwdChangeIntervalDTO = fillUserPwdChangeIntervalDTO(account, pwdChangeStatus, curDate, userAccountId, cfgDTOList);
                userSecurityManageDao.insertPwdChangeIntervalInfo(userPwdChangeIntervalDTO);
                log.debug("修改密码时insert更换密码时间间隔信息，userAccountId：{}", userAccountId);
            } else {
                userSecurityManageDao.updatePwdChangeTimeStatus(userAccountId, pwdChangeStatus, lastPwdChangeTime);
                log.debug("修改密码时update更换密码时间间隔信息，userAccountId：{}", userAccountId);
            }
        } catch (Exception e) {
            log.warn("新增/更新密码更换时间间隔信息异常：{}", e.getMessage());
        }
    }

    private UserPwdChangeIntervalDTO fillUserPwdChangeIntervalDTO(UserAccountDTO account, String pwdChangeStatus, Date curDate, Long userAccountId, List<BizgwPluginBusinessCfgDTO> cfgDTOList) {
        UserPwdChangeIntervalDTO userPwdChangeIntervalDTO = new UserPwdChangeIntervalDTO();
        userPwdChangeIntervalDTO.setId(BigDecimal.valueOf(idWorker.nextId()));
        userPwdChangeIntervalDTO.setDataVersion("1");
        userPwdChangeIntervalDTO.setCreateTime(curDate);
        userPwdChangeIntervalDTO.setCreateUserId(BigDecimal.valueOf(userAccountId));
        userPwdChangeIntervalDTO.setCreateUserName(account.getUserAccountName());
        userPwdChangeIntervalDTO.setLastUpdateTime(curDate);
        userPwdChangeIntervalDTO.setLastUpdateUserId(BigDecimal.valueOf(userAccountId));
        userPwdChangeIntervalDTO.setLastUpdateUserName(account.getUserAccountName());
        userPwdChangeIntervalDTO.setUserName((String) account.getAccount().getOrDefault("username", null));
        userPwdChangeIntervalDTO.setUserAccountName(account.getUserAccountName());
        userPwdChangeIntervalDTO.setUserValidStartTime(curDate);
        userPwdChangeIntervalDTO.setLastPwdChangeTime(curDate);
        userPwdChangeIntervalDTO.setPwdChangeInterval(cfgDTOList.get(0).getValue());
        // userPwdChangeIntervalDTO.setPwdChangeIntervalName();
        userPwdChangeIntervalDTO.setUserAccountId(BigDecimal.valueOf(userAccountId));
        userPwdChangeIntervalDTO.setPwdChangeStatus(pwdChangeStatus);
        // userPwdChangeIntervalDTO.setPwdChangeStatusName();
        return userPwdChangeIntervalDTO;
    }

    private boolean tableNotExist(String tableName) {
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        String appCode = AuthContext.getContext().getAppCode();
        if (StrUtil.hasBlank(lesseeCode, appCode)) {
            return true;
        }
        String fullTableName = lesseeCode + "_" + appCode + "_" + tableName;
        String sql = "SELECT table_name FROM information_schema.TABLES WHERE table_name =? And table_schema = ?";
        List<Map<String, Object>> tables = jdbcTemplate.queryForList(sql, fullTableName, PropertyUtil.getDbName());
        if (CollUtil.isEmpty(tables)) {
            log.debug("表{}不存在", fullTableName);
            return true;
        }
        return false;
    }

}


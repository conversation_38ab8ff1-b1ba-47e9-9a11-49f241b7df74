package cn.hy.auth.common.security.core.authentication.mobile.vo;

import lombok.Data;

import java.util.Map;

@Data
public class SmsLoginVO {

    private String lessee_code;

    private String mobile;

    private String mobileCipherText;

    private String app_code;

    private String ecName;

    private String apId;

    private String content;

    private String sign;

    private String addSerial;

    private String secretKey;

    private String url;

    // 短信类型 1-短信登录 2-手机号密码找回
    private Integer smsType;

    private Map<String, Object> smsTemplateData;

    private Map<String, Object> extendVariables;

}

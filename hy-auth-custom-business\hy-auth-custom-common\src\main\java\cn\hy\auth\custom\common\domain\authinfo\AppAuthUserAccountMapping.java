package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户账户字段映射关系
 *
 * <AUTHOR>
 * @date 2020-11-30 15:25
 **/
@Data
public class AppAuthUserAccountMapping implements Serializable {
    private static final long serialVersionUID = 5255310457413106209L;
    /**
     * 账户表表名
     */
    private String tableName;
    /**
     * 用户id对应字段编码
     */
    private String uid;
    /**
     * 密码对应字段编码
     */
    private String password;
    /**
     * 用户帐号对应字段编码
     */
    private String username;
    /**
     * 手机号码对应字段编码
     */
    private String mobile;
    /**
     * 邮箱对应字段编码
     */
    private String email;
}

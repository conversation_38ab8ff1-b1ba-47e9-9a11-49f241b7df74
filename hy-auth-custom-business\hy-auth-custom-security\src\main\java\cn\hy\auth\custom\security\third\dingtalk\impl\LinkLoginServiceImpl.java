package cn.hy.auth.custom.security.third.dingtalk.impl;

import cn.hutool.core.map.MapUtil;
import cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.service.LinkThirdLoginAccount;
import cn.hy.auth.common.security.core.authentication.social.service.SocialUsersConnectionService;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * aouth2 的code授权模式
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class LinkLoginServiceImpl implements LinkThirdLoginAccount {

    @Autowired
    private SocialUsersConnectionService socialUsersConnectionService;

    @Override
    public String getUserAccount(String code, String lesseeCode, String appCode, ProviderUserInfo providerUserInfo,
                                 HySocialAuthenticationToken authenticationToken, ProviderAppAccessToken providerAppAccessToken) {
        List<String> userAccountNames = socialUsersConnectionService.findUserAccountName(lesseeCode, appCode, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid());
        String userAccountName = null;
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userAccountNames)) {
            Map<String, Object> thirdUserFromBaseApp = socialUsersConnectionService.getThirdUserFromBaseApp(providerUserInfo.getProviderUserid(), null);
            if (MapUtil.isEmpty(thirdUserFromBaseApp)) {
                Map<String, Object> tipMap = new HashMap<>();
                tipMap.put("code", "A03002");
                tipMap.put("providerId", providerUserInfo.getProviderId());
                tipMap.put("providerUserId", providerUserInfo.getProviderUserid());
                tipMap.put("msg", "该第三方用户下的人员不存在");
                throw new InternalAuthenticationServiceException(JSON.toJSONString(tipMap));
            }
            String accountName = (String) thirdUserFromBaseApp.get("account_name");
            if (StringUtils.isEmpty(accountName)) {
                throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("LinkLoginServiceImpl.result.msg1", null) + thirdUserFromBaseApp.get("hr_code") + LocaleUtil.getMessage("LinkLoginServiceImpl.result.msg2", null));
            }
            userAccountName = accountName;
            socialUsersConnectionService.insertProviderUserInfo(lesseeCode, appCode, providerUserInfo);
            socialUsersConnectionService.bindThirdUser(providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid(), accountName);
        } else if (userAccountNames.size() > 1) {
            log.error("code:{},providerId:{},provideUserId:{},找到多条数据记录。userIds：{}", "", providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid(), userAccountNames);
            throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("LinkLoginServiceImpl.result.msg3", null) + providerUserInfo.getProviderId() + ",provideUserId:" + providerUserInfo.getProviderUserid() + LocaleUtil.getMessage("LinkLoginServiceImpl.result.msg4", null) + userAccountNames.size() + LocaleUtil.getMessage("LinkLoginServiceImpl.result.msg5", null));
        } else {
            userAccountName = userAccountNames.get(0);
        }
        //登陆成功后回填人员表
        socialUsersConnectionService.updateHrMember(providerUserInfo.getProviderId(), userAccountName, providerUserInfo.getProviderUserid());
        return userAccountName;
    }
}

package cn.hy.auth.common.security.core.authentication.common;

import cn.hy.auth.common.security.core.authentication.social.enums.LoginType;
import cn.hy.auth.common.security.core.authentication.social.service.AppThirdLoginAccount;
import cn.hy.auth.common.security.core.authentication.social.service.IThirdLoginAccount;
import cn.hy.auth.common.security.core.authentication.social.service.LinkThirdLoginAccount;

/**
 * 工厂
 * *
 *
 * <AUTHOR>
 * @date ********
 */
public class ThirdLoginBeanFactory {

    public static IThirdLoginAccount getLoginBean(LoginType loginType) {
        switch (loginType) {
            case THIRD_LINK:
                return AuthCenterSpringUtil.getBean(LinkThirdLoginAccount.class);
            case THIRD_APP:
                return AuthCenterSpringUtil.getBean(AppThirdLoginAccount.class);
            default:
                return null;
        }
    }
}

package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 对oauth/token接口的参数，提前做一些参数校验
 * 因为在spring security oauth2的filter之前，自定义的filter需要获取参数做一些处理
 * 当参数非法时，自定义filter处理异常抛出自定义业务异常，就没有抛出接口参数校验异常，
 * 导致异常描述不清晰，所以需要在自定义filter前面实现部分参数校验
 *
 * <AUTHOR>
 * @date 2021-02-02 09:38
 **/
@Order(-1010)
@Component
@Slf4j
public class OauthEndpointParamValidateFilter extends AbstractAuthFilter {

	private static final String       REQUEST_METHOD      = "POST";
	private static final String       LOGIN_URL           = "/oauth/token";
	private static final String       GRANT_TYPE          = "grant_type";
	private static final List<String> GRANT_TYPE_SUPPORTS = Lists.newArrayList("authorization_code", "password", "client_credentials", "implicit",
	                                                                           "refresh_token");

	private RequestMatcher requiresAuthenticationRequestMatcher;

	public OauthEndpointParamValidateFilter() {
		//要拦截的请求
		super();
		requiresAuthenticationRequestMatcher = new AntPathRequestMatcher(LOGIN_URL, REQUEST_METHOD);
	}

	/**
	 * 子类过滤器逻辑
	 *
	 * @param request     .
	 * @param response    .
	 * @param filterChain .
	 * @throws IOException      .
	 * @throws ServletException .
	 */
	@Override
	protected void doMyFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
		if (requiresAuthenticationRequestMatcher.matches(request)) {
			String grantType = request.getParameter(GRANT_TYPE);
			if (StringUtils.isBlank(grantType)) {
				throw new AuthBusinessException(AuthErrorCodeEnum.A0103.code(), "Missing grant type");
			} else if (!GRANT_TYPE_SUPPORTS.contains(grantType)) {
				throw new AuthBusinessException(AuthErrorCodeEnum.A0103.code(), "Unsupported grant type: " + grantType);
			}
		}

		filterChain.doFilter(request, response);
	}
}

package cn.hy.auth.common.security.core.properties;

import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * 表单认证相关配置
 *
 * <AUTHOR>
 * @date 2020/11/12 18:27
 */
@Setter
@Getter
public class FormAuthProperties {
    /**
     * 登录地址
     */
    private String loginPage = "/login";
    /**
     * 登录filter 拦截处理的url
     */
    private String loginProcessingUrl = "/login";
    /**
     * 账号字段名
     */
    private String usernameParameter = "username";
    /**
     * 密码字段名
     */
    private String passwordParameter = "password";

    private Set<String> permitAll = Sets.newHashSet();

    public Set<String> getPermitAll() {
        permitAll.add(loginPage);
        permitAll.add(loginProcessingUrl);
        return permitAll;
    }
}

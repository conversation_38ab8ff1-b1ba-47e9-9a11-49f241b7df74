package cn.hy.auth.common.business.oauth2.starter;

import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeService;
import cn.hy.auth.common.security.core.authentication.mobile.service.ValidateCodeGenerator;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeGenerator;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeLogPrintSender;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeRedisServiceImpl;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeMapServiceImpl;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.oauth2.client.ClientDetailsServiceProvider;
import cn.hy.auth.common.security.oauth2.client.DefaultClientDetailsServiceProvider;
import cn.hy.auth.common.security.oauth2.exception.HyWebResponseExceptionTranslator;
import cn.hy.auth.common.security.oauth2.extend.HyAccessDeniedHandler;
import cn.hy.auth.common.security.oauth2.extend.HyAuthenticationFailureHandler;
import cn.hy.auth.common.security.oauth2.extend.HyAuthenticationSuccessHandler;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

import javax.sql.DataSource;

/**
 * 类描述：通用业务自动装配类
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 16:49
 **/
@Configuration
public class CommonAuthAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean
    public AccessDeniedHandler accessDeniedHandler() {
        return new HyAccessDeniedHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationFailureHandler authenticationFailureHandler(ObjectMapper objectMapper,
                                                                     SecurityProperties securityResponseFormatProperties,
                                                                     ApplicationContext applicationContext,WebResponseExceptionTranslator webResponseExceptionTranslator) {
        return new HyAuthenticationFailureHandler(objectMapper,
                securityResponseFormatProperties, webResponseExceptionTranslator,applicationContext);
    }

    @Bean
    @ConditionalOnMissingBean
    public ClientDetailsServiceProvider clientDetailsServiceProvider(DataSource dataSource) {
        return new DefaultClientDetailsServiceProvider(dataSource);
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationSuccessHandler authenticationSuccessHandler(TokenStore tokenStore,
                                                                     TokenServicesProperties tokenServicesProperties,
                                                                     ApplicationContext applicationContext,
                                                                     ClientDetailsService clientDetailsService,
                                                                     ObjectMapper objectMapper,
                                                                     PasswordEncoder passwordEncoder,
                                                                     SecurityProperties securityResponseFormatProperties) {
        return new HyAuthenticationSuccessHandler(tokenStore, tokenServicesProperties,
                applicationContext, clientDetailsService, objectMapper, passwordEncoder, securityResponseFormatProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public WebResponseExceptionTranslator<OAuth2Exception> webResponseExceptionTranslator() {
        return new HyWebResponseExceptionTranslator();
    }

    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }

    @Bean
    @ConditionalOnMissingBean
    public ValidateCodeGenerator smsCodeGenerator(SecurityProperties securityProperties) {
        return new SmsCodeGenerator(securityProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public SmsCodeSender smsCodeSender() {
        return new SmsCodeLogPrintSender();
    }

    @Bean
    @ConditionalOnMissingBean(SmsCodeService.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "redis"
    )
    public SmsCodeService smsCodeRedisService() {
        return new SmsCodeRedisServiceImpl();
    }

    @Bean
    @ConditionalOnMissingBean(SmsCodeService.class)
    public SmsCodeService smsCodeService() {
        return new SmsCodeMapServiceImpl();
    }
}

package cn.hy.auth.custom.security.verifycode;

import cn.hy.auth.custom.common.utils.LocaleUtil;

/**
 * 类描述：响应信息常量类
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/6/24 10:48
 **/

public enum ReturnCode {
    /**
     * 成功
     */
    SUCCESS("00000", "请求处理成功"),
    /**
     * SAAS成功
     */
    SUCCESS_SAAS("SA0000", "请求处理成功"),
    /**
     * 用户账号或密码错误
     */
    A0200("A0200", "用户账号或密码错误"),
    /**
     * 验证码错误
     */
    A0202("A0202", "验证码错误"),
    /**
     * 密码错误次数达到配置值需要验证码
     */
    A0204("A0204", "需要验证码"),
    /**
     * 访问权限异常
     */
    A0300("A0300", "访问权限异常"),
    /**
     * 访问未授权
     */
    A0301("A0301", "访问未授权"),
    /**
     * 授权已过期
     */
    A0311("A0311", "授权已过期"),
    /**
     * 用户密码错误
     */
    A0201("A0201", "用户账户不存在"),
    /**
     * 账号已过期
     */
    A0203("A0203", "账号已过期"),
    /**
     * 用户请求参数错误
     */
    A0400("A0400", "用户请求参数错误"),
    /**
     * 请求参数值超出允许的范围
     */
    A0420("A0420", "请求参数值超出允许的范围"),
    /**
     * url地址不在服务范围
     */
    A0422("A0422", "url地址不在服务范围"),
    /**
     * 用户输入内容非法
     */
    A0430("A0430", "用户输入内容非法"),
    /**
     * 用户输入内容非法
     */
    A0427("A0427", "请求 JSON 解析失败"),

    /**
     * 用户资源异常
     */
    A0600("A0600", "用户资源异常"),

    /**
     * 数据库服务出错
     */
    C0300("C0300", "数据库服务出错"),
    /**
     * 表不存在
     */
    C0311("C0311", "表不存在"),
    /**
     * 列不存在
     */
    C0312("C0312", "列不存在"),
    /**
     * 表存在数据
     */
    C0313("C0313", "表存在数据"),
    /**
     * 列存在数据
     */
    C0314("C0314", "列存在数据"),
    /**
     * 存在表被引用
     */
    C0315("C0315", "存在表被其他表引用"),
    /**
     * 存在列被引用
     */
    C0316("C0316", "存在列被被其他表引用"),
    /**
     * 系统字段不允许删除
     */
    C0317("C0317", "系统字段不允许删除"),
    /**
     * 系统字段不允许删除
     */
    C0318("C0318", "系统表不允许删除"),
    /**
     * 数据不存在
     */
    C0319("C0319", "数据不存在"),
    /**
     * 数据已存在
     */
    C0320("C0320", "数据已存在"),

    /**
     * 下载导出结果，文件路径为空
     */
    C0321("C0321", "下载文件，文件完整路径不允许为空。"),
    /**
     * 下载导出结果，文件名为空
     */
    C0322("C0322", "下载文件，文件名不允许为空。"),
    /**
     * 下载导出结果，文件不存在
     */
    C0323("C0323", "下载文件，文件不存在。"),
    /**
     * 目录创建失败
     */
    C0324("C0324", "目录创建失败。"),
    /**
     * 文件格式不合法
     */
    C0325("C0325", "文件格式不合法。"),
    /**
     * 文件上传失败
     */
    C0326("C0326", "文件上传失败。"),
    /**
     * 文件上传失败
     */
    C0327("C0327", "表元数据导入失败。"),

    /**
     * 导入导出模板不存在
     */
    C0328("C0328", "导入导出模板不存在。"),

    /**
     * 业务建模API不存在
     */
    C0329("C0329", "业务建模API不存在。"),

    /**
     * 存在关联
     */
    C0340("C0340", "存在关联"),

    /**
     * 表业务字段为空
     */
    C0330("C0330", "表的业务字段为空，不创建模板。"),

    /**
     * 单元格信息组装失败
     */
    C0331("C0331", "导入导出单元格信息构建失败。"),

    /**
     * 导入导出Sheet名称不允许重复
     */
    C0332("C0332", "导入导出模板Sheet名称不允许重复。"),

    /**
     * 超级表无法回退
     */
    C0333("C0333", "超级表不是转换而来，无法回退"),

    /**
     * 调用第三方服务出错
     */
    C0001("C0001", "调用第三方服务出错"),
    /**
     * 系统错误
     */
    B0001("B0001", "系统执行出错");

    /**
     * 响应码
     */
    private final String code;
    /***
     *  响应信息
     */
    private final String msg;

    ReturnCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String code() {
        return code;
    }

    public String msg() {
        return LocaleUtil.getMessage("ReturnCode." + this.name(), null);
    }

    @Override
    public String toString() {
        return "ReturnCode{" +
                "code='" + code + '\'' +
                ", msg='" + LocaleUtil.getMessage("ReturnCode." + this.name(), null) + '\'' +
                '}';
    }
}



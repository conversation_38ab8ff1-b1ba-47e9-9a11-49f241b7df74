package cn.hy.auth.custom.security.third.miniprogram.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import com.alibaba.fastjson.JSONObject;
import lombok.Setter;

import java.util.Map;

/**
 * 微信小程序用户信息
 *
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Setter
public class MiniProgramProvider implements ProviderInfo{

    private String providerId;
    private String appKey;
    private String appSecret;
    private String originConfig;

    @Override
    public String getProviderId() {
        return providerId;
    }

    @Override
    public String getAppKey() {
        return appKey;
    }

    @Override
    public String getAppSecret() {
        return appSecret;
    }

    @Override
    public String getAppAccessTokenUrl() {
        return "/cgi-bin/token";
    }

    @Override
    public String getAppUserInfoUrl() {
        return  null;
    }

    @Override
    public Map<String, Object> originConfigMap() {
        if(originConfig!=null){
            return (Map<String, Object>) JSONObject.parse(originConfig);
        }
        return null;
    }
}

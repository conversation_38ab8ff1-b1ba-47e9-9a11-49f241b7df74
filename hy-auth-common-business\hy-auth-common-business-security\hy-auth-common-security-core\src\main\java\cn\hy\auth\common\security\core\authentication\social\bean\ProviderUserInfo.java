package cn.hy.auth.common.security.core.authentication.social.bean;

/**
 * 类描述：第三方服务提供商中的用户信息
 *
 * <AUTHOR> by fuxin<PERSON>
 * @date 2022/9/9 16:28
 **/
public interface ProviderUserInfo {
    /**
     * 第三方开放平台标识
     * @return .
     */
    String getProviderId();
    /**
     *  用户id
     * @return .
     */
    String getProviderUserid();

    /**
     * 第三方应用用户唯一标识
     * @return .
     */
    String getUnionId();

    /**
     * 用户显示名称
     * @return .
     */
    String getUserDisplayName();
    /**
     * 头像
     * @return .
     */
    String getImageUrl();
    /**
     * email
     * @return .
     */
    String getEmail();
    /**
     * 手机电话
     * @return .
     */
    String getPhone();

    /**
     *  接口返回用户信息的原始json字符串
     * @return .
     */
    String getUserInfoJson();

}

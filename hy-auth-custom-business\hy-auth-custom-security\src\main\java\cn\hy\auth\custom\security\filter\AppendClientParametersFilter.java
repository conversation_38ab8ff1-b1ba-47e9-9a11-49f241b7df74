package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 对/login 和普通的oauth/token账号密码登录方式自动增加client_id 和client_secrt ,
 * 确保client_id 和client_secrt不会明文传输，保证安全性
 *
 * 自动添加默认的
 *
 * <AUTHOR>
 * @date 2023-11-02 09:38
 **/

@Order(-1010)
@Component
@Slf4j
@ConditionalOnProperty(
        prefix = "hy.security.oauth2",
        name = "defaultClientEnable",
        havingValue = "true",matchIfMissing = true
)
public class AppendClientParametersFilter extends AbstractAuthFilter {
    private static final String       REQUEST_METHOD      = "POST";
    private static final String       LOGIN_URL           ="/login";

    private final RequestMatcher requiresAuthenticationRequestMatcher;
    private final RequestMatcher copyTokenRequestMatcher;
    @Value("${hy.security.oauth2.defaultClientId:client_hy_web}")
    private String defaultClientId;
    @Value("${hy.security.oauth2.defaultClientSecret:hy123456}")
    private String defaultClientSecret;
    private AppendClientParametersFilter(){
        requiresAuthenticationRequestMatcher = new AntPathRequestMatcher(LOGIN_URL, REQUEST_METHOD);
        copyTokenRequestMatcher = new AntPathRequestMatcher("/login/copy-token", REQUEST_METHOD);
    }
    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (isCommonLogin(request) || isCopyTokenLogin(request) || isPasswordLogin(request)||isRefreshToken(request)) {
            if (blankClientInfo(request)) {
                // 登录情况下，没有client 信息，自动补充默认的
                Map<String, String[]> additionalParams = new HashMap<>();
                additionalParams.put(LoginParamterConts.CLIENT_ID, new String[]{defaultClientId});
                additionalParams.put(LoginParamterConts.CLIENT_SECRET, new String[]{defaultClientSecret});
                EnhancedHttpRequest enhancedHttpRequest = new EnhancedHttpRequest(request, additionalParams);
                // pass the request along the filter chain
                filterChain.doFilter(enhancedHttpRequest, response);
            } else {
                filterChain.doFilter(request, response);
            }
        } else {
            filterChain.doFilter(request, response);
        }
    }

    private boolean blankClientInfo(HttpServletRequest request){
      return StringUtils.isBlank(request.getParameter(LoginParamterConts.CLIENT_ID)) || StringUtils.isBlank(request.getParameter(LoginParamterConts.CLIENT_SECRET));
    }
    private boolean isCommonLogin(HttpServletRequest request){
        return requiresAuthenticationRequestMatcher.matches(request);
    }
       private boolean isCopyTokenLogin(HttpServletRequest request){
        return copyTokenRequestMatcher.matches(request);
    }
    /**
     * 判断是否Client模式的登录
     * @param request .
     * @return .
     */
    private Boolean isPasswordLogin(HttpServletRequest request){
        String grantType = request.getParameter(LoginParamterConts.GRANT_TYPE);
        return "password".equals(grantType);
    }

    /**
     * 判断是否refresh token
     * @param request .
     * @return .
     */
    private Boolean isRefreshToken(HttpServletRequest request){
        String grantType = request.getParameter(LoginParamterConts.GRANT_TYPE);
        return "refresh_token".equals(grantType);
    }



}

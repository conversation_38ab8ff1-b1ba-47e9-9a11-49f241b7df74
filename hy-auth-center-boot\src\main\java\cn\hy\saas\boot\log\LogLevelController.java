package cn.hy.saas.boot.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/4/27 14:05
 **/
@RestController
@RequestMapping("/free")
public class LogLevelController {
    @GetMapping("/log/setLevel")
    public Boolean changeLogLevel(@RequestParam(name = "logLevel") String logLevel,
                                  @RequestParam(name = "packageName") String packageName) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        if (Objects.isNull(packageName)) {
            loggerContext.getLogger("root").setLevel(Level.toLevel(logLevel));
        } else {
            loggerContext.getLogger(packageName).setLevel(Level.toLevel(logLevel));
        }
        return true;
    }
}

package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * 账户状态对应处理策略
 * 状态值定义  0:禁用，1：启用。默认是1
 *
 * <AUTHOR>
 * @date 2020-12-03 16:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthAccountStatusPolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -311060378397460604L;
    private static final boolean DEFAULT_VALUE = Boolean.TRUE;
    private static final String TRUE_FLAG = "1";
    private static final String FALSE_FLAG = "0";

    /**
     * 帐号状态对应字段编码
     * 0:禁用，1：启用。
     */
    private String accountStatus;

    /**
     * 解析出属性值
     *
     * @param data 数据集
     * @return 账号是否启用
     */
    public Boolean parseTheValue(Map<String, Object> data) {
        if (!isEnable() || MapUtils.isEmpty(data)) {
            //未做用户状态控制，则用户默认状态为启用
            return true;
        }
        String value = MapUtils.getString(data, accountStatus, TRUE_FLAG);
        return TRUE_FLAG.equals(value);
    }
}

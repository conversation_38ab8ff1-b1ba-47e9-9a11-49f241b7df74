<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-auth-common-business-security</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-common-security-oauth2</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>hy-auth-common-security-oauth2</name>
    <url>http://www.example.com</url>
    <description>oauth2协议为主体的认证项目,适用于前后端分离以及app等智能终端项目</description>

    <dependencies>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-common-security-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
    </dependencies>
</project>

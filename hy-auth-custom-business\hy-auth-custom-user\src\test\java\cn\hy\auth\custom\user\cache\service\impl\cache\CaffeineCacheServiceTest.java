package cn.hy.auth.custom.user.cache.service.impl.cache;

import cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;
import cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import com.github.benmanes.caffeine.cache.Cache;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.Mockito.when;

/**
 * 类描述: Caffeine缓存类测试
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/3
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CaffeineCacheServiceImpl.class})
public class CaffeineCacheServiceTest {

    @Resource(name = "caffeineCacheServiceImpl")
    private NativeDiffCacheService nativeDiffCacheService;

    @MockBean
    private Cache cache;

    @Test
    public void handle() {
        ConcurrentHashMap map = new ConcurrentHashMap();
        map.put(UserCacheKeyConst.LOGIN_NAME_PREFIX + 1, 1L);

        when(cache.asMap()).thenReturn(map);
        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(cache).build();
        Set<Long> userId = nativeDiffCacheService.handle(nativeCacheDTO);
        Assert.assertEquals(map.size(), userId.size());
    }
}
package cn.hy.auth.custom.multi.systable;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.common.utils.PropertyUtil;
import cn.hy.auth.custom.multi.event.AppAuthInfoLoadEvent;
import cn.hy.id.IdWorker;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 认证中心涉及的应用信息--系统表
 * 该表全局只有一个，无需增加表名前缀
 *
 * <AUTHOR>
 * @date 2020-12-07 10:37
 **/
@Service
@Slf4j
public class AppInvolvedTableServiceImpl implements AppAuthSystemTableService {

    private static final String TABLE_NAME = "auth_app_involved";

    private final JdbcTemplate jdbcTemplate;

    private final CacheManager cacheManager;
    private final IdWorker idWorker;
    static final String ROOT_CACHE_NAME = "hy_auth_auth_app_involved";

    private static final Pattern DATABASE_NAME_PATTERN = Pattern.compile("jdbc:(?<db>\\w+):.*((//)|@)(?<host>.+):(?<port>\\d+)(/|(;DatabaseName=)|:)(?<dbName>\\w+.+)\\?");
    private final  ConcurrentHashMap<String,Object> parallelLockMap = new ConcurrentHashMap<>();

    @Autowired
    private DataSourceProperties dataSourceProperties;

    public AppInvolvedTableServiceImpl(JdbcTemplate jdbcTemplate, @Qualifier("hyAuthSystemConfigCacheManager") CacheManager cacheManager, IdWorker idWorker) {
        this.jdbcTemplate = jdbcTemplate;
        this.cacheManager = cacheManager;
        this.idWorker = idWorker;
    }


    @EventListener
    @Override
    public void onAuthInfoLoaded(AppAuthInfoLoadEvent event) {
        String lesseeCode = event.getLesseeCode();
        String appCode = event.getAppCode();

        log.info("初始化涉及的应用信息表--【{}】-【{}】", lesseeCode, appCode);
        if (StringUtils.isBlank(lesseeCode) || StringUtils.isBlank(appCode)) {
            log.debug("租户编码或应用编码为空，跳过处理。");
            return;
        }

        if (!isTableExist()) {
            log.info("涉及的应用信息表不存在，准备创建表：{}", TABLE_NAME);
            createTable();
        }

        if (!isDataExist(lesseeCode, appCode)) {
            log.info("涉及的应用信息数据不存在，准备插入数据");
            insertData(lesseeCode, appCode);
        }
    }

    /**
     * 检查判断表结构是否存在
     *
     * @return 是否存在表
     */
    private boolean isTableExist() {
        String sql = "SELECT table_name FROM information_schema.TABLES WHERE table_schema = ? And table_name =?";
        List<Map<String, Object>> data = jdbcTemplate.queryForList(sql, PropertyUtil.getDbName(), TABLE_NAME);
        return CollectionUtils.isNotEmpty(data);
    }

    /**
     * 执行建表语句
     */
    private void createTable() {
        String sql = "CREATE TABLE " + TABLE_NAME + " ( id BIGINT NOT NULL AUTO_INCREMENT, " +
                "lessee_code VARCHAR (32) NULL, app_code VARCHAR (32) NULL,`status` TINYINT(4) NULL DEFAULT '1' COMMENT '应用可登陆状态，0：不可登陆；1：可登陆', PRIMARY KEY (id));";
        jdbcTemplate.execute(sql);
    }

    /**
     * 判断应用信息是否存在表中
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @return 是否存在对应数据
     */
    private boolean isDataExist(String lesseeCode, String appCode) {
        String sql = "Select id From " + TABLE_NAME + " Where lessee_code=? And app_code=?";
        List<Map<String, Object>> data = jdbcTemplate.queryForList(sql, lesseeCode, appCode);
        return CollectionUtils.isNotEmpty(data);
    }

    /**
     * 插入数据
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     */
    private void insertData(String lesseeCode, String appCode) {
        long id = idWorker.nextId();
        String sql =
                "Insert into " + TABLE_NAME + "(id,lessee_code, app_code) values('" +id+"','"+ lesseeCode + "','" + appCode + "')";
        jdbcTemplate.execute(sql);

    }


    /**
     * 验证应用信息是否合法
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @return .
     */
    @Override
    public Map<String, Object> getAppInfo(String lesseeCode, String appCode) {
        if (StringUtils.isBlank(lesseeCode) || StringUtils.isBlank(appCode)) {
            return Collections.emptyMap();
        }
        String key = lesseeCode+"_"+appCode;
        Map<String, Object> appInfo = getAppInfoFromCache(key);
        if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){
            synchronized (getLock(key)){
                try {
                    appInfo = getAppInfoFromCache(key);
                    if (org.springframework.util.CollectionUtils.isEmpty(appInfo)){
                        log.debug("从数据库加载应用{},AppInvolved信息缓存",key);
                        String sql ="Select *  From " + TABLE_NAME + " Where lessee_code=? And app_code=? limit 1";
                        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, lesseeCode, appCode);
                        if (CollectionUtils.isEmpty(result)) {
                            String noInitAppSql = "SELECT COUNT(1) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?";
                            Integer i = jdbcTemplate.queryForObject(noInitAppSql, Integer.class, getDataBaseName(dataSourceProperties.getUrl()), lesseeCode + "_" + appCode + "_auth_config");
                            if (i > 0) {
                                Map<String, Object> map = new HashMap<>();
                                map.put("lessee_code", lesseeCode);
                                map.put("app_code", appCode);
                                map.put("status", 1);
                                result.add(map);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(result)){
                            appInfo = CollectionUtils.isNotEmpty(result)?result.get(0):Collections.emptyMap();
                            getRootCache().put(key,JSON.toJSONString(appInfo));
                        }
                    }
                }finally {
                    removeLock(key);
                }
            }
        }
        return appInfo;
    }

    public static String getDataBaseName(String url) {
        Matcher m = DATABASE_NAME_PATTERN.matcher(url);
        if(m.find()) {
            return m.group("dbName");
        }
        return "";
    }

    private Map<String,Object> getAppInfoFromCache(String key){
        String appInvolvedStr = getRootCache().get(key, String.class);
        if (StringUtils.isBlank(appInvolvedStr)){
            return null;
        }
        return JSON.parseObject(appInvolvedStr);
    }
    private Object getLock(String identify) {
        // 线程安全
        return parallelLockMap.computeIfAbsent(identify, t -> new Object());

    }

    private void removeLock(String identify) {
        // 线程安全
        parallelLockMap.remove(identify);
    }

    /**
     * 获取根部缓存信息
     *
     * @return 返回缓存对象
     */
    private Cache getRootCache() {
        String cacheName = ROOT_CACHE_NAME;
        log.debug("AppInvolvedTableServiceImpl cacheName: {}",cacheName);
       // Boolean autoAttachCachePrefix = CachePrefixUtil.isAutoAttachCcahePrefix();
        //try {
          //  CachePrefixUtil.setAutoAttachCachePrefix(false);
            return cacheManager.getCache(cacheName);
       // }finally {
            // 恢复原样
            //CachePrefixUtil.setAutoAttachCachePrefix(autoAttachCachePrefix);
        //}
    }
    /**
     * 修改应用信息的状态
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @return .
     */
    public int updateAppInfoStatus(String lesseeCode, String appCode,Integer status) {
        String sql ="update " + TABLE_NAME + " set status = ? Where lessee_code=? And app_code=? ;";
        int update = jdbcTemplate.update(sql, status, lesseeCode, appCode);
        // 情况缓存信息
        String key = lesseeCode+"_"+appCode;
        Cache rootCache = getRootCache();
        if (rootCache != null){
            //getRootCache().put(key,"");
            rootCache.evict(key);
        }
        AuthContext.getContext().setLesseeCode(lesseeCode);
        AuthContext.getContext().setAppCode(appCode);
        rootCache = getRootCache();
        if (rootCache != null){
            //getRootCache().put(key,"");
            rootCache.evict(key);
        }
        log.info("清除应用{},AppInvolved信息缓存",key);
        return update;
    }

    public void clearAppStatusCache(String lesseeCode, String appCode) {
        // 清空缓存信息
        String key = lesseeCode + "_" + appCode;
        Cache rootCache = getRootCache();
        if (rootCache != null) {
            //getRootCache().put(key,"");
            rootCache.evict(key);
        }
        AuthContext.getContext().setLesseeCode(lesseeCode);
        AuthContext.getContext().setAppCode(appCode);
        rootCache = getRootCache();
        if (rootCache != null) {
            //getRootCache().put(key,"");
            rootCache.evict(key);
        }
        log.info("通知清除{}应用AppInvolved信息缓存", key);
    }

}

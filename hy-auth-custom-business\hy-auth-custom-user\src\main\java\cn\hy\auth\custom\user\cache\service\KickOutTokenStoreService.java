package cn.hy.auth.custom.user.cache.service;

import cn.hy.auth.custom.user.account.service.KickOutEnum;

/**
 * KickOutTokenStoreService：挤出token缓存类
 *
 * <AUTHOR>
 * @version 2024/10/11 11:33
 **/
public interface KickOutTokenStoreService {
    /**
     *  存入挤出的token
     * @param token .
     * @param kickOutEnum .
     */
    void putKickOutToken(String token, KickOutEnum kickOutEnum);

    /**
     * 是否是已挤出的token
     * @param token .
     * @return .
     */
    boolean isKickOutToken(String token);

    /**
     * 是否是已挤出的token
     * @param token .
     * @return .
     */
    KickOutEnum getKickOutTokenMsg(String token);
}

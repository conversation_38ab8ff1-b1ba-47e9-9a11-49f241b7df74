package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.security.oauth2.userdetails.MyUserDetailsServiceImpl;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {AccountStatusProviderer.class, MyUserDetailsServiceImpl.class})
public class AccountStatusProviderTest {
    @Autowired
    AccountStatusProviderer accountStatusProvider;
    @MockBean
    private UserAccountService userAccountService;

    @MockBean
    private UserDetailsService myUserDetailsService;

    private Authentication authentication;
    private UserAccountDTO userAccountDTO;
    private HyUserDetails hyUserDetails;

    @Before
    public void setUp() throws Exception {
        authentication = mock(Authentication.class);
        doReturn("zhangsan").when(authentication).getPrincipal();

        userAccountDTO = new UserAccountDTO();
                //UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountStartEffectiveTime(System.currentTimeMillis() - 10000).passwordExpireTimestamp(System.currentTimeMillis()+10000).build();
        hyUserDetails = HyUserDetails.builderOf(userAccountDTO);
        doReturn(userAccountDTO).when(userAccountService).getUserByMultiField(any(), eq("zhangsan"));
        doReturn(hyUserDetails).when(myUserDetailsService).loadUserByUsername(any());
    }

    @Test
    public void testSupport() {
        AuthContext.getContext().loginState().setLoginType(LoginTypeEnum.USERNAME_PASSWORD);
        Assertions.assertThat(accountStatusProvider.isSupport(authentication)).isTrue();
    }

    @Test
    public void testUnSupport() {
        AuthContext.getContext().loginState().setLoginType(LoginTypeEnum.OAUTH2_CLIENT);
        Assertions.assertThat(accountStatusProvider.isSupport(authentication)).isFalse();
    }

    @Test(expected = BadCredentialsException.class)
    public void testAccountIsNull() {
        doReturn(null).when(userAccountService).getUserByMultiField(any(), eq("zhangsan"));
        doReturn(null).when(myUserDetailsService).loadUserByUsername(any());

        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test
    public void testStateIsAllNull() {
        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test
    public void testAccountNotLock() {
        userAccountDTO.setAccountNonLocked(true);
        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test(expected = LockedException.class)
    @Ignore //TODO 锁定功能还未实现
    public void testAccountLock() {
        userAccountDTO.setAccountNonLocked(false);
        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test
    public void testAccountEnable() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").enabled(true).build())).when(myUserDetailsService).loadUserByUsername(any());
        //accountStatusProvider.doAuthenticate(authentication);
    }

    @Test(expected = DisabledException.class)
    public void testAccountDisabled() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").enabled(false).build())).when(myUserDetailsService).loadUserByUsername(any());
        //accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 有效开始时间为空，结束时间有效
     */
    @Test
    public void testEffectiveStartTimeIsNull1() {
        userAccountDTO.setAccountStartEffectiveTime(null);
        userAccountDTO.setAccountEndEffectiveTime(System.currentTimeMillis() + 1000 * 60);
        accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 有效开始时间为空，结束时间过期
     */
    @Test(expected = AccountExpiredException.class)
    public void testEffectiveStartTimeIsNull2() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountEndEffectiveTime(System.currentTimeMillis() - 1000 * 60).build())).when(myUserDetailsService).loadUserByUsername(any());
        //accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 有效开始时间有效，结束时间为空
     */
    @Test
    public void testEffectiveEndTimeIsNull1() {
        userAccountDTO.setAccountStartEffectiveTime(System.currentTimeMillis() - 1000 * 60);
        userAccountDTO.setAccountEndEffectiveTime(null);
        accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 有效开始时间无效，结束时间为空
     */
    @Test(expected = AccountExpiredException.class)
    public void testEffectiveEndTimeIsNull2() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountStartEffectiveTime(System.currentTimeMillis() + 1000 * 60).build())).when(myUserDetailsService).loadUserByUsername(any());
       // accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 账号未生效
     */
    @Test(expected = AccountExpiredException.class)
    public void testEffectiveTime1() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountEndEffectiveTime(System.currentTimeMillis() + 1000 * 60).accountStartEffectiveTime(System.currentTimeMillis() + 1000 * 60).build())).when(myUserDetailsService).loadUserByUsername(any());
       // accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 账号已过期
     */
    @Test(expected = AccountExpiredException.class)
    public void testEffectiveTime2() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountEndEffectiveTime(System.currentTimeMillis() - 1000 * 60).accountStartEffectiveTime(System.currentTimeMillis() - 1000 * 60).build())).when(myUserDetailsService).loadUserByUsername(any());
        //accountStatusProvider.doAuthenticate(authentication);
    }

    /**
     * 账号正在生效
     */
    @Test
    public void testEffectiveTime3() {
        userAccountDTO.setAccountStartEffectiveTime(System.currentTimeMillis() - 1000 * 600);
        userAccountDTO.setAccountEndEffectiveTime(System.currentTimeMillis() + 1000 * 600);
        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test
    public void testCredentialsNotExpired() {
        userAccountDTO.setPasswordExpireTimestamp(System.currentTimeMillis() + 1000 * 600);
        accountStatusProvider.doAuthenticate(authentication);
    }

    @Test(expected = CredentialsExpiredException.class)
    public void testCredentialsExpired() {
        //doReturn(HyUserDetails.builderOf(UserAccountDTO.builder().id(1L).userAccountName("zhangsan").password("123").accountStartEffectiveTime(System.currentTimeMillis() - 10000).passwordExpireTimestamp(System.currentTimeMillis() - 1000 * 600).build())).when(myUserDetailsService).loadUserByUsername(any());
        //accountStatusProvider.doAuthenticate(authentication);
    }
}
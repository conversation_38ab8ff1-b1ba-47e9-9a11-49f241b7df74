package cn.hy.auth.boot.utils;

import cn.hy.auth.boot.AuthApplication;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * 集成测试
 *
 * <AUTHOR>
 * @date 2020/6/16
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = AuthApplication.class)
@Transactional // 数据库自动回滚
public abstract class BaseJunit4Test {

}

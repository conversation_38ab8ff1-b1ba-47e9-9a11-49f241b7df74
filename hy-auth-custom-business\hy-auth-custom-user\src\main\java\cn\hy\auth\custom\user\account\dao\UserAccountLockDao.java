package cn.hy.auth.custom.user.account.dao;

import cn.hy.auth.custom.user.account.domain.UserAccountLock;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户账号锁定表
 *
 * <AUTHOR>
 * @date 2022-04-14 11:07
 **/
public interface UserAccountLockDao {
    /**
     * 删除记录
     * @param uid .
     * @return .
     */
    int deleteByUid(@Param("uid") Object uid);

    /**
     *  插入记录
     * @param record .
     * @return .
     */
    int insert(UserAccountLock record);

    /**
     * 查询记录
     * @param id .
     * @return .
     */
    UserAccountLock selectByUid(Object id);
    /**
     * 查询所有记录
     * @return .
     */
    List<UserAccountLock> selectAll();

    /**
     * 更新记录
     * @param record .
     * @return .
     */
    int updateByPrimaryKey(UserAccountLock record);
}
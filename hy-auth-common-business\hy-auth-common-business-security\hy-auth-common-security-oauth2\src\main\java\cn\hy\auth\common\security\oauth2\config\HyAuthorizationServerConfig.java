package cn.hy.auth.common.security.oauth2.config;

import cn.hy.auth.common.security.oauth2.client.ClientDetailsServiceProvider;
import cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter;
import cn.hy.auth.common.security.oauth2.properties.ClientLoadProperties;
import cn.hy.auth.common.security.oauth2.properties.ClientProperties;
import cn.hy.auth.common.security.oauth2.properties.Oauth2AuthUrlMappingProperties;
import cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties;
import cn.hy.auth.common.security.oauth2.token.DefaultTokenServicesWrapper;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.config.annotation.builders.ClientDetailsServiceBuilder;
import org.springframework.security.oauth2.config.annotation.builders.InMemoryClientDetailsServiceBuilder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeTokenGranter;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.security.oauth2.provider.implicit.ImplicitTokenGranter;
import org.springframework.security.oauth2.provider.password.ResourceOwnerPasswordTokenGranter;
import org.springframework.security.oauth2.provider.refresh.RefreshTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;


/**
 * 类描述：认证服务器配置类
 * 可以参考OAuth2AuthorizationServerConfiguration 的实现
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/10 17:45
 **/
@Configuration
@EnableAuthorizationServer
public class HyAuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
    /**
     * 认证管理器，oauth2默认有实现了。一般不需要修改
     */
    @Autowired
    private AuthenticationManager authenticationManager;
    /**
     * token 生成增强类，
     * 可以为null
     */
    @Autowired(required = false)
    private List<TokenEnhancer> tokenEnhancers;
    /**
     * 用户信息接口，必须要业务方实现
     */
    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 加密算法，需要注入,start中可以考虑提供默认的实现
     * 为null时表示不加密
     */
    @Autowired
    private PasswordEncoder passwordEncoder;
    /**
     * 客户端信息加载服务类,由业务方实现
     * 为null时，表示使用内存模式
     */
    @Autowired
    private ClientDetailsService clientDetailsService;
    /**
     * token 存储方式，可以为null，
     * 为null时是用InMemoryTokenStore
     * 为null时，默认初始化为InMemoryTokenStore
     */
    @Autowired
    private TokenStore tokenStore;
    /**
     * 客户端client信息配置，内存模式时才生效
     */
    @Autowired
    private ClientLoadProperties clientLoadProperties;
    /**
     * tokenService 参数配置
     */
    @Autowired
    private TokenServicesProperties tokenServicesProperties;
    /**
     * oauth2 授权url地址的映射
     */
    @Autowired
    private Oauth2AuthUrlMappingProperties oauth2AuthUrlMapping;
    /**
     * 异常翻译处理器，可以为null。由调用方配置注入
     */
    @Autowired
    private WebResponseExceptionTranslator<OAuth2Exception> webResponseExceptionTranslator;
    @Autowired
    private ClientDetailsServiceProvider clientDetailsServiceProvider;

    @Autowired
    private ApplicationContext applicationContext;
    /**
     * 认证入口点,处理认证（登录）时的错误。用于token校验失败返回信息
     */
    @Autowired(required = false)
    private AuthenticationEntryPoint authenticationEntryPoint = new OAuth2AuthenticationEntryPoint();

    public HyAuthorizationServerConfig(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
    @PostConstruct
    public void afterCreate() {
        clientDetailsService = clientDetailsServiceProvider.getClientDetailsService();
    }

    /**
     * 令牌端点token endpoint 的安全约束
     * 声明安全约束，哪些允许访问，哪些不允许访问
     *
     * @param security .
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) {

        // 配置加密器，为null标识不加密
        security.passwordEncoder(passwordEncoder);
        // 对于CheckEndpoint控制器[框架自带的校验]的/oauth/check端点允许所有客户端发送器请求而不会被Spring-security拦截
        security.tokenKeyAccess("permitAll()");
        security.checkTokenAccess("permitAll()");
        // 此处可添加自定义过滤器，对oauth相关的请求做进一步处理,只对token类的请求有效!!!
        //security.addTokenEndpointAuthenticationFilter();
        // 注册clientCredentialsTokenEndpointFilter
        // security.allowFormAuthenticationForClients();
        // ### 可在此处添加租户和应用的入参判断
        // 注册自定义的clientCredentialsTokenEndpointFilter
        CustomClientCredentialsTokenEndpointFilter endpointFilter = new CustomClientCredentialsTokenEndpointFilter(security);
        endpointFilter.afterPropertiesSet();
        endpointFilter.setAuthenticationEntryPoint(authenticationEntryPoint);
        endpointFilter.setApplicationContext(applicationContext);
        security.addTokenEndpointAuthenticationFilter(endpointFilter);

    }

    /**
     * 配置客户端详情(客户端clientId，secret，授权类型，scope等)
     *
     * @param clients .
     * @throws Exception .
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        // 2种模式，通过参数配置进行选择.或者考虑2者兼容同时存在
        if (clientLoadProperties.isClientInMemory()) {
            InMemoryClientDetailsServiceBuilder builder = clients.inMemory();
            if (ArrayUtils.isNotEmpty(clientLoadProperties.getClients())) {
                for (ClientProperties config : clientLoadProperties.getClients()) {
                    ClientDetailsServiceBuilder<InMemoryClientDetailsServiceBuilder>.ClientBuilder clientBuilder = builder
                            // 设置客户端和密码
                            .withClient(config.getClientId()).secret(config.getClientSecret())
                            // 是否自动授权
                            .autoApprove(config.isAutoApprove())
                            // 授权域
                            .scopes(config.getScope().toArray(new String[0]))
                            // 支持的认证方式
                            .authorizedGrantTypes(config.getAuthorizedGrantTypes().toArray(new String[0]));
                    if (config.getRefreshTokenValiditySeconds() > 0) {
                        // 设置refreshToken有效期,优先级高于tokenServices,如果不设置则使用tokenSevice里的配置值
                        clientBuilder.refreshTokenValiditySeconds(config.getRefreshTokenValiditySeconds());
                    }
                    if (config.getAccessTokenValiditySeconds() > 0) {
                        // 设置token有效期,优先级高于tokenServices,如果不设置则使用tokenSevice里的配置值
                        clientBuilder.accessTokenValiditySeconds(config.getAccessTokenValiditySeconds());
                    }
                }
            }
        } else {
            clients.withClientDetails(clientDetailsService);
        }
    }

    /**
     * 配置授权（authorization）以及令牌（token）的访问端点和
     * 令牌服务（token services），还有token的存储方式（tokenStore）
     *
     * @param endpoints .
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
        endpoints.authenticationManager(authenticationManager)
                .userDetailsService(userDetailsService);
        endpoints.tokenStore(tokenStore);
        endpoints.allowedTokenEndpointRequestMethods(HttpMethod.GET, HttpMethod.POST);
        if (!CollectionUtils.isEmpty(tokenEnhancers)) {
            TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
            tokenEnhancerChain.setTokenEnhancers(new ArrayList<>(tokenEnhancers));
            endpoints.tokenEnhancer(tokenEnhancerChain);
        }
        // 配置自定义的带事件扩展点的TokenServices
        DefaultTokenServicesWrapper tokenServices = new DefaultTokenServicesWrapper(applicationContext, tokenServicesProperties.isReuseRefreshToken());
        tokenServices.setTokenStore(endpoints.getTokenStore());
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setClientDetailsService(endpoints.getClientDetailsService());
        tokenServices.setTokenEnhancer(endpoints.getTokenEnhancer());
        // access_token默认有效时长为12个小时,优先使用client设置的有效期
        tokenServices.setAccessTokenValiditySeconds(tokenServicesProperties.getAccessTokenValiditySeconds());
        // refresh_token默认时长为30天,优先使用client设置的有效期
        tokenServices.setRefreshTokenValiditySeconds(tokenServicesProperties.getRefreshTokenValiditySeconds());
        endpoints.tokenServices(tokenServices);
        // 自定义确认授权页面
        //endpoints.pathMapping("/oauth/confirm_access", oauth2AuthUrlMapping.getConfrimAccess());
        // 自定义错误页
        // endpoints.pathMapping("/oauth/error", oauth2AuthUrlMapping.getConfrimAccess());
        // 自定义异常转换类
        endpoints.exceptionTranslator(webResponseExceptionTranslator);
        OAuth2RequestFactory oAuth2RequestFactory = endpoints.getOAuth2RequestFactory();
        AuthorizationCodeServices authorizationCodeServices = endpoints.getAuthorizationCodeServices();
        endpoints.tokenGranter(new TokenGranter() {
         		private CompositeTokenGranter delegate;

				@Override
				public OAuth2AccessToken grant(String grantType, TokenRequest tokenRequest) {
					if (delegate == null) {
						delegate = new CompositeTokenGranter(getDefaultTokenGranters(clientDetailsService, tokenServices, oAuth2RequestFactory, authorizationCodeServices));
					}
					return delegate.grant(grantType, tokenRequest);
				}
			});

    }

	private List<TokenGranter> getDefaultTokenGranters(ClientDetailsService clientDetails,AuthorizationServerTokenServices tokenServices,OAuth2RequestFactory requestFactory,AuthorizationCodeServices authorizationCodeServices) {
		List<TokenGranter> tokenGranters = new ArrayList<>();
		tokenGranters.add(new AuthorizationCodeTokenGranter(tokenServices, authorizationCodeServices, clientDetails,
				requestFactory));
		tokenGranters.add(new RefreshTokenGranter(tokenServices, clientDetails, requestFactory));
		ImplicitTokenGranter implicit = new ImplicitTokenGranter(tokenServices, clientDetails, requestFactory);
		tokenGranters.add(implicit);
        ClientCredentialsTokenGranter clientCredentialsTokenGranter = new ClientCredentialsTokenGranter(tokenServices, clientDetails, requestFactory);
        clientCredentialsTokenGranter.setAllowRefresh(tokenServicesProperties.isAllowRefresh());
        tokenGranters.add(clientCredentialsTokenGranter);
		if (authenticationManager != null) {
			tokenGranters.add(new ResourceOwnerPasswordTokenGranter(authenticationManager, tokenServices,
					clientDetails, requestFactory));
		}
		return tokenGranters;
	}
}

package cn.hy.auth.custom.common.domain;

import java.io.Serializable;
import java.util.Date;
/**
 * 类描述: 登录信息
 *
 * <AUTHOR>
 * @date :创建于 2020/11/16
 */
public class UserLoginInfoDTOBuilder implements Serializable {
        private Long id;
        private String clientId;
        private Integer clientType;
        private String loginType;
        private Long userId;
        private String userAccountName;
        private String ip;
        private String mac;
        private Integer type;
        private Integer loginCount;
        private Integer errorCode;
        private String errorMessage;
        private String dataVersion;
        private Long createUserId;
        private Date createTime;
        private String createUserName;
        private Long lastUpdateUserId;
        private Date lastUpdateTime;
        private String lastUpdateUserName;
        private Long sequence;
        private Date lastLoginTime;
        private Date currentLoginTime;
        private Date lastRealLoginTime;
        private String lastLoginIp;
        private String currentIp;

        UserLoginInfoDTOBuilder() {
        }

        public UserLoginInfoDTOBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public UserLoginInfoDTOBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public UserLoginInfoDTOBuilder clientType(Integer clientType) {
            this.clientType = clientType;
            return this;
        }

        public UserLoginInfoDTOBuilder loginType(String loginType) {
            this.loginType = loginType;
            return this;
        }

        public UserLoginInfoDTOBuilder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public UserLoginInfoDTOBuilder userAccountName(String userAccountName) {
            this.userAccountName = userAccountName;
            return this;
        }

        public UserLoginInfoDTOBuilder ip(String ip) {
            this.ip = ip;
            return this;
        }

        public UserLoginInfoDTOBuilder mac(String mac) {
            this.mac = mac;
            return this;
        }

        public UserLoginInfoDTOBuilder type(Integer type) {
            this.type = type;
            return this;
        }

        public UserLoginInfoDTOBuilder loginCount(Integer loginCount) {
            this.loginCount = loginCount;
            return this;
        }

        public UserLoginInfoDTOBuilder errorCode(Integer errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public UserLoginInfoDTOBuilder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public UserLoginInfoDTOBuilder dataVersion(String dataVersion) {
            this.dataVersion = dataVersion;
            return this;
        }

        public UserLoginInfoDTOBuilder createUserId(Long createUserId) {
            this.createUserId = createUserId;
            return this;
        }

        public UserLoginInfoDTOBuilder createTime(Date createTime) {
            this.createTime = createTime;
            return this;
        }

        public UserLoginInfoDTOBuilder createUserName(String createUserName) {
            this.createUserName = createUserName;
            return this;
        }

        public UserLoginInfoDTOBuilder lastUpdateUserId(Long lastUpdateUserId) {
            this.lastUpdateUserId = lastUpdateUserId;
            return this;
        }

        public UserLoginInfoDTOBuilder lastUpdateTime(Date lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
            return this;
        }

        public UserLoginInfoDTOBuilder lastUpdateUserName(String lastUpdateUserName) {
            this.lastUpdateUserName = lastUpdateUserName;
            return this;
        }

        public UserLoginInfoDTOBuilder sequence(Long sequence) {
            this.sequence = sequence;
            return this;
        }

        public UserLoginInfoDTOBuilder lastLoginTime(Date lastLoginTime) {
            this.lastLoginTime = lastLoginTime;
            return this;
        }

        public UserLoginInfoDTOBuilder currentLoginTime(Date currentLoginTime) {
            this.currentLoginTime = currentLoginTime;
            return this;
        }

        public UserLoginInfoDTOBuilder lastRealLoginTime(Date lastRealLoginTime) {
            this.lastRealLoginTime = lastRealLoginTime;
            return this;
        }

        public UserLoginInfoDTOBuilder lastLoginIp(String lastLoginIp) {
            this.lastLoginIp = lastLoginIp;
            return this;
        }

        public UserLoginInfoDTOBuilder currentIp(String currentIp) {
            this.currentIp = currentIp;
            return this;
        }

        public UserLoginInfoDTO build() {
            return new UserLoginInfoDTO(this.id, this.clientId, this.clientType, this.loginType, this.userId, this.userAccountName, this.ip, this.mac, this.type, this.loginCount, this.errorCode, this.errorMessage, this.dataVersion, this.createUserId, this.createTime, this.createUserName, this.lastUpdateUserId, this.lastUpdateTime, this.lastUpdateUserName, this.sequence, this.lastLoginTime, this.currentLoginTime, this.lastRealLoginTime, this.lastLoginIp, this.currentIp);
        }

        @Override
        public String toString() {
            return "UserLoginInfoDTOBuilder(id=" + this.id + ", clientId=" + this.clientId + ", clientType=" + this.clientType + ", loginType=" + this.loginType + ", userId=" + this.userId + ", userAccountName=" + this.userAccountName + ", ip=" + this.ip + ", mac=" + this.mac + ", type=" + this.type + ", loginCount=" + this.loginCount + ", errorCode=" + this.errorCode + ", errorMessage=" + this.errorMessage + ", dataVersion=" + this.dataVersion + ", createUserId=" + this.createUserId + ", createTime=" + this.createTime + ", createUserName=" + this.createUserName + ", lastUpdateUserId=" + this.lastUpdateUserId + ", lastUpdateTime=" + this.lastUpdateTime + ", lastUpdateUserName=" + this.lastUpdateUserName + ", sequence=" + this.sequence + ", lastLoginTime=" + this.lastLoginTime + ", currentLoginTime=" + this.currentLoginTime + ", lastRealLoginTime=" + this.lastRealLoginTime + ", lastLoginIp=" + this.lastLoginIp + ", currentIp=" + this.currentIp + ")";
        }
    }
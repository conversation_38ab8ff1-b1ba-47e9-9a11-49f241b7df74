package cn.hy.auth.custom.user.cache.service.impl;

import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import cn.hy.auth.custom.user.config.context.NativeDiffCacheContext;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import com.alibaba.fastjson.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 类描述: 用户缓存测试类
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/2
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UserCacheServiceImpl.class})
public class UserCacheServiceImplTest {

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private UserCacheTokenService userCacheTokenService;

    @MockBean
    private UserAccountService userAccountService;

    @MockBean
    private NativeDiffCacheContext nativeDiffCacheContext;

    @MockBean
    private NativeDiffCacheService nativeDiffCacheService;

    @MockBean
    private CacheManager cacheManager;

    @MockBean
    private CaffeineCache cache;

    @MockBean
    private AppInfoParseProxy appInfoParseProxy;

    @MockBean
    private AuthLoginInfoService authLoginInfoService;

    private MockHttpServletRequest request = new MockHttpServletRequest();

    private MockHttpServletResponse response = new MockHttpServletResponse();

    private static final String TOKEN_ID = "hy.erp.app.1b7cf41f-da6c-4929-a165-9788af379eed";

    private static final String USER_NAME = "admin";

    private static final Long USER_ID = 1L;

    private static final String CACHE_NAME = "hy_auth";

    @Before
    public void init() {
        request.addHeader("Authorization", "Bearer " + TOKEN_ID);
        RequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @After
    public void destroy() {
        RequestContextHolder.resetRequestAttributes();
    }

    /**
     * 公共缓存获取配置
     */
    private void commonSet() {
        when(cacheManager.getCache(any())).thenReturn(cache);
        //按登录账号获取用户主键
        when(cache.get(UserCacheKeyConst.LOGIN_NAME_PREFIX + USER_NAME, Long.class)).thenReturn(USER_ID);
        //按用户主键查询用户信息
        when(cache.get(UserCacheKeyConst.COMPLETE_USER_PREFIX + USER_ID, String.class)).thenReturn(JSON.toJSONString(createUserAccount()));
        //按用户主键获取tokenId
        when(cache.get(UserCacheKeyConst.USER_TOKEN_PREFIX + USER_ID, String.class)).thenReturn(TOKEN_ID);
        //按tokenId获取用户主键
        when(cache.get(UserCacheKeyConst.TOKEN_USER_PREFIX + TOKEN_ID, Long.class)).thenReturn(USER_ID);
        //按用户主键获取用户信息
        when(cache.get(UserCacheKeyConst.COMPLETE_USER_PREFIX + USER_ID, String.class)).thenReturn(JSON.toJSONString(createUserAccount()));
    }

    @Test
    public void cacheUser() {

        when(userAccountService.getUserAllInfoByUserId(USER_ID)).thenReturn(createUserAccount());
        commonSet();
        //userCacheService.cacheUser(USER_ID);
    }

    @Test
    public void getCacheByUserId() {
        commonSet();
        //userCacheService.getCacheByUserId(USER_ID);
    }

    @Test
    public void getCacheByLoginName() {
        commonSet();
        userCacheService.getUserAccountByLoginName(USER_NAME);
    }

    @Test
    public void getUserIdByLoginName() {
        commonSet();
        //userCacheService.getUserIdByLoginName(USER_NAME);
    }

    @Test
    public void removeCacheByUserId() {
        commonSet();
        userCacheService.removeCacheByUserId(USER_ID);
    }

    @Test
    public void removeCacheByLoginName() {
        commonSet();
        userCacheService.removeCacheByLoginName(USER_NAME);
    }

    @Test
    public void getOnlineUserId() {
        commonSet();
        when(nativeDiffCacheContext.getNativeCacheHandleMap(anyString())).thenReturn(nativeDiffCacheService);
        userCacheService.getOnlineUserId();
    }

    @Test
    public void getUserCacheByTokenId() {
        commonSet();
        userCacheTokenService.getUserCacheByTokenId(TOKEN_ID);
    }

    @Test
    public void getUserIdByTokenId() {
        commonSet();
        //userCacheTokenService.getUserIdByTokenId(TOKEN_ID);
    }

    @Test
    public void deleteUserCacheByTokenId() {
        commonSet();
        userCacheTokenService.deleteUserCacheByTokenId(TOKEN_ID);
    }

    /**
     * 创建用户对象
     *
     * @return 返回用户对象
     */
    private UserAccountDTO createUserAccount() {
        UserAccountDTO userAccountDTO = new UserAccountDTO();

        Map<String, Object> account = new HashMap<>();
        account.put("user_account_name", USER_NAME);
        account.put("id", USER_ID);
        account.put("password", "123456");
        userAccountDTO.setAccount(account);
        userAccountDTO.setUser(account);

        userAccountDTO.setId(USER_ID);
        userAccountDTO.setUserAccountName(USER_NAME);
        userAccountDTO.setPassword("123456");

        return userAccountDTO;
    }

}
package cn.hy.auth.custom.user.config.cache;

import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import cn.hy.auth.custom.user.cache.service.impl.ForgetPasswordMapCacheManage;
import cn.hy.auth.custom.user.cache.service.impl.ForgetPasswordRedisManage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;

@Configuration
public class ForgetPasswordCacheConfig {


    @Bean
    @ConditionalOnMissingBean(ForgetPasswordCacheManage.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "redis"
    )
    public ForgetPasswordCacheManage getForgetPasswordRedisManage(RedisConnectionFactory factory) {
        return new ForgetPasswordRedisManage(factory);
    }

    @Bean
    @ConditionalOnMissingBean(ForgetPasswordCacheManage.class)
    @ConditionalOnProperty(
            prefix = "auth.token.store",
            name = "type",
            havingValue = "redisLocalCache"
    )
    public ForgetPasswordCacheManage getForgetPasswordRedisLocalCacheManage(RedisConnectionFactory factory) {
        // 先默认使用redis
        return new ForgetPasswordRedisManage(factory);
    }

    @Bean
    @ConditionalOnMissingBean(ForgetPasswordCacheManage.class)
    public ForgetPasswordCacheManage getForgetPasswordMapCacheManage() {
        return new ForgetPasswordMapCacheManage();
    }


}

package cn.hy.auth.custom.route.service.impl;


import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.exceptions.AuthRouteException;
import cn.hy.auth.custom.route.domain.OauthRequestRouting;
import cn.hy.auth.custom.route.service.OauthRequestRoutingService;
import cn.hy.auth.custom.route.service.RequestRoutingService;
import cn.hy.auth.custom.route.service.route.RouteExecuteInterceptor;
import cn.hy.auth.custom.route.service.route.SendResponseService;
import cn.hy.auth.custom.route.service.route.ServerHostPicker;
import cn.hy.auth.custom.route.service.route.SimpleHostRoutingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * 类描述：转发到外部第三方系统
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/24 10:20
 **/
@Slf4j
@Component
public class ThirdPartRequestRoutingServiceImpl implements RequestRoutingService {
    private OauthRequestRoutingService oauthRequestRoutingService;
    private ServerHostPicker serverHostPicker;
    private SimpleHostRoutingService simpleHostRoutingService;
    private SendResponseService sendResponseService;
    private List<RouteExecuteInterceptor> routeExecuteInterceptors;

    public ThirdPartRequestRoutingServiceImpl(OauthRequestRoutingService oauthRequestRoutingService,
                                              ServerHostPicker serverHostPicker,
                                              SimpleHostRoutingService simpleHostRoutingService,
                                              SendResponseService sendResponseService, List<RouteExecuteInterceptor> routeExecuteInterceptors) {
        this.oauthRequestRoutingService = oauthRequestRoutingService;
        this.serverHostPicker = serverHostPicker;
        this.simpleHostRoutingService = simpleHostRoutingService;
        this.sendResponseService = sendResponseService;
        this.routeExecuteInterceptors = routeExecuteInterceptors;
    }

    @Override
    public void run(Object systemAreaIdentify, HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 0 从数据库里取出对应url的转发地址和数据结构转换脚本
        String sourceUrl = request.getRequestURI();
        log.info("转发地址："+sourceUrl);
        OauthRequestRouting oauthRequestRoutingDO = oauthRequestRoutingService.selectBySystemAreaIdentify(systemAreaIdentify.toString(),sourceUrl);
        if (oauthRequestRoutingDO == null) {
            log.warn("{} 没有查找到对应的外部第三方系统的数据库信息配置，继续由本系统执行", systemAreaIdentify);
            filterChain.doFilter(request, response);
            return ;
        }
        // 2 获得外部系统ip地址 http://127.0.0.1:6060
        String serverHost = null;
        if (!StringUtils.isEmpty(oauthRequestRoutingDO.getTargetAddress())){
            String targetAddress = oauthRequestRoutingDO.getTargetAddress();
            if (targetAddress != null){
                // 从 "https://"或"http://" 后的位置开始查找
                int index = targetAddress.indexOf('/', targetAddress.indexOf("//") + 2);
                if (index != -1) {
                    // 获取第一个部分（协议 + 域名）
                    serverHost = targetAddress.substring(0, index);
                }else {
                    serverHost = targetAddress;
                }
            }
        }else {
            serverHost = serverHostPicker.getServerHost(systemAreaIdentify.toString());
        }
        if (StringUtils.isEmpty(serverHost)){
            log.warn("{} 没有查找到对应的外部第三方系统server host配置，继续由本系统执行", systemAreaIdentify);
            filterChain.doFilter(request, response);
            return ;
        }
        ParameterRequestWrapper requestWrapper = new ParameterRequestWrapper(request);
        pre(oauthRequestRoutingDO,requestWrapper,response);
        String targetUrl;
        if (!StringUtils.isEmpty(oauthRequestRoutingDO.getTargetAddress())){
            targetUrl = oauthRequestRoutingDO.getTargetAddress()+oauthRequestRoutingDO.getTargetUrl();
        }else {
            targetUrl = serverHost+oauthRequestRoutingDO.getTargetUrl();
        }
        // 1 构建转发请求request
        // 3 转发请求
        try {
            HttpResponse httpResponse = execute(serverHost,targetUrl,requestWrapper);
            if (httpResponse == null){
                log.error("{} 调用后，获得的响应结果是null", targetUrl);
                throw new AuthRouteException(AuthErrorCodeEnum.C0319.code(),AuthErrorCodeEnum.C0319.msg());
            }
            log.info(targetUrl+"回调结果："+httpResponse);
            log.info(targetUrl+"执行完毕，开始调用grovvy处理数据：");
            post(oauthRequestRoutingDO,requestWrapper,response,httpResponse);
            // 5 返回响应
            log.info(targetUrl+"响应结果。");
            sendResponseService.sendResponse(response,httpResponse);
            //}
        } catch (Exception e) {
            log.error("调用第三方系统进行认证失败。{} ", targetUrl,e);
            throw new AuthRouteException(AuthErrorCodeEnum.C0001.code(),AuthErrorCodeEnum.C0001.msg()+":"+e.getMessage());
        }
    }

    /**
     * 执行前预处理,主要是参数之间的转发
     * @param oauthRequestRoutingDO .
     * @param request .
     * @param response .
     */
    private void pre(OauthRequestRouting oauthRequestRoutingDO, HttpServletRequest request, HttpServletResponse response) {
        for (RouteExecuteInterceptor routeExecuteInterceptor : routeExecuteInterceptors) {
            routeExecuteInterceptor.preExe(oauthRequestRoutingDO, request, response);
        }
    }

    private HttpResponse execute(String hostUrl,String targetUrl, HttpServletRequest request) throws Exception {
        return simpleHostRoutingService.run(request,hostUrl,targetUrl);
    }

    /**
     *   4 获得结果并进行转换
     * @param oauthRequestRoutingDO .
     * @param request .
     * @param response .
     * @param httpResponse .
     */
    private void post(OauthRequestRouting oauthRequestRoutingDO, HttpServletRequest request,
                      HttpServletResponse response, HttpResponse httpResponse) {
        for (RouteExecuteInterceptor routeExecuteInterceptor : routeExecuteInterceptors) {
            routeExecuteInterceptor.postExe(oauthRequestRoutingDO, request, response,httpResponse);
        }
    }


}

package cn.hy.auth.custom.multi.filter;

import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.multi.authinfo.service.AppAuthStrategyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 认证规则初始化拦截器
 * 职责：根据租户编码、应用编码加载应用的认证规则；
 *
 * <AUTHOR>
 * @date 2020-11-30 14:09
 **/
@Order(-990)
@Component
@Slf4j
public class AppAuthInfoEixtFilter extends AbstractAuthFilter {

	@Autowired
	@Lazy
	private AppInfoParseProxy      appInfoParseProxy;
	@Autowired
	@Lazy
	private AppAuthStrategyManager authInfoManager;


	@Override
	protected void doMyFilter(HttpServletRequest request, HttpServletResponse response,
	                          FilterChain filterChain) throws ServletException, IOException {
		if (!RequestTypeEnum.NON_BUSINESS.equals(appInfoParseProxy.parseType(request))) {
			log.debug("非普通业务请求：【{}】，准备初始化认证策略数据。", request.getRequestURL());
			//初始化应用的认证规则
			AppAuthStrategyDTO authInfoDTO = new AppAuthStrategyDTO();
			if (authInfoManager != null) {
				authInfoDTO = authInfoManager.get();
			}

			//log.debug("初始化认证策略数据：{}", authInfoDTO);
			AuthContext.getContext().setAppAuthStrategy(authInfoDTO);
		}

		filterChain.doFilter(request, response);
	}
}

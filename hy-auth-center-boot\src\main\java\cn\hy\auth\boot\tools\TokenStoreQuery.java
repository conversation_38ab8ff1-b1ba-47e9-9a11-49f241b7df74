package cn.hy.auth.boot.tools;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询TokenStore中所有的clientId
 */
@Component
public class TokenStoreQuery implements CommandLineRunner {

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void run(String... args) {
        if (args.length > 0 && "query-clientids".equals(args[0])) {
            Set<String> clientIds = getTokenStoreClientIds();
            System.out.println("============ TokenStore中存在的所有clientId ============");
            for (String clientId : clientIds) {
                System.out.println(clientId);
            }
            System.out.println("=====================================================");
            
            // 查询完成后关闭应用
            ((ConfigurableApplicationContext) applicationContext).close();
        }
    }

    /**
     * 获取TokenStore中所有的clientId
     * @return clientId集合
     */
    private Set<String> getTokenStoreClientIds() {
        // 使用Redis连接工厂
        RedisConnectionFactory connectionFactory = stringRedisTemplate.getConnectionFactory();
        if (connectionFactory == null) {
            return Collections.emptySet();
        }

        RedisConnection connection = connectionFactory.getConnection();
        Set<String> clientIds = new HashSet<>();
        
        try {
            // 获取所有client_id_to_access:开头的key
            Set<byte[]> keys = connection.keys("client_id_to_access:*".getBytes(StandardCharsets.UTF_8));
            if (keys == null || keys.isEmpty()) {
                return Collections.emptySet();
            }
            
            // 从key中提取clientId
            for (byte[] key : keys) {
                String keyStr = new String(key, StandardCharsets.UTF_8);
                if (keyStr.startsWith("client_id_to_access:")) {
                    clientIds.add(keyStr.substring("client_id_to_access:".length()));
                }
            }
        } finally {
            connection.close();
        }
        
        return clientIds;
    }

    public static void main(String[] args) {
        String[] queryArgs = {"query-clientids"};
        SpringApplication.run(TokenStoreQuery.class, queryArgs);
    }
} 
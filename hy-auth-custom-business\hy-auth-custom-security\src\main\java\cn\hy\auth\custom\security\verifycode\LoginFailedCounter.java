package cn.hy.auth.custom.security.verifycode;

/**
 * 验证码-登陆失败统计
 * hy-authentication-center-cn.hy.auth.custom.security.verifycode
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/4/15 11:01
 */
public interface LoginFailedCounter {
    /**
     * 添加失败次数
     * @param login 登陆名
     * @return 最新的失败次数
     */
    Long addFailed(String login);

    /**
     * 获取失败次数
     * @param login 登陆名
     * @return 失败次数
     */
    Long getFailedCount(String login);

    /**
     * 如果超时则清除失败记录
     * @param login 登陆账号
     */
    void cleanIfNeed(String login);
}

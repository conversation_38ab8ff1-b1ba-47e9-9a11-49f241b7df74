package cn.hy.auth.common.security.core.authentication.mobile.service.impl;

import cn.hy.auth.common.security.core.authentication.mobile.ValidateCode;
import cn.hy.auth.common.security.core.authentication.mobile.service.ValidateCodeGenerator;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import org.apache.commons.lang3.RandomStringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 短信验证码创建器
 *
 * <AUTHOR>
 * @date 2020-11-24 10:57
 */
public class SmsCodeGenerator implements ValidateCodeGenerator {

    private SecurityProperties securityProperties;

    public SmsCodeGenerator(SecurityProperties securityProperties) {
        this.securityProperties = securityProperties;
    }

    @Override
    public ValidateCode createCode(HttpServletRequest request, HttpServletResponse response) {
        String code = RandomStringUtils.randomNumeric(securityProperties.getSmsAuth().getLength());
        return new ValidateCode(code, 59);
    }
}

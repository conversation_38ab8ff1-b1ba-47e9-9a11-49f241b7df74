package cn.hy.auth.custom.common.appinfo.service;

import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 解析租户、应用信息
 *
 * <AUTHOR>
 * @date 2020-12-13 13:35
 **/
@Component
public class AppInfoParseProxy {
    @Autowired
    private List<BaseAppInfoParser> requestInfoParsers;

    /**
     * 从请求信息中解析出租户、应用编码信息
     *
     * @param httpRequest 请求信息
     * @return 租户、应用编码信息
     */
    public AppInfoDTO parse(HttpServletRequest httpRequest) {
        return Optional.ofNullable(parseWithRequestType(httpRequest)).map(ImmutablePair::getLeft).orElse(null);
    }

    /**
     * 解析请求类型
     * @param httpRequest 请求信息
     * @return 请求类型
     */
    public RequestTypeEnum parseType(HttpServletRequest httpRequest) {
        return requestInfoParsers.stream()
                .sorted()
                .map(parser -> parser.parseType(httpRequest))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    /**
     * 从请求信息中解析出租户、应用编码信息以及请求类型
     *
     * @param httpRequest 请求信息
     * @return .
     */
    public ImmutablePair<AppInfoDTO, RequestTypeEnum> parseWithRequestType(HttpServletRequest httpRequest) {
        return requestInfoParsers.stream()
                .sorted()
                .map(parser -> parser.parse(httpRequest))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }
}

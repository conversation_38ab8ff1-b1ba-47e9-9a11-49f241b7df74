package cn.hy.auth.custom.security.common;


import java.util.Map;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxin<PERSON>
 * @date 2021/8/27 17:51
 **/
public class SqlCreateUtil {
    private static final String INSERT_SQL_TEMPLATE = "INSERT INTO  `{tableName}` (%s) values (%s)";
    private static final String IGNORE_SQL_TEMPLATE = "INSERT IGNORE INTO `{tableName}` (%s) values (%s)";
    private static final String TABLE_NAME_TEMPLATE = "{tableName}";

    public static String createInsertSql(String tableName, Map<String, Object> record) {
        StringBuilder columns = new StringBuilder();
        StringBuilder valueTemplate = new StringBuilder();
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            appendColumns(columns, entry.getKey());
            appendValueTemplate(valueTemplate, entry.getValue());
        }
        String result = INSERT_SQL_TEMPLATE.replace(TABLE_NAME_TEMPLATE, tableName);
        return String.format(result, columns.substring(0, columns.length() - 1), valueTemplate.substring(0, valueTemplate.length() - 1));
    }

    public static String createInsertSql(String tableName, Map<String, Object> record, Boolean ignore) {
        StringBuilder columns = new StringBuilder();
        StringBuilder valueTemplate = new StringBuilder();
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            appendColumns(columns, entry.getKey());
            appendValueTemplate(valueTemplate, entry.getValue());
        }
        String result = INSERT_SQL_TEMPLATE.replace(TABLE_NAME_TEMPLATE, tableName);
        if (ignore) {
            result = IGNORE_SQL_TEMPLATE.replace(TABLE_NAME_TEMPLATE, tableName);
        }
        return String.format(result, columns.substring(0, columns.length() - 1), valueTemplate.substring(0, valueTemplate.length() - 1));
    }

    /**
     * append columns part of replace into sql template
     *
     * @param columns .
     * @param key     .
     */
    private static void appendColumns(StringBuilder columns, String key) {
        columns.append("`").append(key).append("`").append(",");
    }

    private static void appendValueTemplate(StringBuilder valueTemplate, Object value) {
        valueTemplate.append(value == null ? null : "'" + value.toString() + "'").append(",");
    }

    private SqlCreateUtil() {
    }

}

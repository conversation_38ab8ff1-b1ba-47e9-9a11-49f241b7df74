package cn.hy.auth.custom.user.config.context;

import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述: 不同缓存处理类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@Component
public class NativeDiffCacheContext {

    private final Map<String, NativeDiffCacheService> nativeCacheHandleMap = new HashMap<>();

    /**
     * 获取获取类型实现类
     *
     * @param cacheType 缓存类型
     * @return 返回对应缓存类型的实现类
     */
    public NativeDiffCacheService getNativeCacheHandleMap(String cacheType) {
        return nativeCacheHandleMap.get(cacheType);
    }

    /**
     * 设置缓存类型
     *
     * @param cacheType              缓存类型
     * @param nativeDiffCacheService 对应的缓存类型实现类
     */
    public void putNativeCacheHandleMap(String cacheType, NativeDiffCacheService nativeDiffCacheService) {
        nativeCacheHandleMap.put(cacheType, nativeDiffCacheService);
    }

}

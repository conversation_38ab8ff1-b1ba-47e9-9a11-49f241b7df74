package cn.hy.auth.boot.listener;

/**
 * 应用安装的安装类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/9/20
 */
public enum AppInstallTypeEnums {

    /**
     * 初始安装
     */
    FIRST_INSTALL(1, "初始安装"),
    /**
     * 升级安装
     */
    UPGRADE_INSTALL(2, "升级安装"),
    /**
     * 回滚安装
     */
    ROLLBACK_INSTALL(3, "回滚安装"),
    /**
     * 预览安装
     */
    PREVIEW_INSTALL(4, "预览安装"),
    /**
     * 空类型
     */
    NULL(-1,"空类型");



    private final Integer value;
    private final String name;

    AppInstallTypeEnums(Integer value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static AppInstallTypeEnums getByValue(Integer value){
        for (AppInstallTypeEnums statusEnums : values()) {
            if (statusEnums.getValue().equals(value)){
                return statusEnums;
            }
        }
        return AppInstallTypeEnums.NULL;
    }

}

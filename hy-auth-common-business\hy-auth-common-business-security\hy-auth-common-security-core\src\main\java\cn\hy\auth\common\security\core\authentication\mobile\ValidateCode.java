package cn.hy.auth.common.security.core.authentication.mobile;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2018/12/3 0003 19:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidateCode implements Serializable {

    private String code;

    private boolean hasNoExpired;

    private boolean codeError;

    private LocalDateTime expireTime;

    public ValidateCode(String code, int seconds) {
        this.code = code;
        this.expireTime = LocalDateTime.now().plusSeconds(seconds);
    }

    public ValidateCode(String code, int seconds, boolean hasNoExpired) {
        this.code = code;
        this.expireTime = LocalDateTime.now().plusSeconds(seconds);
        this.hasNoExpired = hasNoExpired;
    }

    public boolean isExpried() {
        return LocalDateTime.now().isAfter(expireTime);
    }

    @Override
    public String toString() {
        return "ValidateCode{" +
                "code='" + code + '\'' +
                ", expireTime=" + expireTime +
                '}';
    }
}

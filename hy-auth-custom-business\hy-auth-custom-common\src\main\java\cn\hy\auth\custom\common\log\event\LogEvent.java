package cn.hy.auth.custom.common.log.event;

import cn.hy.auth.custom.common.log.domain.LogDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 类描述: 日志事件
 *
 * <AUTHOR>
 * @date ：创建于 2022/6/1
 */
public class LogEvent extends ApplicationEvent {

    @Getter
    private final LogDTO logWithBlobDTO;
    /**
     * 是否保存到日志表中
     */
    @Getter
    private final Boolean isSaveToTable;
    @Getter
    private final String token;
    public LogEvent(Object source, LogDTO logWithBlobDTO, Boolean isSaveToTable, String token) {
        super(source);
        this.logWithBlobDTO = logWithBlobDTO;
        this.isSaveToTable = isSaveToTable;
        this.token = token;
    }
}

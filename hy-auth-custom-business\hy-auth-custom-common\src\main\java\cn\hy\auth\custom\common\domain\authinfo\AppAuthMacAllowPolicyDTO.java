package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录时，MAC限制策略
 * 不启用，则不限制Mac登录。
 *
 * <AUTHOR>
 * @date 2020-12-03 14:20
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthMacAllowPolicyDTO extends BasePolicyDTO {

    private static final long serialVersionUID = -7623341181272051852L;
    /**
     * mac限制策略对应帐号表的字段编码
     * 多个mac地址用逗号隔开。
     */
    private String mac;
}

package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthInfoParser;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.user.account.dao.HrMemberDao;
import cn.hy.auth.custom.user.account.domain.*;
import cn.hy.auth.custom.user.account.mapper.DynamicUserAccountMapper;
import cn.hy.auth.custom.user.account.service.KickOutEnum;
import cn.hy.auth.custom.user.account.service.OauthLogoutService;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.account.service.UserSecurityManageService;
import cn.hy.auth.custom.user.utils.PasswordAesUtil;
import cn.hy.dataengine.context.DataEngineContextProxy;
import cn.hy.id.IdWorker;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 用户账号管理
 * 查询用户账号信息，及缓存维护
 *
 * <AUTHOR>
 * @date 2020/11/25 17:19
 **/
@Slf4j
@Service
public class UserAccountServiceImpl implements UserAccountService {

    /**
     * 密码规则
     */
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^[a-zA-Z0-9\\s\\\\.~!@#$%^&*()-_=+{}\\]:;\"',/?|]{6,18}$");
    @Value("${hy.security.paas.pwdKey:flowhysuseraesco}")
    private String paasPwdKey;

    private DynamicUserAccountMapper userAccountMapper;

    private HrMemberDao hrMemberDao;

    private UserSecurityManageService userSecurityManageService;

    @Autowired
    @Lazy
    private OauthLogoutService authBaseService;

    @Autowired
    private IdWorker idWorker;

    public UserAccountServiceImpl(DynamicUserAccountMapper userAccountMapper, HrMemberDao hrMemberDao, UserSecurityManageService userSecurityManageService, IdWorker idWorker) {
        this.userAccountMapper = userAccountMapper;
        this.hrMemberDao = hrMemberDao;
        this.userSecurityManageService = userSecurityManageService;
        this.idWorker = idWorker;
    }

    /**
     * 根据多个字段条件查询用户信息，只有一个字段符合指定值，就返回用户信息
     *
     * @param fieldCodes 字段列表
     * @param value      指定值
     * @return 用户信息
     */
    @Override
    public UserAccountDTO getUserByMultiField(List<String> fieldCodes, Object value) {
        return userAccountMapper.selectByMultiField(fieldCodes, value);
    }

    @Override
    public UserAccountDTO getUserByUserName(String userName) {
        //从缓存中获取用户主键
        UserAccountDTO userAccountDTO = userAccountMapper.selectByUserName(userName);
        if (ObjectUtil.isNull(userAccountDTO)) {
            log.info(String.format("用户名:[%s],查询账号表数据不存在", userName));
        }
        AuthAssertUtils.isNotNull(userAccountDTO, AuthErrorCodeEnum.C0319.code(), LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg1", null));
        return userAccountDTO;
        //Long userId = getUserIdByUserName(userName);
        //AuthAssertUtils.isNotNull(userId, AuthErrorCodeEnum.C0319.code(), String.format("用户名:[%s],查询账号表数据不存在", userName));
        // fxr 模糊提示
        // if (userId == null){
        //    log.info(String.format("用户名:[%s],查询账号表数据不存在", userName));
        //}
        //AuthAssertUtils.isNotNull(userId, AuthErrorCodeEnum.C0319.code(), LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg1", null));
        //return getUserByUserId(userId);
    }

    @Override
    public UserAccountDTO getUserAllInfoByUserName(String loginName) {
        List<String> fields = AppAuthInfoParser.INSTANCE.parseLoginFields(loginName);
        UserAccountDTO userAccountDTO = this.getUserByMultiField(fields, loginName);
        //查询用户所有关联信息
        if (userAccountDTO != null) {
            Map<String, Object> user = this.getUserAllInfoById(userAccountDTO.getId());
            userAccountDTO.setUser(user);
            return userAccountDTO;
        }
        return null;
    }

    /**
     * UserCacheServiceImpl和UserAccountServiceImpl（69）
     *
     * @param userId 用户主键 。
     * @return 。
     */
    @Override
    public UserAccountDTO getUserAllInfoByUserId(@NotNull Long userId) {
        UserAccountDTO userAccount = userAccountMapper.selectById(userId);
        AuthAssertUtils.isNotNull(userAccount, AuthErrorCodeEnum.C0319.code(), String.format(LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg2", null), userId));

        //查询用户信息
        Map<String, Object> user = userAccountMapper.getUserAllInfoById(userId);
        userAccount.setUser(user);

        //返回账号信息
        return userAccount;
    }

    @Override
    public Map<String, Object> getUserAllInfoById(Long userId) {
        return userAccountMapper.getUserAllInfoById(userId);
    }


    @Override
    public void updateUserPassword(String tokenId, UserPwdUpdateDTO userPwdUpdateDTO) {

        UserAccountDTO account = userAccountMapper.selectByUserName(userPwdUpdateDTO.getUserName());

        // 不允许修改服务内置账号密码
        Integer isSystemRecode = (Integer) account.getAccount().get("is_system_recode");
        AuthAssertUtils.isFalse(isSystemRecode != null && isSystemRecode == 3,
                AuthErrorCodeEnum.A0126.code(), AuthErrorCodeEnum.A0126.msg());

        EncryptTypeEnum encryptType = EncryptTypeEnum.codeOf(userPwdUpdateDTO.getPwdEncryptionType());
        //密码校验是否通过
        boolean isPass = PasswordAesUtil.matches(account.getPassword(), userPwdUpdateDTO.getOldPwd(),
                userPwdUpdateDTO.getUserName(), encryptType);
        AuthAssertUtils.isTrue(isPass, AuthErrorCodeEnum.A0120.code(), AuthErrorCodeEnum.A0120.msg());
        // 不能和原密码相同
        AuthAssertUtils.isFalse(PasswordAesUtil.matches(userPwdUpdateDTO.getNewPwd(), userPwdUpdateDTO.getOldPwd(), userPwdUpdateDTO.getUserName(), EncryptTypeEnum.PASSWORD_ENCRYPTION),
                AuthErrorCodeEnum.A0123.code(), AuthErrorCodeEnum.A0123.msg());

        //加密之后的密码
        String encryptPassword = PasswordAesUtil.extractPwd(userPwdUpdateDTO.getNewPwd(),
                userPwdUpdateDTO.getUserName(),
                encryptType);
        checkPwdPattern(encryptPassword, userPwdUpdateDTO);
        //更新用户信息的密文
        userAccountMapper.updatePassword(account.getId(), encryptPassword);
        //更新密码最近修改时间
        userAccountMapper.updatePasswordModifyTime(account.getId());
        // 更新默认密码标记
        try {
            userAccountMapper.updatePasswordPwdStatus(account);
        } catch (Exception e) {
            log.warn("更新默认密码标识失败,不影响主流程. tokenId: {},租户：{},应用号:{}，用户名:{}", tokenId, account.getLesseeCode(), account.getAppCode(), account.getUserAccountName());
        }
        // 新增/更新 密码最近修改时间、密码修改状态、时间间隔
        userSecurityManageService.insertOrUpdatePwdChangeTimeStatus(account, "1", new Date());

        // 踢出该用户的所有token
        authBaseService.kickOut(AuthContext.getContext().getLesseeCode(), account.getUserAccountName(), KickOutEnum.RESET_PWD);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bingThirdAccount(ThirdUserBindDTO thirdUserBindDTO) {
        AuthAssertUtils.notNull(thirdUserBindDTO.getUserAccountName(), "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg3", null));
        AuthAssertUtils.notNull(thirdUserBindDTO.getProviderId(), "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg4", null));
        String providerId = thirdUserBindDTO.getProviderId();
        String providerUserId = thirdUserBindDTO.getProviderUserId();
        log.info("第三方用户表主键ID：{}，第三方用户ID：{}",thirdUserBindDTO.getThirdUserId(),providerUserId);
        if(providerUserId==null || "".equals(providerUserId)){
            if (thirdUserBindDTO.getThirdUserId() != null) {
                Map<String, Object> map = userAccountMapper.queryThirdById(thirdUserBindDTO.getThirdUserId());
                if (!MapUtil.isEmpty(map)) {
                    providerUserId = (String) map.get("provider_user_id");
                    log.info("获取到第三方用户ID：{}",providerUserId);
                }
            }
        }
        AuthAssertUtils.notNull(providerUserId, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg5", null));
        userAccountMapper.deleteThirdRelation(providerId, providerUserId, thirdUserBindDTO.getUserAccountName());
        ThirdUserRelationDTO userRelationDTO = ThirdUserRelationDTO.builder()
                .id(UUIDRandomUtils.getSnowUuid())
                .userAccountName(thirdUserBindDTO.getUserAccountName())
                .providerId(providerId)
                .providerUserId(providerUserId)
                .createTime(new Date())
                .createUserId(1L)
                .lastUpdateUserId(1L)
                .lastUpdateTime(new Date()).build();
        userAccountMapper.saveThirdRelation(userRelationDTO);
        return providerUserId;
    }

    @Override
    public Boolean birdThirdMember(@NonNull String userAccountName, @NonNull String providerId, @NonNull String providerUserid,
                                   Boolean isBindThirdUser) {
        List<Map<String, Object>> hrCodeByAccount = hrMemberDao.getHrCodeByAccount(userAccountName);
        if (CollectionUtils.isEmpty(hrCodeByAccount)) {
            AuthAssertUtils.isTrue(false, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg6", null));
        }
        try {
            String hrCode = (String) (hrCodeByAccount.get(0).get("hr_member_code"));
            if (providerId.equals(ProviderType.DING_TALK.getCode())) {
                hrMemberDao.updateByHrCode(hrCode, providerUserid, null);
            } else if (providerId.equals(ProviderType.WECHAT.getCode())) {
                hrMemberDao.updateByHrCode(hrCode, null, providerUserid);
            }
            if (isBindThirdUser) {
                //如果需要绑定第三方用户id
                ThirdUserBindDTO thirdUser = new ThirdUserBindDTO();
                thirdUser.setThirdUserId(providerUserid);
                thirdUser.setProviderId(providerId);
                thirdUser.setUserAccountName(userAccountName);
                bingThirdAccount(thirdUser);
            }
        } catch (Exception e) {
            log.error("回填人员表数据失败:{}", e);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean checkUserAccount(String providerId, String userAccountName, String providerUserId) {
        // AuthAssertUtils.notNull(providerUserId,"SA500",LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg5", null));
        AuthAssertUtils.notNull(userAccountName, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg3", null));
        AuthAssertUtils.notNull(providerId, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg4", null));
        //判断该账号是否存在
        Map<String, Object> userAccountDTO = userAccountMapper.selectOneByAccout(userAccountName);
        if (MapUtil.isEmpty(userAccountDTO)) {
            AuthAssertUtils.isTrue(false, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg7", null) + userAccountName + LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg8", null));
        }
        //当前第三方号是否已被应用其他账号绑定
        List<Map<String, Object>> userAccount = userAccountMapper.findUserAccount(providerId, userAccountName, providerUserId);
        if (CollectionUtil.isNotEmpty(userAccount) && StringUtils.isNotBlank(providerUserId)) {
            AuthAssertUtils.isTrue(false, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg9", null));
        }

        //当前账号是否已被其他第三方绑定
        List<Map<String, Object>> providerUser = userAccountMapper.findProviderUser(providerId, userAccountName, providerUserId);
        if (CollectionUtil.isNotEmpty(providerUser) && StringUtils.isNotBlank(userAccountName)) {
            AuthAssertUtils.isTrue(false, "SA500", LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg10", null));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeThirdBing(String providerId, String providerUserId, String userAccountName) {
        //根据手机号码获取用户id
        userAccountMapper.deleteThirdRelation(providerId, providerUserId, userAccountName);
        return true;
    }

    @Override
    public void updateUserForgetPassword(UserForgetPwdUpdateDTO userPwdUpdateDTO) {
        UserLoginAccountDTO accountDTO = userAccountMapper.selectUserIdByUserNameOrMobile(userPwdUpdateDTO.getUserName(), userPwdUpdateDTO.getPhoneNumber());

        EncryptTypeEnum encryptType = EncryptTypeEnum.codeOf(userPwdUpdateDTO.getPwdEncryptionType());

        //加密之后的密码
        String encryptPassword = PasswordAesUtil.extractPwd(userPwdUpdateDTO.getNewPwd(),
                userPwdUpdateDTO.getUserName(),
                encryptType);
        UserPwdUpdateDTO pwdUpdateDTO = new UserPwdUpdateDTO();
        pwdUpdateDTO.setPaasPwd(false);
        checkPwdPattern(encryptPassword, pwdUpdateDTO);
        //更新用户信息的密文
        userAccountMapper.updatePassword(accountDTO.getId(), encryptPassword);
        //更新密码最近修改时间
        userAccountMapper.updatePasswordModifyTime(accountDTO.getId());
    }

    @Override
    public UserLoginAccountDTO getUserByUserNameOrPhone(String userName, String phone) {
        return userAccountMapper.getUserByUserNameOrPhone(userName, phone);
    }

    @Override
    public void changeLoginInfoByUserNameList(List<String> userNames, UserOnLineStatus userOnLineStatus) {
        String lesseeCode = DataEngineContextProxy.getContext().getLesseeCode();
        String appCode = DataEngineContextProxy.getContext().getAppCode();
        if (!checkOnLineStatusTableIsExist(lesseeCode, appCode)) {
            return;
        }
        userAccountMapper.changeLoginInfoByUserNameList(userNames, userOnLineStatus.getStatus(), userOnLineStatus.getName());
    }

    @Override
    public boolean checkOnLineStatusTableIsExist(String lesseeCode, String appCode) {
        Integer integer = userAccountMapper.checkOnLineStatusTableIsExist(lesseeCode, appCode);
        return integer == 1;
    }

    @Override
    public void insertOrUpdateUserOnlineStataus(List<String> list, UserOnLineStatus userOnLineStatus) {
        SysdevUserOnlineStatusDTO statusDTO = new SysdevUserOnlineStatusDTO();
        statusDTO.setId(idWorker.nextId());
        statusDTO.setDataVersion("1");
        statusDTO.setCreateUserId(1L);
        statusDTO.setCreateUserName("系统生成");
        statusDTO.setCreateTime(new Date());
        statusDTO.setLastUpdateUserId(1L);
        statusDTO.setLastUpdateUserName("系统生成");
        statusDTO.setLastUpdateTime(new Date());
        statusDTO.setSequence(1L);
        statusDTO.setUserAccountName(list.get(0));
        statusDTO.setOnlineStatus(userOnLineStatus.getStatus());
        statusDTO.setOnlineStatusName(userOnLineStatus.getName());
        userAccountMapper.insertOrUpdateUserOnlineStataus(statusDTO);
    }

    @Override
    public void changeLoginStatusNotInSet(Set<String> userNames, UserOnLineStatus userOnLineStatus) {
        userAccountMapper.changeLoginStatusNotInSet(userNames, userOnLineStatus.getStatus(), userOnLineStatus.getName());
    }

    private void checkPwdPattern(String encryptPassword, UserPwdUpdateDTO userPwdUpdateDTO) {
        //解密之后的密码
        String decryptPassword = AesUtil.decryptByDefaultKey(encryptPassword);
        if (userPwdUpdateDTO.getPaasPwd()) {
            // 用paas的密钥来解码出明文，用于判断密码规则
            decryptPassword = AesUtil.decrypt(encryptPassword, paasPwdKey);
        }
        //验证密码
        //校验密码规则
        AuthAssertUtils.isTrue(PASSWORD_PATTERN.matcher(decryptPassword).matches(), AuthErrorCodeEnum.A0101.code(), LocaleUtil.getMessage("UserAccountServiceImpl.assertUtil.msg11", null));
    }

    @Override
    public UserLoginAccountDTO getUserByUserEmail(String email) {
        return userAccountMapper.getUserByUserEmail(email);
    }

    @Override
    public void updateUserForgetPasswordEmail(UserForgetPwdUpdateDTO userPwdUpdateDTO) {
        UserLoginAccountDTO accountDTO = userAccountMapper.getUserByUserEmail(userPwdUpdateDTO.getEmail());
        EncryptTypeEnum encryptType = EncryptTypeEnum.codeOf(userPwdUpdateDTO.getPwdEncryptionType());
        //加密之后的密码
        String encryptPassword = PasswordAesUtil.extractPwd(userPwdUpdateDTO.getNewPwd(),
                userPwdUpdateDTO.getUserName(),
                encryptType);
        UserPwdUpdateDTO pwdUpdateDTO = new UserPwdUpdateDTO();
        pwdUpdateDTO.setPaasPwd(false);
        checkPwdPattern(encryptPassword, pwdUpdateDTO);
        //更新用户信息的密文
        userAccountMapper.updatePassword(accountDTO.getId(), encryptPassword);
        //更新密码最近修改时间
        userAccountMapper.updatePasswordModifyTime(accountDTO.getId());
    }

}

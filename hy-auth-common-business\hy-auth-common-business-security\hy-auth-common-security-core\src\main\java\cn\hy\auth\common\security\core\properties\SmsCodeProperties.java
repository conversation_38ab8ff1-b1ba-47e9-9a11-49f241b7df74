package cn.hy.auth.common.security.core.properties;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020-11-24 10:30
 */
@Getter
@Setter
public class SmsCodeProperties {

    /**
     * 短信验证码长度
     */
    private int length = 6;
    /**
     * 验证码有效期,单位秒
     */
    private int expireInSeconds = 60;
    /**
     * 登录url
     */
    private String loginUrl = "/login/mobile";
    /**
     * 手机号参数名称
     */
    private String mobileParameter = "mobile";
    /**
     * 如果有加密需求则输出加密手机号
     */
    private String mobileCipherText = "mobileCipherText";

    /**
     * 验证码参数名称
     */
    private String codeParameter = "code";
}

package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.exceptions.BlockedLoginException;
import cn.hy.auth.custom.security.oauth2.event.PwdErrorEvent;
import cn.hy.auth.custom.user.account.domain.UserLoginFailure;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import cn.hy.auth.custom.user.utils.PasswordAesUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户密码验证
 *
 * <AUTHOR>
 * @date 2020-11-26 16:52
 **/
@Component
@Slf4j
public class PwdAuthenticationProviderer extends AbstractAuthenticationProvider {

    private final UserDetailsService userDetailsService;
    private final UserAccountLockService userAccountLockService;
    private final ApplicationContext applicationContext;
    public PwdAuthenticationProviderer(UserDetailsService userDetailsService, UserAccountLockService userAccountLockService, ApplicationContext applicationContext) {
        this.userDetailsService = userDetailsService;
        this.userAccountLockService = userAccountLockService;
        this.applicationContext = applicationContext;
    }

    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return 只能return null,否则会截断后续的处理
     */
    @Override
    protected Authentication doAuthenticate(Authentication authentication) {

        EncryptTypeEnum encryptType = getPwdEncryptType(authentication);

        String userName = (String)authentication.getPrincipal();
        if (StringUtils.isBlank(userName)) {
            log.error("用户名参数为空，不需要记录验证码次数。");
            throw new BadCredentialsException(LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg1", null));
        }

        log.debug("账号密码检查-->验证用户【{}】的密码。", userName);
        HyUserDetails account = getUserDetail(userName);
        if (Objects.isNull(account)) {
            log.error("账号密码检查-->根据用户名【" + userName + "】无法获取用户信息。");
            // 账号错误也需要记录验证码次数
            applicationContext.publishEvent(new PwdErrorEvent(userName));
            throw new BadCredentialsException(LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg1", null));
        }
        String pwdOfParam = (String)authentication.getCredentials();
        if(StringUtils.isBlank(pwdOfParam)){
            log.error("密码参数为空。");
            applicationContext.publishEvent(new PwdErrorEvent(userName));
            throw new BadCredentialsException(LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg1", null));
        }
        //密码验证结果
        boolean isPwdPass = PasswordAesUtil.matches(account.getPassword(), pwdOfParam, userName, encryptType);
        if (!isPwdPass) {
            log.error("账号密码检查-->用户【{}】密码错误。", userName);
            applicationContext.publishEvent(new PwdErrorEvent(userName));
            UserLoginFailure userLoginFailure = userAccountLockService.saveLoginFail(account.getUserId(), account.getUsername());
            String msg = LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg1", null);
            if(userLoginFailure != null){
                String errorThreshold = AuthContext.getContext().appAuthStrategy().getLoginRule().getAccountLockPolicy().getLoginErrorThreshold();
                int errorThresholdInt = Integer.parseInt(errorThreshold);
                long leftChance = errorThresholdInt - userLoginFailure.getLoginFailureCount();
                if (leftChance <=0){
                    msg = UserAccountLockAuthenticationProviderer.getAccountLockedTip();
                } else if (leftChance <= 3){
                    msg = LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg2", "")+userLoginFailure.getLoginFailureCount()+ LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg3", "") +leftChance+ LocaleUtil.getMessage("PwdAuthenticationProviderer.result.msg4", "");
                }
            }
            throw new BadCredentialsException(msg);
        }
        //doubleCheckUserAccountLock(account);
        //userAccountLockService.clearLoginFail(account.getUserId(), account.getUsername());
        log.debug("账号密码检查-->用户【{}】密码验证通过。", userName);
        return null;
    }

    private void doubleCheckUserAccountLock(HyUserDetails account) {
        if(userAccountLockService.isUserAccountLock(account.getUserId())){
            String msg = UserAccountLockAuthenticationProviderer.getAccountLockedTip();
            log.info("租户号：{}，应用号：{}，用户账号：{}，提示：{}",
                    AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),account.getUsername(),msg);
            throw new BlockedLoginException(msg);
        }
        log.info("第二次检查用户账号是否锁定-->用户账号【{}】没有锁定。", account.getUsername());
    }
    private HyUserDetails getUserDetail(String userName){
        return (HyUserDetails) userDetailsService.loadUserByUsername(userName);
    }
    /**
     * 获取密码加密类型，为空表示请求参数中没有指明类型
     *
     * @param authentication .
     * @return 密码加密类型，为空表示请求参数中没有指明类型
     */
    private EncryptTypeEnum getPwdEncryptType(Authentication authentication) {
        EncryptTypeEnum resultType = null;
        if (authentication.getDetails() instanceof Map) {
            Map<String, Object> params = (Map<String, Object>) authentication.getDetails();
            String encryptType = MapUtils.getString(params, LoginParamterConts.PWD_ENCRYPTION_TYPE, StringUtils.EMPTY);

            AuthAssertUtils.isNotBlank(encryptType, AuthErrorCodeEnum.A0101.code(),
                    LocaleUtil.getMessage("PwdAuthenticationProviderer.assertUtil.msg1", null) + LoginParamterConts.PWD_ENCRYPTION_TYPE + LocaleUtil.getMessage("PwdAuthenticationProviderer.assertUtil.msg2", null));
            resultType = EncryptTypeEnum.codeOf(Integer.parseInt(encryptType));

            AuthAssertUtils.isNotNull(resultType, AuthErrorCodeEnum.A0101.code(), LocaleUtil.getMessage("PwdAuthenticationProviderer.assertUtil.msg3", null) + encryptType + "】");
        }
        return resultType;
    }

    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    @Override
    protected int order() {
        return 5;
    }

    /**
     * 判断是否是用户密码登录模式
     *
     * @param authentication .
     * @return .
     */
    @Override
    protected boolean isSupport(Authentication authentication) {
        List<LoginTypeEnum> supports = Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD,
                LoginTypeEnum.OAUTH2_PASSWORD);

        return supports.contains(AuthContext.getContext().loginState().getLoginType());
    }
}

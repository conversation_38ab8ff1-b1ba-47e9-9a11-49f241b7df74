package cn.hy.auth.common.security.core.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/11/22 0022 11:54
 */
@ConfigurationProperties(prefix = "hy.security")
@Component
@Data
public class SecurityProperties {

    /**
     * 账号密码的表单登录
     */
    private FormAuthProperties formAuth = new FormAuthProperties();
    /**
     * 手机验证码的登录
     */
    private SmsCodeProperties smsAuth = new SmsCodeProperties();
    /**
     * 社交的登录
     */
    private SocialProperties social = new SocialProperties();
    /**
     * hy 第三方绑定登录
     */
    private HySocialProperties hySocialProperties = new HySocialProperties();

    /**
     * hy 人脸登录
     */
    private HyFaceLoginProperties hyFaceLoginProperties = new HyFaceLoginProperties();


    /**
     * hy 第三方绑定登录
     */
    private HyExternalProperties hyFkyProperties = new HyExternalProperties();

    /**
     * 二次认证
     */
    private ReAuthenticationProperties reAuthenticationProperties = new ReAuthenticationProperties();

    /**
     * 类描述：spring security 类型登录后响应的格式配置
     **/
    private SecurityResponseFormatProperties response = new SecurityResponseFormatProperties();
}

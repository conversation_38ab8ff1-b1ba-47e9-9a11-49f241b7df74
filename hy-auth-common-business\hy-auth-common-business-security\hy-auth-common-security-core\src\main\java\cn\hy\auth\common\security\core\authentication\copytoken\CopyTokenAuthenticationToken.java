package cn.hy.auth.common.security.core.authentication.copytoken;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Objects;

/**
 * 复制token的认证token
 *
 * <AUTHOR>
 * @date 2024-12-04 10:32
 */

public class CopyTokenAuthenticationToken extends AbstractAuthenticationToken {

    private static final long serialVersionUID = 13213L;
    private final Object principal;
    @Getter
    private final String token;
    @Getter
    private final String refreshToken;

    public CopyTokenAuthenticationToken(Object principal, String token, String refreshToken) {
        //因为刚开始并没有认证，因此用户没有任何权限，并且设置没有认证的信息（setAuthenticated(false)）
        super(null);
        //这里的principal就是手机号
        this.principal = principal;
        this.token = token;
        this.refreshToken = refreshToken;
        this.setAuthenticated(false);
    }

    CopyTokenAuthenticationToken(Object principal, String token, String refreshToken, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.token = token;
        this.refreshToken = refreshToken;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) {
        if (isAuthenticated) {
            throw new IllegalArgumentException("Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        } else {
            super.setAuthenticated(false);
        }
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CopyTokenAuthenticationToken that = (CopyTokenAuthenticationToken) o;
        return Objects.equals(principal, that.principal);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), principal);
    }
}

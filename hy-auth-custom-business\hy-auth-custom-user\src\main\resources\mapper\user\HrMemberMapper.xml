<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.user.account.dao.HrMemberDao">

    <select id="queryForMap" resultType="java.util.Map">
        select m.hr_name,m.hr_code,m.ding_ding_id,m.enterprise_wechat_id,ac.account_name
        from hr_member m
        left join hr_member_account ac
        on m.hr_code = ac.hr_member_code
        where m.ding_ding_id =#{userId} or m.enterprise_wechat_id=#{userId}
        limit 1
    </select>

    <select id="getHrCodeByAccount" resultType="java.util.Map">
        select hr_member_code from hr_member_account where account_name = #{userAccountName}
    </select>

    <update id="updateByHrCode">
        UPDATE hr_member
        <set>
            <if test="null != dingDingId and '' != dingDingId">ding_ding_id = #{dingDingId},</if>
            <if test="null != weChatId and '' != weChatId">enterprise_wechat_id = #{weChatId},</if>
            last_update_time = now()
        </set>
        WHERE hr_code = #{hrCode}
        <if test="null != dingDingId and '' != dingDingId"> and (ding_ding_id is null or ding_ding_id = '') </if>
        <if test="null != weChatId and '' != weChatId"> and (enterprise_wechat_id is null or enterprise_wechat_id = '')</if>
    </update>

    <select id="queryAccountByCode" resultType="java.util.Map">
        select m.hr_name,m.hr_code,ac.account_name
        from hr_member m
        left join hr_member_account ac
        on m.hr_code = ac.hr_member_code
        where 1=1
        <if test="hrCode!=null and hrCode!=''"> and m.hr_code = #{hrCode}</if>
        <if test="accountName!=null and accountName!=''"> and ac.account_name = #{accountName}</if>
        limit 1
    </select>

    <update id="updateThirdUserId">
        update hr_member
        <trim prefix="set" suffixOverrides=",">
            <if test="dingTalkId!=null and dingTalkId!=''">ding_ding_id=#{dingTalkId},</if>
            <if test="wechatId!=null and wechatId!=''">enterprise_wechat_id=#{wechatId},</if>
        </trim>
        WHERE hr_code=#{hrCode}
        <if test="dingTalkId!=null and dingTalkId!=''">and (ding_ding_id is null or ding_ding_id ='')</if>
        <if test="wechatId!=null and wechatId!=''">and (enterprise_wechat_id is null or enterprise_wechat_id='' )</if>
    </update>

</mapper>
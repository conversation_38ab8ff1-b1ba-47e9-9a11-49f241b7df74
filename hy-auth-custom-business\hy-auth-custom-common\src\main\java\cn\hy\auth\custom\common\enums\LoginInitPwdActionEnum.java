package cn.hy.auth.custom.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 登录初始密码处理类型
 *
 * <AUTHOR>
 * @date 2020-12-03 15:05
 **/
public enum LoginInitPwdActionEnum {
    /**
     * 仅提醒
     */
    ONLY_REMIND("onlyRemind"),
    /**
     * 强制修改不能登录成功
     */
    MUST_BE_MODIFIED("mustBeModified"),
    /**
     * 忽略
     */
    IGNORE("ignore");


    private String code;

    public String getCode() {
        return code;
    }

    LoginInitPwdActionEnum(String code) {
        this.code = code;
    }

    public static LoginInitPwdActionEnum codeOf(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (LoginInitPwdActionEnum en : LoginInitPwdActionEnum.values()) {
            if (en.getCode().equals(code)) {
                return en;
            }
        }
        return null;
    }
}

package cn.hy.auth.custom.security.oauth2.client.domain;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * hy_1_oauth_client_details
 * oauth客户端信息表
 *
 * <AUTHOR>
 * @date 2020/12/08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OauthClientDetailsDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * 客户端主键
     */
    private String clientId;

    /**
     * 客户端refresh_token_validity的有效期
     */
    private Integer refreshTokenValidity;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 客户端的access_token的有效时间值
     */
    private Integer accessTokenValidity;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    /**
     * 设置用户是否自动Approval操作
     */
    private String autoapprove;

    /**
     * 客户端支持的grant_type
     */
    private String authorizedGrantTypes;

    /**
     * 客户端所拥有的权限值
     */
    private String authorities;

    /**
     * 这是一个预留的字段
     */
    private String additionalInformation;

    /**
     * 客户端申请的权限范围
     */
    private String scope;

    /**
     * 客户端的重定向URI
     */
    private String webServerRedirectUri;

    /**
     * 用于指定客户端(client)的访问密匙
     */
    private String clientSecret;

    /**
     * 客户端所能访问的资源id集合
     */
    private String resourceIds;


}
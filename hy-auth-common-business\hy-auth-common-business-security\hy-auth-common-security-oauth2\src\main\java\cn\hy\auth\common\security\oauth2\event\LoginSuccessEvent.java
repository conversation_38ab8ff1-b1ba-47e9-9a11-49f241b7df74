package cn.hy.auth.common.security.oauth2.event;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 13:39
 **/
@ToString
public class LoginSuccessEvent extends ApplicationEvent {
    /**
     * 登录方式
     */
    private LoginSuccessEventType type;


    public LoginSuccessEvent(Authentication authentication, LoginSuccessEventType type) {
        super(authentication);
        this.type = type;
    }

    public LoginSuccessEventType getType() {
        return type;
    }

    public Authentication getAuthentication() {
        return (Authentication) getSource();
    }
}

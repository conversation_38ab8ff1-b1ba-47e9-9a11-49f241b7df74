package cn.hy.auth.common.security.oauth2.properties;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * oauth2 的客户端配置
 *
 * <AUTHOR>
 * @date 2020-11-12
 */
@Setter
@Getter
public class ClientProperties {
    /**
     * 授权客户端ID
     */
    private String clientId;
    /**
     * 授权客户端密钥
     */
    private String clientSecret;
    /**
     * 是否自动授权
     */
    private boolean autoApprove = true;
    /**
     * access_token有效期 0表示启用tokenSevice设置的access_token有效期
     */
    private int accessTokenValiditySeconds = 0;
    /**
     * refresh_token有效期 ,默认30天。
     * 0表示启用tokenSevice设置的refresh_token有效期
     */
    private int refreshTokenValiditySeconds = 0;
    /**
     * 支持的授权模式
     */
    private List<String> authorizedGrantTypes = Lists.newArrayList("password", "authorization_code", "client_credentials", "implicit", "refresh_token");
    /**
     * 支持的scope
     */
    private List<String> scope = Lists.newArrayList("app", "write");

}
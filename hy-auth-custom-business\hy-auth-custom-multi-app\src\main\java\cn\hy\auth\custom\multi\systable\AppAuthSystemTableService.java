package cn.hy.auth.custom.multi.systable;

import cn.hy.auth.custom.multi.event.AppAuthInfoLoadEvent;

import java.util.Map;

/**
 * 系统表维护接口
 *
 * <AUTHOR>
 * @date 2020-12-07 09:43
 **/
public interface AppAuthSystemTableService {
    /**
     * 认证规则加载完成事件
     *
     * @param event 事件数据
     */
    void onAuthInfoLoaded(AppAuthInfoLoadEvent event);


    /**
     * 获取应用信息
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @return .
     */
     Map<String, Object> getAppInfo(String lesseeCode, String appCode);
}

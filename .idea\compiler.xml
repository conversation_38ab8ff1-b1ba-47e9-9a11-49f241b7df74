<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="hy-auth-test" />
        <module name="hy-auth-common-security-common" />
        <module name="hy-auth-common-security-session" />
        <module name="hy-auth-custom-business-starter" />
        <module name="hy-auth-custom-common" />
        <module name="hy-auth-center-boot" />
        <module name="hy-auth-custom-multi-app" />
        <module name="hy-auth-common-security-session-starter" />
        <module name="hy-auth-custom-user" />
        <module name="hy-auth-custom-multi-datasource" />
        <module name="hy-auth-common-business-tool" />
        <module name="hy-auth-custom-security" />
        <module name="hy-auth-common-security-oauth2-starter" />
        <module name="hy-auth-common-security-oauth2" />
        <module name="hy-auth-custom-route" />
        <module name="hy-auth-common-security-core" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="hy-auth-custom-dubbo" target="1.8" />
      <module name="hy-auth-sdk" target="1.8" />
      <module name="hy-auth-sdk-starter" target="1.8" />
    </bytecodeTargetLevel>
  </component>
</project>
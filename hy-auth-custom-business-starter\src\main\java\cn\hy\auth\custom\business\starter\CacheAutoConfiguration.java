package cn.hy.auth.custom.business.starter;

import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.multi.cache.LesseeCaffeineCacheManager;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 类描述: 缓存动态切换工具类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/26
 */
@Slf4j
@Configuration
public class CacheAutoConfiguration {

    /**
     * 过期时间(s),没有配置则为半小时
     */
    @Value(value = "${auth.cache.expire_time:7200}")
    private Long expireTime;
    @Value(value = "${auth.cache.maximumSize:20000}")
    private Long maximumSize;

    /**
     * 过期时间(s),没有配置则为半小时
     */
    @Value(value = "${auth.cache.kickOut.expire_time:21600}")
    private Long kickOutExpireTime;
    @Value(value = "${auth.cache.kickOut.maximumSize:20000}")
    private Long kickOutMaximumSize;
    /**
     * 过期时间(s),没有配置则为半小时
     */
    @Value(value = "${auth.system_config.cache.expire_time:7200}")
    private Long systemConfigExpireTime;
    @Value(value = "${auth.system_config.cache.maximumSize:20000}")
    private Long systemConfigMaximumSize;

    @Bean("hyAuthAutoCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "caffeine",
            matchIfMissing = true
    )
    public CacheManager cacheManagerWithCaffeine() {
        log.debug("Caffeine cache 配置");
        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();
        Caffeine caffeine = Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(100)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
                .maximumSize(maximumSize)
                .expireAfterWrite(expireTime, TimeUnit.SECONDS);
        cacheManager.setCaffeine(caffeine);
        //不允许设置空值
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    /**
     *  系统基座级别的缓存，比如，AppInvolvedTable配置表信息，AppAuthStrategy应用登录配置信息，OauthClient登录客户端信息
     * @return .
     */
    @Bean("hyAuthSystemConfigCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.system_config.cache",
            name = "type",
            havingValue = "caffeine",
            matchIfMissing = true
    )
    public CacheManager systemConfigCacheManagerCaffeine() {
        log.debug("systemConfig Caffeine cache 配置");
        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();
        Caffeine caffeine = Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(100)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
                .maximumSize(systemConfigMaximumSize)
                .expireAfterWrite(systemConfigExpireTime, TimeUnit.SECONDS);
        cacheManager.setCaffeine(caffeine);
        //不允许设置空值
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    @Bean("hyAuthAutoCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "redis"
    )
    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {
        log.debug("redis cache 配置");

        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                //不允许存入空值
                .disableCachingNullValues()
                .computePrefixWith(compute -> CachePrefixUtil.get() + compute + "::")
                //设置默认超过期时间是60000秒
                .entryTtl(Duration.ofSeconds(expireTime));

        //初始化RedisCacheManager
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);
    }

    @Bean("hyAuthSystemConfigCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.system_config.cache",
            name = "type",
            havingValue = "redis"
    )
    public CacheManager systemConfigCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {
        log.debug("systemConfig redis cache 配置");

        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                //不允许存入空值
                .disableCachingNullValues()
                .computePrefixWith(compute -> CachePrefixUtil.get() + compute + "::")
                //设置默认超过期时间是60000秒
                .entryTtl(Duration.ofSeconds(systemConfigExpireTime));

        //初始化RedisCacheManager
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);
    }

    //
    @Bean("hyKickOutTokenCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "caffeine",
            matchIfMissing = true
    )
    public CacheManager kickOutTokenCacheManager() {
        log.debug("kickOutTokenCacheManager Caffeine cache 配置");
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        Caffeine<Object,Object> caffeine = Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(100)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
                .maximumSize(maximumSize)
                .expireAfterWrite(kickOutExpireTime, TimeUnit.SECONDS);
        cacheManager.setCaffeine(caffeine);
        //不允许设置空值
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    @Bean("hyKickOutTokenCacheManager")
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "redis"
    )
    public CacheManager kickOutTokenCacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {
        log.debug("kickOutTokenCacheManager redis cache 配置");

        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                //不允许存入空值
                .disableCachingNullValues()
                //设置默认超过期时间是60000秒
                .entryTtl(Duration.ofSeconds(kickOutExpireTime));
        //初始化RedisCacheManager
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);
    }

    /**
     * 因为按key模糊获取数据的时候没有提供对应的方法，所有如果是redis的时候暴露处理方便使用
     *
     * @param factory factory
     * @return RedisTemplate
     */
    @Bean
//    @ConditionalOnMissingBean(RedisTemplate.class)
//    @ConditionalOnProperty(
//            prefix = "auth.cache",
//            name = "type",
//            havingValue = "redis"
//    )
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        // 创建一个模板类
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 将刚才的redis连接工厂设置到模板类中
        template.setConnectionFactory(factory);

        // 设置redis的String/Value的默认序列化方式
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.java());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(RedisSerializer.java());
        template.afterPropertiesSet();
        return template;
    }

}

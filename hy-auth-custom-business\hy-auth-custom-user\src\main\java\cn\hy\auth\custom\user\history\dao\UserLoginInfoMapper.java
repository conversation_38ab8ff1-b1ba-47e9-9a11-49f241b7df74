package cn.hy.auth.custom.user.history.dao;

import cn.hy.auth.custom.user.history.domain.UserLoginInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类描述: 登录信息
 *
 * <AUTHOR>
 * @date :创建于 2020/11/17
 */
public interface UserLoginInfoMapper {

    /**
     * 保存登录数据
     *
     * @param record 记录
     * @return 返回插入条数
     */
    int insert(UserLoginInfoDO record);

    /**
     * 按主键查询登录信息
     *
     * @param id 主键
     * @return 返回对应的数据
     */
    UserLoginInfoDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 获取上一次登录成功的数据
     *
     * @param clientId 客户端标识
     * @param userId   用户主键
     * @return 返回上一次登录信息
     */
    List<UserLoginInfoDO> getLastTwoSuccessLoginInfo(@Param("clientId") String clientId, @Param("userId") String userId);

    /**
     *  获取上一次登录成功信息
     * @param clientId .
     * @param userAccountName .
     * @return .
     */
    UserLoginInfoDO getLastSuccessLoginInfo(@Param("clientId") String clientId, @Param("userAccountName") String userAccountName);
    /**
     * 获取最大排序号
     *
     * @return 返回排序号
     */
    Long getMaxSequence();
}
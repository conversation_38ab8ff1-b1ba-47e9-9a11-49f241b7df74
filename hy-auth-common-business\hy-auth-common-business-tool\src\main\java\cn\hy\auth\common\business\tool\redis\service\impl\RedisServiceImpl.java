package cn.hy.auth.common.business.tool.redis.service.impl;

import cn.hy.auth.common.business.tool.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: CAIPENG
 * @create: 2019-09-04 17:08
 **/
@Service
@Slf4j
public class RedisServiceImpl implements RedisService {

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 批量获取多个key的数据
     *
     * @param keys
     * @return
     */
    @Override
    public List<Object> multiGet(List<String> keys) {
        return redisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * set存数据 支持Map
     *
     * @param key
     * @param map
     */
    @Override
    public void setHash(String key, Map<String, Object> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * set存数据 支持List
     *
     * @param key
     * @param list
     */
    @Override
    public void setList(String key, List<Object> list) {
        redisTemplate.opsForList().rightPushAll(key, list);
    }


    /**
     * 獲取List
     *
     * @param key
     */
    @Override
    public List<Object> getList(String key) {
       return redisTemplate.opsForList().range(key, 0, -1);
    }


    /**
     * 查询KEY是否存在
     *
     * @param key
     * @return
     */
    @Override
    public boolean existsKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 重名名key，如果newKey已经存在，则newKey的原值被覆盖
     *
     * @param oldKey
     * @param newKey
     */
    @Override
    public void renameKey(String oldKey, String newKey) {
        redisTemplate.rename(oldKey, newKey);
    }

    /**
     * newKey不存在时才重命名
     *
     * @param oldKey
     * @param newKey
     * @return 修改成功返回true
     */
    @Override
    public boolean renameKeyNotExist(String oldKey, String newKey) {
        return redisTemplate.renameIfAbsent(oldKey, newKey);
    }

    /**
     * 删除key
     *
     * @param key
     */
    @Override
    public void deleteKey(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 删除多个key
     *
     * @param keys
     */
    @Override
    public void deleteKey(String... keys) {
        Set<String> kSet = Stream.of(keys).map(k -> k).collect(Collectors.toSet());
        redisTemplate.delete(kSet);
    }

    /**
     * 删除Key的集合
     *
     * @param keys
     */
    @Override
    public void deleteKey(Collection<String> keys) {
        Set<String> kSet = keys.stream().map(k -> k).collect(Collectors.toSet());
        redisTemplate.delete(kSet);
    }

    /**
     * 设置key的生命周期
     *
     * @param key
     * @param time
     * @param timeUnit
     */
    @Override
    public void expireKey(String key, long time, TimeUnit timeUnit) {
        redisTemplate.expire(key, time, timeUnit);
    }

    /**
     * 指定key在指定的日期过期
     *
     * @param key
     * @param date
     */
    @Override
    public void expireKeyAt(String key, Date date) {
        redisTemplate.expireAt(key, date);
    }

    /**
     * 查询key的生命周期
     *
     * @param key
     * @param timeUnit
     * @return
     */
    @Override
    public long getKeyExpire(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key, timeUnit);
    }

    /**
     * 将key设置为永久有效
     *
     * @param key
     */
    @Override
    public void persistKey(String key) {
        redisTemplate.persist(key);
    }


    @Override
    public void publish(String channel, Object value) {

        log.info("[RedisTest]发布-->[{}]--[{}]", channel, value);
        redisTemplate.convertAndSend(channel, value);
    }

    /**
     * 根据前缀获取Key列表
     *
     * @param prefix 前缀字符，不需要包括通配符
     * @return
     */
    @Override
    public Set<String> getKeys(String prefix) {
        return redisTemplate.keys(prefix + "*");
    }

    /**
     * 根据后缀获取Key列表
     *
     * @param suffix 后缀字符，不需要包括通配符
     * @return
     */
    @Override
    public Set<String> getKeysBySuffix(String suffix) {
        return redisTemplate.keys("*" + suffix);
    }

    /**
     * 根据前缀获取value列表
     *
     * @param prefix 前缀字符，不需要包括通配符
     * @return
     */
    @Override
    public List<Object> getValuesByPrefix(String prefix) {
        Set<String> keys = getKeys(prefix);
        return redisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * 根据后缀获取value列表
     *
     * @param suffix 后缀字符，不需要包括通配符
     * @return
     */
    @Override
    public List<Object> getValuesBySuffix(String suffix) {
        Set<String> keys = getKeysBySuffix(suffix);
        return redisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * 根据前后缀获取value列表
     *
     * @param affix 前后缀字符，不需要包括通配符
     * @return
     */
    @Override
    public List<Object> getValuesByAffix(String affix) {
        Set<String> keys = redisTemplate.keys("*" + affix + "*");
        return redisTemplate.opsForValue().multiGet(keys);
    }
}

package cn.hy.auth.common.security.oauth2.config;

import cn.hy.auth.common.security.core.authentication.common.AbstractHySecurityConfigurerAdapter;
import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationDetailsSource;
import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationProviderChain;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.oauth2.extend.HyLoginUrlAuthenticationEntryPoint;
import cn.hy.auth.common.security.oauth2.properties.ResourceServerProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 资源服务器配置类(提供默认实现，以及扩展配置)
 * 可以参考 OAuth2ResourceServerConfiguration
 *
 * <AUTHOR>
 * @date 2020-11-20
 */
@Configuration
@EnableResourceServer
public class HyResourceServerConfig extends ResourceServerConfigurerAdapter {
    @Autowired(required = false)
    private ResourceServerProperties resourceServerProperties = new ResourceServerProperties();
    /**
     * 登录验证成功的处理器
     */
    @Autowired(required = false)
    private AuthenticationSuccessHandler myAuthenticationSuccessHandler;
    /**
     * 登录验证失败的处理器
     */
    @Autowired(required = false)
    private AuthenticationFailureHandler myAuthenticationFailureHandler;
    /**
     * 自定义登录业务验证链
     */
    @Autowired(required = false)
    private HyAuthenticationProviderChain hyAuthenticationProviderChain;
    // copy from oauth2里的 AuthorizationServerSecurityConfigurer
    /**
     * 用户访问没有权限资源的处理.授权失败(forbidden)时返回信息
     */
    @Autowired(required = false)
    private AccessDeniedHandler accessDeniedHandler;

    /**
     * 表单登录的配置
     */
    @Autowired
    private SecurityProperties securityProperties;
    /**
     * 认证入口点,处理认证（登录）时的错误。用于tokan校验失败返回信息
     */
    @Autowired(required = false)
    private AuthenticationEntryPoint authenticationEntryPoint;
    /**
     * 自定义增加的securityConfigurerAdapter配置
     */
    @Autowired(required = false)
    @Lazy
    private List<AbstractHySecurityConfigurerAdapter> securityConfigurerAdapters;

    /**
     * 用户信息查询的服务,必须由调用方实现
     */
    @Autowired
    private UserDetailsService userDetailsService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        // 如果关闭 stateless，则 accessToken 使用时的 session id 会被记录，后续请求不携带 accessToken 也可以正常响应
        // 如果 stateless 为 true 打开状态，则 每次请求都必须携带 accessToken 请求才行，否则将无法访问
        resources.resourceId(resourceServerProperties.getResourceId()).stateless(resourceServerProperties.isStateless());
    }

    /**
     * 数据库验证模式
     */
    private DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);
        daoAuthenticationProvider.setUserDetailsService(userDetailsService);
        return daoAuthenticationProvider;
    }

    /**
     * @param http .
     * @throws Exception .
     */
    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 资源服务器拦截的路径 注意此路径不要拦截主过滤器（spring security）放行的URL
        // 在HyWebSecurityConfig里配置permitAll()即可,实验验证了2者效果一样
        formLoginConfig(http);
        // 短信验证码等其他登录方式配置
        if (!CollectionUtils.isEmpty(securityConfigurerAdapters)) {
            for (AbstractHySecurityConfigurerAdapter securityConfigureAdapter : securityConfigurerAdapters) {
                http.apply(securityConfigureAdapter);
            }
        }
        http.exceptionHandling()
                .authenticationEntryPoint(getAuthenticationEntryPoint())
                .accessDeniedHandler(accessDeniedHandler)
                //允许访问
                .and().authorizeRequests().anyRequest().authenticated()
                //禁用跨站伪造
                .and().csrf().disable();
    }

    /**
     * 表单账号密码登录的配置
     *
     * @param http .
     * @throws Exception .
     */
    private void formLoginConfig(HttpSecurity http) throws Exception {
        if (this.hyAuthenticationProviderChain != null) {
            this.hyAuthenticationProviderChain.getAuthenticationProviders().forEach(
                    http::authenticationProvider
            );
        }
        http.authenticationProvider(daoAuthenticationProvider());
        //表单登录,loginPage为登录请求的url,loginProcessingUrl为表单登录处理的URL
        http.formLogin()
                .loginPage(securityProperties.getFormAuth().getLoginPage())
                .loginProcessingUrl(securityProperties.getFormAuth().getLoginProcessingUrl())
                .usernameParameter(securityProperties.getFormAuth().getUsernameParameter())
                .passwordParameter(securityProperties.getFormAuth().getPasswordParameter())
                .authenticationDetailsSource(new HyAuthenticationDetailsSource(securityProperties.getFormAuth().getPasswordParameter(), "client_secret"))
                //登录成功之后的处理
                .successHandler(myAuthenticationSuccessHandler)
                //登录失败之后的处理
                .failureHandler(myAuthenticationFailureHandler);
        //.and()
        //.logout()
        //.deleteCookies("JSESSIONID")
        //.logoutUrl("/logout") 登出暂时不处理，由业务层或者默认security处理
        //.logoutSuccessHandler(securityLogoutSuccessHandler)
        //.permitAll()
    }

    private AuthenticationEntryPoint getAuthenticationEntryPoint() {
        if (authenticationEntryPoint == null) {
            authenticationEntryPoint = new HyLoginUrlAuthenticationEntryPoint(securityProperties.getFormAuth().getLoginProcessingUrl());
        }
        return authenticationEntryPoint;
    }
}
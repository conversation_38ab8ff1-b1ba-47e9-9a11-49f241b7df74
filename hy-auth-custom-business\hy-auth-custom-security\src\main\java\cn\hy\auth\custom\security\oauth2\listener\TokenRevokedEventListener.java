package cn.hy.auth.custom.security.oauth2.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.LoginOutTypeEnum;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.security.oauth2.event.LogOutLogEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cn.hy.auth.custom.common.constant.LoginParamterConts.LOGIN_MAC;

/**
 * 类描述：token撤销事件监听器
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/26 12:19
 **/
@Component
@Slf4j
public class TokenRevokedEventListener implements ApplicationListener<TokenRevokedEvent> {

    private final UserCacheTokenService userCacheTokenService;

    private final ApplicationContext applicationContext;

    private static final String CLIENT_ID = "client_id";

    public TokenRevokedEventListener(UserCacheTokenService userCacheTokenService, ApplicationContext applicationContext) {
        this.userCacheTokenService = userCacheTokenService;
        this.applicationContext = applicationContext;
    }

    @Override
    public void onApplicationEvent(TokenRevokedEvent tokenRevokedEvent) {
        log.debug("接收到token撤销事件:tokenId = [{}],result = [{}]", tokenRevokedEvent.getTokenValue(), tokenRevokedEvent.isRevokeToken());

        String ip = IpUtils.getIpAddress();
        //记录登出日志
        UserLoginInfoDTO loginLogDTO = UserLoginInfoDTO.builder().ip(ip).type(LoginOutTypeEnum.LOGOUT_SUCCESS.getCode()).build();

        UserAccountDTO userAccountDTO = userCacheTokenService.getUserCacheByTokenId(tokenRevokedEvent.getTokenValue());

        if (ObjectUtil.isNotNull(userAccountDTO)) {
            loginLogDTO.setUserId(userAccountDTO.getId());
            loginLogDTO.setCreateUserId(userAccountDTO.getId());
            loginLogDTO.setLastUpdateUserId(userAccountDTO.getId());
            loginLogDTO.setUserAccountName(userAccountDTO.getUserAccountName());
        }

        //移除缓存
        userCacheTokenService.deleteUserCacheByTokenId(tokenRevokedEvent.getTokenValue());

        Authentication userAuthentication = ObjectUtil.isNull(tokenRevokedEvent.getOAuth2Authentication()) ? null : tokenRevokedEvent.getOAuth2Authentication().getUserAuthentication();
        if (userAuthentication != null) {
            Map<String, Object> detail = (Map<String, Object>) userAuthentication.getDetails();
            if (MapUtils.isNotEmpty(detail)) {
                Object clientId = detail.get(CLIENT_ID);
                loginLogDTO.setMac(MapUtils.getString(detail, LOGIN_MAC));
                loginLogDTO.setClientId(ObjectUtil.isNull(clientId) ? null : clientId.toString());
            }
        }

        //发出登出需要记录日志的事件
        applicationContext.publishEvent(new LogOutLogEvent(loginLogDTO));
    }
}

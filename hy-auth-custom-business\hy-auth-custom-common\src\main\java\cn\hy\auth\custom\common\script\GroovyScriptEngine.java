package cn.hy.auth.custom.common.script;

import com.google.common.collect.ImmutableMap;
import groovy.lang.Binding;
import groovy.lang.Script;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 类描述：线程安全的groovy脚本执行器
 * //@ThreadSafe
 * <AUTHOR> by fuxinrong
 * @date 2021/8/25 11:24
 **/

@Component
public class GroovyScriptEngine implements ScriptEngine {
    private final GroovyScriptFactory groovyScriptFactory;
    public GroovyScriptEngine(){
        this.groovyScriptFactory = new GroovyScriptFactory();
    }
    @Override
    public Object run( final String scriptText, final Map<String, Object> params) {
        final Script script = groovyScriptFactory.borrowScript(scriptText);
       // System.out.println(Thread.currentThread().getName()+" use "+script.toString());
        try {
            if (params != null && params.size() > 0) {
                Binding binding = script.getBinding();
                // 清空历史的参数
                binding.getVariables().clear();
                params.forEach(binding::setVariable);
                script.setBinding(binding);
            }
            return script.run();
        }finally {
            groovyScriptFactory.returnScript(scriptText,script);
        }
    }

    /**
     *  基于线程名称和脚本内容获取Script对象，可以保证多线程并发安全，同时也能控制好类的数量 .
     *  https://www.it1352.com/2439090.html
     */



    public static void main(String[] args) throws InterruptedException {
        GroovyScriptEngine groovyScriptEngine = new GroovyScriptEngine();
        for (int index=0;index<3;index++){
            System.out.println("第"+index+"次================================");
            for (int i = 1; i<=20;i++){
               final int tempI = i;
                Thread thread = new Thread(() -> {
                    Object run = groovyScriptEngine.run("sleep (i%10);return input1+' '+input2",
                            ImmutableMap.<String, Object>builder()
                                    .put("input1", "hello")
                                    .put("input2", "zhangsan" + tempI)
                                    .put("i", tempI)
                                    .build());
                    System.out.println(Thread.currentThread().getName() + " " + run);
                });
                thread.setName("Thread-"+tempI);
                thread.start();
            }
            Thread.sleep(1000);
        }
    }

}

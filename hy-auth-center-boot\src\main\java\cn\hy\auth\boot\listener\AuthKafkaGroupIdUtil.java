package cn.hy.auth.boot.listener;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 *  SaasKafkaGroupIdUtil
 * @date 2023-12-26 18:49
 *  saas标准kafka消费者Id生成工具类
 **/
@Slf4j
@Component("authKafkaGroupIdUtil")
public class AuthKafkaGroupIdUtil {

    @Value("${spring.kafka.consumer.group-id:hy-auth-sys}")
    private String prefix;

    @Value("${server.port:6060}")
    private String serverPort;

    /**
     * 模板。规则是：前缀（yaml可修改）-ip地址-端口
     */
    private static final String TEMPLATE = "%s-%s-%s";

    private static final String HOST = "HOST";
    private static final String PORT = "PORT";

    private static final String IP_REGEX =
            "^(1\\d{2}|2[0-4]\\d|25[0-5]|[0-9]\\d|[0-9])\\."
            + "(1\\d{2}|2[0-4]\\d|25[0-5]|[0-9]\\d|\\d)\\."
            + "(1\\d{2}|2[0-4]\\d|25[0-5]|[0-9]\\d|\\d)\\."
            + "(1\\d{2}|2[0-4]\\d|25[0-5]|[0-9]\\d|\\d)$";

    /**
     * 构造saas的消费者ID
     * @return 前缀（yaml可修改）-ip地址-端口
     */
    public String buildSaasGroupId() {
        String host = System.getenv(HOST);
        String port = System.getenv(PORT);
        // 优先获取环境变量，如果为null或者不符合IP地址规则，则获取容器IP
        if (StrUtil.isBlank(host) || !host.matches(IP_REGEX)) {
            try {
                InetAddress localHost = Inet4Address.getLocalHost();
                host = localHost.getHostAddress();
            } catch (UnknownHostException e) {
                throw new RuntimeException(e);
            }
        }
        // 端口号同理，优先获取环境变量
        if (StrUtil.isBlank(port)) {
            log.warn("当前环境变量中不存在 PORT 变量，kafka消费者端口组成可能有问题！");
            port = serverPort;
        }
        return String.format(TEMPLATE, prefix, host, port);
    }
}

package cn.hy.auth.common.security.core.authentication.copytoken;

import lombok.Setter;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用token来模拟登录，复制一份token，模仿UsernamePasswordAuthenticationFilter
 *
 * <AUTHOR>
 * @date 2024/12/04  10:36
 */
@Setter
public class CopyTokenAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 请求中，参数为mobile
     */
    private static final String REFRESH_TOKEN = "refresh_token";
    private static final String TOKEN = "token";
    private static final String REQUEST_METHOD = "POST";
    private static final String LOGIN_COPY_TOKEN = "/login/copy-token";
    /**
     * 是否只处理post请求
     */
    private boolean postOnly = true;

    public CopyTokenAuthenticationFilter() {
        //要拦截的请求
        super(new AntPathRequestMatcher(LOGIN_COPY_TOKEN, REQUEST_METHOD));
    }

    public CopyTokenAuthenticationFilter(String loginUrl) {
        //要拦截的请求
        super(new AntPathRequestMatcher(loginUrl, REQUEST_METHOD));
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (this.postOnly && !REQUEST_METHOD.equals(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            String refreshToken = this.obtainRefreshToken(request);
            String token = this.obtainToken(request);
            //把手机号传进SmsCodeAuthenticationToken
            CopyTokenAuthenticationToken codeAuthenticationToken = new CopyTokenAuthenticationToken(refreshToken,token, refreshToken);
            codeAuthenticationToken.setDetails(this.authenticationDetailsSource.buildDetails(request));
            //调用AuthenticationManager
            return this.getAuthenticationManager().authenticate(codeAuthenticationToken);
        }
    }

    /**
     * 获取refresh_token
     *
     * @param request appinfo
     * @return String
     */
    private String obtainRefreshToken(HttpServletRequest request) {
        String refreshToken = request.getParameter(REFRESH_TOKEN);
        if (refreshToken == null) {
            refreshToken = "";
        }
        return refreshToken.trim();
    }
    /**
     * 获取token
     *
     * @param request appinfo
     * @return String
     */
    private String obtainToken(HttpServletRequest request) {
        String token = request.getParameter(TOKEN);
        if (token == null) {
            token = "";
        }
        return token.trim();
    }

}

FROM reg.hydevops.com/base_images/hy-jdk-alpine:jdk8_271_arthas

ARG JAR_FILE
ARG JAR_FOLDER
ARG EXPOSE_PORT

ADD /target/hy-auth-center-boot-1.0.0.jar /app/bin/
WORKDIR /app/bin

ENV RUN_JAR_FILE hy-auth-center-boot-1.0.0.jar
ENV RUN_EXPOSE_PORT 6060
ENV JAVA_OPTS "-Xms1024m -Xmx1024m -Xmn700m -XX:SurvivorRatio=6 -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -Xss512k \
	-XX:-HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=../logs/  \
	-XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintHeapAtGC -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=4096K -Xloggc:../logs/gc.log \
	-XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:CMSFullGCsBeforeCompaction=0 -XX:+UseCMSCompactAtFullCollection"


EXPOSE ${RUN_EXPOSE_PORT}

ENTRYPOINT java -server ${JAVA_OPTS} -jar ${RUN_JAR_FILE} $0 $@
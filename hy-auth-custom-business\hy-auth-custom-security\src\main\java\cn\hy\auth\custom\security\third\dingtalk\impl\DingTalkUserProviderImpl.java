package cn.hy.auth.custom.security.third.dingtalk.impl;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.service.HySocialUserInfoApi;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.log.util.OkHttpUtils;
import cn.hy.auth.custom.security.third.dingtalk.domain.DingTalkProviderUser;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * 钉钉用户信息获取
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class DingTalkUserProviderImpl extends AbstractOAuth2ApiBinding implements HySocialUserInfoApi {

    @Value("${third.proxy.dingtalk.url:http://10.0.3.32:9000/ddproxy/oapi}")
    private String baseUrl;
    @Value("${third.proxy.dingtalk.newUrl:https://api.dingtalk.com}")
    private String newUrl;


    /**
     * 响应码
     */
    private static final String ERROR_CODE_PARAMS = "errcode";

    @Override
    public DingTalkProviderUser getProviderUserInfo(String lesseeCode, String appCode, String code, ProviderAppAccessToken providerAppAccessToken,
                                                    ProviderInfo providerInfo, Map<String, Object> requestParamMap) {
        DingTalkProviderUser talkProviderUser = new DingTalkProviderUser();
        String url = baseUrl + providerInfo.getAppUserInfoUrl() + "?code=" + code + "&access_token=" + providerAppAccessToken.getAccessToken();
        String response = getRestTemplate().getForObject(url, String.class);
        if (response != null) {
            JSONObject resObject = JSONObject.parseObject(response);
            if (resObject.getInteger(ERROR_CODE_PARAMS) == 0) {
                JSONObject resultInfo = resObject.getJSONObject("result");
                String name = resultInfo.getString("name");
                String userid = resultInfo.getString("userid");
                String unionid = resultInfo.getString("unionid");
                talkProviderUser.setProviderId(providerInfo.getProviderId());
                talkProviderUser.setProviderUserid(userid);
                talkProviderUser.setUserInfoJson(resultInfo.toJSONString());
                talkProviderUser.setUserDisplayName(name);
                talkProviderUser.setUnionId(unionid);
                return talkProviderUser;
            }
        }
        log.error("查询钉钉用户信息失败!");
        throw new AuthBusinessException("500", LocaleUtil.getMessage("DingTalkUserProviderImpl.result.msg1", null) + response);
    }

    @Override
    public ProviderUserInfo getProviderUserInfo(String code,ProviderAppAccessToken appAccessToken,ProviderInfo providerInfo) {
        DingTalkProviderUser talkProviderUser = new DingTalkProviderUser();
        String url = newUrl + "/v1.0/oauth2/userAccessToken";
        JSONObject map = new JSONObject();
        map.put("clientId", providerInfo.getAppKey());
        map.put("clientSecret", providerInfo.getAppSecret());
        map.put("code", code);
        map.put("grantType", "authorization_code");
        try {
            String response = OkHttpUtils.postJson(url, JSON.toJSONString(map));
            if (response != null) {
                JSONObject resObject = JSONObject.parseObject(response);
                String accessToken = resObject.getString("accessToken");
                if (accessToken != null) {
                    String userUrl = newUrl + "/v1.0/contact/users/me";
                    String userResponse = OkHttpUtils.getByThirdToken(userUrl, new HashMap<>(), "x-acs-dingtalk-access-token", accessToken);
                    if (userResponse != null) {
                        JSONObject userRsposne = JSONObject.parseObject(userResponse);
                        String mobile = userRsposne.getString("mobile");
                        String unionid = userRsposne.getString("unionId");
                        String userId = getUserIdByUnionId(unionid, appAccessToken.getAccessToken());
                        String nick = userRsposne.getString("nick");
                        talkProviderUser.setProviderId(providerInfo.getProviderId());
                        talkProviderUser.setProviderUserid(userId);
                        talkProviderUser.setUserInfoJson(userRsposne.toJSONString());
                        talkProviderUser.setUserDisplayName(nick);
                        talkProviderUser.setUnionId(unionid);
                        talkProviderUser.setPhone(mobile);
                    }
                } else {
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("DingTalkUserProviderImpl.result.msg1", null) + response);
                }
                return talkProviderUser;
            }

        } catch (Exception e) {
            throw new AuthBusinessException("500", LocaleUtil.getMessage("DingTalkUserProviderImpl.result.msg2", null) + e);
        }
        return null;
    }

    /**
     * 根据unionId和token获取用户ID
     *
     * @param unionid 用户的unionId
     * @param token  注意：这里的accessToken 不是第三方用户的token，是微应用的token
     * @return
     * @throws IOException
     */
    private String getUserIdByUnionId(String unionid, String token) throws IOException {
        String url = baseUrl + "/topapi/user/getbyunionid" + "?access_token=" + token;
        JSONObject map = new JSONObject();
        map.put("unionid", unionid);
        String response = OkHttpUtils.postJson(url, JSON.toJSONString(map));
        if (response != null) {
            JSONObject resObject = JSONObject.parseObject(response);
            if (resObject.getInteger(ERROR_CODE_PARAMS) == 0) {
                JSONObject resultInfo = resObject.getJSONObject("result");
                return resultInfo.getString("userid");
            }else{
                throw new AuthBusinessException("500", LocaleUtil.getMessage("DingTalkUserProviderImpl.result.msg1", null) + resObject);
            }
        }
        return null;
    }
}

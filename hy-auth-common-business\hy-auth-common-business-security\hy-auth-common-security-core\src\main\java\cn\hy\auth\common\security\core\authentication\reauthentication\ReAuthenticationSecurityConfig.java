package cn.hy.auth.common.security.core.authentication.reauthentication;

import cn.hy.auth.common.security.core.authentication.common.AbstractHySecurityConfigurerAdapter;
import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationDetailsSource;
import cn.hy.auth.common.security.core.properties.ReAuthenticationProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 二次认证安全配置
 *
 * <AUTHOR>
 * @date 2024/6/7 10:53
 */
@RequiredArgsConstructor
@Component
public class ReAuthenticationSecurityConfig extends AbstractHySecurityConfigurerAdapter {

    private final UserDetailsService myUserDetailsService;

    private final ReAuthenticationProperties reAuthenticationProperties;

    private final List<ReAuthenticationPreProvider<?>> reAuthenticationPreProviders;

    private final AuthenticationSuccessHandler myAuthenticationSuccessHandler;

    private final AuthenticationFailureHandler myAuthenticationFailureHandler;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        ReAuthenticationFilter reAuthenticationFilter =
                new ReAuthenticationFilter(reAuthenticationProperties, reAuthenticationPreProviders);
        reAuthenticationFilter.setAuthenticationDetailsSource(new HyAuthenticationDetailsSource("client_secret"));
        // 设置共享现有的AuthenticationManager(带有自定义的authenticationProvider)
        reAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        // 设置成功失败处理器
        reAuthenticationFilter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        reAuthenticationFilter.setAuthenticationFailureHandler(myAuthenticationFailureHandler);

        ReAuthenticationProvider reAuthenticationProvider = new ReAuthenticationProvider(myUserDetailsService);

        http
                .authenticationProvider(reAuthenticationProvider)
                .addFilterAfter(reAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
}

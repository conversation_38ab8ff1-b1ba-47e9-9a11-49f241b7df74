{"groups": [{"name": "hy.security.oauth2", "type": "cn.hy.auth.common.security.oauth2.properties.ClientLoadProperties", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ClientLoadProperties"}, {"name": "hy.security.oauth2.auth.url", "type": "cn.hy.auth.common.security.oauth2.properties.Oauth2AuthUrlMappingProperties", "sourceType": "cn.hy.auth.common.security.oauth2.properties.Oauth2AuthUrlMappingProperties"}, {"name": "hy.security.oauth2.resource.server", "type": "cn.hy.auth.common.security.oauth2.properties.ResourceServerProperties", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ResourceServerProperties"}, {"name": "hy.security.oauth2.token", "type": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties", "sourceType": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties"}], "properties": [{"name": "hy.security.oauth2.auth.url.confrim-access", "type": "java.lang.String", "description": "自定义确认授权页面", "sourceType": "cn.hy.auth.common.security.oauth2.properties.Oauth2AuthUrlMappingProperties", "defaultValue": "/oauth/confirm_access"}, {"name": "hy.security.oauth2.auth.url.error", "type": "java.lang.String", "description": "自定义错误页", "sourceType": "cn.hy.auth.common.security.oauth2.properties.Oauth2AuthUrlMappingProperties", "defaultValue": "/oauth/error"}, {"name": "hy.security.oauth2.client-in-memory", "type": "java.lang.Bo<PERSON>an", "description": "客户端内存模式", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ClientLoadProperties", "defaultValue": false}, {"name": "hy.security.oauth2.clients", "type": "cn.hy.auth.common.security.oauth2.properties.ClientProperties[]", "description": "客户端clientId和clientSecret配置", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ClientLoadProperties", "defaultValue": []}, {"name": "hy.security.oauth2.resource.server.resource-id", "type": "java.lang.String", "description": "资源服务名称", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ResourceServerProperties", "defaultValue": "hy_auth_center"}, {"name": "hy.security.oauth2.resource.server.stateless", "type": "java.lang.Bo<PERSON>an", "description": "如果 stateless 为 true 打开状态， 则 每次请求都必须携带 accessToken 请求才行，否则将无法访问", "sourceType": "cn.hy.auth.common.security.oauth2.properties.ResourceServerProperties", "defaultValue": true}, {"name": "hy.security.oauth2.token.access-token-validity-seconds", "type": "java.lang.Integer", "description": "access_token有效期 默认12小时.如果client有设置，则优先用client里的设置", "sourceType": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties", "defaultValue": 0}, {"name": "hy.security.oauth2.token.allow-refresh", "type": "java.lang.Bo<PERSON>an", "description": "是否允许client模式下刷新token，默认为true", "sourceType": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties", "defaultValue": true}, {"name": "hy.security.oauth2.token.refresh-token-validity-seconds", "type": "java.lang.Integer", "description": "refresh_token有效期 默认30 天。如果client有设置，则优先用client里的设置", "sourceType": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties", "defaultValue": 0}, {"name": "hy.security.oauth2.token.reuse-refresh-token", "type": "java.lang.Bo<PERSON>an", "description": "是否重用reuseRefreshToken，默认为true", "sourceType": "cn.hy.auth.common.security.oauth2.properties.TokenServicesProperties", "defaultValue": true}], "hints": []}
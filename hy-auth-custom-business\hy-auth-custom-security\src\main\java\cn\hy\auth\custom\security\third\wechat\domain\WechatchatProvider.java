package cn.hy.auth.custom.security.third.wechat.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import com.alibaba.fastjson.JSONObject;
import lombok.Setter;

import java.util.Map;

/**
 * 企业微信用户信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
public class WechatchatProvider implements ProviderInfo {

    private String providerId;
    private String appKey;
    private String appSecret;
    private String originConfig;

    @Override
    public String getProviderId() {
        return providerId;
    }

    @Override
    public String getAppKey() {
        return appKey;
    }

    @Override
    public String getAppSecret() {
        return appSecret;
    }

    @Override
    public String getAppAccessTokenUrl() {
        return "/cgi-bin/gettoken";
    }

    @Override
    public String getAppUserInfoUrl() {
        return  "/cgi-bin/auth/getuserinfo";
    }

    @Override
    public Map<String, Object> originConfigMap() {
        if(originConfig!=null){
             return (Map<String, Object>)JSONObject.parse(originConfig);
        }
        return null;
    }
}

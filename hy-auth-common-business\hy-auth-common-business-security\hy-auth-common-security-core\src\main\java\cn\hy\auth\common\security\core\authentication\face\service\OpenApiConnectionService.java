package cn.hy.auth.common.security.core.authentication.face.service;


import cn.hy.auth.common.security.core.authentication.face.model.HyFaceRecognitionDTO;

import java.util.Map;

/**
 * 类描述：userconnection 表业务管理器
 *
 * <AUTHOR> by <PERSON><PERSON><PERSON><PERSON>
 * @date 2022/9/9 17:03
 **/
public interface OpenApiConnectionService {

    /**
     * 获取人脸识别结果
     * @param recognitionParams
     * @return
     */
    Object getUserByFace(HyFaceRecognitionDTO recognitionParams);

    /**
     * 查询用户账号
     * @param hrCode
     * @return
     */
    Map<String,Object> getUserAccount(String hrCode);
}

package cn.hy.auth.common.security.core.authentication.mobile.service;

import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;

/**
 * <AUTHOR>
 * @title: EmailCodeSender
 * @description: 邮箱
 * @date 2024/10/17
 */
public interface EmailCodeSender {

    /**
     * 发送邮件
     *
     * @param email       手机号
     * @param captcha     验证码
     * @param lesseeCode  租户编码
     * @param smsLoginVO  登录参数
     * @param appParamKey 参数编码
     * @return 发送结果
     */
    String send(String email, String captcha, String appCode, String lesseeCode, SmsLoginVO smsLoginVO, String appParamKey);

}
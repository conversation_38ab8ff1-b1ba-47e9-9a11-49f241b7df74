package cn.hy.auth.custom.security.oauth2.listener;

import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.common.security.oauth2.event.TokenRefreshedSuccessEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;

/**
 * 类描述: 刷新token事件
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/26
 */
@AllArgsConstructor
@Component
@Slf4j
public class TokenRefreshedSuccessEventListener implements ApplicationListener<TokenRefreshedSuccessEvent> {

    private final TokenStore tokenStore;

    private final UserCacheTokenService userCacheTokenService;

    @Override
    public void onApplicationEvent(TokenRefreshedSuccessEvent event) {
        OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(event.getoAuth2AccessToken().getValue());
        log.debug("接收到成功刷新token事件。old refresh_token_value = [{}],new refresh_token_value protected, username=[{}]，tokenId=[{}],expiration=[{}]",
                event.getRefreshTokenValue(),
                oAuth2Authentication.getName(),
                event.getoAuth2AccessToken().getValue(),
                event.getoAuth2AccessToken().getExpiration());

        userCacheTokenService.cacheUser(event.getoAuth2AccessToken().getValue(), oAuth2Authentication);

    }
}

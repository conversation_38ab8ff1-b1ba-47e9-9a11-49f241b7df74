package cn.hy.auth.custom.user.cache.service.impl;

import cn.hy.auth.custom.user.account.service.KickOutEnum;
import cn.hy.auth.custom.user.cache.service.KickOutTokenStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;


/**
 * KickOutTokenStoreServiceImpl：
 *
 * <AUTHOR>
 * @version 2024/10/11 17:06
 **/
@Service
@Slf4j
public class KickOutTokenStoreServiceImpl implements KickOutTokenStoreService {

    private final CacheManager cacheManager;
    private final String CACHENAME = "kickOutToken::";

    public KickOutTokenStoreServiceImpl(@Qualifier("hyKickOutTokenCacheManager") CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    @Override
    public void putKickOutToken(String token, KickOutEnum kickOutEnum) {
        Cache cache = cacheManager.getCache(CACHENAME);
        if (cache == null) {
            log.info("缓存为null，无法剔出token:{}", token);
            return;
        }
        cache.put(token, kickOutEnum.getCode());
    }

    @Override
    public boolean isKickOutToken(String token) {
        return getKickOutTokenMsg(token) != null;
    }

    @Override
    public KickOutEnum getKickOutTokenMsg(String token) {
        if (cacheManager == null) {
            log.error("缓存管理器为null，无法获取缓存。");
            return null;
        }

        Cache cache = cacheManager.getCache(CACHENAME);
        if (cache == null) {
            log.info("缓存为null，判断是否为剔出的token。token:{}", token);
            return null;
        }

        Cache.ValueWrapper valueWrapper = cache.get(token);
        if (valueWrapper == null) {
            return null;
        }

        Object value = valueWrapper.get();
        if (value == null) {
            return null;
        }

        if (!(value instanceof String)) {
            log.warn("缓存中的值类型不正确，期望为String，实际为{}", value.getClass().getName());
            return null;
        }

        try {
            return KickOutEnum.getByCode((String) value);
        } catch (Exception e) {
            log.error("处理缓存值时发生异常", e);
            return null;
        }
    }

}

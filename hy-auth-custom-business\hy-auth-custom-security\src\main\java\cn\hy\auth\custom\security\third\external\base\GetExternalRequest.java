package cn.hy.auth.custom.security.third.external.base;

import cn.hy.auth.custom.common.utils.AuthSpringUtil;
import cn.hy.auth.custom.security.third.external.fky.FkyIdsRequestUrl;
import lombok.NonNull;

/**
 * 表规Bean工厂
 *
 * <AUTHOR>
 * @date 2023/5/17
 */
public class GetExternalRequest {

    public static ExternalRequestUrl getRequestBean(@NonNull String providerId) {

        switch(providerId){
            case "fky":
                return AuthSpringUtil.getBean(FkyIdsRequestUrl.class);
            default:
                return null;
        }
    }
}

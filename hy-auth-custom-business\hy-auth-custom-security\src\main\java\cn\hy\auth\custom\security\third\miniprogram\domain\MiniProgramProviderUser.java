package cn.hy.auth.custom.security.third.miniprogram.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import lombok.Setter;

/**
 *
 * 微信小程序用户信息
 *
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Setter
public class MiniProgramProviderUser implements ProviderUserInfo {

    private String providerId;
    private String providerUserid;
    private String userDisplayName;
    private String imageUrl;
    private String email;
    private String phone;
    private String userInfoJson;
    private String unionId;
    private String openId;

    @Override
    public String getProviderId() {
        return providerId;
    }

    @Override
    public String getProviderUserid() {
        return providerUserid;
    }

    @Override
    public String getUnionId() {
        return unionId;
    }

    @Override
    public String getUserDisplayName() {
        return userDisplayName;
    }

    @Override
    public String getImageUrl() {
        return imageUrl;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public String getPhone() {
        return phone;
    }

    @Override
    public String getUserInfoJson() {
        return userInfoJson;
    }

    public String getOpenId(){return openId;}
}

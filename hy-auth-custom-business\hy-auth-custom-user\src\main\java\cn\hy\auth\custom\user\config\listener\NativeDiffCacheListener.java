package cn.hy.auth.custom.user.config.listener;

import cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import cn.hy.auth.custom.user.config.context.NativeDiffCacheContext;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 类描述: 自定义缓存类型处理类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@AllArgsConstructor
@Component
public class NativeDiffCacheListener implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        Map<String, Object> beans = event.getApplicationContext().getBeansWithAnnotation(NativeCacheHandle.class);
        NativeDiffCacheContext nativeDiffCacheContext = event.getApplicationContext().getBean(NativeDiffCacheContext.class);
        beans.forEach((name, bean) -> {
            NativeCacheHandle nativeCacheHandle = bean.getClass().getAnnotation(NativeCacheHandle.class);
            nativeDiffCacheContext.putNativeCacheHandleMap(nativeCacheHandle.value().getType(), (NativeDiffCacheService) bean);
        });
    }
}

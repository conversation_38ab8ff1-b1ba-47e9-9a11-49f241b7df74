package cn.hy.auth.custom.route.service;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述：认证相关请求的
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/23 13:38
 **/
public interface RequestRoutingService {
    /**
     *  响应请求
     * @param systemAreaIdentify 请求的所属领域标识
     * @param request 请求信息
     * @param response 响应信息
     * @param filterChain .
     * @throws  ServletException .
     * @throws  IOException .
     */
    void run(Object systemAreaIdentify,HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException;

}

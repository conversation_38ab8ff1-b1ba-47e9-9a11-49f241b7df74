package cn.hy.auth.common.security.core.authentication.token;

import lombok.Getter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/7 15:13
 */
@Getter
public class UsbKeyAuthenticationToken extends ReAuthenticationToken {

    /**
     * 签名
     */
    private final String signature;

    public UsbKeyAuthenticationToken(Object principal, String signature, Map<String, Object> additional) {
        // 因为刚开始并没有认证，因此用户没有任何权限，并且设置没有认证的信息（setAuthenticated(false)）
        super(null);
        this.principal = principal;
        this.additional = additional;
        this.signature = signature;
        this.setAuthenticated(false);
    }

    @Override
    public ReAuthenticationType getType() {
        return ReAuthenticationType.USB_KEY;
    }

    @Override
    public ReAuthenticationToken createSuccessAuthentication(UserDetails userDetails, Authentication authentication) {
        UsbKeyAuthenticationToken result = new UsbKeyAuthenticationToken(userDetails, signature, additional);
        result.setDetails(authentication.getDetails());
        super.setAuthenticated(true);
        return result;
    }

    @Override
    public Object getCredentials() {
        return null;
    }
}

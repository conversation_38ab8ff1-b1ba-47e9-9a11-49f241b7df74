package cn.hy.auth.custom.security.log;

import cn.hy.auth.custom.common.log.event.LogEvent;
import cn.hy.auth.custom.security.log.service.LogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.sql.SQLSyntaxErrorException;

/**
 * 类描述：系统日志事件监听器
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/2 14:12
 **/
@Component
@Slf4j
@AllArgsConstructor
public class LogEventListener implements ApplicationListener<LogEvent> {
    private final LogService logService;
    @Async
    @Override
    public void onApplicationEvent(LogEvent event) {
        try {
            logService.saveLogDto(event);
        }catch (Exception e){
            if (e.getMessage().contains("Table") && e.getMessage().contains("doesn't exist")){
                // 表不存在不需要处理和提示
            }else {
                log.error("后台线程处理日志出错了。",e);
            }
        }

    }
}

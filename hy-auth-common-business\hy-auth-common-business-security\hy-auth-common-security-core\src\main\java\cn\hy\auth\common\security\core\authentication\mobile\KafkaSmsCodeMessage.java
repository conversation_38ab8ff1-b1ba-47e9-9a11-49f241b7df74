package cn.hy.auth.common.security.core.authentication.mobile;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KafkaSmsCodeMessage implements Serializable {

    /**
     * 消息主键（uuid）
     */
    private String id;

    private String code;

    private String category;

    private String source;

    private String ts;

    private String type;

    private Map<String, Object> payload;

    public static KafkaSmsCodeMessage getDefaultMessage(String id) {
        KafkaSmsCodeMessage msg = KafkaSmsCodeMessage.builder()
                .id(id)
                .code("sysdev_send_sms_code_event")
                .category("BARE")
                .source("auth_sms_message_topic")
                .ts(System.currentTimeMillis()+"")
                .type("PGRM")
                .build();
        return msg;
    }

    public static KafkaSmsCodeMessage getEmailCodeMessage(String id) {
        return KafkaSmsCodeMessage.builder()
                .id(id)
                .code("sysdev_send_email_code_event")
                .category("BARE")
                .source("AUTH_CAPTCHA_MESSAGE")
                .ts(System.currentTimeMillis() + "")
                .type("PGRM")
                .build();
    }

}

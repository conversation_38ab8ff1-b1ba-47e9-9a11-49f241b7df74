package cn.hy.auth.custom.common.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述: 操作结果枚举
 *
 * <AUTHOR>
 * @date ：创建于 2022/6/1
 */
@AllArgsConstructor
@Getter
public enum OperationResultEnum {

    /**
     * 成功
     */
    SUCCESS("成功"),
    /**
     * 失败
     */
    FAILURE("失败"),
    /**
     * 未知
     */
    UNKNOWN("未知");

    /**
     * 类型描述
     */
    private final String msg;

}

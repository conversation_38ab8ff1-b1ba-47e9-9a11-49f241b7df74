package cn.hy.auth.common.business.tool.redis.service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: CAIPENG
 * @create: 2019-09-04 17:07
 **/
public interface RedisService {

    /**
     * set存数据
     *
     * @param key
     * @param value
     * @return
     */
    void set(String key, Object value);

    /**
     * get获取数据
     *
     * @param key
     * @return
     */
    Object get(String key);

    /**
     * 批量获取多个key的数据
     *
     * @param keys
     * @return
     */
    List<Object> multiGet(List<String> keys);

    /**
     * set存数据 支持Map
     *
     * @param key
     * @param map
     */
    void setHash(String key, Map<String, Object> map);

    /**
     * set存数据 支持List
     *
     * @param key
     * @param list
     */
    void setList(String key, List<Object> list);

    /**
     * .
     * @param key .
     * @return .
     */
    List<Object> getList(String key);
    /**
     * 查询KEY是否存在
     *
     * @param key
     * @return
     */
    boolean existsKey(String key);

    /**
     * 重名名key，如果newKey已经存在，则newKey的原值被覆盖
     *
     * @param oldKey
     * @param newKey
     */
    void renameKey(String oldKey, String newKey);

    /**
     * newKey不存在时才重命名
     *
     * @param oldKey
     * @param newKey
     * @return 修改成功返回true
     */
    boolean renameKeyNotExist(String oldKey, String newKey);

    /**
     * 删除key
     *
     * @param key
     */
    void deleteKey(String key);

    /**
     * 删除多个key
     *
     * @param keys
     */
    void deleteKey(String... keys);

    /**
     * 删除Key的集合
     *
     * @param keys
     */
    void deleteKey(Collection<String> keys);

    /**
     * 设置key的生命周期
     *
     * @param key
     * @param time
     * @param timeUnit
     */
    void expireKey(String key, long time, TimeUnit timeUnit);

    /**
     * 指定key在指定的日期过期
     *
     * @param key
     * @param date
     */
    void expireKeyAt(String key, Date date);

    /**
     * 查询key的生命周期
     *
     * @param key
     * @param timeUnit
     * @return
     */
    long getKeyExpire(String key, TimeUnit timeUnit);

    /**
     * 将key设置为永久有效
     *
     * @param key
     */
    void persistKey(String key);

    /**
     * 发布消息
     *
     * @param channel 通道名
     * @param value   消息内容
     */
    void publish(String channel, Object value);

    /**
     * 根据前缀获取Key列表
     *
     * @param prefix 前缀字符，不需要包括通配符
     * @return
     */
    Set<String> getKeys(String prefix);

    /**
     * 根据后缀获取Key列表
     *
     * @param suffix 后缀字符，不需要包括通配符
     * @return
     */
    Set<String> getKeysBySuffix(String suffix);

    /**
     * 根据前缀获取value列表
     *
     * @param prefix 前缀字符，不需要包括通配符
     * @return
     */
    List<Object> getValuesByPrefix(String prefix);

    /**
     * 根据后缀获取value列表
     *
     * @param suffix 后缀字符，不需要包括通配符
     * @return
     */
    List<Object> getValuesBySuffix(String suffix);

    /**
     * 根据前后缀获取value列表
     *
     * @param affix 前后缀字符，不需要包括通配符
     * @return
     */
    List<Object> getValuesByAffix(String affix);
}

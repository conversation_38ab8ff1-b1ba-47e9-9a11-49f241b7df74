package cn.hy.auth.custom.common.domain;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类描述: 登录信息
 *
 * <AUTHOR>
 * @date :创建于 2020/11/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserLoginInfoDTO implements Serializable {

    /**
     * 主键
     *
     */
    private Long id;
    /**
     * 客户端标识
     */
    private String clientId;
    /**
     * 客户端登录端,预留
     */
    private Integer clientType;
    /**
     * 登录方式
     *
     * @see cn.hy.auth.custom.common.enums.LoginTypeEnum
     */
    private String loginType;
    /**
     * 用户主键
     */
    private Long userId;
    /**
     * 账号
     */
    private String userAccountName;
    /**
     * ip地址（上一次登录的）
     */
    private String ip;



    /**
     * mac地址
     */
    private String mac;
    /**
     * 类型:1登录成功2登录失败3退出登录
     *
     * @see cn.hy.auth.custom.common.enums.LoginOutTypeEnum
     */
    private Integer type;
    /**
     * 成功登录次数
     */
    private Integer loginCount;
    /**
     * 登录异常码
     */
    private Integer errorCode;
    /**
     * 登录异常信息
     */
    private String errorMessage;
    /**
     * 乐观锁
     */
    private String dataVersion;

    /**
     * 创建人
     * 必填
     */
    private Long createUserId;

    /**
     * 创建时间
     * 必填
     */
    private Date createTime;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 更新人
     * 必填
     */
    private Long lastUpdateUserId;

    /**
     * 更新时间
     * 必填
     */
    private Date lastUpdateTime;
    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;
    /**
     * 排序号
     */
    private Long sequence;

    /**
     * 最后登陆登录时间
     */
    private Date lastLoginTime;


    /**
     *   fuxinrong
    * 本次登录时间
     */
    private Date currentLoginTime;

    /**
     *   fuxinrong
     *  上次登录时间
     */
    private Date lastRealLoginTime;
    /**
     * fuxinrong
     *  上次登录ip地址
     */
    private String lastLoginIp;
    /**
     * 当前登录的ip地址
     */
    private String currentIp;

    public static UserLoginInfoDTOBuilder builder() {
        return new UserLoginInfoDTOBuilder();
    }
}

package cn.hy.auth.common.security.core.authentication.reauthentication;

import cn.hy.auth.common.security.core.authentication.token.PasswordAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.token.ReAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.token.ReAuthenticationType;
import cn.hy.auth.common.security.core.authentication.token.UsbKeyAuthenticationToken;
import cn.hy.auth.common.security.core.properties.ReAuthenticationProperties;
import lombok.Setter;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 二次认证 Filter，模仿 UsernamePasswordAuthenticationFilter
 *
 * <AUTHOR>
 * @date 2024/6/7 10:56
 */
public class ReAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 是否只处理post请求
     */
    @Setter
    private boolean postOnly = true;
    private static final String REQUEST_METHOD = "POST";

    private final ReAuthenticationProperties reAuthenticationProperties;
    private final List<ReAuthenticationPreProvider<?>> reAuthenticationPreProviders;

    public ReAuthenticationFilter(ReAuthenticationProperties reAuthenticationProperties,
                                  List<ReAuthenticationPreProvider<?>> reAuthenticationPreProviders) {
        super(new AntPathRequestMatcher(reAuthenticationProperties.getAuthProcessingUrl(), REQUEST_METHOD));
        this.reAuthenticationProperties = reAuthenticationProperties;
        this.reAuthenticationPreProviders = reAuthenticationPreProviders;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
        if (this.postOnly && !REQUEST_METHOD.equals(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        // 通过 client_name 判断认证类型, 创建对应的 Authentication 对象
        ReAuthenticationType securityType = ReAuthenticationType
                .match(request.getParameter(reAuthenticationProperties.getClientNameParameter()));
        ReAuthenticationToken authenticationToken;
        if (ReAuthenticationType.PASSWORD.equals(securityType)) {
            authenticationToken = createPasswordAuthenticationToken(request);
        } else if (ReAuthenticationType.USB_KEY.equals(securityType)) {
            authenticationToken = createUsbKeyAuthenticationToken(request);
        } else {
            throw new BadCredentialsException("无效的认证方式");
        }
        authenticationToken.setDetails(this.authenticationDetailsSource.buildDetails(request));

        // 进行认证
        reAuthenticationPreProviders.forEach(provider -> provider.authenticate(authenticationToken));

        return this.getAuthenticationManager().authenticate(authenticationToken);
    }

    private PasswordAuthenticationToken createPasswordAuthenticationToken(HttpServletRequest request) {
        String password = request.getParameter(reAuthenticationProperties.getPasswordParameter());
        return new PasswordAuthenticationToken(obtainUsername(request), password, buildAdditional(request));
    }

    private UsbKeyAuthenticationToken createUsbKeyAuthenticationToken(HttpServletRequest request) {
        String signature = request.getParameter(reAuthenticationProperties.getSignatureParameter());
        return new UsbKeyAuthenticationToken(obtainUsername(request), signature, buildAdditional(request));
    }

    private String obtainUsername(HttpServletRequest request) {
        return request.getParameter(reAuthenticationProperties.getUsernameParameter());
    }

    /**
     * 构建附加信息, 用于辅助认证
     * 在 UserAccountController#additional() 中返回
     */
    private Map<String, Object> buildAdditional(HttpServletRequest request) {
        Map<String, Object> additional = new HashMap<>();
        // 基于浏览器或页面的生命周期
        additional.put("browserId", request.getParameter(reAuthenticationProperties.getBrowserId()));
        additional.put("pageId", request.getParameter(reAuthenticationProperties.getPageId()));
        return additional;
    }
}

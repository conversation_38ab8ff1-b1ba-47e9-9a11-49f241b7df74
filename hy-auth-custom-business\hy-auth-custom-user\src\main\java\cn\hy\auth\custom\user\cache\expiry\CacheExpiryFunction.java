package cn.hy.auth.custom.user.cache.expiry;

import cn.hy.auth.custom.user.account.service.ForgetPasswordService;
import com.github.benmanes.caffeine.cache.Expiry;

import javax.annotation.Nonnull;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

public class CacheExpiryFunction implements Expiry<String, Object> {
    @Override
    public long expireAfterCreate(@Nonnull String key, @Nonnull Object value, long currentTime) {
        if (key != null && key.startsWith(ForgetPasswordService.ERROR_NUMBER_PREFIX)) {
            // 错误次数在晚上12点自动过期
            LocalDateTime currentDateTime = LocalDateTime.now();
            LocalDateTime endOfDay = currentDateTime.withHour(23).withMinute(59).withSecond(58).withNano(0);
            long secondsDiff = ChronoUnit.SECONDS.between(currentDateTime, endOfDay);
            return TimeUnit.SECONDS.toNanos(secondsDiff);
        } else if (key != null && key.startsWith(ForgetPasswordService.PERMISSION_PREFIX)) {
            // 修改密码的临时权限字符串过期时间为10分钟
            return TimeUnit.MINUTES.toNanos(4);
        }
        // 其余的在1分钟后过期
        return TimeUnit.MINUTES.toNanos(1);
    }

    @Override
    public long expireAfterUpdate(@Nonnull String key, @Nonnull Object value, long currentTime, long currentDuration) {
        return currentDuration;
    }

    @Override
    public long expireAfterRead(@Nonnull String key, @Nonnull Object value, long currentTime, long currentDuration) {
        return currentDuration;
    }
}

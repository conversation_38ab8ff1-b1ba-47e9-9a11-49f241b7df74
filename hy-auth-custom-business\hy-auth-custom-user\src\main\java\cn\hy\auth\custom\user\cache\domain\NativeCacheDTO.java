package cn.hy.auth.custom.user.cache.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类描述: 缓存数据
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NativeCacheDTO {

    /**
     * 缓存数据,目前caffeine缓存使用到
     */
    private Object nativeCacheData;

    /**
     * 缓存根名称
     */
    private String rootCacheName;
}

package cn.hy.auth.common.security.oauth2.exception;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.security.core.AuthenticationException;

/**
 * 类描述: 自定义异常
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/27
 */
@JsonSerialize(using = HyOAuth2ExceptionSerializer.class)
public class Oauth2BusinessException extends AuthenticationException {

    private static final long serialVersionUID = -826777647938782515L;

    public Oauth2BusinessException(String msg) {
        super(msg);
    }

}

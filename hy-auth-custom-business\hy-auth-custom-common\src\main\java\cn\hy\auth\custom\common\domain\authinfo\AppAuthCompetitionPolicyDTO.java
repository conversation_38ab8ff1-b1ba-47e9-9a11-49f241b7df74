package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 登录竞争策略
 *
 * <AUTHOR>
 * @date 2020-12-03 14:08
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AppAuthCompetitionPolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = 4447987012038077213L;
    private static final String KICK_OUT = "kickOut";
    private static final String BLOCK_LOGIN = "blockLogin";
    private static final String IGNORE = "ignore";
    private static final String NONE = "none";

    /**
     * 支持的客户端类型数量
     */
    private Integer loginClientTypeNum;

    /**
     * // 同端登录的竞争策略
     */
    private ClientPolicy sameClient;
    /**
     * // 不同端登录的竞争策略
     */
    private ClientPolicy allClient;

    /**
     * 判断同端是否竞争
     */
    public boolean isSameClientCompetition() {
        return isEnable() && sameClient != null && "competition".equals(sameClient.type);
    }

    /**
     * 判断同端是否并存
     */
    public boolean isSameClientCoexist() {
        return isEnable() && sameClient != null && "coexist".equals(sameClient.type);
    }

    /**
     * 判断不同端是否竞争
     */
    public boolean isAllClientCompetition() {
        return isEnable() && allClient != null && "competition".equals(allClient.type);
    }

    /**
     * 判断不同端是否并存
     */
    public boolean isAllClientCoexist() {
        return isEnable() && allClient != null && "coexist".equals(allClient.type);
    }

    /**
     * 判断不同端是否需要踢出当前会话
     * 此方法用于确定在特定情况下，是否需要终止当前会话以满足特定的业务规则或策略
     * 它检查了几个条件，以确保只有在特定情况下才踢出会话
     *
     * @return 如果需要踢出会话，则返回true；否则返回false
     */
    public boolean isAllClientKickOut() {
        // 检查当前会话是否已启用，以及是否存在相同的客户端会话
        // 并且该相同的客户端会话类型不是默认类型，以及其策略配置为踢出
        // 所有这些条件共同决定了当前会话是否需要被踢出
        return isEnable() && allClient != null && !NONE.equals(allClient.type) && KICK_OUT.equals(allClient.policy);
    }

    /**
     * 判断不同端是否禁止登录
     * 本方法用于确定当前是否应该禁止用户登录它检查是否启用了相关功能，并且存在相同的客户端，
     * 且该客户端类型不是默认类型，并且其策略设置为禁止登录只有当所有这些条件都满足时，
     * 才返回true，表示禁止登录
     *
     * @return boolean 如果满足禁止登录的条件，则返回true；否则返回false
     */
    public boolean isAllClientBlockLogin() {
        return isEnable() && allClient != null && !NONE.equals(allClient.type) && BLOCK_LOGIN.equals(allClient.policy);
    }

    /**
     * 内部类：不同端登录策略
     */
    @Data
    @ToString
    public static class ClientPolicy {
        /**
         * 竞争/并存/不处理,不同端竞争/并存/不处理
         */
        private String remark; //
        // competition/coexist/none ，竞争/并存
        private String type;
        /**
         * // kickOut/blockLogin/ignore，后登者挤出先登者/后登者被阻止登录/忽略不处理
         */
        private String policy;

    }
}

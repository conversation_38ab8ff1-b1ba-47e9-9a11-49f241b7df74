package cn.hy.auth.custom.security.third.external.base;

import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAccessToken;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalProviderUser;

/**
 * 外部接口定义
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
public interface IExternalUserInfo {

    /**
     * 获取第三方用户信息
     * @param accessToken
     * @return
     */
    ExternalProviderUser getUserInfo(ExternalAccessToken accessToken);
}

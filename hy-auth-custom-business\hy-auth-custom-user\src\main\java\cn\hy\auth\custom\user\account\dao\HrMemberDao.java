package cn.hy.auth.custom.user.account.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户人员信息数据库访问类
 *
 * <AUTHOR>
 * @date 2020-11-25 11:07
 **/
public interface HrMemberDao {
    /**
     * 查询返回单行数据,sql语句最好增加limit 1,避免查询出错
     *
     * @param userId sql语句
     * @return 结果
     */
    Map<String, Object> queryForMap(@Param("userId") String userId);

    /**
     * 人员账号
     *
     * @param userAccountName
     * @return 结果
     */
    List<Map<String, Object>> getHrCodeByAccount(@Param("userAccountName") String userAccountName);

    /**
     * 人员账号
     *
     * @param hrCode
     * @param dingDingId
     * @param weChatId
     * @return 结果
     */
    void updateByHrCode(@Param("hrCode")String hrCode, @Param("dingDingId") String dingDingId, @Param("weChatId") String weChatId);

    /**
     *
     * 根据人员查询账号
     * @param hrCode 人员编码
     * @return 结果
     */
    Map<String, Object> queryAccountByCode(@Param("hrCode") String hrCode,@Param("accountName") String accountName);

    /**
     * 更新用户ID根据人员表工号
     * @param hrCode
     * @param dingTalkId
     * @param wechatId
     * @return
     */
    Integer updateThirdUserId(@Param("hrCode") String hrCode, @Param("dingTalkId") String dingTalkId, @Param("wechatId") String wechatId);
}

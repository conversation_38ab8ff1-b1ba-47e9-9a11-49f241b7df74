package cn.hy.auth.common.security.core.authentication.third;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.social.connect.Connection;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.view.AbstractView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 社交账号绑定视图
 *
 * <AUTHOR>
 * @date 2019-01-20 10:01
 */
@Component("connect/status")
public class HyConnectionStatusView extends AbstractView {
    @Override
    protected void renderMergedOutputModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, List<Connection<?>>> connections = (Map<String, List<Connection<?>>>) model.get("connectionMap");
        //key :社交供应商id  value：是否绑定
        Map<String, Boolean> bindResult = connections.keySet().stream().collect(Collectors.toMap(providerId -> providerId, providerId -> CollectionUtils.isNotEmpty(connections.get(providerId)), (a, b) -> b, () -> new HashMap<>(4)));
        response.setContentType("application/json;charset=UTF-8");
        //返回json
        response.getWriter().write(JSON.toJSONString(bindResult));
    }
}

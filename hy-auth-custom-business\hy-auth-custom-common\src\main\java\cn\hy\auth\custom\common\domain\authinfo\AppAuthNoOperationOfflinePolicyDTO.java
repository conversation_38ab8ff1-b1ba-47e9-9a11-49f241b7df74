package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 无操作自动下线策略
 *
 * <AUTHOR>
 * @date 2020-12-03 16:42
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthNoOperationOfflinePolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = 3204243296306079695L;
    /**
     * 无操作自动下线时间阈值，单位秒
     */
    private String noOperationOfflineThreshold;

    @Override
    public boolean isEnable() {
        return super.isEnable() && StringUtils.isNotBlank(noOperationOfflineThreshold);
    }
}

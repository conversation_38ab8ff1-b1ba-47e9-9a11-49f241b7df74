package cn.hy.auth.common.security.core.authentication.face;

import cn.hy.auth.common.security.core.properties.HyFaceLoginProperties;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 人脸登录Filter ，模仿UsernamePasswordAuthenticationFilter
 *
 * <AUTHOR>
 * @date 2023/06/05  15:36
 */
public class HyFaceAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 是否只处理post请求
     */
    private boolean postOnly = false;
    private static final String REQUEST_METHOD = "POST";
    private HyFaceLoginProperties faceLoginProperties;
    private HyFacePreAuthenticationProvider hyFacePreAuthenticationProvider;

    public HyFaceAuthenticationFilter(HyFaceLoginProperties faceLoginProperties, HyFacePreAuthenticationProvider hyFacePreAuthenticationProvider) {
        //要拦截的请求
        super(new AntPathRequestMatcher(faceLoginProperties.getAuthProcessingUrl(), REQUEST_METHOD));
        this.faceLoginProperties = faceLoginProperties;
        this.hyFacePreAuthenticationProvider = hyFacePreAuthenticationProvider;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (this.postOnly && !REQUEST_METHOD.equals(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            String facePic = this.obtainPic(request);
            String appCode = this.obtainAppCode(request);
            String lessCode = this.obtainLesseeCode(request);
            String loginType = this.obtainLoginType(request);
            Assert.notNull(facePic, LocaleUtil.getMessage("HyFaceAuthenticationFilter.assert.msg1", null) + facePic);
            Assert.notNull(facePic, LocaleUtil.getMessage("HyFaceAuthenticationFilter.assert.msg2", null) + lessCode);
            Assert.notNull(facePic, LocaleUtil.getMessage("HyFaceAuthenticationFilter.assert.msg3", null) + appCode);
            Assert.notNull(loginType, LocaleUtil.getMessage("HyFaceAuthenticationFilter.assert.msg4", null) + loginType);
            HyFaceAuthenticationToken authRequest = new HyFaceAuthenticationToken(facePic, facePic, lessCode, appCode, loginType);
            this.setDetails(request, authRequest);
            if (hyFacePreAuthenticationProvider != null) {
                hyFacePreAuthenticationProvider.authenticate(authRequest);
            }
            //调用AuthenticationManager
            return this.getAuthenticationManager().authenticate(authRequest);
        }
    }

    private String obtainLoginType(HttpServletRequest request) {
        return request.getParameter(this.faceLoginProperties.getLoginTypeParamter());
    }

    private String obtainPic(HttpServletRequest request) {
        return request.getParameter(this.faceLoginProperties.getPicParameter());
    }


    private String obtainAppCode(HttpServletRequest request) {
        return request.getParameter(this.faceLoginProperties.getAppCodeParamter());
    }

    private String obtainLesseeCode(HttpServletRequest request) {
        return request.getParameter(this.faceLoginProperties.getLesseeCodeParamter());
    }

    private void setDetails(HttpServletRequest request, HyFaceAuthenticationToken faceAuthenticationToken) {
        faceAuthenticationToken.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }


    public void setPostOnly(boolean postOnly) {
        this.postOnly = postOnly;
    }

}

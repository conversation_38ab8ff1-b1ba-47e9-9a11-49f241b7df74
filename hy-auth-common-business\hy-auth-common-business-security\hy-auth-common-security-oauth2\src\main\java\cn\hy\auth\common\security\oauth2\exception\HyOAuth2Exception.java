package cn.hy.auth.common.security.oauth2.exception;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;

/**
 * 继承OAuth2Exception，为了自定义序列化处理，避免有些字符被转成html标记
 *
 * <AUTHOR>
 * @date 2021-01-07 19:44
 **/
@JsonSerialize(using = HyOAuth2ExceptionSerializer.class)
public class HyOAuth2Exception extends OAuth2Exception {
	private static final long serialVersionUID = 8501643939227141963L;

	public HyOAuth2Exception(String msg) {
		super(msg);
	}
}
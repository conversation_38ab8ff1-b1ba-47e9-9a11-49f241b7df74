<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <artifactId>hy-comprehensive-platform</artifactId>
        <groupId>cn.hy</groupId>
        <version>1.0.0</version>
    </parent>
    <groupId>cn.hy.auth</groupId>
    <artifactId>hy-authentication-center</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>hy-authentication-center</name>
    <description>认证中心</description>

    <modules>
        <module>hy-auth-center-boot</module>
        <module>hy-auth-common-business</module>
        <module>hy-auth-common-business-starter</module>
        <module>hy-auth-custom-business</module>
        <module>hy-auth-custom-business-starter</module>
        <module>hy-auth-custom-multi-datasource</module>
        <module>hy-auth-test</module>
        <module>hy-auth-common-business/hy-auth-common-business-security/hy-auth-common-security-common</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <oauth2.version>2.3.2.RELEASE</oauth2.version>

        <hutool.version>5.3.10</hutool.version>
        <mapstruct.version>1.2.0.Final</mapstruct.version>

        <swagger.version>2.9.2</swagger.version>
        <swagger.bootstrapui.version>1.9.0</swagger.bootstrapui.version>
        <io.swagger.version>1.5.21</io.swagger.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--日志打印配置-->
        <dependency>
            <groupId>cn.hy</groupId>
            <artifactId>hy-logging-generic-starter</artifactId>
            <version>1.6.1.3</version>
        </dependency>

        <!--雪花ID生成器-->
        <dependency>
            <groupId>cn.hy</groupId>
            <artifactId>hy-id-generator-starter</artifactId>
            <version>1.2.1</version>
        </dependency>

        <!--Hutool工具类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!--引用对象转换工具map_struct开始-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!--引用对象转换工具map_struct结束-->

        <!-- swagger2 配置开始 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.persistence</groupId>
                    <artifactId>persistence-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--解决进入swagger页面报类型转换错误，排除2.9.2中的引用，手动增加1.5.21版本-->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${io.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${io.swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>${swagger.bootstrapui.version}</version>
        </dependency>

        <!-- swagger2 配置结束 -->


        <!-- 测试 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter-test</artifactId>
            <version>1.3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>5.3.7.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>prepare-unit-tests</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <phase>pre-integration-test</phase>
                        <configuration>
                            <propertyName>itCoverageAgent</propertyName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
            <distributionManagement>
                <repository>
                    <id>hy-maven-repository-releases</id>
                    <name>Nexus Release Repository</name>
                    <url>http://nexus.hydevops.com/repository/maven-hy-release/</url>
                </repository>
                <snapshotRepository>
                    <id>hy-maven-repository-snapshots</id>
                    <name>Nexus Snapshot Repository</name>
                    <url>http://nexus.hydevops.com/repository/maven-hy-snapshot/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>
</project>

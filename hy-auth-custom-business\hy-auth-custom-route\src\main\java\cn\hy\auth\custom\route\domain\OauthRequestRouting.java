package cn.hy.auth.custom.route.domain;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 请求转发到第三方系统的配置
 * </p>
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OauthRequestRouting {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * token领域的标识，如2.x
     */
    private String systemAreaIdentify;

    /**
     * 源url 相对地址
     */
    private String sourceUrl;

    /**
     * 目标url相对地址
     */
    private String targetUrl;

    /**
     * 调用成功后需要执行的groovy脚本，http code为200
     */
    private String successScript;
    /**
     * 路由转发前的预处理脚本
     */
    private String preRouteScript;

    /**
     * 调用失败后需要执行的groovy脚本，http code非200
     */
    private String failureScript;

    private Date gmtModified;

    private Date gmtCreate;

    /**
     * 目标url域名地址(ip+host)
     */
    private String targetAddress;

}

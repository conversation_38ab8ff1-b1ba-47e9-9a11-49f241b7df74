package cn.hy.auth.common.security.core.authentication.face.model;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 人脸认证结果
 *
 * <AUTHOR>
 * @Date 2023-06-05
 */
@Getter
@Setter
public class HyFaceRecognitionDTO{

    private String clientType;

    private String pic;

    private String lesseeCode;

    private String appCode;

    private  String faceWidth;

    private  String faceHeight ;

    private  String faceFormat;

    private LocalDateTime loginTime;
}

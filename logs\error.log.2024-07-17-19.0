2024-07-17 19:58:30.556 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.common.config.configcenter.DynamicConfigurationFactory name consul on cn.hy.auth.custom.dubbo.ConsulDynamicConfigurationFactory and org.apache.dubbo.configcenter.consul.ConsulDynamicConfigurationFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 19:58:30.849 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.865 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.867 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.869 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.872 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.874 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.875 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.878 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.879 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.882 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.884 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.886 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.887 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.888 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:30.891 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] ERROR c.h.m.e.api.cache.DatabaseMetaDataCacheManagerImpl - 缓存名称：temp_query_data，清理失败，原因：cache definition not found: area=default,cacheName=temp_query_data
2024-07-17 19:58:31.570 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********
2024-07-17 19:58:31.570 [,,,,Auth is starting] [main] ERROR org.apache.dubbo.common.extension.ExtensionLoader -  [DUBBO] Duplicate extension org.apache.dubbo.registry.RegistryFactory name consul on cn.hy.auth.custom.dubbo.ConsulRegistryFactory and org.apache.dubbo.registry.consul.ConsulRegistryFactory, dubbo version: 2.7.8, current host: *********

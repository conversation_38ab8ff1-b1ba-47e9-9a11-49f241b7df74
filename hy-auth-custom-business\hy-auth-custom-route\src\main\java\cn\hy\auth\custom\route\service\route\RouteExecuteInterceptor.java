package cn.hy.auth.custom.route.service.route;

import cn.hy.auth.custom.route.domain.OauthRequestRouting;
import org.apache.http.HttpResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 类描述：路由转发拦截器
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/25 11:53
 **/
public interface RouteExecuteInterceptor {
    /**
     *  执行前处理
     * @param oauthRequestRoutingDO .
     * @param request .
     * @param response .
     */
    void preExe(OauthRequestRouting oauthRequestRoutingDO, HttpServletRequest request,
                HttpServletResponse response);
    /**
     *  执行后处理
     * @param oauthRequestRoutingDO .
     * @param request .
     * @param response .
     * @param httpResponse .
     */
    void postExe(OauthRequestRouting oauthRequestRoutingDO, HttpServletRequest request,
                 HttpServletResponse response, HttpResponse httpResponse);
}

package cn.hy.auth.custom.common.appinfo.service.impl;

import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.BaseAppInfoParser;
import cn.hy.auth.custom.common.constant.AuthenticateUriConst;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;

/**
 * 认证相关请求解析器
 *
 * <AUTHOR>
 * @date 2020-12-13 11:39
 **/
@Service
@Slf4j
public class AuthenticateAppInfoParser extends BaseAppInfoParser {

    /**
     * token最少分片数
     */
    private static final int MIN_TOKEN_PART = 3;
    /**
     * 其他认证类型的grantType
     */
    private static final List<String> AUTHENTICATE_GRANT_TYPES = Lists.newArrayList("refresh_token");
    /**
     * 请求头中token参数值的前缀，不区分大小写
     */
    private static final String TOKEN_HEADER_PREFIX = "bearer";

    /**
     * 解析调度，根据不同的url采用不同的解析方法
     */
    private Map<String, Function<HttpServletRequest, AppInfoDTO>> parserDispatcher = new HashMap<>(4);

    @PostConstruct
    private void init() {
        parserDispatcher.put(AuthenticateUriConst.LOGIN_OAUTH2, this::parseOfRefresh);
        parserDispatcher.put(AuthenticateUriConst.AUTH_CHECK_TOKEN, this::parseOfCheck);
        parserDispatcher.put(AuthenticateUriConst.LOGIN_COPY_TOKEN, this::parseOfCheck);
    }

    /**
     * 排序值，越小越靠前
     *
     * @return 排序值
     */
    @Override

    protected int order() {
        return 2;
    }

    /**
     * 支持的uri模板列表
     *
     * @return 支持的uri模板列表
     */
    @Override
    protected @NonNull List<String> uriPatterns() {
        return new ArrayList<>(requestTypeProperties.getAuthenticate());
    }

    /**
     * 是否支持该处理
     *
     * @param request 请求信息
     * @return 当前实例是否支持对该请求处理
     */
    @Override
    protected boolean supports(HttpServletRequest request) {
        boolean isSupportUri = super.supports(request);
        boolean isMatchGrantType = true;
        if (pathMatcher.match(AuthenticateUriConst.LOGIN_OAUTH2, request.getRequestURI())) {
            String grantType = obtainParameter(request, GRANT_TYPE_PARAM);
            isMatchGrantType = AUTHENTICATE_GRANT_TYPES.contains(grantType.toLowerCase());
        }
        if (pathMatcher.match(AuthenticateUriConst.LOGIN_COPY_TOKEN, request.getRequestURI())){
            isMatchGrantType = true;
        }

        return isSupportUri && isMatchGrantType;
    }

    /**
     * 解析得到租户、应用编码信息已经请求分类
     *
     * @param request 请求信息
     * @return 第一个属性：租户、应用编码；第二个属性：请求类型
     */
    @Override
    public ImmutablePair<AppInfoDTO, RequestTypeEnum> parse(HttpServletRequest request) {
        if (supports(request)) {
            Function<HttpServletRequest, AppInfoDTO> func = parserDispatcher.entrySet()
                    .stream()
                    .filter(entry -> pathMatcher.match(entry.getKey(), request.getRequestURI()))
                    .findFirst()
                    .map(Map.Entry::getValue)
                    .orElse(this::parseOfHeaderParam);

            AppInfoDTO info = func.apply(request);
            return Optional.ofNullable(info).map(i -> ImmutablePair.of(i, RequestTypeEnum.AUTHENTICATE)).orElse(null);
        }
        return null;
    }

    /**
     * 解析请求类型信息
     *
     * @param request 请求信息
     * @return 请求分类
     */
    @Override
    public RequestTypeEnum parseType(HttpServletRequest request) {
        if (supports(request)) {
            return RequestTypeEnum.AUTHENTICATE;
        }
        return null;
    }

    /**
     * 从刷新token请求中解析
     *
     * @param request 请求信息
     * @return 租户、应用编码，解析失败则返回null
     */
    private AppInfoDTO parseOfRefresh(@NonNull HttpServletRequest request) {
        String token = obtainParameter(request, "refresh_token");
        if (log.isDebugEnabled()) {
            log.debug("请求uri:【{}】;根据参数[refresh_token]解析token:【{}】", request.getRequestURI(), token);
        }

        return parseOfToken(token);
    }

    /**
     * 从验证token请求中解析
     *
     * @param request 请求信息
     * @return 租户、应用编码，解析失败则返回null
     */
    private AppInfoDTO parseOfCheck(@NonNull HttpServletRequest request) {
        String token = obtainParameter(request, "token");
        if (log.isDebugEnabled()) {
            log.debug("请求uri:【{}】;根据参数[token]解析token:【{}】", request.getRequestURI(), token);
        }

        return parseOfToken(token);
    }

    /**
     * 从登出请求中解析
     *
     * @param request 请求信息
     * @return 租户、应用编码，解析失败则返回null
     */
    private AppInfoDTO parseOfHeaderParam(@NonNull HttpServletRequest request) {
        String token = request.getHeader("Authorization") == null? request.getHeader("Blade-Auth"):
                request.getHeader("Authorization");
        if (log.isDebugEnabled()) {
            log.debug("请求uri:【{}】;根据请求头参数[Authorization]解析token:【{}】", request.getRequestURI(), token);
        }
        if (StringUtils.isBlank(token) || !token.toLowerCase().startsWith(TOKEN_HEADER_PREFIX)) {
            throw new AuthBusinessException(AuthErrorCodeEnum.A0102.code(), LocaleUtil.getMessage("AuthenticateAppInfoParser.result.msg1", null));
        }

        token = token.substring(TOKEN_HEADER_PREFIX.length()).trim();
        return parseOfToken(token);
    }

    /**
     * 根据tokenId内容解析租户、应用编码
     *
     * @param tokenId token编码
     * @return 租户、应用编码
     */
    private AppInfoDTO parseOfToken(String tokenId) {
        if (StringUtils.isBlank(tokenId)) {
            log.error("token为空，解析租户和应用信息失败。");
            return null;
        }
        tokenId = tokenId.trim();
        String[] tokenPart = tokenId.split("\\.");
        if (tokenPart.length < MIN_TOKEN_PART) {
            log.error("token内容不合法，解析租户和应用信息失败。tokenId：【{}】", tokenId);
            return null;
        }

        return AppInfoDTO.builder().lesseeCode(tokenPart[0]).appCode(tokenPart[1]).build();
    }
}

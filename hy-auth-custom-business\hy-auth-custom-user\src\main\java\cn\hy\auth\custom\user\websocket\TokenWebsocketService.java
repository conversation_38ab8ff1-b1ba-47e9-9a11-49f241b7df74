package cn.hy.auth.custom.user.websocket;

import cn.hutool.core.collection.CollUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.AuthSpringUtil;
import cn.hy.auth.custom.user.account.service.OauthLogoutService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/17 9:34
 */
@Slf4j
@RequiredArgsConstructor
@Service
@ServerEndpoint("/websocket/{lesseeCode}/{appCode}/token/{token}")
public class TokenWebsocketService {

    public static final Long HEART_BEAT_PERIOD = 60L;
    public static final String PING_MSG = "ping";
    public static final String PONG_MSG = "pong";

    private static final Map<String, WebSocketClient> CLIENTS = new ConcurrentHashMap<>();

    private static ScheduledExecutorService scheduler;

    @PostConstruct
    public void initInstance() {
        if (scheduler != null) {
            return;
        }
        // 每60秒检查一次客户端是否在线
        scheduler = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().daemon(true).build());
        scheduler.scheduleAtFixedRate(this::checkAndCloseInactiveClients, 0, HEART_BEAT_PERIOD, TimeUnit.SECONDS);
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        TokenStore tokenStore = AuthSpringUtil.getBean(TokenStore.class);
        if (tokenStore == null) {
            try {
                log.error("[websocket] TokenStore 对象不存在");
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "服务器异常，请联系管理员"));
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            return;
        }

        // 检查 Token 是否有效
        OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
        if (accessToken == null || accessToken.isExpired()) {
            log.debug("[websocket] token无效, 关闭连接, token={}", token);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.GOING_AWAY, "Invalid token"));
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            return;
        }

        log.debug("[websocket] 连接成功, token={}", token);
        CLIENTS.put(token, new WebSocketClient(session));
    }

    @OnMessage
    public void onMessage(String message, @PathParam("token") String token) {
        WebSocketClient webSocketClient = CLIENTS.get(token);
        if (webSocketClient == null) {
            return;
        }
        if (PING_MSG.equals(message)) {
            log.debug("[websocket] 收到 ping, token={}", token);
            webSocketClient.keepAlive();
            webSocketClient.sendMessage(PONG_MSG);
        }
    }

    @OnClose
    public void onClose(Session session,
                        @PathParam("lesseeCode") String lesseeCode,
                        @PathParam("appCode") String appCode,
                        @PathParam("token") String token) {
        log.debug("[websocket] 连接关闭, token={}", token);

        try {
            // 注销 Token
            WebSocketClient client = CLIENTS.remove(token);
            if (client != null) {
                AuthContext.getContext().setLesseeCode(lesseeCode);
                AuthContext.getContext().setAppCode(appCode);

                OauthLogoutService oauthLogoutService = AuthSpringUtil.getBean(OauthLogoutService.class);
                if (oauthLogoutService != null) {
                    oauthLogoutService.revokeToken(token);
                }
            }
        } catch (InvalidTokenException ignored) {
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable, @PathParam("token") String token) {
        log.error("[websocket] 连接异常, token={}, msg={}", token, throwable.getMessage(), throwable);
    }

    private void checkAndCloseInactiveClients() {
        Set<String> tokensToRemove = CollUtil.newHashSet();
        CLIENTS.forEach((token, client) -> {
            if (!client.isActive()) {
                tokensToRemove.add(token);
            }
        });

        for (String token : tokensToRemove) {
            WebSocketClient client = CLIENTS.get(token);
            if (client == null) {
                continue;
            }
            try {
                log.debug("[websocket] 客户端离线, 关闭连接, token={}", token);
                client.getSession().close(new CloseReason(CloseReason.CloseCodes.GOING_AWAY, "客户端离线"));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}

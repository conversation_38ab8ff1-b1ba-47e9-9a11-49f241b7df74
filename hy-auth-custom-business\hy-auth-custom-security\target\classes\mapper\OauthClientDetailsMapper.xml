<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.security.oauth2.client.dao.OauthClientDetailsMapper">
    <resultMap id="BaseResultMap" type="cn.hy.auth.custom.security.oauth2.client.domain.OauthClientDetailsDO">
        <id column="id" jdbcType="DECIMAL" property="id"/>
        <result column="create_user_id" jdbcType="DECIMAL" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="data_version" jdbcType="VARCHAR" property="dataVersion"/>
        <result column="last_update_user_id" jdbcType="DECIMAL" property="lastUpdateUserId"/>
        <result column="client_id" jdbcType="VARCHAR" property="clientId"/>
        <result column="refresh_token_validity" jdbcType="INTEGER" property="refreshTokenValidity"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="sequence" jdbcType="BIGINT" property="sequence"/>
        <result column="access_token_validity" jdbcType="INTEGER" property="accessTokenValidity"/>
        <result column="last_update_user_name" jdbcType="VARCHAR" property="lastUpdateUserName"/>
        <result column="autoapprove" jdbcType="LONGVARCHAR" property="autoapprove"/>
        <result column="authorized_grant_types" jdbcType="LONGVARCHAR" property="authorizedGrantTypes"/>
        <result column="authorities" jdbcType="LONGVARCHAR" property="authorities"/>
        <result column="additional_information" jdbcType="LONGVARCHAR" property="additionalInformation"/>
        <result column="scope" jdbcType="LONGVARCHAR" property="scope"/>
        <result column="web_server_redirect_uri" jdbcType="LONGVARCHAR" property="webServerRedirectUri"/>
        <result column="client_secret" jdbcType="LONGVARCHAR" property="clientSecret"/>
        <result column="resource_ids" jdbcType="LONGVARCHAR" property="resourceIds"/>
    </resultMap>


    <select id="selectByClientId" resultMap="BaseResultMap">
        select id, create_user_id, create_time, create_user_name, data_version, last_update_user_id,
        client_id, refresh_token_validity, last_update_time, sequence, access_token_validity,
        last_update_user_name, autoapprove, authorized_grant_types, authorities, additional_information,
        scope, web_server_redirect_uri, client_secret, resource_ids
        from oauth_client_details
        where client_id = #{clientId,jdbcType=VARCHAR}
    </select>

    <select id="selectAppTokenConfig" resultType="java.util.HashMap">
        select id,bus_cfg_key as cfg_key,address as cfg_val from bizgw_plugin_driver_business_cfg where bus_cfg_key in ("token_validate_time","auto_logout_no_opt")
    </select>

    <select id="selectGlobalTokenConfig" resultType="java.util.HashMap">
        select `address`, bus_cfg_key from application_param_config where bus_cfg_key in ("token_validate_time","auto_logout_no_opt")
    </select>

</mapper>
package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录时，IP限制策略
 * 不启用，则不限制ip登录。
 *
 * <AUTHOR>
 * @date 2020-12-03 14:20
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthIpAllowPolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -3101388363320263670L;
    /**
     * ip限制策略对应帐号表的字段编码
     * 多个ip地址用逗号隔开。
     */
    private String ip;
}

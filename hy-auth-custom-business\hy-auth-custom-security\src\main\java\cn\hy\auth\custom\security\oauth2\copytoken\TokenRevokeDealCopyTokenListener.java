package cn.hy.auth.custom.security.oauth2.copytoken;

import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 类描述：token撤销事件监听器
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/26 12:19
 **/
@Component
@Slf4j
public class TokenRevokeDealCopyTokenListener implements ApplicationListener<TokenRevokedEvent> {
    private final CopyTokenService copyTokenService;
    private final ConsumerTokenServices consumerTokenServices;

    public TokenRevokeDealCopyTokenListener(CopyTokenService copyTokenService, ConsumerTokenServices consumerTokenServices) {
        this.copyTokenService = copyTokenService;
        this.consumerTokenServices = consumerTokenServices;
    }

    @Override
    public void onApplicationEvent(TokenRevokedEvent tokenRevokedEvent) {
        try {
            String tokenValue = tokenRevokedEvent.getTokenValue();
            String refreshToken = tokenRevokedEvent.getOAuth2AccessToken().getRefreshToken().getValue();
            String name = tokenRevokedEvent.getOAuth2Authentication().getName();
            String[] split = tokenValue.split("\\.");
            String lesseeCode = split[0];
            String appCode = split[1];
            if (tokenValue.endsWith("-copy")) {
                // copy token登出时，需要copyToken缓存删除自己；
                copyTokenService.removeCopyToken(lesseeCode, appCode, name, tokenValue);
                copyTokenService.removeRefreshCopyAccessToken(refreshToken);
            } else {
                //  token 登出时，需要删除它的附属copyToken缓存；
                List<Object> allCopyTokens = copyTokenService.getAllCopyTokens(lesseeCode, appCode, name);
                if (CollectionUtils.isNotEmpty(allCopyTokens)) {
                    log.info("主token {} 登出时，需要删除它的附属copyToken缓存，copyTokens：{}", tokenValue, JSON.toJSONString(allCopyTokens));
                    for (Object allCopyToken : allCopyTokens) {
                        consumerTokenServices.revokeToken(allCopyToken.toString());
                    }
                }
                copyTokenService.removeAllCopyTokens(lesseeCode, appCode, name);
            }
        } catch (Exception e) {
            log.info("token 登出时，删除copyToken异常，tokenValue:{}", tokenRevokedEvent.getTokenValue(), e);
        }

    }
}

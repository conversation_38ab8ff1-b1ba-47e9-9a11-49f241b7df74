package cn.hy.auth.custom.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述: 登录密码类型
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/20
 */
@AllArgsConstructor
@Getter
public enum EncryptTypeEnum {
    /**
     * 不加密
     */
    NOT_ENCRYPTION(1, "密码明文"),
    /**
     * 帐号名明文+密码明文加密
     */
    LOGIN_NAME_PASSWORD_ENCRYPTION(2, "帐号名明文+密码明文加密"),
    /**
     * 密码明文加密
     */
    PASSWORD_ENCRYPTION(3, "密码明文加密");

    /**
     * 密码类型
     */
    private Integer code;
    /**
     * 密码描述
     */
    private String describe;

    public static EncryptTypeEnum codeOf(Integer code) {
        EncryptTypeEnum[] enums = EncryptTypeEnum.values();
        for (EncryptTypeEnum em : enums) {
            if (code.equals(em.getCode())) {
                return em;
            }
        }
        return null;
    }

}

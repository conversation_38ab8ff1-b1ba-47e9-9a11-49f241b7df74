package cn.hy.auth.custom.security.third.dingtalk.impl;

import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class HyDingTalkConnectionFactory extends HyConnectionFactory {

    protected HyDingTalkConnectionFactory(HyDingTalkServiceProvider serviceProvider) {
        super(ProviderType.DING_TALK.getCode(), serviceProvider);
    }
}

package cn.hy.auth.custom.multi.systable;

import cn.hy.auth.custom.multi.event.AppAuthInfoLoadEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

/**
 * 登录失败计数信息--系统表
 *
 * <AUTHOR>
 * @date 2020-12-07 11:03
 **/
@Slf4j
public class LogonFailureCountTableServiceImpl  {
    @EventListener
    public void onAuthInfoLoaded(AppAuthInfoLoadEvent event) {
        //remeber me  3.2增加用户锁定功能时再补充功能
        log.info("初始化登录失败计数信息表");
    }
}

package cn.hy.auth.common.security.oauth2.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 类描述：oauth2 授权url地址的映射
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/13 11:52
 **/
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "hy.security.oauth2.auth.url")
public class Oauth2AuthUrlMappingProperties {
    /**
     * 自定义确认授权页面
     */
    private String confrimAccess = "/oauth/confirm_access";
    /**
     * 自定义错误页
     */
    private String error = "/oauth/error";
}

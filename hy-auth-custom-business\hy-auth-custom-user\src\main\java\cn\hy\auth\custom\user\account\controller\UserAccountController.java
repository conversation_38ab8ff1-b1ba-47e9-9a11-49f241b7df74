package cn.hy.auth.custom.user.account.controller;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.event.CheckUserAccountLockEvent;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.token.ReAuthenticationToken;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.common.security.oauth2.token.TokenStoreExtends;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.LoginUserHistoryDTO;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.enums.UserOnLineStatus;
import cn.hy.auth.custom.common.enums.UserResetPasswordType;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.log.annotation.ConfigLog;
import cn.hy.auth.custom.common.log.enums.ModuleNameEnum;
import cn.hy.auth.custom.common.log.enums.OperationTypeEnum;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.user.account.domain.*;
import cn.hy.auth.custom.user.account.service.*;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述: 退出登录
 *
 * <AUTHOR>
 * @date ：创建于 2020/10/29
 */
@RestController
public class UserAccountController implements InitializingBean {

    private final UserAccountService userAccountService;

    private final UserCacheService userCacheService;


    private final List<ForgetPasswordService> updatePasswordCheckServices;

    private Map<String, ForgetPasswordService> checkServiceMap;

    private final OauthLogoutService oauthLogoutService;

    private final TokenStoreExtends tokenStoreExtends;

    private final ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() {
        checkServiceMap = updatePasswordCheckServices
                            .stream()
                            .collect(Collectors.toMap(ForgetPasswordService::getCheckType, Function.identity()));
    }


    public UserAccountController(UserAccountService userAccountService,
                                 UserCacheService userCacheService,
                                 List<ForgetPasswordService> updatePasswordCheckServices,
                                 OauthLogoutService oauthLogoutService,
                                 TokenStoreExtends tokenStoreExtends,
                                 ApplicationContext applicationContext) {
        this.userAccountService = userAccountService;
        this.userCacheService = userCacheService;
        this.updatePasswordCheckServices = updatePasswordCheckServices;
        this.oauthLogoutService = oauthLogoutService;
        this.tokenStoreExtends = tokenStoreExtends;
        this.applicationContext = applicationContext;
    }

    /**
     * 获取当前的账号及关联信息
     *
     * @return 返回当前账号信息
     */
    @GetMapping("/user/current")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "获取当前的账号及关联信息", operationType = OperationTypeEnum.LIST,isSave = false)
    public UserAccountDTO getCurrentUserAccount() {
        String userName = LoginUtil.getLoginName();
        AuthAssertUtils.isNotNull(userName, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());
        return userCacheService.getUserAccountByLoginName(userName);
    }

    /**
     * 获取当前的账号信息
     *
     * @return 返回当前账号信息
     */
    @GetMapping("/user/account/current")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "获取当前的账号信息", operationType = OperationTypeEnum.LIST,isSave = false)
    public Map<String, Object> getCurrentAccount() {
        String userName = LoginUtil.getLoginName();
        AuthAssertUtils.isNotNull(userName, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());

        UserAccountDTO user = userCacheService.getUserAccountByLoginName(userName);
        return ObjectUtil.isNotNull(user) ? user.getAccount() : Collections.emptyMap();
    }

    /**
     * 获取当前的账号关联信息
     *
     * @return 返回当前账号信息
     */
    @GetMapping("/user/association/current")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "获取当前的账号关联信息", operationType = OperationTypeEnum.LIST,isSave = false)
    public Map<String, Object> getCurrentAssociationUser() {
        String userName = LoginUtil.getLoginName();
        AuthAssertUtils.isNotNull(userName, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());

        UserAccountDTO user = userCacheService.getUserAccountByLoginName(userName);
        return ObjectUtil.isNotNull(user) ? user.getUser() : Collections.emptyMap();
    }

    /**
     * 获取登录用户信息
     *
     * @return 返回在线用户的主键集合
     */
    @GetMapping("/free/account/online/list")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "获取在线用户的主键集合", operationType = OperationTypeEnum.LIST,isSave = false)
    public Set<Long> getOnlineUserIds() {
        return userCacheService.getOnlineUserId();
    }

    /**
     * 密码修改
     *
     * @param userPwdUpdateDTO 修改密码对象
     * @return 返回成功标识
     */
    @PutMapping("/user/pwd")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "修改用户登录密码", operationType = OperationTypeEnum.CHANGE_PWD)
    public Map<String, String> updateUserPassword(@Valid @RequestBody UserPwdUpdateDTO userPwdUpdateDTO) {
        String tokenId = TokenUtil.getRequestTokenID();
        AuthAssertUtils.isNotNull(tokenId, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());
        String loginName = LoginUtil.getLoginName();
        AuthAssertUtils.isNotNull(loginName, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());
        AuthAssertUtils.isTrue(loginName.equalsIgnoreCase(userPwdUpdateDTO.getUserName()), AuthErrorCodeEnum.A0124.code(), AuthErrorCodeEnum.A0124.msg());
        userPwdUpdateDTO.setUserName(loginName);
        userAccountService.updateUserPassword(tokenId, userPwdUpdateDTO);
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("result", AuthErrorCodeEnum.SUCCESS.msg());
        return resultMap;

    }

    @PutMapping("/user/clearExpireToken")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "更改登录过期用户状态", operationType = OperationTypeEnum.CHANGE_PWD)
    public Map<String, String> clearExpireToken() {
        Set<String> userNames = tokenStoreExtends.getNoExpiredTokenUserNames();
        userAccountService.changeLoginStatusNotInSet(userNames, UserOnLineStatus.OFFLINE);
        return new HashMap<>();
    }

    @PostMapping("/user/forget/sendSmsCode")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "忘记密码功能-发送短信", operationType = OperationTypeEnum.OTHER)
    public Map<String, Object> checkUserForgetPassword(@RequestParam("phoneNumber") String phoneNumber,
                                                       @RequestParam("type") String type,
                                                       @RequestParam(value = "mobileCipherText",required = false) String mobileCipherText,
                                                       @RequestBody SmsLoginVO smsLoginVO) {
        boolean phoneIsValid = Validator.isMobile(phoneNumber);
        boolean typeValid = UserResetPasswordType.isIncludeType(type);
        AuthAssertUtils.isTrue(typeValid, AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg3", ""));
        AuthAssertUtils.isTrue(phoneIsValid, AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg4", ""));

        CheckUserAccountLockEvent checkUserAccountLockEvent = new CheckUserAccountLockEvent(StringUtils.isNotBlank(mobileCipherText) ? mobileCipherText : phoneNumber);
        applicationContext.publishEvent(checkUserAccountLockEvent);
        String resultMessage = "";
        Map<String, Object> resultMap = new HashMap<>();
        if (checkUserAccountLockEvent.isLock()) {
            resultMessage = LocaleUtil.getMessage("UserAccountController.result.msg1", "");
        } else if (!checkUserAccountLockEvent.getUserExist()) {
            resultMessage = LocaleUtil.getMessage("UserAccountController.result.msg2", "");
        }
        if (StringUtils.isNotBlank(resultMessage)) {
            resultMap.put("result", false);
            resultMap.put("message", resultMessage);
            return resultMap;
        }
        ForgetPasswordService checkService = checkServiceMap.get(type);
        resultMap = checkService.sendToThirdService(type, phoneNumber, smsLoginVO);
        return resultMap;
    }

    @PostMapping("/user/forget/sendEmail")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "忘记密码功能-发送邮件", operationType = OperationTypeEnum.OTHER)
    public Map<String, Object> checkUserForgetPasswordEmail(@RequestParam("email") String email,
                                                            @RequestParam("type") String type,
                                                            @RequestParam(value = "emailCipherText",required = false) String emailCipherText,
                                                            @RequestBody SmsLoginVO smsLoginVO) {
        boolean emailIsValid = Validator.isEmail(email);
        boolean typeValid = UserResetPasswordType.isIncludeType(type);
        AuthAssertUtils.isTrue(typeValid, AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg3", ""));
        AuthAssertUtils.isTrue(emailIsValid, AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg13", ""));

        CheckUserAccountLockEvent checkUserAccountLockEvent = new CheckUserAccountLockEvent("", StringUtils.isNotBlank(emailCipherText) ? emailCipherText : email);
        applicationContext.publishEvent(checkUserAccountLockEvent);
        String resultMessage = "";
        Map<String, Object> resultMap = new HashMap<>();
        if (checkUserAccountLockEvent.isLock()) {
            resultMessage = LocaleUtil.getMessage("UserAccountController.result.msg1", "");
        } else if (!checkUserAccountLockEvent.getUserExist()) {
            resultMessage = LocaleUtil.getMessage("UserAccountController.result.msg14", "");
        }
        if (StringUtils.isNotBlank(resultMessage)) {
            resultMap.put("result", false);
            resultMap.put("message", resultMessage);
            return resultMap;
        }
        ForgetPasswordService checkService = checkServiceMap.get(type);
        resultMap = checkService.sendToThirdService(type, email, smsLoginVO);
        return resultMap;
    }

    @Autowired
    UserAccountLockService userAccountLockService;

    @PostMapping("/user/forget/check")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "忘记密码功能-校验合法", operationType = OperationTypeEnum.OTHER)
    public Map<String, Object> checkUserForgetPassword(@RequestBody CheckUpdatePasswordDTO checkUpdatePasswordDTO) {
        String checkType = checkUpdatePasswordDTO.getCheckType();
        boolean typeValid = UserResetPasswordType.isIncludeType(checkType);
        AuthAssertUtils.isTrue(typeValid, AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg5", ""));
        ForgetPasswordService checkService = checkServiceMap.get(checkUpdatePasswordDTO.getCheckType());
        Map<String, Object> checkResultMap = new HashMap<>();
        String userExistMessage = "";
        UserResetPasswordType typeEnum = UserResetPasswordType.getByType(checkType);
        if (typeEnum == null) {
            throw new AuthBusinessException(AuthErrorCodeEnum.A0400.code(), LocaleUtil.getMessage("UserAccountController.result.msg5", ""));
        }
        switch (typeEnum) {
            case PHONE_SMS_CODE:
            case SECURITY_QUESTION:
                String value = UserResetPasswordType.SECURITY_QUESTION.getType().equals(checkType) ? checkUpdatePasswordDTO.getUserName() : checkUpdatePasswordDTO.getPhoneNumber();
                checkResultMap = userAccountLockService.isUserAccountLockByUserNameOrMobile(value);
                userExistMessage = UserResetPasswordType.SECURITY_QUESTION.getType().equals(checkType) ? LocaleUtil.getMessage("UserAccountController.result.msg7", "") : LocaleUtil.getMessage("UserAccountController.result.msg8", "");
                break;
            case EMAIL_CODE:
                checkResultMap = userAccountLockService.isUserAccountLockByEmail(checkUpdatePasswordDTO.getEmail());
                userExistMessage = LocaleUtil.getMessage("UserAccountController.result.msg14", "");
                break;
        }
        String resultMessage = "";
        Map<String, Object> resultMap = new HashMap<>();
        if ((Boolean)checkResultMap.get("judge")) {
            resultMessage = LocaleUtil.getMessage("UserAccountController.result.msg6", "");
        } else if (!(Boolean)checkResultMap.get("userExist")) {
            resultMessage = userExistMessage;
        }
        if (StringUtils.isNotBlank(resultMessage)) {
            resultMap.put("message", resultMessage);
            resultMap.put("result", false);
            return resultMap;
        }
        return checkService.doCheck(checkUpdatePasswordDTO);
    }

    @PutMapping("/user/forget/pwd")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "忘记密码功能-修改密码", operationType = OperationTypeEnum.CHANGE_PWD)
    public Map<String, Object> updateUserForgetPassword(@Valid @RequestBody UserForgetPwdUpdateDTO userForgetPwdUpdateDTO) {
        boolean allBlank = StringUtils.isBlank(userForgetPwdUpdateDTO.getPhoneNumber())
                && StringUtils.isBlank(userForgetPwdUpdateDTO.getUserName())
                && StringUtils.isBlank(userForgetPwdUpdateDTO.getEmail());
        AuthAssertUtils.isFalse(allBlank, AuthErrorCodeEnum.A0311.code(), LocaleUtil.getMessage("UserAccountController.result.msg9", ""));
        String insurePwd = userForgetPwdUpdateDTO.getInsurePwd();
        String newPwd = userForgetPwdUpdateDTO.getNewPwd();
        Boolean result = false;
        String message = "";
        Map<String, Object> resultMap = new HashMap<>(4);
        if ((insurePwd == null || newPwd == null) || !insurePwd.equals(newPwd)) {
            resultMap.put("result", false);
            resultMap.put("message", LocaleUtil.getMessage("UserAccountController.result.msg10", ""));
            return resultMap;
        }
        boolean judge = false;
        UserLoginAccountDTO userLoginAccountDTO;
        boolean updPwdByEmail = false;
        if (StringUtils.isBlank(userForgetPwdUpdateDTO.getEmail())) {
            userLoginAccountDTO = userAccountService.getUserByUserNameOrPhone(userForgetPwdUpdateDTO.getUserName(), userForgetPwdUpdateDTO.getPhoneNumber());
        } else {
            userLoginAccountDTO = userAccountService.getUserByUserEmail(userForgetPwdUpdateDTO.getEmail());
            updPwdByEmail = true;
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        userLoginAccountDTO.setLesseeCode(paramMap.get("lessee_code"));
        userLoginAccountDTO.setAppCode(paramMap.get("app_code"));
        for (ForgetPasswordService forgetPasswordService : updatePasswordCheckServices) {
            judge |= forgetPasswordService.checkTempPermission(userLoginAccountDTO, userForgetPwdUpdateDTO.getTempPermissionIdentification());
        }
        if (!judge) {
            result = false;
            message = LocaleUtil.getMessage("UserAccountController.result.msg11", "");
        } else {
            if (!updPwdByEmail) {
                userAccountService.updateUserForgetPassword(userForgetPwdUpdateDTO);
            } else {
                userAccountService.updateUserForgetPasswordEmail(userForgetPwdUpdateDTO);
            }
            result = true;
            message = LocaleUtil.getMessage("UserAccountController.result.msg12", "");
        }
        resultMap.put("result", result);
        resultMap.put("message", message);
        return resultMap;
    }

    /**
     * 获取当前的账号、关联信息以及登录信息
     *
     * @return 返回当前账号信息所有信息
     */
    @GetMapping("/user/currentLoginUser")
    //@ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "获取当前的账号、关联信息以及登录信息", operationType = OperationTypeEnum.LIST,isSave = false)
    public LoginUserHistoryDTO getCurrentUserWithLoginInfo() {
        String userName = LoginUtil.getLoginName();
        AuthAssertUtils.isNotNull(userName, AuthErrorCodeEnum.A0311.code(), AuthErrorCodeEnum.A0311.msg());
        UserAccountDTO currentUserAccount = userCacheService.getUserAccountByLoginName(userName);
        UserLoginInfoDTO userLoginInfoDTO = userCacheService.getUserLoginInfoByTokenId(TokenUtil.getTokenID());
        return LoginUserHistoryDTO.builder().userAccountDTO(currentUserAccount).userLoginInfoDTO(userLoginInfoDTO).build();
    }

    /**
     * 踢人
     *
     * @return 通过username踢人
     */
    @PutMapping("/user/kickout")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = " 通过username踢人", operationType = OperationTypeEnum.KICK_OUT)
    public Map<String, String> kickOut(@RequestParam("username") String username) {
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        oauthLogoutService.kickOut(lesseeCode, username, KickOutEnum.OTHER);
        userAccountService.changeLoginInfoByUserNameList(Arrays.asList(username), UserOnLineStatus.OFFLINE);
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("result", AuthErrorCodeEnum.SUCCESS.msg());
        return resultMap;
    }

    @PutMapping("/user/resetpwd/kickout")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = " 通过username踢人", operationType = OperationTypeEnum.KICK_OUT)
    public Map<String, String> kickOutByResetPwd(@RequestParam("username") String username) {
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        oauthLogoutService.kickOut(lesseeCode, username, KickOutEnum.RESET_PWD);
        userAccountService.changeLoginInfoByUserNameList(Arrays.asList(username), UserOnLineStatus.OFFLINE);
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("result", AuthErrorCodeEnum.SUCCESS.msg());
        return resultMap;
    }

    /**
     * 绑定第三方账号
     */
    @PostMapping("/user/bingThirdAccount")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = " 绑定第三方账号", operationType = OperationTypeEnum.OTHER, isSave = false)
    public Map<String, String> bingThirdAccount(@RequestBody ThirdUserBindDTO thirdUserBindDTO) {
        String providerUserId = userAccountService.bingThirdAccount(thirdUserBindDTO);
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("result", providerUserId);
        return resultMap;
    }

    /**
     * 绑定人员和第三方用户
     */
    @PostMapping("/user/birdThirdMember")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = " 绑定第三方账号", operationType = OperationTypeEnum.OTHER, isSave = false)
    public Boolean birdThirdMember(@RequestBody ThirdMemberBindDTO memDto) {
        return userAccountService.birdThirdMember(memDto.getUserAccountName(), memDto.getProviderId(),
                memDto.getProviderUserId(), memDto.getIsBindThird());
    }

    /**
     * 解除绑定第三方账号
     *
     * @return 通过username踢人
     */
    @GetMapping("/user/removeThirdBing")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "解除绑定", operationType = OperationTypeEnum.OTHER, isSave = false)
    public Boolean removeThirdBing(@RequestParam("providerId") String providerId,
                                   @RequestParam(value = "providerUserId", required = false) String providerUserId) {
        UserLoginInfoDTO userLoginInfoDTO = userCacheService.getUserLoginInfoByTokenId(TokenUtil.getTokenID());
        return userAccountService.removeThirdBing(providerId, providerUserId, userLoginInfoDTO.getUserAccountName());
    }

    /**
     * 检查账号是否被绑定
     *
     * @return
     */
    @GetMapping("/{lesseeCode}/{appCode}/user/checkUserAccount")
    @ConfigLog(moduleName = ModuleNameEnum.USER_MANAGE, operationObj = "检查账号", operationType = OperationTypeEnum.OTHER, isSave = false)
    public Boolean checkUserAccount(@PathVariable("lesseeCode") String lesseeCode, @PathVariable("appCode") String appCode,
                                    @RequestParam("providerId") String providerId,
                                    @RequestParam(value = "userAccountName") String userAccountName,
                                    @RequestParam(value = "providerUserId", required = false) String providerUserId) {
        AuthContext.getContext().setLesseeCode(lesseeCode).setAppCode(appCode);
        return userAccountService.checkUserAccount(providerId, userAccountName, providerUserId);
    }

    /**
     * 获取令牌附加信息
     */
    @GetMapping("/user/additional")
    public Map<String, Object> additional() {
        OAuth2Authentication authentication = (OAuth2Authentication) SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> result = new HashMap<>();
        if (authentication.getUserAuthentication() instanceof ReAuthenticationToken) {
            ReAuthenticationToken userAuthentication = (ReAuthenticationToken) authentication.getUserAuthentication();
            HyUserDetails principal = (HyUserDetails) authentication.getPrincipal();
            result.putAll(userAuthentication.getAdditional());
            result.put("username", principal.getUsername());
            result.put("type", userAuthentication.getType().getCode());
        }
        return result;
    }
}

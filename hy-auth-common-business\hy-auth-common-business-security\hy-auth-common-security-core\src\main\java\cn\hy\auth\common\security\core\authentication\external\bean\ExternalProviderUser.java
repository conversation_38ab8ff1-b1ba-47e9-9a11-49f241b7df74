package cn.hy.auth.common.security.core.authentication.external.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
@Getter
public class ExternalProviderUser {

    private String lesseeCode;
    private String appCode;
    private String userId;
    private String userName;
    private String userAccountName;
    private String email;
    private String phone;
    private String userInfoJson;
    private String unionId;


}

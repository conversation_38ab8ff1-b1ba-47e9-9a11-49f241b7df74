package cn.hy.auth.custom.security.oauth2.listener;

import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.common.security.oauth2.event.TokenCreatedSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 类描述：token创建成功事件监听器
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/26 12:19
 **/
@Component
@Slf4j
public class TokenCreatedSuccessEventListener implements ApplicationListener<TokenCreatedSuccessEvent> {

    private final UserCacheTokenService userCacheTokenService;

    public TokenCreatedSuccessEventListener(UserCacheTokenService userCacheTokenService) {
        this.userCacheTokenService = userCacheTokenService;
    }

    @Override
    public void onApplicationEvent(TokenCreatedSuccessEvent tokenCreatedSuccessEvent) {
        log.debug("接收到token成功生成的事件: username = [{}] , tokenId = [{}],expiration = [{}]", tokenCreatedSuccessEvent.getAuthentication().getName(),
                tokenCreatedSuccessEvent.getAccessToken(), tokenCreatedSuccessEvent.getAccessToken().getExpiration());

        String tokenId = tokenCreatedSuccessEvent.getAccessToken().getValue();
        userCacheTokenService.deleteUserCacheByTokenId(tokenId);
        userCacheTokenService.cacheUser(tokenId, tokenCreatedSuccessEvent.getAuthentication());
    }
}

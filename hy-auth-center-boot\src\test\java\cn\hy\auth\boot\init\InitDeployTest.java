package cn.hy.auth.boot.init;

import cn.hutool.core.io.FileUtil;
import junit.framework.TestCase;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/5/20 17:55
 **/
public class InitDeployTest extends TestCase {
    //@Test
    public  void test() throws IOException {
        File file = new File("E:\\git_repository\\hy-authentication-center\\hy-auth-center-boot\\src\\main\\resources\\config\\application-prod.yml");

        new InitDeploy().preHandleYml(FileUtil.readString(file, StandardCharsets.UTF_8),
                "mysqlHost","mysqlPort","dbName","dbUser","dbPwd","authAddress","x2AuthCenter");


    }
}
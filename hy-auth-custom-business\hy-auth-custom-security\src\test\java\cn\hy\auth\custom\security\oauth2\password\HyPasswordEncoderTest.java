package cn.hy.auth.custom.security.oauth2.password;

import cn.hy.auth.custom.common.utils.AesUtil;
import cn.hy.auth.custom.user.history.service.impl.AuthLoginInfoServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * 类描述: 密码校验测试类
 *
 * <AUTHOR>
 * @date ：创建于 2021/2/1
 */
public class HyPasswordEncoderTest {

    @Test
    public void encode() {
        String password = "123456";
        String encryptPassword = AesUtil.encrypt(password, AesUtil.DEFAULT_KEY);
        HyPasswordEncoder hyPasswordEncoder = new HyPasswordEncoder();
        String result = hyPasswordEncoder.encode(password);
        assertEquals(encryptPassword, result);
    }

    @Test
    public void matches() {
        String password = "123456";
        String encryptPassword = AesUtil.encrypt(password, AesUtil.DEFAULT_KEY);

        HyPasswordEncoder hyPasswordEncoder = new HyPasswordEncoder();
        boolean matches = hyPasswordEncoder.matches(password, encryptPassword);
        assertTrue(matches);
    }
}
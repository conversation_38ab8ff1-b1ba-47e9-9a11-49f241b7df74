package cn.hy.auth.custom.security.reauthentication.dao;

import cn.hy.auth.custom.security.reauthentication.domain.AuthUsbkeyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 10:02
 */
@Mapper
public interface AuthUsbkeyMapper {

    List<AuthUsbkeyDO> selectByHrCode(@Param("hrCode") String hrCode);


    void revokeNonce(@Param("id") Long id);
}

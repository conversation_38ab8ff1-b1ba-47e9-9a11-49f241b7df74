package cn.hy.auth.custom.common.api;

import cn.hy.auth.custom.common.domain.LoginUserHistoryDTO;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;

import java.util.Map;

/**
 * 类描述：
 * 已过期，兼容历史版，不再进行新的扩展
 * <AUTHOR> by fuxinrong
 * @date 2023/2/8 10:08
 **/
@Deprecated
public interface UserAccountApi {
    /**
     * .
     * @param value .
     * @return .
     */
    Map<String, Object> checkToken(String value);

    /**
     * 获取当前的账号及关联信息
     * @param token .
     * @return 返回当前账号信息
     */
     UserAccountDTO getCurrentUserAccount(String token);

    /**
     * 获取当前的账号信息
     * @param token .
     * @return 返回当前账号信息
     */
     Map<String, Object> getCurrentAccount(String token);

    /**
     * 获取当前的账号关联信息
     * @param token .
     * @return 返回当前账号信息
     */
     Map<String, Object> getCurrentAssociationUser(String token);


    /**
     * 获取当前的账号、关联信息以及登录信息
     * @param token .
     * @return 返回当前账号信息所有信息
     */
     LoginUserHistoryDTO getCurrentUserWithLoginInfo(String token);

    /**
     * 查询上一次成功登录信息
     * @param token .
     * @return 返回最后登录信息
     */
    UserLoginInfoDTO getLastSuccessLoginInfo(String token);

}

package cn.hy.auth.custom.multi.utils;

import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.multi.cache.LesseeCaffeineCacheManager;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 从cn.hy.auth.custom.business.starter.CacheAutoConfiguration拷贝，供单元测试时用
 *
 * <AUTHOR>
 * @date 2020-12-04 11:58
 **/
@TestConfiguration
@Slf4j
public class TestCacheConfiguration {

    /**
     * 过期时间(s),没有配置则为半小时
     */
    @Value(value = "${auth.cache.expire_time:3600}")
    private Long expireTime;

    @Bean
    @ConditionalOnMissingBean(CacheManager.class)
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "caffeine",
            matchIfMissing = true
    )
    public CacheManager cacheManagerWithCaffeine() {
        log.debug("Test Caffeine cache 配置");
        LesseeCaffeineCacheManager cacheManager = new LesseeCaffeineCacheManager();
        Caffeine caffeine = Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(100)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
                .maximumSize(5000)
                .expireAfterWrite(expireTime, TimeUnit.SECONDS);
        cacheManager.setCaffeine(caffeine);
        //不允许设置空值
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    @Bean
    @ConditionalOnMissingBean(CacheManager.class)
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "redis"
    )
    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {
        log.debug("Test redis cache 配置");

        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                //不允许存入空值
                .disableCachingNullValues()
                .computePrefixWith(compute -> CachePrefixUtil.get() + compute + "::")
                //设置默认超过期时间是60000秒
                .entryTtl(Duration.ofSeconds(expireTime));

        //初始化RedisCacheManager
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);
    }

    /**
     * 因为按key模糊获取数据的时候没有提供对应的方法，所有如果是redis的时候暴露处理方便使用
     *
     * @param factory factory
     * @return RedisTemplate
     */
    @Bean
    @ConditionalOnMissingBean(RedisTemplate.class)
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "redis"
    )
    public RedisTemplate redisTemplate(RedisConnectionFactory factory) {
        // 创建一个模板类
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 将刚才的redis连接工厂设置到模板类中
        template.setConnectionFactory(factory);

        // 设置redis的String/Value的默认序列化方式
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.java());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(RedisSerializer.java());
        template.afterPropertiesSet();
        return template;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.user.account.dao.UserSecurityQuestionDao">

    <select id="selectOne" resultType="java.lang.Integer">
        select
            count(id)
        from
            sysdev_set_security_question
        where
            user_account_name = #{userName}
            and answer = #{answer}
            and question_id = #{securityQuestionId}
    </select>
</mapper>
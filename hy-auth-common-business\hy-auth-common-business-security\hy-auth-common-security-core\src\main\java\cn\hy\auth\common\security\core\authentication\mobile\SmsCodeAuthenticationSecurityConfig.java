package cn.hy.auth.common.security.core.authentication.mobile;

import cn.hy.auth.common.security.core.authentication.common.AbstractHySecurityConfigurerAdapter;
import cn.hy.auth.common.security.core.authentication.common.HyAuthenticationDetailsSource;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeService;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * 手机验证码认证安全配置
 *
 * <AUTHOR>
 * @date 2020-11-24 10:39
 */
@Component
public class SmsCodeAuthenticationSecurityConfig extends AbstractHySecurityConfigurerAdapter {

    @Autowired
    private UserDetailsService myUserDetailsService;
    @Autowired
    private AuthenticationSuccessHandler myAuthenticationSuccessHandler;
    @Autowired
    private AuthenticationFailureHandler myAuthenticationFailureHandler;
    @Autowired
    private SecurityProperties securityProperties;
    @Autowired
    private SmsCodeService smsCodeService;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        SmsCodeAuthenticationFilter smsCodeAuthenticationFilter = new SmsCodeAuthenticationFilter();
        smsCodeAuthenticationFilter.setAuthenticationDetailsSource(new HyAuthenticationDetailsSource("client_secret"));
        //设置共享现有的AuthenticationManager(带有自定义的authenticationProvider)
        smsCodeAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        //设置成功失败处理器
        smsCodeAuthenticationFilter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        smsCodeAuthenticationFilter.setAuthenticationFailureHandler(myAuthenticationFailureHandler);
        //设置认证的provider
        SmsCodeAuthenticationProvider smsCodeAuthenticationProvider = new SmsCodeAuthenticationProvider();
        smsCodeAuthenticationProvider.setMyUserDetailsService(myUserDetailsService);
        http
                // 注册到AuthenticationManager中去
                .authenticationProvider(smsCodeAuthenticationProvider)
                // 添加到 UsernamePasswordAuthenticationFilter 之后
                // 貌似所有的入口都是 UsernamePasswordAuthenticationFilter
                // 然后UsernamePasswordAuthenticationFilter的provider不支持这个地址的请求
                // 所以就会落在我们自己的认证过滤器上。完成接下来的认证
                .addFilterAfter(smsCodeAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        // 添加手机验证码校验过滤器
        SmsCodeValidateFilter smsCodeFilter = new SmsCodeValidateFilter(myAuthenticationFailureHandler, securityProperties, smsCodeService, new HyAuthenticationDetailsSource("client_secret"), applicationContext);
        smsCodeFilter.afterPropertiesSet();
        http.addFilterBefore(smsCodeFilter, UsernamePasswordAuthenticationFilter.class);
    }
}

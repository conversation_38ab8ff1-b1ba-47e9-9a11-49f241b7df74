<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-auth-common-business-security</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-common-security-session</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>hy-auth-common-security-session</name>
    <url>http://www.example.com</url>
    <description>session协议为主体的认证项目,适用于基于浏览器的web或者前后端分离项目</description>

    <dependencies>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-common-security-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>

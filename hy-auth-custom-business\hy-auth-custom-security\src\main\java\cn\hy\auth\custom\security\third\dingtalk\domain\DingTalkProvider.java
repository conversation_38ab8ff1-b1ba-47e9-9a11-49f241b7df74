package cn.hy.auth.custom.security.third.dingtalk.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;
import cn.hy.auth.custom.security.third.dingtalk.proxy.DingTalkConst;
import com.alibaba.fastjson.JSONObject;
import lombok.Setter;

import java.util.Map;

/**
 * 钉钉用户信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
public class DingTalkProvider implements ProviderInfo {

    private String providerId;
    private String appKey;
    private String appSecret;
    private String originConfig;

    @Override
    public String getProviderId() {
        return providerId;
    }

    @Override
    public String getAppKey() {
        return appKey;
    }

    @Override
    public String getAppSecret() {
        return appSecret;
    }

    @Override
    public String getAppAccessTokenUrl() {
        return "/gettoken";
    }

    @Override
    public String getAppUserInfoUrl() {
        return "/topapi/v2/user/getuserinfo";
    }

    @Override
    public Map<String, Object> originConfigMap() {
        if(originConfig!=null){
             return (Map<String, Object>)JSONObject.parse(originConfig);
        }
        return null;
    }
}

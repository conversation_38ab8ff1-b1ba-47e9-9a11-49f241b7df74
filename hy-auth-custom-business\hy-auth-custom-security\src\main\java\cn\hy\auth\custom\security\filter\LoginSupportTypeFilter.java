package cn.hy.auth.custom.security.filter;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.common.filter.AbstractAuthFilter;
import cn.hy.auth.custom.common.utils.LoginTypeMatcher;
import cn.hy.auth.custom.security.exceptions.UnsupportedLoginTypeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 判断登录类型是否支持的过滤器
 *
 * <AUTHOR>
 * @date 2020-12-02 14:06
 **/
@Order(-980)
@Component
@Slf4j
public class LoginSupportTypeFilter extends AbstractAuthFilter {

    @Autowired
    private LoginTypeMatcher loginTypeMatcher;

    @Override
    protected void doMyFilter(HttpServletRequest request, HttpServletResponse response,
                              FilterChain filterChain) throws ServletException, IOException {
        LoginTypeEnum loginType = loginTypeMatcher.match(request);
        if (loginType != null) {
            log.info("识别到的登录类型：【{}】", loginType);
            //登录类型支持判断
            boolean isSupportLoginType = AuthContext.getContext()
                    .appAuthStrategy()
                    .getLoginRule()
                    .getLoginSupportType()
                    .contains(loginType);
            if (!isSupportLoginType) {
                String msg = "当前应用【" + AuthContext.getContext().getLesseeCode() + "-" +
                        AuthContext.getContext().getAppCode() + "】不支持该登录类型【" + loginType + "】";
                throw new UnsupportedLoginTypeException(msg);
            }

            //设置到上下文
            AuthContext.getContext().loginState().setLoginType(loginType);
        }

        //不是登录的uri
        filterChain.doFilter(request, response);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.security.log.dao.AuthSysLogMapper">

  <insert id="insert" parameterType="cn.hy.auth.custom.security.log.domain.AuthSysLog">
    insert into auth_sys_log (id, create_user_id, last_update_time,
    sequence, create_user_name, create_time,
    data_version, last_update_user_id, last_update_user_name,
    user_account, transaction_id, operation_type,
    operation_obj, module_name, ip,
    operation_result, operation_result_desc, url,
    required_method, elapsedtime, query_string,
    description,client_type)
    values (#{id,jdbcType=DECIMAL}, #{createUserId,jdbcType=DECIMAL}, #{lastUpdateTime,jdbcType=TIMESTAMP},
    #{sequence,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{dataVersion,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=DECIMAL}, #{lastUpdateUserName,jdbcType=VARCHAR},
    #{userAccount,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR},
    #{operationObj,jdbcType=VARCHAR}, #{moduleName,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR},
    #{operationResult,jdbcType=VARCHAR}, #{operationResultDesc,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
    #{requiredMethod,jdbcType=VARCHAR}, #{elapsedtime,jdbcType=VARCHAR}, #{queryString},
    #{description},#{clientType})
  </insert>


</mapper>
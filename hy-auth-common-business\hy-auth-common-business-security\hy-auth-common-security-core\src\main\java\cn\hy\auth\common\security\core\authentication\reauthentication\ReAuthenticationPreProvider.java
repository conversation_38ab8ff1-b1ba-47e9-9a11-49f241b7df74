package cn.hy.auth.common.security.core.authentication.reauthentication;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

/**
 * <AUTHOR>
 * @date 2024/6/7 14:01
 */
public abstract class ReAuthenticationPreProvider<T extends AbstractAuthenticationToken> implements AuthenticationProvider {

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (!getAuthentication().isInstance(authentication)) {
            return null;
        }
        return doAuthenticate(getAuthentication().cast(authentication));
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return getAuthentication().isAssignableFrom(authentication);
    }

    protected abstract Class<T> getAuthentication();

    protected abstract Authentication doAuthenticate(T authentication);
}

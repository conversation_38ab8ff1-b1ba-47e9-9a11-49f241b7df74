package cn.hy.auth.custom.security.third.miniprogram.impl;

import cn.hy.auth.common.security.core.authentication.social.service.HyOauth2Operations;
import cn.hy.auth.common.security.core.authentication.social.service.HyProviderInfoService;
import cn.hy.auth.common.security.core.authentication.social.service.HyServiceProvider;
import cn.hy.auth.common.security.core.authentication.social.service.HySocialUserInfoApi;
import cn.hy.auth.custom.security.third.miniprogram.domain.MiniProgramAccessToken;
import cn.hy.auth.custom.security.third.miniprogram.domain.MiniProgramProvider;
import cn.hy.auth.custom.security.third.miniprogram.domain.MiniProgramProviderUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/5/10
 */
@Component
public class HyMiniProgramServiceProvider implements HyServiceProvider {

    @Autowired
    private MiniProgramProviderTokenImpl miniProgramProviderToken;
    @Autowired
    private MiniProgramUserProviderImpl miniProgramUserProvider;
    @Autowired
    private MiniProgramProviderInfoImpl miniProgramProviderInfo;

    @Override
    public HyOauth2Operations getOauth2Operations() {
        return miniProgramProviderToken;
    }

    @Override
    public HySocialUserInfoApi getSocialUserInfoApi() {
        return miniProgramUserProvider;
    }

    @Override
    public HyProviderInfoService getProviderInfoService() {
        return miniProgramProviderInfo;
    }
}

package cn.hy.auth.common.security.oauth2.event;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;

import javax.servlet.http.HttpServletRequest;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 13:39
 **/
@ToString
public class LoginFailureEvent extends ApplicationEvent {
    /**
     * 认证的信息
     */
    private Authentication authentication;
    private transient HttpServletRequest request;

    public LoginFailureEvent(Exception exception) {
        super(exception);
    }


    public Exception getException() {
        return (Exception) getSource();
    }


    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public Authentication getAuthentication() {
        return authentication;
    }

    public void setAuthentication(Authentication authentication) {
        this.authentication = authentication;
    }
}

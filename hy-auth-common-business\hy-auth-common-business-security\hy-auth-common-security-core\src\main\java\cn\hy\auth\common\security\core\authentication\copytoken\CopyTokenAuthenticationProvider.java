package cn.hy.auth.common.security.core.authentication.copytoken;

import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;

import java.util.Map;
import java.util.Objects;

/**
 * 复制token的认证提供者
 *
 * <AUTHOR>
 * @date 2024-12-04 10:34
 */
@Data
@Slf4j
public class CopyTokenAuthenticationProvider implements AuthenticationProvider {


    @Setter
    protected boolean hideUserNotFoundExceptions = true;
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    private final TokenStore tokenStore;

    public CopyTokenAuthenticationProvider(TokenStore tokenStore) {
        this.tokenStore = tokenStore;
    }

    @Override
    public Authentication authenticate(Authentication authentication) {
        //这个authentication就是SmsCodeAuthenticationToken
        CopyTokenAuthenticationToken authenticationToken = (CopyTokenAuthenticationToken) authentication;
        try {
            // 通过手机号查询用户信息
            OAuth2AccessToken oAuth2AccessToken = tokenStore.readAccessToken(authenticationToken.getToken());
            if (oAuth2AccessToken == null){
                log.error("输入参数不正确。token:{}，在tokenStore中没有查询到对应的token", authenticationToken.getToken());
                throw new BadCredentialsException("输入参数token不正确");
            }
            OAuth2RefreshToken refreshToken = oAuth2AccessToken.getRefreshToken();
            if (!Objects.equals(refreshToken.getValue(), authenticationToken.getRefreshToken())) {
                log.error("输入参数不正确。refreshToken:{}，不匹配", refreshToken.getValue());
                throw new BadCredentialsException("输入参数refresh_token不正确");
            }
            //这时候已经认证成功了
            OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(oAuth2AccessToken);

            CopyTokenAuthenticationToken authenticationResult = new CopyTokenAuthenticationToken(oAuth2Authentication.getUserAuthentication().getPrincipal(), authenticationToken.getToken(), authenticationToken.getRefreshToken(), oAuth2Authentication.getAuthorities());
            Object details = authenticationToken.getDetails();
            if (details instanceof Map && oAuth2Authentication.getUserAuthentication().getDetails() instanceof Map){
                Map<String,Object> userLoginMap = (Map<String, Object>) oAuth2Authentication.getUserAuthentication().getDetails();
                ((Map)details).putIfAbsent("lessee_code",userLoginMap.get("lessee_code"));
                ((Map)details).putIfAbsent("app_code",userLoginMap.get("app_code"));
                ((Map)details).putIfAbsent("username",userLoginMap.get("username"));
                ((Map)details).putIfAbsent("client_type","copy");
            }
            authenticationResult.setDetails(details);
            return authenticationResult;
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }

    }

    @Override
    public boolean supports(Class<?> authentication) {
        //该SmsCodeAuthenticationProvider智支持SmsCodeAuthenticationToken的token认证
        return CopyTokenAuthenticationToken.class.isAssignableFrom(authentication);
    }

}

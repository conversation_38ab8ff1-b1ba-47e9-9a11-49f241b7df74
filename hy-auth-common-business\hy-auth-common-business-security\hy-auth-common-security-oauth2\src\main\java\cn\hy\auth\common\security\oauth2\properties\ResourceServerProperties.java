package cn.hy.auth.common.security.oauth2.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 类描述：资源服务器的配置
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 11:16
 **/
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "hy.security.oauth2.resource.server")
public class ResourceServerProperties {
    /**
     * 资源服务名称
     */
    private String resourceId = "hy_auth_center";
    /**
     * 如果 stateless 为 true 打开状态，
     * 则 每次请求都必须携带 accessToken 请求才行，否则将无法访问
     */
    private boolean stateless = true;
}

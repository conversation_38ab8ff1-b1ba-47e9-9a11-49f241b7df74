package cn.hy.auth.custom.security.oauth2.environment.domain;

import lombok.*;

import java.util.Date;

/**
 *
 * 环境标识表DO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OauthSysMDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 当前系统环境标识
     */
    private String uuid;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

}

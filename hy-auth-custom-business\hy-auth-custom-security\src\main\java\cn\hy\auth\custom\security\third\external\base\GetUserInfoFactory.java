package cn.hy.auth.custom.security.third.external.base;

import cn.hy.auth.custom.common.utils.AuthSpringUtil;
import cn.hy.auth.custom.security.third.external.fky.FkyUserInfoAdapter;
import lombok.NonNull;

/**
 * 表规Bean工厂
 *
 * <AUTHOR>
 * @date 2023/5/17
 */
public class GetUserInfoFactory {

    public static IExternalUserInfo getRequestBean(@NonNull String providerId) {

        switch(providerId){
            case "fky":
                return AuthSpringUtil.getBean(FkyUserInfoAdapter.class);
            default:
                return null;
        }
    }
}

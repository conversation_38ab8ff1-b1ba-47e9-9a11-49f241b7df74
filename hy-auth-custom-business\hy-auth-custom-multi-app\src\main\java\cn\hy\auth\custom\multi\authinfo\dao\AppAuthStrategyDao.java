package cn.hy.auth.custom.multi.authinfo.dao;

import org.apache.ibatis.annotations.Param;

/**
 * 应用认证规则数据访问接口
 *
 * <AUTHOR>
 * @date 2020-12-07 14:29
 **/
public interface AppAuthStrategyDao {

    /**
     * 获取应用对应的认证规则信息
     *
     * @return .
     */
    String getAppAuthStrategyJson();

    /**
     * 校验字段是否存在用户表中
     * @param field .
     * @return .
     */
    Object checkFieldInUserAccount(@Param("field") String field);
}

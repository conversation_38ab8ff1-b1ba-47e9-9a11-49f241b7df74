package cn.hy.auth.custom.security.third.external;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAccessToken;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalAuthConfig;
import cn.hy.auth.common.security.core.authentication.external.bean.ExternalProviderUser;
import cn.hy.auth.common.security.core.authentication.external.def.ExternalAuthService;
import cn.hy.auth.common.security.oauth2.util.OkHttpTools;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.security.common.SqlCreateUtil;
import cn.hy.auth.custom.security.third.external.base.ExternalRequestUrl;
import cn.hy.auth.custom.security.third.external.base.GetExternalRequest;
import cn.hy.auth.custom.security.third.external.base.GetUserInfoFactory;
import cn.hy.auth.custom.security.third.external.base.IExternalUserInfo;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import cn.hy.saas.commons.SpringContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 佛科院认证相关业务逻辑
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
@Service
@Slf4j
public class ExternalAuthServiceImpl implements ExternalAuthService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TableRelocateUtil tableRelocateUtil;

    @Autowired
    private OkHttpTools okHttpTools;

    @Override
    public ExternalAccessToken getAccessToke(String code, ExternalAuthConfig authConfig) {
        ExternalRequestUrl requestBean = GetExternalRequest.getRequestBean(authConfig.getProviderId());
        if (requestBean == null || requestBean.getAccessTokenUrl() == null) {
            throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg1", null) + authConfig.getProviderId());
        }
        String accessTokenUrl = requestBean.getAccessTokenUrl();
        log.info("请求获取accessToken地址:{}", accessTokenUrl);
        FormBody responseBody = new FormBody.Builder()
                .add("grant_type", authConfig.getGrantType() == null ? "authorization_code" : authConfig.getGrantType())
                .add("client_id", authConfig.getClientId())
                .add("client_secret", authConfig.getClientSecret())
                .add("code", code)
                .add("redirect_uri", authConfig.getUrl()).build();
        ExternalAccessToken externalAccessToken;
        try {
            String postResult = okHttpTools.postForm(accessTokenUrl, responseBody);
            log.info("获取第三方请求token,接口返回结果：{}", postResult);
            if (postResult != null) {
                externalAccessToken = new ExternalAccessToken();
                JSONObject resObj = JSONObject.parseObject(postResult);
                if (resObj.containsKey("error") && !resObj.getInteger("error").equals(200)) {
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg2", null) + responseBody + "," +
                            LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg3", null) + resObj.getString("error_description") + "】");
                }
                String accessToken = resObj.getString("access_token");
                String tokenType = resObj.getString("token_type");
                Integer expiresIn = resObj.getInteger("expires_in");
                String refreshToken = resObj.getString("refresh_token");
                Calendar nowTime = Calendar.getInstance();
                nowTime.add(Calendar.SECOND, expiresIn);
                Date expiresTime = nowTime.getTime();
                externalAccessToken.setAccessToken(accessToken);
                externalAccessToken.setErrCode(200);
                externalAccessToken.setExpiresIn(expiresIn);
                externalAccessToken.setExpiresTime(expiresTime);
                externalAccessToken.setTokenType(tokenType);
                externalAccessToken.setRefreshToken(refreshToken);
                externalAccessToken.setProviderId(authConfig.getProviderId());
                externalAccessToken.setLesseeCode(authConfig.getLesseeCode());
                externalAccessToken.setAppCode(authConfig.getAppCode());
                return externalAccessToken;
            } else {
                throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg4", null));
            }
        } catch (Exception e) {
            throw new AuthBusinessException("500", "" + e.getMessage() + "");
        }
    }

    @Override
    public ExternalProviderUser getUserInfo(ExternalAccessToken accessToken) {
        IExternalUserInfo requestBean = GetUserInfoFactory.getRequestBean(accessToken.getProviderId());
        if (requestBean == null) {
            throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg5", null));
        }
        return requestBean.getUserInfo(accessToken);
    }

    @Override
    public Map<String, Object> getByUserAccount(ExternalProviderUser userInfo) {
        List<Map<String, Object>> resultMap = findUserAccountName(userInfo.getLesseeCode(), userInfo.getAppCode(), userInfo.getUserId());
        if (!CollectionUtils.isEmpty(resultMap)) {
            return resultMap.get(0);
        }
        return null;
    }

    @Override
    public Boolean regirstUser(ExternalProviderUser userInfo) {
        Map<String, Object> parseResult = new HashMap<>();
        parseResult.put("id", BigDecimal.valueOf(UUIDRandomUtils.getSnowUuid()));
        parseResult.put("password", "Ziykbgi8BMoadAYSAyiAxg==");
        parseResult.put("old_id", userInfo.getUserId());
        parseResult.put("user_account_name", userInfo.getUserAccountName());
        parseResult.put("username", userInfo.getUserName());
        parseResult.put("create_user_id", 1);
        parseResult.put("last_update_user_id", 1);
        parseResult.put("is_system_recode", "0");
        parseResult.put("sequence", 1);
        parseResult.put("data_version", "1");
        parseResult.put("create_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        parseResult.put("last_update_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        log.info("组装用户参数：{}", parseResult);
        String insertSql = SqlCreateUtil.createInsertSql(getTableName(userInfo.getLesseeCode(), userInfo.getAppCode(), "user_account"), parseResult);
        jdbcTemplate.execute(insertSql);
        return Boolean.TRUE;
    }

    @Override
    public ExternalAuthConfig getAuthConfig(String providerId, String lesseeCode, String appCode) {
        ExternalAuthConfig authConfig;
        try {
            if (jdbcTemplate == null) {
                jdbcTemplate = SpringContextHolder.getApplicationContext().getBean(JdbcTemplate.class);
            }
            String querySql = "SELECT config_content FROM " + lesseeCode + "_" + appCode + "_auth_config order by create_time desc limit 1 ";
            List<Map<String, Object>> configList = jdbcTemplate.queryForList(querySql);
            authConfig = new ExternalAuthConfig();
            if (CollectionUtils.isNotEmpty(configList)) {
                Map<String, Object> configMap = configList.get(0);
                String configContent = (String) configMap.get("config_content");
                Map<String, Object> configInfo = (Map<String, Object>) JSONObject.parse(configContent);
                if (configInfo.get("appInfoMapping") != null) {
                    Map<String, Object> appInfo = (Map<String, Object>) configInfo.get("appInfoMapping");
                    if (appInfo.get(providerId) != null) {
                        Map<String, Object> config = (Map<String, Object>) appInfo.get(providerId);
                        String appKey = (String) config.get("appKey");
                        String appSecret = (String) config.get("appSecret");
                        String redirectUrl = (String) config.get("redirectUrl");
                        String grantType = (String) config.get("grantType");
                        authConfig.setClientId(appKey);
                        authConfig.setClientSecret(appSecret);
                        authConfig.setUrl(redirectUrl);
                        authConfig.setGrantType(grantType);
                        authConfig.setProviderId(providerId);
                        authConfig.setLesseeCode(lesseeCode);
                        authConfig.setAppCode(appCode);
                        return authConfig;
                    } else {
                        throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg6", null) + providerId + LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg7", null));
                    }
                } else {
                    throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg8", null));
                }
            } else {
                throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg9", null));
            }
        } catch (Exception e) {
            log.error("查询[auth_config]第三方应用信息失败,{},{}", e, lesseeCode, appCode);
            throw new AuthBusinessException("500", LocaleUtil.getMessage("ExternalAuthServiceImpl.result.msg10", null) + e.getMessage());
        }
    }

    /**
     * 修改表名
     *
     * @param lesseeCode 租户编码
     * @param appCode    应用编码
     * @param tableCode  表编码
     * @return
     */
    @Override
    public String getTableName(String lesseeCode, String appCode, String tableCode) {
        Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(tableCode);
        if (tableMeta.isPresent()) {
            TableMData tableMData = tableMeta.get();
            lesseeCode = tableMData.getLessCode();
            appCode = tableMData.getAppCode();
        }
        StringBuilder builder = new StringBuilder(128);
        return builder.append(lesseeCode).append("_").append(appCode).append("_").append(tableCode).toString();
    }


    private List<Map<String, Object>> findUserAccountName(String lesseeCode, String appCode, String userId) {
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append(" select * from " + getTableName(lesseeCode, appCode, "user_account ") + " where old_id = ? ");
        return jdbcTemplate.queryForList(sqlBuilder.toString(), userId);
    }
}

package cn.hy.auth.custom.user.account.service.impl;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthLoginPwdStatusPolicyDTO;
import cn.hy.auth.custom.common.enums.LoginInitPwdActionEnum;
import cn.hy.auth.custom.user.account.service.PwdStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/8/23 13:55
 **/
@Service
@Slf4j
public class PwdStatusServiceImpl implements PwdStatusService {

    @Override
    public boolean handleInitPwd(HyUserDetails account) {
        AppAuthLoginPwdStatusPolicyDTO pwdStatusPolicy = AuthContext.getContext().appAuthStrategy().getLoginRule().getPwdStatusPolicy();
        if (LoginInitPwdActionEnum.MUST_BE_MODIFIED.equals(pwdStatusPolicy.getInitPwdAction())) {
            Object pwdStatus= account.getAccount().get(pwdStatusPolicy.getPwdStatus());
            if (!account.getAccount().containsKey(pwdStatusPolicy.getPwdStatus())){
                log.info("当前用户信息中不包含{}字段,不启用初始密码更新策略。可以检查用户表确定是否正常。",pwdStatusPolicy.getPwdStatus());
                return false;
            }
            boolean initPwdStats= "1".equals(pwdStatus) || Objects.equals(pwdStatus,1);
            boolean defaultPwd = null==pwdStatus && "Ziykbgi8BMoadAYSAyiAxg==".equals(account.getPassword());
            // 是初始密码
            return initPwdStats || defaultPwd;
        }
        return false;
    }
}

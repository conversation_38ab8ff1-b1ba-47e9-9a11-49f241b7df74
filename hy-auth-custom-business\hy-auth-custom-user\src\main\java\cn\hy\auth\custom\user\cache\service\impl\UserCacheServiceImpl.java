package cn.hy.auth.custom.user.cache.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.business.tool.token.service.UserCacheTokenService;
import cn.hy.auth.common.security.oauth2.util.HttpContextUtil;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.AppInfoParseProxy;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.common.utils.LoginUtil;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;
import cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;
import cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;
import cn.hy.auth.custom.user.cache.service.UserCacheService;
import cn.hy.auth.custom.user.config.context.NativeDiffCacheContext;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类描述: 用户缓存信息实现类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/27
 */
@Slf4j
@Service
public class UserCacheServiceImpl implements UserCacheService, UserCacheTokenService {
    private final TokenStore tokenStore;
    private final ObjectMapper objectMapper;
    /**
     * 缓存实现类，该处已实现租户应用间的隔离
     */
    private final CacheManager cacheManager;

    /**
     * 用户账号信息实现类
     */
    private final UserAccountService userAccountService;

    /**
     * 缓存在线人员不同缓存的处理实现
     */
    private final NativeDiffCacheContext nativeDiffCacheContext;

    /**
     * 获取租户应用实现
     */
    private final AppInfoParseProxy appInfoParseProxy;

    /**
     * 用户登录信息
     */
    private final AuthLoginInfoService authLoginInfoService;

    /**
     * 跟主键标识
     */
    private static final String ROOT_CACHE_NAME = "hy_auth";
    /**
     * 登录账号
     */
    private static final String USER_NAME = "username";

    private final ConcurrentHashMap<String,Object> parallelLockMap = new ConcurrentHashMap<>();

    public UserCacheServiceImpl(ObjectMapper objectMapper, @Qualifier("hyAuthAutoCacheManager") CacheManager cacheManager, UserAccountService userAccountService, NativeDiffCacheContext nativeDiffCacheContext, AppInfoParseProxy appInfoParseProxy, AuthLoginInfoService authLoginInfoService, TokenStore tokenStore) {
        this.objectMapper = objectMapper;
        this.cacheManager = cacheManager;
        this.userAccountService = userAccountService;
        this.nativeDiffCacheContext = nativeDiffCacheContext;
        this.appInfoParseProxy = appInfoParseProxy;
        this.authLoginInfoService = authLoginInfoService;
        this.tokenStore = tokenStore;
    }


    /**
     * 查询缓存数据
     *
     * @param userId 用户主键
     * @return 返回用户信息
     */
    private UserAccountDTO getUserAccountFromCacheByUserId(@NotNull Long userId) {
        //缓存标识
        String userCacheKey = getUserCacheKey(userId);
        //获取缓存信息
        String user = getRootCache().get(userCacheKey, String.class);

        try {
            return StringUtils.isBlank(user) ? null : objectMapper.readValue(user, UserAccountDTO.class);
        } catch (IOException e) {
            log.error("json 反序列化失败.msg:{},{}",user,e.getMessage());
        }

        return null;
    }

    @Override
    public UserAccountDTO getUserAccountByLoginName(@NotBlank String loginName) {
        UserAccountDTO userAccountDTO = null;
        if (StringUtils.isBlank(loginName)) {
            userAccountDTO = loadUserAccountByTokenStore();
        }
        if (ObjectUtil.isNull(userAccountDTO)) {
            //获取用户主键
            userAccountDTO = getUserAccountFromCacheByLoginName(loginName);
        }
        if (ObjectUtil.isNull(userAccountDTO)) {
            String lockIdentify = StringUtils.join(AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),loginName);
            synchronized (getLock(lockIdentify)){
                try {
                    userAccountDTO = getUserAccountFromCacheByLoginName(loginName);
                    if (userAccountDTO == null){
                        // 从数据库加载会有localdatetime类型，从缓存取是从json解析过来的，没有localdatetime类型了
                        userAccountDTO = loadUserAccountByLoginNameAndCache();
                    }
                    if (userAccountDTO == null) {
                        userAccountDTO = loadUserAccountByTokenStore();
                    }
                }finally {
                    removeLock(lockIdentify);
                }
            }
        }
        return userAccountDTO;
    }

    private UserAccountDTO loadUserAccountByTokenStore() {
        log.debug("从token附件信息里获取用户信息");
        String token = AuthContext.getContext().get("token").isPresent()?String.valueOf(AuthContext.getContext().get("token").get()): TokenUtil.getTokenID() ;
        OAuth2AccessToken oAuth2AccessToken = tokenStore.readAccessToken(token);
        if (oAuth2AccessToken != null) {
            Map<String, Object> additionalInformation = oAuth2AccessToken.getAdditionalInformation();
            if (additionalInformation != null) {
                Map<String, Object> userInfoMap = (Map<String, Object>) additionalInformation.get("user_info");
                if (userInfoMap != null) {
                    UserAccountDTO userAccountDTO = new UserAccountDTO();
                    userAccountDTO.setStatus(1);
                    userAccountDTO.setEnabled(true);
                    userAccountDTO.setAccountStartEffectiveTime(System.currentTimeMillis() - 3000);
                    userAccountDTO.setId(Long.parseLong(userInfoMap.get("id").toString()));
                    userAccountDTO.setUserAccountName((String) userInfoMap.get("user_account_name"));
                    userAccountDTO.setAccountNonLocked(true);
                    userAccountDTO.setLesseeCode(AuthContext.getContext().getLesseeCode());
                    userAccountDTO.setAppCode(AuthContext.getContext().getAppCode());
                    // 存放角色的权限信息
                    userAccountDTO.setUser(new HashMap<>());
                    userAccountDTO.setAccount(userInfoMap);
                    return userAccountDTO;
                }
            }
        }
        return null;
    }

    /**
     * 查询缓存数据
     *
     * @param loginName 登录名
     * @return 返回用户信息
     */
    private UserAccountDTO getUserAccountFromCacheByLoginName(@NotBlank String loginName) {
        //获取用户主键
        Long userId = getUserIdFromCacheByLoginName(loginName);
        return ObjectUtil.isNull(userId) ? null : getUserAccountFromCacheByUserId(userId);
    }



    /**
     * 查询缓存数据，没有找到则从数据库中获取并放入缓存
     *
     * @param loginName 登录名
     * @return 返回用户主键
     */
    private Long getUserIdFromCacheByLoginName(@NotBlank String loginName) {
        //缓存标识
        String loginNameCacheKey = getLoginNameCacheKey(loginName);
        //获取用户主键
        return getRootCache().get(loginNameCacheKey, Long.class);
    }

    @Override
    public void removeCacheByUserId(@NotBlank Long userId) {
        String tokenId = getTokenIdByUserId(userId);
        if (StringUtils.isBlank(tokenId)) {
            log.warn("按用户主键[{}]获取tokenId失败,获取为空", userId);
            return;
        }
        //移除缓存信息
        deleteUserCacheByTokenId(tokenId);
    }

    @Override
    public void removeCacheByLoginName(@NotBlank String loginName) {
        Long userId = getUserIdFromCacheByLoginName(loginName);
        if (ObjectUtil.isNull(userId)) {
            log.warn("按登录账号[{}]获取用户主键失败，主键为空", loginName);
            return;
        }
        removeCacheByUserId(userId);
    }

    @Override
    public Set<Long> getOnlineUserId() {
        Cache cache = cacheManager.getCache(ROOT_CACHE_NAME);
        Object nativeCache = cache.getNativeCache();
        if (ObjectUtil.isNull(nativeCache)) {
            return Collections.emptySet();
        }
        NativeCacheEnum nativeCacheEnum = NativeCacheEnum.classOf(cache);
        if (ObjectUtil.isNull(nativeCacheEnum)) {
            log.debug("没有找到对应的缓存类型[{}]", cache.getName());
            return Collections.emptySet();
        }
        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().nativeCacheData(nativeCache).rootCacheName(CachePrefixUtil.get() + ROOT_CACHE_NAME).build();
        return nativeDiffCacheContext.getNativeCacheHandleMap(nativeCacheEnum.getType()).handle(nativeCacheDTO);
    }

    @Override
    public UserLoginInfoDTO getUserLoginInfoByTokenId(@NotBlank String tokenId) {
        return getLoginInfo(tokenId);
    }

    /**
     * 获取缓存中的登录用户信息
     *
     * @param tokenId ..
     * @return 返回登录信息
     */
    private UserLoginInfoDTO getLoginInfo(String tokenId) {
        UserLoginInfoDTO loginInfoFromCache = getLoginInfoFromCache(tokenId);
        if (loginInfoFromCache == null){
            synchronized (getLock(tokenId)){
                try {
                    loginInfoFromCache = getLoginInfoFromCache(tokenId);
                    if (loginInfoFromCache == null){
                        Long userIdByTokenId = getOriginalUserIdByTokenId(tokenId);
                        if(userIdByTokenId == null){
                            userIdByTokenId = LoginUtil.getLoginUserId();
                            log.debug("tokenId={},从认证对象Authentication中取userId {}",tokenId,userIdByTokenId);
                        }
                        UserLoginInfoDTO userLoginInfoDTO = cacheUserLoginInfo(tokenId, userIdByTokenId);
                        if (userLoginInfoDTO == null){
                            log.warn("{},从数据库里查询到的登录用户信息为空!!!",tokenId);
                        }
                        return userLoginInfoDTO;
                    }
                }finally {
                    removeLock(tokenId);
                }
            }
        }
        return loginInfoFromCache;
    }

    private Object getLock(String identify) {
        // 线程安全
        return parallelLockMap.computeIfAbsent(identify, t -> new Object());

    }

    private void removeLock(String identify) {
        // 线程安全
         parallelLockMap.remove(identify);
    }
    /**
     * 获取缓存中的登录用户信息
     *
     * @param tokenId ..
     * @return 返回登录信息
     */
    private UserLoginInfoDTO getLoginInfoFromCache(String tokenId) {
        String tokenIdUserLoginInfoCacheKey = getTokenIdUserLoginInfoCacheKey(tokenId);
        String loginInfoStr = getRootCache().get(tokenIdUserLoginInfoCacheKey, String.class);
        if (StringUtils.isNotBlank(loginInfoStr)) {
            try {
                return objectMapper.readValue(loginInfoStr, UserLoginInfoDTO.class);
            }catch (Exception e){
                log.error("json 反序列化失败.{}",loginInfoStr);
            }
        }
        return null;
    }
    @Override
    public UserAccountDTO cacheUser(@NotBlank String tokenId, @NotNull OAuth2Authentication oAuth2Authentication) {
        Long userId = getUserIdFormPrincipal(oAuth2Authentication);
        if (ObjectUtil.isNotNull(userId)) {
            UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);
            if (userAccountDTO == null){
                userAccountDTO = getUserAccountByUserIdFromDb(userId);
            }
            if (ObjectUtil.isNotNull(userAccountDTO)) {
                putUserToCache(tokenId, userAccountDTO);
            }
            return userAccountDTO;
        }
        String loginName = getLoginNameFormDetail(oAuth2Authentication);
        if (StringUtils.isBlank(loginName)){
            return null;
        }
        UserAccountDTO userAccountDTO = getUserAccountFromCacheByLoginName(loginName);
        if (userAccountDTO == null){
            userAccountDTO = getUserAccountByLoginFromDb(loginName);
        }
        if (ObjectUtil.isNotNull(userAccountDTO)) {
            putUserToCache(tokenId, userAccountDTO);
        }
        //缓存数据
        return userAccountDTO;
    }

    /**
     * 登录时已经设置了用户主键，直接取放入缓存
     *
     * @param oAuth2Authentication oAuth2Authentication
     * @return 返回用户对象
     */
    private Long getUserIdFormPrincipal(OAuth2Authentication oAuth2Authentication) {
        Authentication userAuthentication = oAuth2Authentication.getUserAuthentication();
        if (ObjectUtil.isNull(userAuthentication)) {
            return null;
        }
        Object principal = userAuthentication.getPrincipal();
        if (principal instanceof HyUserDetails) {
            //如果已经设置了用户主键,直接拿取
            return  ((HyUserDetails) principal).getUserId();
        }
        return null;
    }

    /**
     * 从请求参数中获取
     *
     * @param oAuth2Authentication oAuth2Authentication
     * @return 返回用户对象
     */
    private String getLoginNameFormDetail(OAuth2Authentication oAuth2Authentication) {
        Authentication userAuthentication = oAuth2Authentication.getUserAuthentication();
        if (ObjectUtil.isNull(userAuthentication)) {
            return null;
        }
        //如果没有设置则从参数获取
        Object details = userAuthentication.getDetails();
        if (!(details instanceof Map)) {
            log.debug("登录详情不是Map对象");
            return null;
        }
        Map paramMap = (Map) details;
        //不存在用户主键,使用多方式登录模式获取用户信息
        return  (String) paramMap.get(USER_NAME);
    }

    /**
     * 缓存用户登录信息
     *
     * @param tokenId     tokenId
     * @param uid 当前登录用户id
     */
    private UserLoginInfoDTO cacheUserLoginInfo(String tokenId, Long uid) {
        String clientId = LoginUtil.getClientId();
        log.debug("客户端标识:{}", clientId);
        UserLoginInfoDTO lastSuccessLoginInfo = authLoginInfoService.getLastSuccessLoginInfo(clientId, uid);
        //获取获取数据
        Cache cache = getRootCache();
        if (ObjectUtil.isNotNull(lastSuccessLoginInfo)) {
            String tokenIdUserLoginInfoCacheKey = getTokenIdUserLoginInfoCacheKey(tokenId);
            log.debug("缓存token用户登信息：缓存标识:[{}]-tokenId:[{}]", tokenIdUserLoginInfoCacheKey, tokenId);
            try {
                String lastSuccessLoginInfoStr = objectMapper.writeValueAsString(lastSuccessLoginInfo);
                cache.put(tokenIdUserLoginInfoCacheKey, lastSuccessLoginInfoStr);
            } catch (JsonProcessingException e) {
                log.warn("json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}",lastSuccessLoginInfo,e.getMessage());
            }

        }
        return lastSuccessLoginInfo;
    }
    /**
     * 移除缓存用户登录信息
     *
     * @param tokenId     tokenId
     */
    private void removeUserLoginInfo(String tokenId) {
        //获取获取数据
        String tokenIdUserLoginInfoCacheKey = getTokenIdUserLoginInfoCacheKey(tokenId);
        log.debug("移除缓存token用户登录信息：缓存标识:[{}]-tokenId:[{}]", tokenIdUserLoginInfoCacheKey, tokenId);
        getRootCache().evict(tokenIdUserLoginInfoCacheKey);
    }
    /**
     * 换成租户应用信息
     *
     * @param userAccount 用户账号信息
     */
    private void setLesseeAppCodeToUserAccount(UserAccountDTO userAccount) {
        AppInfoDTO parse;
        if (HttpContextUtil.getHttpServletRequest() != null){
            parse = appInfoParseProxy.parse(HttpContextUtil.getHttpServletRequest());
        } else {
            parse =  AppInfoDTO.builder()
                    .lesseeCode(AuthContext.getContext().getLesseeCode())
                    .appCode( AuthContext.getContext().getAppCode()).build();
        }
        userAccount.setLesseeCode(parse.getLesseeCode());
        userAccount.setAppCode(parse.getAppCode());
    }

    @Override
    public UserAccountDTO getUserCacheByTokenId(@NotBlank String tokenId) {
        UserAccountDTO userAccountDTO = getOriginalUserCacheByTokenId(tokenId);
        if (ObjectUtil.isNull(userAccountDTO)) {
            log.debug("按tokenId[{}]获取用户主键失败", tokenId);
            //重新获取放入缓存
            return loadUserAccountByLoginNameAndCache();
        }
        return userAccountDTO;
    }

    /**
     * 从缓存中获取用户信息
     *
     * @param tokenId tokenId
     * @return 返回用户信息
     */
    private UserAccountDTO getOriginalUserCacheByTokenId(@NotBlank String tokenId) {
        Long userId = getOriginalUserIdByTokenId(tokenId);
        if (ObjectUtil.isNull(userId)) {
            log.debug("按tokenId[{}]获取用户主键失败", tokenId);
            //重新获取放入缓存
            return null;
        }
        return getUserAccountFromCacheByUserId(userId);
    }

//    @Override
//    public Long getUserIdByTokenId(@NotBlank String tokenId) {
//        //获取用户主键
//        Long userId = getOriginalUserIdByTokenId(tokenId);
//        if (ObjectUtil.isNull(userId)) {
//            UserAccountDTO userAccount = getUserAccount();
//            if (userAccount != null) {
//                userId = userAccount.getId();
//            }
//        }
//        return userId;
//    }

    /**
     * 按tokenId获取
     *
     * @param tokenId tokenId
     * @return 返回用户主键
     */
    private Long getOriginalUserIdByTokenId(@NotBlank String tokenId) {
        //缓存标识
        String tokenIdCacheKey = getTokenIdUserCacheKey(tokenId);
        //获取用户主键
        return getRootCache().get(tokenIdCacheKey, Long.class);
    }

    @Override
    public void deleteUserCacheByTokenId(@NotBlank String tokenId) {
        Long userId = getOriginalUserIdByTokenId(tokenId);
        if (ObjectUtil.isNull(userId)) {
            log.warn("按tokenId[{}]获取用户主键失败,获取为空", tokenId);
            return;
        }
        UserAccountDTO userAccountDTO = getUserAccountFromCacheByUserId(userId);
        if (userAccountDTO == null) {
            log.warn("按用户主键[{}]获取用户信息失败,获取为空", userId);
            return;
        }
        //移除登录账号与用户主键关联信息
        removeLoginNameCache(userAccountDTO.getUserAccountName());
        //移除用户信息
        removeUserCache(userId);
        //移除token用户信息
        removeUserTokenCache(tokenId, userId);
         // 移除用户上次登录信息
        removeUserLoginInfo(tokenId);
    }

    /**
     * 按用户名移除账号与用户主键的关系缓存
     *
     * @param loginName 账号
     */
    private void removeLoginNameCache(String loginName) {
        //获取缓存标识
        String loginNameCacheKey = getLoginNameCacheKey(loginName);
        log.debug("移除登录账号与用户主键关系缓存,缓存标识[{}]", loginNameCacheKey);
        //如果存在则移除缓存
        getRootCache().evict(loginNameCacheKey);
    }

    /**
     * 移除账号信息
     *
     * @param userId 用户主键
     */
    private void removeUserCache(Long userId) {
        //获取缓存标识
        String userCacheKey = getUserCacheKey(userId);
        log.debug("移除用户信息,缓存标识[{}]", userCacheKey);
        //如果存在则移除缓存
        getRootCache().evict(userCacheKey);
    }

    /**
     * 移除用户账号信息信息
     *
     * @param tokenId tokenId
     * @param userId  用户主键
     */
    private void removeUserTokenCache(String tokenId, Long userId) {
        Cache cache = getRootCache();
        //获取缓存标识
        String tokenIdUserCacheKey = getTokenIdUserCacheKey(tokenId);
        log.debug("移除token用户信息,缓存标识[{}]", tokenIdUserCacheKey);
        //如果存在则移除缓存
        cache.evict(tokenIdUserCacheKey);

        //获取缓存标识
        String userTokenIdCacheKey = getUserTokenIdCacheKey(userId);
        log.debug("移除用户token信息,缓存标识[{}]", userTokenIdCacheKey);
        //如果存在则移除缓存
        cache.evict(userTokenIdCacheKey);
    }

//    private UserAccountDTO cacheUser(@NotNull Long userId) {
//        String tokenId = TokenUtil.getTokenID();
//        return cacheUser(tokenId, userId);
//    }

    private UserAccountDTO getUserAccountByUserIdFromDb(@NotNull Long userId) {
        //AuthAssertUtils.isNotNull(userAccount, AuthErrorCodeEnum.C0319.code(), String.format("主键:[%d],查询账号表数据不存在", userId));
        if (userId == null){
            log.info("getUserAccountFromDb 入参 userId is null");
            return null;
        }
        return userAccountService.getUserAllInfoByUserId(userId);
    }
    /**
     * 缓存用户信息
     *
     * @param tokenId     tokenId
     * @param userAccount 用户账号信息
     * @return 返回用户账号信息
     */
    private UserAccountDTO putUserToCache(String tokenId, UserAccountDTO userAccount) {
        //获取生成账号缓存标识
        String userCacheKey = getUserCacheKey(userAccount.getId());
        // 租户应用信息
        setLesseeAppCodeToUserAccount(userAccount);
        // 缓存用户信息
        try {

            getRootCache().put(userCacheKey, objectMapper.writeValueAsString(userAccount));

            log.debug("缓存用户信息：用户主键:[{}]-缓存标识:[{}]", userAccount.getId(), userCacheKey);
        } catch (JsonProcessingException e) {
            log.warn("json lastSuccessLoginInfo 序列化失败,不影响功能，继续运行.msg:{},{}",userAccount,e.getMessage());
        }


        // 缓存用户基本信息
        putUserIdToCacheByKeyAccountName(userAccount);
        //缓存token用户信息
        cacheTokenUserRelation(tokenId, userAccount.getId());
        // 用户最后登录信息 转移到调用接口再缓存，
        // cacheUserLoginInfo(tokenId, userAccount);

        return userAccount;
    }

    /**
     * 缓存登录名与账号的信息
     *
     * @param userAccount 用户对象
     */
    private void putUserIdToCacheByKeyAccountName(UserAccountDTO userAccount) {
        Long userId = userAccount.getId();
        Map<String, Object> account = userAccount.getAccount();
        if (ObjectUtil.isNull(userId) || MapUtils.isEmpty(account)) {
            return;
        }
        if (ObjectUtil.isNotNull(userAccount.getId()) && StringUtils.isNotBlank(userAccount.getUserAccountName())) {
            String cacheKey = getLoginNameCacheKey(userAccount.getUserAccountName());
            log.debug("缓存账号与主键关联信息：用户主键:[{}]-缓存标识:[{}]-账号:[{}]-缓存值{}", userAccount.getId(), cacheKey, userAccount.getUserAccountName(), userAccount.getId());
            getRootCache().put(cacheKey, userAccount.getId());
        }
    }

    /**
     * 缓存token用户信息
     *
     * @param tokenId tokenId
     * @param userId  用户主键
     */
    private void cacheTokenUserRelation(String tokenId, Long userId) {
        //获取获取数据
        Cache cache = getRootCache();
        //获取token与用户
        cacheTokenUser(cache, tokenId, userId);
        //获取用户与token
        cacheUserToken(cache, tokenId, userId);

    }

    /**
     * 缓存token用户信息
     *
     * @param tokenId tokenId
     * @param userId  用户主键
     */
    private void cacheUserToken(Cache cache, String tokenId, Long userId) {
        if (ObjectUtil.isNotNull(userId) && StringUtils.isNotBlank(tokenId)) {
            String userTokenIdCacheKey = getUserTokenIdCacheKey(userId);
            log.debug("缓存用户主键与token关联信息：用户主键:[{}]-缓存标识:[{}]-tokenId:[{}]-缓存值{}", userId, userTokenIdCacheKey, tokenId, tokenId);
            cache.put(userTokenIdCacheKey, tokenId);
        }
    }

    /**
     * 缓存token与用户信息
     *
     * @param cache   缓存
     * @param tokenId tokenId
     * @param userId  用户主键
     */
    private void cacheTokenUser(Cache cache, String tokenId, Long userId) {
        if (ObjectUtil.isNotNull(userId) && StringUtils.isNotBlank(tokenId)) {
            String tokenIdUserCacheKey = getTokenIdUserCacheKey(tokenId);
            log.debug("缓存token与用户主键关联信息：用户主键:[{}]-缓存标识:[{}]-tokenId:[{}]-缓存值{}", userId, tokenIdUserCacheKey, tokenId, userId);
            cache.put(tokenIdUserCacheKey, userId);
        }
    }

    /**
     * 生成缓存用户信息标识
     *
     * @param userId 用户主键
     * @return 返回标识名
     */
    private String getUserCacheKey(Long userId) {
        return UserCacheKeyConst.COMPLETE_USER_PREFIX + userId;
    }

    /**
     * 根据登录名获取保存用户ID的标识
     *
     * @param loginName 登录名
     * @return 返回标识
     */
    private String getLoginNameCacheKey(String loginName) {
        return UserCacheKeyConst.LOGIN_NAME_PREFIX + loginName.toLowerCase();
    }

    /**
     * 根据tokenId获取保存用户ID的标识
     *
     * @param tokenId tokenId
     * @return 返回标识
     */
    private String getTokenIdUserCacheKey(String tokenId) {
        return UserCacheKeyConst.TOKEN_USER_PREFIX + tokenId.toLowerCase();
    }

    /**
     * 根据用户ID获取保存tokenId的标识
     *
     * @param userId 用户主键
     * @return 返回标识
     */
    private String getUserTokenIdCacheKey(Long userId) {
        return UserCacheKeyConst.USER_TOKEN_PREFIX + userId;
    }

    /**
     * 根据tokenId获取登录信息的标识
     *
     * @param tokenId tokenId
     * @return 返回标识
     */
    private String getTokenIdUserLoginInfoCacheKey(String tokenId) {
        return UserCacheKeyConst.AUTH_USER_LAST_LOGIN_PREFIX + tokenId.toLowerCase();
    }

    /**
     * 按用户主键获取tokenId
     *
     * @param userId 用户主键
     * @return 返回tokenId
     */
    private String getTokenIdByUserId(@NotBlank Long userId) {
        //缓存标识
        String userTokenIdCacheKey = getUserTokenIdCacheKey(userId);
        //获取tokenId
        return getRootCache().get(userTokenIdCacheKey, String.class);
    }

    /**
     * 获取根部缓存信息
     *
     * @return 返回缓存对象
     */
    private Cache getRootCache() {
        return cacheManager.getCache(ROOT_CACHE_NAME);
    }

    /**
     * 从库里加载用户信息并缓存
     *
     * @return 返回用户账号信息
     */
    private UserAccountDTO loadUserAccountByLoginNameAndCache() {
        UserAccountDTO userAccountDTO = getUserAccountByLoginFromDb();
        if (userAccountDTO == null) {
            return null;
        }
        return putUserToCache(TokenUtil.getTokenID(), userAccountDTO);
    }

    /**
     * 按登录模式获取用户账号信息
     *
     * @return 返回账号信息
     */
    private UserAccountDTO getUserAccountByLoginFromDb() {
        String loginName = LoginUtil.getLoginName();
        return getUserAccountByLoginFromDb(loginName);
    }
    private UserAccountDTO getUserAccountByLoginFromDb(String loginName){
        if (StringUtils.isNotBlank(loginName)) {
           return userAccountService.getUserAllInfoByUserName(loginName);
        }
        log.debug("从数据库中获取账号信息为空");
        return null;
    }

}

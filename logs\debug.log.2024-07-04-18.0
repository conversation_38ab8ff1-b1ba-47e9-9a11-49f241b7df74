2024-07-04 18:00:00.015 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 17:45:00,014 to 2024-07-04 18:00:00,014
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 18:06:01.537 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:01.840 [,,,,Not Auth Request!] [Thread-49] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-04 18:06:05.430 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-04 18:06:05.655 [,,,,Not Auth Request!] [Thread-62] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=21128&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:05.717 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://**************:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=21128&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:05.822 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:05.935 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:06.620 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:06.767 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:07.500 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:13.895 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Unexport service: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:15.486 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.qos.server.Server -  [DUBBO] qos-server stopped., dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:15.495 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.rpc.protocol.injvm.InjvmProtocol -  [DUBBO] Unexport service: injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:15.822 [,,,,Not Auth Request!] [Thread-62] WARN  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] All registry instances have been destroyed, failed to fetch any instance. Usually, this means no need to try to do unnecessary redundant resource clearance, all registries has been taken care of., dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:16.253 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] unregister listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:16.305 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] unregister listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:16.356 [,,,,Not Auth Request!] [Exporter-Unexport-thread-1] INFO  o.a.dubbo.registry.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:17.068 [,,,,Not Auth Request!] [Thread-62] WARN  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] All registry instances have been destroyed, failed to fetch any instance. Usually, this means no need to try to do unnecessary redundant resource clearance, all registries has been taken care of., dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:17.069 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] unregister listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:17.720 [,,,,Not Auth Request!] [Thread-62] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] unregister listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: *********
2024-07-04 18:06:18.761 [,,,,Not Auth Request!] [Thread-62] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is about to shutdown..., dubbo version: 2.7.8, current host: *********

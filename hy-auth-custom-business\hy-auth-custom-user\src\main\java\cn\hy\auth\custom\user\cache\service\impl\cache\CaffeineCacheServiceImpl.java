package cn.hy.auth.custom.user.cache.service.impl.cache;

import cn.hy.auth.custom.user.cache.annotation.NativeCacheHandle;
import cn.hy.auth.custom.user.cache.constant.UserCacheKeyConst;
import cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;
import cn.hy.auth.custom.user.cache.enums.NativeCacheEnum;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.ConcurrentMap;

/**
 * 类描述: caffeine缓存处理
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/30
 */
@Service
@NativeCacheHandle(value = NativeCacheEnum.CAFFEINE)
public class CaffeineCacheServiceImpl implements NativeDiffCacheService {

    @Override
    public Set<Long> handle(NativeCacheDTO nativeCacheDTO) {
        Cache caffeineCache = (Cache) nativeCacheDTO.getNativeCacheData();
        ConcurrentMap concurrentMap = caffeineCache.asMap();

        //需要返回的用户主键集合
        Set<Long> userIds = Sets.newHashSetWithExpectedSize(concurrentMap.size());
        concurrentMap.forEach((k, v) -> {
            if (StringUtils.startsWith(k.toString(), UserCacheKeyConst.LOGIN_NAME_PREFIX)) {
                userIds.add(Long.valueOf(concurrentMap.get(k).toString()));
            }
        });

        return userIds;
    }
}

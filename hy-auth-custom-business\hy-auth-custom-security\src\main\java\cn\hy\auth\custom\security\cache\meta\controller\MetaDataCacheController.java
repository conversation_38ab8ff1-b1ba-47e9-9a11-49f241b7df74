package cn.hy.auth.custom.security.cache.meta.controller;

import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: MetaDataController
 * @time: 2022-11-23 16:55
 * @desc: 元数据相关接口
 **/
@AllArgsConstructor
@RestController
public class MetaDataCacheController {

    private final MetaDataCacheService metaDataCacheService;

    /**
     * 清除元数据缓存（目前给paas调用）
     * @param lessCode 租户编号
     * @param appCode 应用编号
     * @return 结果
     */
    @PostMapping("/meta/data/cache/clear")
    public Map<String,String> cacheClear(@RequestParam("lessCode") String lessCode, @RequestParam("appCode") String appCode) {
        metaDataCacheService.cacheClear(lessCode, appCode);
        Map<String, String> resultMap = new HashMap<>(1);
        resultMap.put("result", LocaleUtil.getMessage("MetaDataCacheController.controllerMap.msg1", ""));
        return resultMap;
    }
}

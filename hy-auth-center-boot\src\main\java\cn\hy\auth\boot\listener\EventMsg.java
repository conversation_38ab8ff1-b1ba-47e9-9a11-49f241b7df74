package cn.hy.auth.boot.listener;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2023/7/18 16:39
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventMsg implements Serializable {
    private Object source;
    private long timestamp;
    private String lessCode;
    private String appCode;
    private String installPath;
    private boolean isSandBox;
    private AppInstallTypeEnums installType ;
    private String uuid;
}

package cn.hy.auth.common.security.oauth2.extend;

import cn.hy.auth.common.security.core.properties.SecurityProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述：尚未登录情况的错误处理
 * 引入OAUTH2之后,可能不需要该类也可以实现OAUTH2的尚未登录的错误处理流程（校验token的流程）.
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 12:01
 **/
@Slf4j
public class HyLoginUrlAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint {
    /**
     * oauth2 对尚未登录情况的错误处理
     */
    private OAuth2AuthenticationEntryPoint oAuth2AuthenticationEntryPoint = new OAuth2AuthenticationEntryPoint();

    @Autowired(required = false)
    private SecurityProperties securityProperties = new SecurityProperties();

    /**
     * @param loginFormUrl URL where the login page can be found. Should either be
     *                     relative to the web-app context path (include a leading {@code /}) or an absolute
     *                     URL.
     */
    public HyLoginUrlAuthenticationEntryPoint(String loginFormUrl) {
        super(loginFormUrl);
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {
        if (securityProperties.getResponse().isJsonFormat()) {
            // 调用oauth2 的处理方式来处理.
            oAuth2AuthenticationEntryPoint.commence(request, response, authException);
        } else {
            super.commence(request, response, authException);
        }
    }
}
package cn.hy.auth.common.security.oauth2.event;

import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 13:39
 **/
@Getter
@ToString
public class CopyTokenSuccessEvent extends ApplicationEvent {
    private final OAuth2AccessToken token;
    public CopyTokenSuccessEvent(Authentication authentication, OAuth2AccessToken token1) {
        super(authentication);
        this.token = token1;
    }


    public Authentication getAuthentication() {
        return (Authentication) getSource();
    }
}

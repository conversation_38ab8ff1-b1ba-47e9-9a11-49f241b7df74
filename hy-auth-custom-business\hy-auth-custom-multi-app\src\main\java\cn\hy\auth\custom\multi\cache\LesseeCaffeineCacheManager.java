package cn.hy.auth.custom.multi.cache;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 类描述: 继承Caffeine缓存，重新获取cahce方法适应与多租户多应用
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/1
 */
public class LesseeCaffeineCacheManager extends CaffeineCacheManager {

    @Override
    public void setCacheNames(Collection<String> cacheNames) {
        if (ObjectUtil.isNull(cacheNames)) {
            super.setCacheNames(null);
            return;
        }
        String prefix = CachePrefixUtil.get();
        Collection<String> newCacheNames = cacheNames.stream().filter(ObjectUtil::isNotNull).map(name -> prefix + name).collect(Collectors.toSet());
        super.setCacheNames(newCacheNames);
    }

    @Override
    public Cache getCache(String name) {
        String prefix = CachePrefixUtil.get();
        return super.getCache(prefix + name);
    }

}

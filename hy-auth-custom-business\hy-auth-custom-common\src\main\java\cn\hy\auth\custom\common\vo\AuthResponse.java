package cn.hy.auth.custom.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/5/29 18:04
 **/
@ApiModel(value = "通用返回对象")
public interface AuthResponse<R> {
    /**
     * 结果
     *
     * @return .
     */
    @ApiModelProperty(value = "结果")
    R getResult();

    /**
     * 返回码
     *
     * @return .
     */
    @ApiModelProperty(value = "返回码")
    String getCode();

    /**
     * 描述信息
     *
     * @return .
     */
    @ApiModelProperty(value = "异常信息")
    String getMsg();

    /**
     * 时间戳
     *
     * @return .
     */
    @ApiModelProperty(value = "时间戳")
    long getTimestamp();
}

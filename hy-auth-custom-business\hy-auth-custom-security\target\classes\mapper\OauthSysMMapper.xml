<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.security.oauth2.environment.dao.OauthSysMMapper">

    <select id="count" resultType="int">
        SELECT COUNT(*) FROM oauth_sys_m
    </select>

    <select id="selectFirstUuid" resultType="java.lang.String">
        SELECT uuid FROM oauth_sys_m LIMIT 1
    </select>

    <insert id="insert" parameterType="cn.hy.auth.custom.security.oauth2.environment.domain.OauthSysMDO">
        insert into oauth_sys_m (id, uuid, gmt_create, gmt_modified)
        values (#{id,jdbcType=DECIMAL},  #{uuid,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
        #{gmtModified,jdbcType=TIMESTAMP})
    </insert>

</mapper>
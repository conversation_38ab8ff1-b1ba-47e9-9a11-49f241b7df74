package cn.hy.auth.custom.security.third.wechat.impl;

import cn.hy.auth.common.security.core.authentication.social.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class HyWechatServiceProvider implements HyServiceProvider {

    @Autowired
    private WechatProviderTokenImpl wechatProviderToken;

    @Autowired
    private WechatUserProviderImpl wechatUserProvider;

    @Autowired
    private WechatProviderInfoImpl wechatProviderInfo;

    @Override
    public HyOauth2Operations getOauth2Operations() {
        return wechatProviderToken;
    }

    @Override
    public HySocialUserInfoApi getSocialUserInfoApi() {
        return wechatUserProvider;
    }

    @Override
    public HyProviderInfoService getProviderInfoService() {
        return  wechatProviderInfo;
    }
}

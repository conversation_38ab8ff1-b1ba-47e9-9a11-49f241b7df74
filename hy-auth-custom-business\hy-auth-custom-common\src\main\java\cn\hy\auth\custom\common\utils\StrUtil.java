package cn.hy.auth.custom.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: zqb
 * @Description: 字符串工具类
 * @CreateDate: 2019/7/29 10:54
 */
public class StrUtil {

    private StrUtil() {
    }

    /**
     * 从用户名+密码的字符串中，删除用户名，并返回密码
     *
     * @param srcText 原始字符串
     * @param prefix  需要移除的字符串
     * @return .
     */
    public static String removeWithStart(String srcText, String prefix) {
        if (StringUtils.isBlank(srcText) || StringUtils.isBlank(prefix)) {
            return srcText;
        }

        if (srcText.length() <= prefix.length()) {
            return srcText;
        }

        String srcPrefix = srcText.substring(0, prefix.length());
        //从源字符串截取前缀进行比对，忽略大小写
        if (srcPrefix.equalsIgnoreCase(prefix)) {
            return srcText.substring(prefix.length());
        } else {
            //如果不是指定字符串开头，则返回原字符串
            return srcText;
        }
    }

    /**
     * 判断是否以特定字符串开头（忽略大小写）
     *
     * @param srcText 原始字符串
     * @param prefix  特定开头字符串
     * @return .
     */
    public static Boolean startsWithIgnoreCase(String srcText, String prefix) {
        if (StringUtils.isBlank(srcText) || StringUtils.isBlank(prefix)) {
            return false;
        }

        if (srcText.length() <= prefix.length()) {
            return false;
        }

        String srcPrefix = srcText.substring(0, prefix.length());
        //从源字符串截取前缀进行比对，忽略大小写
        return srcPrefix.equalsIgnoreCase(prefix);

    }

}

package cn.hy.auth.custom.security.oauth2.event;

import cn.hy.auth.custom.common.context.AuthContext;
import org.springframework.context.ApplicationEvent;

/**
 * 异步事件结构基类
 *
 * <AUTHOR>
 * @date 2020-12-09 10:16
 **/
public abstract class BaseAsyncEvent extends ApplicationEvent {
    private static final long serialVersionUID = 8442035148211712547L;

    /**
     * 租户编码
     */
    private String lesseeCode;
    /**
     * 应用编码
     */
    private String appCode;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public BaseAsyncEvent(Object source) {
        super(source);
        this.lesseeCode = AuthContext.getContext().getLesseeCode();
        this.appCode = AuthContext.getContext().getAppCode();
    }

    /**
     * The object on which the Event initially occurred.
     *
     * @return The object on which the Event initially occurred.
     */
    @Override
    public Object getSource() {
        AuthContext.getContext().setLesseeCode(this.getLesseeCode());
        AuthContext.getContext().setAppCode(this.getAppCode());
        return super.getSource();
    }

    public String getLesseeCode() {
        return lesseeCode;
    }

    public String getAppCode() {
        return appCode;
    }
}

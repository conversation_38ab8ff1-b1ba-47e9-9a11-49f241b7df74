package cn.hy.auth.custom.business.starter;

import cn.hy.auth.custom.security.oauth2.password.HyPasswordEncoder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 类描述: 业务层自动配置类
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/9
 */
@Configuration
public class CommonAuthBusinessAutoConfiguration {

    /**
     * 密码加密
     */
    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        return new HyPasswordEncoder();
    }
}

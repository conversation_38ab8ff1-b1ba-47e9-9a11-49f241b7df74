package cn.hy.auth.custom.security.reauthentication.provider;

import cn.hy.auth.common.security.core.authentication.reauthentication.ReAuthenticationPreProvider;
import cn.hy.auth.common.security.core.authentication.token.PasswordAuthenticationToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

/**
 * 二次密码认证
 *
 * <AUTHOR>
 * @date 2024/6/7 14:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PasswordAuthenticationProvider extends ReAuthenticationPreProvider<PasswordAuthenticationToken> {

    @Override
    protected Authentication doAuthenticate(PasswordAuthenticationToken authentication) {
        // 跳过, 在 PwdAuthenticationProviderer 进行认证
        return authentication;
    }

    @Override
    protected Class<PasswordAuthenticationToken> getAuthentication() {
        return PasswordAuthenticationToken.class;
    }
}

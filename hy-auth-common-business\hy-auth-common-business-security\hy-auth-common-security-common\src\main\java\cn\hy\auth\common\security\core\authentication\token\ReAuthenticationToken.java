package cn.hy.auth.common.security.core.authentication.token;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/11 10:17
 */
public abstract class ReAuthenticationToken extends AbstractAuthenticationToken {

    protected Object principal;

    /**
     * 附加信息
     */
    @Getter
    protected Map<String, Object> additional;

    public ReAuthenticationToken(Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
    }

    /**
     * 认证方式
     */
    public abstract ReAuthenticationType getType();

    public abstract ReAuthenticationToken createSuccessAuthentication(UserDetails userDetails, Authentication authentication);

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) {
        super.setAuthenticated(true);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        PasswordAuthenticationToken that = (PasswordAuthenticationToken) o;
        return Objects.equals(principal, that.principal);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), principal);
    }
}

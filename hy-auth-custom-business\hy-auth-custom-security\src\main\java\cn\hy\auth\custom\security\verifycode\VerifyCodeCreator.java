package cn.hy.auth.custom.security.verifycode;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import cloud.tianai.captcha.validator.impl.SimpleImageCaptchaValidator;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.security.verifycode.domain.SliderCaptchaTrackDTO;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 验证码创建
 * hy-paas-cn.hy.paas.user.account.service.impl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/2/17 15:55
 */
@Slf4j
@Component
public class VerifyCodeCreator {

    /**
     * 验证码缓存
     */
    private static final ConcurrentHashMap<String, VerifyCodeDTO> verifyCodeCache = new ConcurrentHashMap<>(32);

    /**
     * 验证码保留的秒数
     */
    @Value("${hy.paas.verifyCode.live_seconds:300}")
    private Long liveTimeSeconds;
    /**
     * 滑动条验证码结果保留秒数
     */
    private static final Long sliderCaptchaResultLiveTimeSeconds = 60L;

    /**
     * 缓存key
     */
    private static final String CACHE_KEY = "auth-slider-captcha-id";

    /**
     * 一天登陆失败指定次数后启用验证码
     */
    @Value("${hy.security.verifyCode.maxFailedCount:1}")
    private Integer maxFailedCount;

    /**
     * 容错值
     */
    @Value("${hy.security.verifyCode.tolerant:0.03f}")
    private Float tolerant;

    private final ImageCaptchaApplication imageCaptchaApplication;

    private final LoginFailedCounter loginFailedCounter;

    private final CacheManager cacheManager;


    public VerifyCodeCreator(ImageCaptchaApplication imageCaptchaApplication, LoginFailedCounter loginFailedCounter,
                             @Qualifier("hyAuthAutoCacheManager") CacheManager cacheManager) {
        this.imageCaptchaApplication = imageCaptchaApplication;
        this.loginFailedCounter = loginFailedCounter;
        this.cacheManager = cacheManager;
    }

    /**
     * 生成滑块验证码
     * @param loginName 登陆名
     * @return CaptchaResponse<SliderCaptchaVO>
     */
    public CaptchaResponse<ImageCaptchaVO> generateSliderCaptcha(String loginName){
        CaptchaResponse<ImageCaptchaVO> captchaVOCaptchaResponse = imageCaptchaApplication.generateCaptcha(CaptchaTypeConstant.SLIDER);
        log.info("{}生成滑块验证码：{}", loginName, captchaVOCaptchaResponse);
        return captchaVOCaptchaResponse;
    }

    /**
     * 执行验证滑动条验证码
     * @return 验证结果
     */
    public VerifyResult sliderCaptchaVerify(SliderCaptchaTrackDTO sliderCaptchaTrackDTO){
        String originLesseeCode = AuthContext.getContext().getLesseeCode();
        String originAppCode = AuthContext.getContext().getLesseeCode();
        try {
            // 切换上下文
            AuthContext.getContext().setLesseeCode(sliderCaptchaTrackDTO.getLesseeCode());
            AuthContext.getContext().setAppCode(sliderCaptchaTrackDTO.getAppCode());
            // 清除旧数据
            cleanOldCache(sliderCaptchaTrackDTO.getId());
            // 设置容错
            SimpleImageCaptchaValidator simpleImageCaptchaValidator = new SimpleImageCaptchaValidator();
            simpleImageCaptchaValidator.setDefaultTolerant(tolerant);
            // 重新设置校验器
            imageCaptchaApplication.setImageCaptchaValidator(simpleImageCaptchaValidator);
            // 开始验证
            boolean isPassed = imageCaptchaApplication.matching(sliderCaptchaTrackDTO.getId(), sliderCaptchaTrackDTO.getSliderCaptchaTrack());
            if (isPassed) {
                // 缓存验证结果
                addSliderCaptchaResultToCache(sliderCaptchaTrackDTO.getId());
                return new VerifyResult(ReturnCode.SUCCESS_SAAS.code(), "验证通过", sliderCaptchaTrackDTO.getId());
            }else {
                return new VerifyResult(AuthErrorCodeEnum.A0205.code(), "验证不通过");
            }
        }finally {
            // 还原上下文
            AuthContext.getContext().setLesseeCode(originLesseeCode);
            AuthContext.getContext().setLesseeCode(originAppCode);
        }
        
    }

    /**
     * 登陆时再检查验证结果是否过期
     * @param id 验证码ID
     * @return 验证结果
     */
    public VerifyResult recheckSliderCaptchaResultForLogin(String id, String loginName){
        log.info("登陆时再检查验证结果是否过期，验证码ID：{}，loginName:{}", id, loginName);
        //loginName空判断
        if(StringUtils.isEmpty(loginName) ){
            return new VerifyResult(AuthErrorCodeEnum.SUCCESS.code(), AuthErrorCodeEnum.SUCCESS.msg());
        }
        //登陆失败次数
        Long failedCount = getFailedCount(loginName);
        //是否需要验证码
        if(!this.needToVerify(loginName, failedCount)){
            return new VerifyResult(AuthErrorCodeEnum.SUCCESS.code(), AuthErrorCodeEnum.SUCCESS.msg());
        }
        //需要验证但没有传验证码
        if(StringUtils.isEmpty(id)){
            return new VerifyResult(AuthErrorCodeEnum.A0205.code(), AuthErrorCodeEnum.A0205.msg());
        }
        Cache cache = this.getCache();
        if (Objects.isNull(cache)) {
            return new VerifyResult(AuthErrorCodeEnum.A0205.code(), AuthErrorCodeEnum.A0205.msg());
        }
        SliderCaptchaResult sliderCaptchaResult = cache.get(id, SliderCaptchaResult.class);
        // 本地缓存
        if (Objects.nonNull(sliderCaptchaResult)) {
            // 验证通过则清除
            cache.evict(id);
            // 清除失败次数
            loginFailedCounter.cleanIfNeed(loginName);
            return new VerifyResult(AuthErrorCodeEnum.SUCCESS.code(), AuthErrorCodeEnum.SUCCESS.msg());
        }else {
            return new VerifyResult(AuthErrorCodeEnum.A0205.code(), "验证过期或验证未通过");
        }
    }

    /**
     * 取失败次数
     * @param loginName 登陆名
     * @return 失败次数
     */
    private long getFailedCount(String loginName){
        // 这里不需要验证登陆名是否合法，交给登录拦截器/接口去校验
        return loginFailedCounter.getFailedCount(loginName);
    }

    /**
     * 确认需要返回的错误码
     * @param loginName 登陆名
     * @return 返回码
     */
    public AuthErrorCodeEnum confirmReturnCode(String loginName, Long failedCount){
        if(needToVerify(loginName, failedCount)){
            return AuthErrorCodeEnum.A0205;
        }else{
            return AuthErrorCodeEnum.A0300;
        }
    }

    /**
     * 缓存验证结果
     * @param id 验证码id
     */
    private void addSliderCaptchaResultToCache(String id) {
        SliderCaptchaResult sliderCaptchaResult = new SliderCaptchaResult(id, true, System.currentTimeMillis());
        Cache cache = this.getCache();
        if (Objects.nonNull(cache)) {
            cache.put(id, sliderCaptchaResult);
        }
    }

    /**
     * 返回缓存的所有普通验证码
     * @return 验证码
     */
    public String getAllCacheJson(){
        return JSON.toJSONString(verifyCodeCache);
    }

    private void clean() {
        //清除普通验证码
        for (String key : verifyCodeCache.keySet()) {
            VerifyCodeDTO verifyCodeDTO = verifyCodeCache.get(key);
            if (isTimeout(liveTimeSeconds, verifyCodeDTO.getTimeMillis())) {
                verifyCodeCache.remove(key);
            }
        }
    }

    /**
     * 清理旧缓存
     * @param id 验证码主键
     */
    private void cleanOldCache(String id) {
        Cache cache = this.getCache();
        if (Objects.nonNull(cache)) {
            SliderCaptchaResult sliderCaptchaResult = cache.get(id, SliderCaptchaResult.class);
            if (Objects.nonNull(sliderCaptchaResult) && isTimeout(sliderCaptchaResultLiveTimeSeconds, sliderCaptchaResult.getTimeMillis())) {
                cache.evict(id);
            }
        }
    }

    /**
     * 校验验证码时间是否超时
     * @param standardLive 验证码当前时间
     * @param startTimeMillis 验证码开始时间
     * @return 校验结果
     */
    private boolean isTimeout(Long standardLive, Long startTimeMillis) {
        long timeDifferences = System.currentTimeMillis() - startTimeMillis;
        long seconds = timeDifferences/1000;
        return seconds> standardLive;
    }

    /**
     *  是否需要使用验证码
     * @param  loginName 登陆账号
     * @return true：需要；false：不需要
     */
    public boolean needToVerify(String loginName, Long failedCount) {
        log.info("{}是否需要验证码，失败次数{}", loginName, failedCount);
        if(failedCount!=null){
            return maxFailedCount <= failedCount;
        }
        return false;
    }

    /**
     * 获取缓存
     * @return 缓存
     */
    private Cache getCache() {
        return cacheManager.getCache(CACHE_KEY);
    }

    @Data
    @AllArgsConstructor
    public static class VerifyCodeDTO implements Serializable {
        /**
         * 验证码
         */
        @JsonIgnore
        private String code;
        /**
         * 图片Base64
         */
        private String imgBase64;
        /**
         * 验证码图片ID，用于提交验证
         */
        private String id;
        /**
         * 生成的时间辍
         */
        private long timeMillis;
    }

    @Data
    @AllArgsConstructor
    public static class VerifyResult implements Serializable{
        String code;
        String msg;
        String verCodeId;

        public VerifyResult(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Data
    @AllArgsConstructor
    public static class SliderCaptchaResult implements Serializable{
        /**
         * 验证码ID
         */
        private String id;
        /**
         * 成功还是失败
         */
        private boolean result;
        /**
         * 生成的时间辍
         */
        private long timeMillis;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hy.auth.custom.route.dao.OauthRequestRoutingDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hy.auth.custom.route.domain.OauthRequestRouting">
        <id column="id" property="id" />
        <result column="token_area_identify" property="tokenAreaIdentify" />
        <result column="source_url" property="sourceUrl" />
        <result column="target_url" property="targetUrl" />
        <result column="pre_route_script" property="preRouteScript" />
        <result column="success_script" property="successScript" />
        <result column="failure_script" property="failureScript" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="target_address" property="targetAddress" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, system_area_identify, source_url, target_url, pre_route_script,success_script, failure_script, gmt_modified, gmt_create, target_address
    </sql>
    <select id="selectBySystemAreaIdentify" resultType="cn.hy.auth.custom.route.domain.OauthRequestRouting">
        select <include refid="Base_Column_List"/> from oauth_request_routing where system_area_identify = #{systemAreaIdentify} and source_url = #{sourceUrl} limit 1
    </select>

</mapper>

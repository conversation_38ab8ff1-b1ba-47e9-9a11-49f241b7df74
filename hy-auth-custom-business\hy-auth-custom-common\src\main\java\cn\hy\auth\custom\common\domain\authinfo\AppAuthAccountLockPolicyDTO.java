package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账户锁定策略
 * 从指定的表里查询是锁定了。
 * 登录时判断是否锁定，同时判断是否能解锁（锁定有效期）。
 * 1、账号锁定表：id，uid，lock_start_time,lock_end_time,ip,mac,client_type.
 * 2、登录错误记录表usr_series_login_error_info：id，uid，logintime，error_count.
 * 如果启用该策略的话，会通过元数据引擎接口往插入表
 *
 * <AUTHOR>
 * @date 2020-12-03 16:33
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthAccountLockPolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = -555347941404361346L;

    /**
     * 登陆错误多少次，就锁定账户
     * 默认为空，值范围：4~15
     */
    private String loginErrorThreshold;
    /**
     * 锁定类型
     * forever（永久锁定）,minuteTime（指定分钟锁定）,sameDay（当天内锁定）
     */
    private String lockType;
    /**
     * 当lock_type为minuteTime时的分钟值
     */
    private String lockMinute;
}

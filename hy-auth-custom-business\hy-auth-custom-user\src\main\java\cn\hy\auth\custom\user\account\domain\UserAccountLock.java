package cn.hy.auth.custom.user.account.domain;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * user_account_lock
 * 用户账号锁定表
 * <AUTHOR>
 * @date   2022/04/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserAccountLock {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 创建人主键
     */
    private BigDecimal createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * ip
     */
    private String ip;

    /**
     * 结束锁定的时间
     */
    private Date lockEndTime;

    /**
     * 客户端类型
     */
    private String clientType;

    /**
     * 最后修改人主键
     */
    private BigDecimal lastUpdateUserId;

    /**
     * mac
     */
    private String mac;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 用户主键
     */
    private BigDecimal userId;

    /**
     * 开始锁定的时间
     */
    private Date lockStartTime;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    private String lockRecodeType;
}
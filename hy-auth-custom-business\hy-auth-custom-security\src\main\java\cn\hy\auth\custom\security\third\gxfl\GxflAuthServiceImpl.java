package cn.hy.auth.custom.security.third.gxfl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hy.auth.common.security.core.authentication.gxfl.bean.GxflProviderUser;
import cn.hy.auth.common.security.core.authentication.gxfl.service.GxflAuthService;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.params.service.impl.ParamsConfigServiceImpl;
import cn.hy.auth.custom.common.utils.UUIDRandomUtils;
import cn.hy.auth.custom.security.common.SqlCreateUtil;
import cn.hy.dataengine.relocate.utils.TableRelocateUtil;
import cn.hy.metadata.engine.api.md.vo.TableMData;
import com.alibaba.fastjson.JSONObject;
import com.szgx.scmp.gateway.sdk.constant.SdkConstant;
import com.szgx.scmp.gateway.sdk.enums.HttpMethod;
import com.szgx.scmp.gateway.sdk.enums.ParamPosition;
import com.szgx.scmp.gateway.sdk.model.ApiRequest;
import com.szgx.scmp.gateway.sdk.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.social.oauth2.AbstractOAuth2ApiBinding;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/3/13
 */
@Service
@Slf4j
public class GxflAuthServiceImpl extends AbstractOAuth2ApiBinding implements GxflAuthService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private TableRelocateUtil tableRelocateUtil;
    @Autowired
    private ParamsConfigServiceImpl paramsConfigServiceImpl;

    @Override
    public GxflProviderUser getUserInfo(String lesseeCode, String appcode, String code,Map<String, Object> requestInfo) {
        GxflProviderUser providerUser = new GxflProviderUser();
        try {
            String loginType = (String) requestInfo.get("login_type");
            // loginType == app 就是应用  否则就是网页
            if (loginType.equals("app")){
                JSONObject userInfoByToken = getUserInfoByToken(lesseeCode,appcode,code);
                if (!userInfoByToken.getBoolean("flag") || !userInfoByToken.getString("errMsg").equals("success") || userInfoByToken.getJSONObject("data") == null){
                    throw new AuthBusinessException("500", "获取第三方用户信息失败!" + "返回错误编码：【"+ userInfoByToken.getString("errCode") +"】，返回错误信息：【" + userInfoByToken.getString("errMsg") + "】");
                }
                JSONObject data = userInfoByToken.getJSONObject("data");
                String userType = data.getString("userType");
                JSONObject userInfo = null;
                // 2 是企业
                if (userType.equals("2")){
                    userInfo = data.getJSONObject("corporationInfo");
                }else {
                    userInfo = data.getJSONObject("personalInfo");
                }
                if (userInfo == null){
                    throw new AuthBusinessException("500", "获取第三方用户信息失败！ 返回错误编码：【"+ userInfoByToken.getString("errCode") +"】，返回错误信息：【" + userInfoByToken.getString("errMsg") + "】");
                }
                providerUser.setId(userInfo.getString("id"));
                providerUser.setUserAccountName(userInfo.getString("loginName"));
                providerUser.setUserName(userType.equals("2")?userInfo.getString("enterpriseName"):userInfo.getString("userName"));
                providerUser.setToken(code);
                providerUser.setLesseeCode(lesseeCode);
                providerUser.setAppCode(appcode);
                providerUser.setUserInfoJson(userInfo.toJSONString());
                return providerUser;
            }else {
                // 请求获取用户信息 code 就是token
                String ythUserinfoUrl = paramsConfigServiceImpl.getConfigValue(lesseeCode, appcode, "yth_userinfo_url");
                String userInfoUrl = ythUserinfoUrl+"/tokeninfo?access_token="+code;
                String resUserInfo = getRestTemplate().getForObject(userInfoUrl, String.class);
                // 用户信息
                if (resUserInfo == null){
                    throw new AuthBusinessException("500", "获取第三方用户信息失败！请求地址信息：" + userInfoUrl );
                }
                JSONObject resObjUserInfo = JSONObject.parseObject(resUserInfo);
                if (resObjUserInfo.containsKey("errorCode")){
                    throw new AuthBusinessException("500", "获取第三方用户信息失败！返回错误编码：：【" + resObjUserInfo.getString("errorCode") + "】," +
                            "返回结果：【" + resObjUserInfo.getString("message") + "】");
                }
                // 用户类型  1  个人  2  企业  3 代办人企业
                String userType = resObjUserInfo.getString("userType");
                JSONObject userInfo = resObjUserInfo.getJSONObject("userInfo");
                if (userType == null || userInfo == null){
                    throw new AuthBusinessException("500", "获取第三方用户信息失败！返回错误编码：：【" + resObjUserInfo.getString("errorCode") + "】," +
                            "返回结果：【" + resObjUserInfo.getString("message") + "】");
                }
                providerUser.setId(userInfo.getString("id"));
                providerUser.setUserAccountName(userInfo.getString("loginName"));
                providerUser.setUserName(userType.equals("2")?userInfo.getString("enterpriseName"):userInfo.getString("userName"));
                providerUser.setToken(code);
                providerUser.setLesseeCode(lesseeCode);
                providerUser.setAppCode(appcode);
                providerUser.setUserInfoJson(userInfo.toJSONString());
                return providerUser;
            }
        }catch (Exception e){
            throw new AuthBusinessException("500", "" + e.getMessage() + "");
        }
    }

    @Override
    public Map<String, Object> getByUserAccount(GxflProviderUser userInfo) {
        List<Map<String, Object>> resultMap = findUserAccountName(userInfo.getLesseeCode(), userInfo.getAppCode(), userInfo.getUserAccountName());
        if (!CollectionUtils.isEmpty(resultMap)) {
            return resultMap.get(0);
        }
        return null;
    }

    @Override
    public Boolean registerUser(GxflProviderUser userInfo) {
        Map<String, Object> parseResult = new HashMap<>();
        parseResult.put("id", BigDecimal.valueOf(UUIDRandomUtils.getSnowUuid()));
        parseResult.put("password", "");
        //parseResult.put("old_id", userInfo.getId());
        parseResult.put("user_account_name", userInfo.getUserAccountName());
        parseResult.put("username", userInfo.getUserName());
        parseResult.put("create_user_id", 1);
        parseResult.put("last_update_user_id", 1);
        parseResult.put("is_system_recode", "0");
        //parseResult.put("sequence", 1);
        parseResult.put("data_version", "1");
        parseResult.put("create_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        parseResult.put("last_update_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        log.info("组装用户参数：{}", parseResult);
        String insertSql = SqlCreateUtil.createInsertSql(getTableName(userInfo.getLesseeCode(), userInfo.getAppCode(), "user_account"), parseResult, true);
        jdbcTemplate.execute(insertSql);
        return Boolean.TRUE;
    }

    @Override
    public String getTableName(String lesseeCode, String appCode, String tableCode) {
        Optional<TableMData> tableMeta = tableRelocateUtil.getTableByCode(tableCode);
        if (tableMeta.isPresent()) {
            TableMData tableMData = tableMeta.get();
            lesseeCode = tableMData.getLessCode();
            appCode = tableMData.getAppCode();
        }
        return lesseeCode + "_" + appCode + "_" + tableCode;
    }

    /**
     * 查询用户信息
     * @param lesseeCode 租户
     * @param appCode 应用
     * @param user_account 账号
     * @return 。
     */
    private List<Map<String, Object>> findUserAccountName(String lesseeCode, String appCode, String user_account) {
        StringBuilder sqlBuilder = new StringBuilder(128);
        sqlBuilder.append(" select * from " + getTableName(lesseeCode, appCode, "user_account ") + " where user_account_name = ? ");
        return jdbcTemplate.queryForList(sqlBuilder.toString(), user_account);
    }

    /**
     * 获取用户基本信息
     * @return ..
     */
    private JSONObject getUserInfoByToken(String lesseeCode, String appcode,String token) {
        // 获取配置信息
        String zgtAppKey = paramsConfigServiceImpl.getConfigValue(lesseeCode, appcode, "ZgtAppKey");
        String zgtAppSecret = paramsConfigServiceImpl.getConfigValue(lesseeCode, appcode, "ZgtAppSecret");
        String zgtIp = paramsConfigServiceImpl.getConfigValue(lesseeCode, appcode, "zgt_ip");
        String zgtLoginUri = paramsConfigServiceImpl.getConfigValue(lesseeCode, appcode, "zgt_login_uri");
        // 请求获取用户信息
        ZgtWstRestClient zgtWstRestClient = new ZgtWstRestClient(zgtAppKey, zgtAppSecret, zgtIp);
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, zgtLoginUri);
        request.addParam("token", token, ParamPosition.QUERY, true);
        request.addParam("serviceId", "1", ParamPosition.QUERY, true);
        request.addParam("roleCode", "", ParamPosition.QUERY, true);
        ApiResponse response = zgtWstRestClient.sendSyncRequest(request);
        if (response == null){
            throw new AuthBusinessException("500", "获取第三方用户信息失败！参数：" + request.getFormParams() + "," +
                    "返回结果：【" + response.getMessage()+ "】");
        }
        String body = new String(response.getBody(), SdkConstant.CLOUDAPI_ENCODING);
        return JSONObject.parseObject(body);
    }

}

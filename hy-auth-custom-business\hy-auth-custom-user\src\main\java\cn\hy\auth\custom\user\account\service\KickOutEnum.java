package cn.hy.auth.custom.user.account.service;

import lombok.Getter;

/**
 * KickOutEnum：剔人的类型
 *
 * <AUTHOR>
 * @version 2024/10/16 19:47
 **/
@Getter
public enum KickOutEnum {
    /**
     * 登录竞争踢人
     */
    LOGIN_COMPETITION("Token has been kicked out", "A0401"),
    /**
     * 重置密码踢人
     */
    RESET_PWD("Token has been kicked out", "A0402"),
    /**
     * 其他踢人
     */
    OTHER("Token has been kicked out", "A0403");
    private final String msg;
    private final String code;

    KickOutEnum(String msg, String code) {
        this.msg = msg;
        this.code = code;
    }


    /**
     * 根据给定的代码获取对应的 KickOutEnum 枚举值。
     *
     * @param code 要查找的代码
     * @return 对应的 KickOutEnum 枚举值，如果找不到则返回 null
     */
    public static KickOutEnum getByCode(String code) {
        // 检查 code 是否为 null
        if (code == null) {
            return null;
        }
        // 假设 KickOutEnum 是一个枚举类，实现具体的查找逻辑
        for (KickOutEnum kickOutEnum : KickOutEnum.values()) {
            if (kickOutEnum.getCode().equals(code)) {
                return kickOutEnum;
            }
        }
        // 如果没有找到匹配的枚举值，返回 null
        return null;
    }


}

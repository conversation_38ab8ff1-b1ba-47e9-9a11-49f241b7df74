package cn.hy.auth.custom.common.utils;

import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 类描述: SDK公共错误提示类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/16
 */
public class AuthAssertUtils extends Assert {

    private AuthAssertUtils() {

    }

    /**
     * 如果str不为空，则抛异常
     *
     * @param str       待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void isBlank(String str, String errorCode, String msg) {
        if (StringUtils.isNotBlank(str)) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

    /**
     * 如果str为空，则抛异常
     *
     * @param str       待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void isNotBlank(String str, String errorCode, String msg) {
        if (StringUtils.isBlank(str)) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

    /**
     * 如果object是null，则抛异常
     *
     * @param object    待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void isNotNull(Object object, String errorCode, String msg) {
        if (Objects.isNull(object)) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

    /**
     * 如果object是null，则抛异常
     *
     * @param object    待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void notNull(@Nullable Object object, String errorCode, String msg) {
        if (object == null) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

    /**
     * value为false时抛异常
     *
     * @param value     待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void isTrue(boolean value, String errorCode, String msg) {
        if (!value) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

    /**
     * value为true时抛异常
     *
     * @param value     待判断值
     * @param errorCode 错误编码
     * @param msg       错误描述信息
     */
    public static void isFalse(boolean value, String errorCode, String msg) {
        if (value) {
            throw new AuthBusinessException(errorCode, msg);
        }
    }

}

package cn.hy.auth.custom.user.history.service.impl;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import cn.hy.auth.custom.common.utils.AuthAssertUtils;
import cn.hy.auth.custom.common.utils.IpUtils;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.history.dao.UserLoginInfoMapper;
import cn.hy.auth.custom.user.history.domain.UserLoginInfoDO;
import cn.hy.auth.custom.user.history.mapstruct.LoginInfoStructMapper;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 类描述: 登录信息
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/17
 */
@Slf4j
@AllArgsConstructor
@Service
public class AuthLoginInfoServiceImpl implements AuthLoginInfoService {

    private final UserLoginInfoMapper userLoginInfoMapper;

    @Override
    public int insert(UserLoginInfoDTO record) {
        return userLoginInfoMapper.insert(LoginInfoStructMapper.INSTANCE.dtoTodo(record));
    }

    @Override
    public UserLoginInfoDTO selectByPrimaryKey(Long id) {
        UserLoginInfoDO userLoginInfoDO = userLoginInfoMapper.selectByPrimaryKey(id);
        return LoginInfoStructMapper.INSTANCE.doToDto(userLoginInfoDO);
    }

    @Override
    public UserLoginInfoDTO getLastSuccessLoginInfo(@NotBlank String clientId, @NotBlank String userName) {
        UserLoginInfoDO userLoginInfoDO = userLoginInfoMapper.getLastSuccessLoginInfo(clientId,userName);
        return LoginInfoStructMapper.INSTANCE.doToDto(userLoginInfoDO);
    }

    @Override
    public UserLoginInfoDTO getLastSuccessLoginInfo(String clientId, Long userId) {
        //AuthAssertUtils.isNotNull(userId, AuthErrorCodeEnum.A0201.code(), AuthErrorCodeEnum.A0201.msg());
        if (userId == null) {
            // 没有用户账号信息，登录信息为null。修复bug QX202303170007
            return null;
        }
        List<UserLoginInfoDO> loginInfoArray = userLoginInfoMapper.getLastTwoSuccessLoginInfo(clientId, String.valueOf(userId));
        if (CollectionUtils.isEmpty(loginInfoArray)) {
            //登录信息为空
            return null;
        }
        UserLoginInfoDTO loginInfoDTO;
        //获取最后登录的两条记录,第一条为当次的登录，第二条为上次的登录
        if (2 == loginInfoArray.size()) {
            //含有上次登录信息
            UserLoginInfoDO secondUserLoginInfoDO = loginInfoArray.get(1);
            UserLoginInfoDO firstUserLoginInfoDO = loginInfoArray.get(0);
            loginInfoDTO = LoginInfoStructMapper.INSTANCE.doToDto(secondUserLoginInfoDO);
            loginInfoDTO.setLastLoginIp(secondUserLoginInfoDO.getIp());
            loginInfoDTO.setLastRealLoginTime(secondUserLoginInfoDO.getCreateTime());
            loginInfoDTO.setCurrentIp(firstUserLoginInfoDO.getIp());
            loginInfoDTO.setCurrentLoginTime(firstUserLoginInfoDO.getCreateTime());
            log.debug("获取最后一次登录信息，userId={},clientId={},Detail:{}", userId, clientId, JSON.toJSONString(loginInfoDTO));
        } else {
            //第一次登陆
            loginInfoDTO = UserLoginInfoDTO.builder().build();
            UserLoginInfoDO firstUserLoginInfoDO = loginInfoArray.get(0);
            //最后一次登录时间
            loginInfoDTO.setLastLoginTime(firstUserLoginInfoDO.getCreateTime());
            //最新的登录ip作为当前ip
            loginInfoDTO.setCurrentIp(firstUserLoginInfoDO.getIp());
            loginInfoDTO.setCurrentLoginTime(firstUserLoginInfoDO.getCreateTime());
        }
        return loginInfoDTO;
    }

    @Override
    public Long getMaxSequence() {
        return userLoginInfoMapper.getMaxSequence();
    }

}

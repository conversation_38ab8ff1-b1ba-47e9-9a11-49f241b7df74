package cn.hy.auth.common.security.core.authentication.social.service;

/**
 * 类描述：负责和第三方开放平台交互相关组件的工厂
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/9 14:56
 **/
public abstract class HyConnectionFactory {
    /**
     * 第三方开放平台标识
     */
    private final String providerId;
    private final HyServiceProvider serviceProvider;

    protected HyConnectionFactory(String providerId, HyServiceProvider serviceProvider) {
        this.providerId = providerId;
        this.serviceProvider = serviceProvider;
    }

    public String getProviderId() {
        return providerId;
    }

    public HyServiceProvider getServiceProvider() {
        return serviceProvider;
    }
}

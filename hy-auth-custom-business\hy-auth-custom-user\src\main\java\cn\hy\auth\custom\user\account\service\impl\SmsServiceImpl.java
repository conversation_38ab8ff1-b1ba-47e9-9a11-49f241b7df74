package cn.hy.auth.custom.user.account.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hy.auth.common.security.core.authentication.mobile.service.SmsCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.service.impl.SmsCodeMapServiceImpl;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.authentication.validate.ValidateErrorService;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.auth.custom.common.enums.UserAccountLockType;
import cn.hy.auth.custom.common.enums.UserResetPasswordType;
import cn.hy.auth.custom.user.account.domain.CheckUpdatePasswordDTO;
import cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO;
import cn.hy.auth.custom.user.account.service.ForgetPasswordService;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SmsServiceImpl extends ForgetPasswordService implements ValidateErrorService {

    private final static String SMS_PREFIX = "SMS_CODE_";

    protected String ERROR_NUMBER_PREFIX_SMS = ERROR_NUMBER_PREFIX + "SMS_";

    @Autowired
    private UserAccountLockService userAccountLockService;

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private SmsCodeSender smsCodeSender;

    @Autowired
    private AppConfigParamUtil appConfigParamUtil;

    @Autowired
    private ForgetPasswordCacheManage cacheManage;

    @Override
    public Map<String, Object> doCheck(CheckUpdatePasswordDTO checkUpdatePasswordDTO) {
        String smsCode = checkUpdatePasswordDTO.getSmsCode();
        String phoneNumber = checkUpdatePasswordDTO.getPhoneNumber();

        String smsCodeCacheKey = SMS_PREFIX + phoneNumber;
        String errorNUmberCacheKey = ERROR_NUMBER_PREFIX_SMS + phoneNumber;

        SmsCodeMapServiceImpl.SmsCode cacheSmsCode = (SmsCodeMapServiceImpl.SmsCode) cacheManage.get(smsCodeCacheKey);
        Map<String, Object> resultMap = new HashMap<>();
        String message = "";
        String tempPermissionIdentification = "";
        boolean result = false;
        Object ifPresent = cacheManage.get(errorNUmberCacheKey);
        Integer errorNumber = ifPresent == null ? 0 : (Integer) ifPresent;
        // 查询是否有锁定记录
        Map<String, Object> judgeMap = userAccountLockService.isUserAccountLockByUserNameOrMobile(phoneNumber);
        Boolean judge = (Boolean) judgeMap.get("judge");
        Boolean userExist = (Boolean) judgeMap.get("userExist");
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        if (!userExist) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg1", "");
        } else if (judge) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg2", "");
        } else if (cacheSmsCode == null || cacheSmsCode.isExpried()) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg3", "");
        } else if (!smsCode.equals(cacheSmsCode.getCode())) {

            Object errorLimitObj = appConfigParamUtil.getByKey(paramMap.get("lessee_code"), paramMap.get("app_code"), "sysdevForgetPassworSmsErrorLimit");
            Integer errorLimit = errorLimitObj == null ? 5 : Integer.parseInt(errorLimitObj.toString());
            int totalErrorNumber = errorNumber + 1;
            cacheManage.put(errorNUmberCacheKey, totalErrorNumber);
            message = String.format(LocaleUtil.getMessage("SmsServiceImpl.resule.msg4", ""), totalErrorNumber, errorLimit - totalErrorNumber);
            if (totalErrorNumber >= errorLimit) {
                message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg5", "");
                userAccountLockService.lockUserAccount("", phoneNumber, UserAccountLockType.MOBILE_SMS.getType());
                cacheManage.invalidate(errorNUmberCacheKey);
            }
        } else {
            result = true;
            String permissionCacheKey = PERMISSION_PREFIX + phoneNumber;
            tempPermissionIdentification = generateTempIdentification(paramMap, Objects.toString(judgeMap.get("userId"), ""), Objects.toString(judgeMap.get("userAccountName"), ""));
            cacheManage.put(permissionCacheKey, tempPermissionIdentification);
            cacheManage.invalidate(smsCodeCacheKey);
            cacheManage.invalidate(errorNUmberCacheKey);
        }
        resultMap.put("tempPermissionIdentification", tempPermissionIdentification);
        resultMap.put("message", message);
        resultMap.put("result", result);
        return resultMap;
    }

    @Override
    public Map<String, Object> sendToThirdService(String type, String account, SmsLoginVO smsLoginVO) {
        String smsCodeCacheKey = SMS_PREFIX + account;
        SmsCodeMapServiceImpl.SmsCode cacheValue = (SmsCodeMapServiceImpl.SmsCode) cacheManage.get(smsCodeCacheKey);
        Map<String, Object> resultMap = new HashMap<>();
        Boolean result = false;
        String message = "";
        if (cacheValue != null) {
            message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg6", "");
        } else {
            String smsCodeStr = RandomStringUtils.randomNumeric(securityProperties.getSmsAuth().getLength());
            log.info("手机号为{}的忘记密码功能手机号找回方式生成的验证码为{}", account, smsCodeStr);
            SmsCodeMapServiceImpl.SmsCode smsCode = new SmsCodeMapServiceImpl.SmsCode(account, smsCodeStr, LocalDateTime.now().plusSeconds(60));

            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            Map<String, String> paramMap = ServletUtil.getParamMap(request);
            smsLoginVO.setSmsType(2);
            String sendResult = smsCodeSender.send(account, smsCodeStr, paramMap.get("app_code"), paramMap.get("lessee_code"), smsLoginVO, "sysdevForgetPasswordSmsLimit");
            if (StringUtils.isNotBlank(sendResult)) {
                message = sendResult;
                result = false;
            } else {
                cacheManage.put(smsCodeCacheKey, smsCode, 1, TimeUnit.MINUTES);
                String errorNUmberCacheKey = ERROR_NUMBER_PREFIX_SMS + account;
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                LocalDateTime tomorrowMidnight = LocalDateTime.of(tomorrow, LocalTime.MIDNIGHT);
                Date date = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
                cacheManage.put(errorNUmberCacheKey, 0, date);
                result = true;
                message = LocaleUtil.getMessage("SmsServiceImpl.resule.msg7", "");
            }
        }
        resultMap.put("result", result);
        resultMap.put("message", message);
        return resultMap;
    }

    @Override
    public String getCheckType() {
        return UserResetPasswordType.PHONE_SMS_CODE.getType();
    }

    @Override
    public boolean checkTempPermission(UserLoginAccountDTO userLoginAccountDTO, String tempPermissionIdentification) {
        return doCheckTempPermission(userLoginAccountDTO, tempPermissionIdentification);
    }

    @Override
    public void clearErrorTime(String mobile, String accountName, String mobileCipherText, Long userId) {
        cacheManage.invalidate(ERROR_NUMBER_PREFIX_SMS + mobile);
    }

}

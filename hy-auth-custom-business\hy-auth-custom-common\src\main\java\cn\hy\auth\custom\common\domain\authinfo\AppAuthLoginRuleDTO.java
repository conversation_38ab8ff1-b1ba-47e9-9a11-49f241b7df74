package cn.hy.auth.custom.common.domain.authinfo;

import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 登录规则
 *
 * <AUTHOR>
 * @date 2020-11-30 15:08
 **/
@Data
public class AppAuthLoginRuleDTO implements Serializable {

    private static final long serialVersionUID = 678389441345471116L;

    /**
     * 应用支持的登录方式
     */
    private List<LoginTypeEnum> loginSupportType;
    /**
     * 支持登录的字段信息列表
     */
    private List<AppAuthLoginFieldDTO> loginField;
    /**
     * 登录竞争策略。
     */
    private AppAuthCompetitionPolicyDTO competitionPolicy;
    /**
     * ip限制策略
     */
    private AppAuthIpAllowPolicyDTO ipAllowListPolicy;
    /**
     * mac限制策略
     */
    private AppAuthMacAllowPolicyDTO macAllowListPolicy;
    /**
     * 登录状态处理策略
     */
    private AppAuthAccountStatusPolicyDTO accountStatusPolicy;
    /**
     * 登录密码状态处理策略
     */
    private AppAuthLoginPwdStatusPolicyDTO pwdStatusPolicy;
    /**
     * 密码过期前提醒策略
     */
    private AppAuthPwdExpirePolicyDTO pwdExpirePolicy;
    /**
     * 账号锁定策略
     */
    private AppAuthAccountLockPolicyDTO accountLockPolicy;

    public AppAuthAccountLockPolicyDTO getAccountLockPolicy() {
        if (accountLockPolicy == null){
            accountLockPolicy = new AppAuthAccountLockPolicyDTO();
        }
        return accountLockPolicy;
    }

    /**
     * 账号有效期处理策略
     */
    private AppAuthAccountValidityDatePolicyDTO accountValidityDatePolicy;

    /**
     * 密码强制更新策略
     */
    private AppAuthPwdUpdatePolicyDTO pwdUpdatePolicy;


    public List<LoginTypeEnum> getLoginSupportType() {
        if (loginSupportType == null) {
            return Collections.emptyList();
        }
        // 所有应用都支持 USB_KEY 认证
        if (!loginSupportType.contains(LoginTypeEnum.USB_KEY)) {
            loginSupportType.add(LoginTypeEnum.USB_KEY);
        }
          // 所有应用都支持 COPY_TOKEN 认证
        if (!loginSupportType.contains(LoginTypeEnum.COPY_TOKEN)) {
            loginSupportType.add(LoginTypeEnum.COPY_TOKEN);
        }
        return loginSupportType;
    }
}

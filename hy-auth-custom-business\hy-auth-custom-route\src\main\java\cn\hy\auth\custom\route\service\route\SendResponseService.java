package cn.hy.auth.custom.route.service.route;

import org.apache.http.HttpResponse;

import javax.servlet.http.HttpServletResponse;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/26 13:43
 **/
public interface SendResponseService {
    /**
     * 发送响应
     * @param servletResponse .
     * @param httpResponse .
     */
     void sendResponse(HttpServletResponse servletResponse, HttpResponse httpResponse);
}

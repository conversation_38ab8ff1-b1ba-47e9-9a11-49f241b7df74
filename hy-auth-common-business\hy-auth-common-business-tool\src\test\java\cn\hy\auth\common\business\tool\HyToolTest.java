package cn.hy.auth.common.business.tool;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 测试启动类
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = HyToolTest.C.class)
@RunWith(SpringRunner.class)
public class HyToolTest {

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void test() {

    }

    @Configuration
    static class C {

    }
}

package cn.hy.auth.custom.common.log.annotation;


import cn.hy.auth.custom.common.log.enums.ModuleNameEnum;
import cn.hy.auth.custom.common.log.enums.OperationTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 类描述: 日志注解类
 *
 * <AUTHOR>
 * @date ：创建于 2020/8/4
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ConfigLog {

    /**
     * 模块名称
     *
     * @return 模块名称
     */
    ModuleNameEnum moduleName();

    /**
     * 操作对象
     *
     * @return 返回操作对象
     */
    String operationObj();

    /**
     * 操作类型
     *
     * @return 返回操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 是否需要入库保存
     *
     * @return true 入库false不入库
     */
    boolean isSave() default true;

    /**
     * 是否是表达式句子
     * 当为true的时候操作对象必须是一个Freemarker支持的表达式operationObj
     *
     * @return true是false否
     */
    boolean isExpression() default false;
}

package cn.hy.auth.common.security.core.authentication.iam.def;


import cn.hy.auth.common.security.core.authentication.iam.bean.IamProviderUser;
import java.util.Map;

/**
 * 佛科院认证相关业务逻辑
 *
 * <AUTHOR>
 * @date 2022-09-08 17:34
 */
public interface IamAuthService {


    /**
     * 获取token
     * @param lesseeCode
     * @param appcode
     * @param code
     * @return
     */
    IamProviderUser getUserInfo(String lesseeCode, String appcode, String code);

    /**
     * 根据用户账号查询用户信息
     *
     * @param userInfo
     * @return
     */
    Map<String, Object> getByUserAccount(IamProviderUser userInfo);

    /**
     * 用户注册
     *
     * @param userInfo
     * @return
     */
    Boolean regirstUser(IamProviderUser userInfo);


    /**
     * 查询表名
     *
     * @param lesseeCode
     * @param appCode
     * @param tableCode
     * @return
     */
    String getTableName(String lesseeCode, String appCode, String tableCode);
}

package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.security.oauth2.userdetails.MyUserDetailsServiceImpl;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import com.google.common.collect.Maps;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {PwdAuthenticationProviderer.class, MyUserDetailsServiceImpl.class})
public class PwdAuthenticationProviderTest {
    @Autowired
    PwdAuthenticationProviderer pwdAuthenticationProvider;

    @MockBean
    private UserAccountService userAccountService;

    private Authentication authentication;
    private UserAccountDTO userAccountDTO;

    @Before
    public void setUp() throws Exception {
        authentication = mock(Authentication.class);
        doReturn("zhangsan").when(authentication).getPrincipal();
        Map<String,Object> map = Maps.newHashMap();
        map.put(LoginParamterConts.PWD_ENCRYPTION_TYPE, "3");
        doReturn("123").when(authentication).getCredentials();
        doReturn(map).when(authentication).getDetails();

        //userAccountDTO = UserAccountDTO.builder().password("123").userAccountName("zhangsan").build();
        //doReturn(userAccountDTO).when(userAccountService).getUserByUserName(eq("zhangsan"));

    }

    @Test
    public void testSupport() {
        AuthContext.getContext().loginState().setLoginType(LoginTypeEnum.USERNAME_PASSWORD);
        Assertions.assertThat(pwdAuthenticationProvider.isSupport(authentication)).isTrue();
    }

    @Test
    public void testUnSupport() {
        AuthContext.getContext().loginState().setLoginType(LoginTypeEnum.OAUTH2_CLIENT);
        Assertions.assertThat(pwdAuthenticationProvider.isSupport(authentication)).isFalse();
    }

    @Test
    public void testPasswordMatches() {
        Assertions.assertThat(pwdAuthenticationProvider.doAuthenticate(authentication)).isNull();
    }

    @Test(expected = BadCredentialsException.class)
    public void testPasswordDoesNotMatches() {
        doReturn("1234").when(authentication).getCredentials();
        pwdAuthenticationProvider.doAuthenticate(authentication);
    }

    @Test(expected = BadCredentialsException.class)
    public void testUserNotExist() {
        doReturn(null).when(userAccountService).getUserByUserName(eq("zhangsan"));
        pwdAuthenticationProvider.doAuthenticate(authentication);
    }
}
package cn.hy.auth.custom.security.oauth2.environment.service;


/**
 * 环境标识操作服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/11
 */
public interface OauthSysMService {

    /**
     * 初始化系统标识数据
     */
    void initializeSystemEnvironmentSymbol();

    /**
     * 获取系统标识
     * @return 系统标识
     */
    String getSystemEnvironmentSymbol();

    /**
     * 清除缓存
     * @return 清除缓存
     */
    String clearCache();
}

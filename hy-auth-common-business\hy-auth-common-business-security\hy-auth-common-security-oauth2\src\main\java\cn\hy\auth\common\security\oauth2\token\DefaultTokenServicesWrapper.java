package cn.hy.auth.common.security.oauth2.token;

import cn.hy.auth.common.security.oauth2.event.TokenCreatedSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.TokenRefreshedSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;

/**
 * 类描述：自定义token service,增强DefaultTokenServices的功能
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/13 16:52
 **/
public class DefaultTokenServicesWrapper extends DefaultTokenServices {
    private final ApplicationContext applicationContext;
    public DefaultTokenServicesWrapper(ApplicationContext applicationContext, boolean reuseRefreshTokeneuse) {
        this.applicationContext = applicationContext;
        setReuseRefreshToken(reuseRefreshTokeneuse);
    }

    @Override
    public OAuth2AccessToken createAccessToken(OAuth2Authentication authentication) {
        OAuth2AccessToken accessToken = super.createAccessToken(authentication);
        TokenCreatedSuccessEvent tokenCreateSuccessEvent = new TokenCreatedSuccessEvent(authentication, accessToken);
        applicationContext.publishEvent(tokenCreateSuccessEvent);
        return accessToken;
    }

    @Override
    public OAuth2AccessToken refreshAccessToken(String refreshTokenValue, TokenRequest tokenRequest) {
        OAuth2AccessToken oAuth2AccessToken = super.refreshAccessToken(refreshTokenValue, tokenRequest);
        TokenRefreshedSuccessEvent tokenRefreshedSuccessEvent = new TokenRefreshedSuccessEvent(refreshTokenValue, tokenRequest, oAuth2AccessToken);
        applicationContext.publishEvent(tokenRefreshedSuccessEvent);
        return oAuth2AccessToken;
    }

    @Override
    public boolean revokeToken(String tokenValue) {
        OAuth2Authentication oAuth2Authentication = super.loadAuthentication(tokenValue);
        OAuth2AccessToken oAuth2AccessToken = super.readAccessToken(tokenValue);
        boolean revokeToken = super.revokeToken(tokenValue);
        TokenRevokedEvent tokenRevokedEvent = new TokenRevokedEvent(tokenValue, revokeToken, oAuth2Authentication,oAuth2AccessToken);
        applicationContext.publishEvent(tokenRevokedEvent);
        return revokeToken;
    }
}

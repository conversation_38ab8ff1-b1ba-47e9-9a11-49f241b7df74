package cn.hy.auth.custom.user.account.service;

import cn.hy.auth.custom.common.domain.HyUserDetails;
import cn.hy.auth.custom.common.domain.UserAccountDTO;
import cn.hy.auth.custom.user.account.domain.BizgwPluginBusinessCfgDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: UserSecurityManageService
 * @description: 用户安全管理接口
 * @date 2024/1/15
 */
public interface UserSecurityManageService {

    /**
     * 是否存在指定参数配置
     *
     * @param code 参数编码
     * @return 存在与否
     */
    boolean existsEnableParamCfg(String code);


    /**
     * 获取配置
     *
     * @param code
     * @return
     */
    List<BizgwPluginBusinessCfgDTO> getBizPluginBusinessCfgList(String code);

    /**
     * 查看当前用户是否设置密码更换白名单
     *
     * @param account 用户信息
     * @return 设置与否
     */
    boolean existsIgnoreWhiteList(HyUserDetails account);

    /**
     * 修改密码状态
     *
     * @param username        账号名
     * @param pwdChangeStatus 密码更新状态
     */
    void updatePwdChangeStatus(String username, String pwdChangeStatus);

    /**
     * 密码更换时间间隔
     * 是否到达密码更改时间节点
     *
     * @param account 用户名
     * @return 到期与否
     */
    boolean changeTimeReached(HyUserDetails account);

    /**
     * 登录ip是否受限
     *
     * @param account 用户信息
     * @param ip      IP信息
     * @return 受限与否
     */
    boolean ipRestrict(HyUserDetails account, String ip);

    /**
     * 新增/更新用户变更时间间隔信息
     *
     * @param account           用户信息
     * @param pwdChangeStatus   密码更新状态
     * @param lastPwdChangeTime 密码最近修改时间
     */
    void insertOrUpdatePwdChangeTimeStatus(UserAccountDTO account, String pwdChangeStatus, Date lastPwdChangeTime);

}

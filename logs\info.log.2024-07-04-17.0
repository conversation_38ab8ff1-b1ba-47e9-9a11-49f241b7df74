2024-07-04 17:00:00.034 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 16:45:00,014 to 2024-07-04 17:00:00,003
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 17:07:25.423 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:07:27.341 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:07:34.033 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:07:43.819 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:07:44.729 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=**********, epoch=8338) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 17:07:44.728 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=**********, epoch=8346) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 17:07:46.909 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:07:46.925 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:07:53.292 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:07:53.292 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:07:57.264 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:07:58.829 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:07:58.835 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:03.341 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:04.517 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:04.517 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:04.839 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:04.840 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:04.884 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:05.186 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:05.186 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:06.401 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:06.897 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:06.914 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:07.263 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:07.264 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:07.588 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:07.588 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:08.214 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:09.268 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:09.269 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:09.611 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:09.745 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:09.745 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:10.133 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:10.133 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:10.436 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:11.736 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:16.836 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:16.837 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:23.311 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:23.311 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:28.448 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:28.448 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:28.465 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:39.916 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:39.916 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:45.418 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:45.418 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:50.736 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:50.915 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:50.915 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:52.903 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:52.904 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:53.255 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:53.256 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:53.298 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:53.662 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:53.662 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:54.420 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:55.397 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:55.397 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:55.458 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:55.749 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:55.771 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:56.123 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {}->http://**************:18500: The target server failed to respond
2024-07-04 17:08:56.123 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:57.172 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: *********
2024-07-04 17:08:57.219 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:08:57.233 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:08:57.234 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry - 检测到dubbo服务实例掉线，第【15】次尝试重新注册服务实例到consul成功！！！  retry register for url: "dubbo://**************:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=21128&release=2.7.8&side=provider&timestamp=*************", check id is: e7832abf
2024-07-04 17:08:57.372 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:08:57.863 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset
2024-07-04 17:08:57.863 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=*********, epoch=8343) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 17:08:57.863 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:57.888 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:08:57.919 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:08:57.919 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:08:57.962 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://**************:18500: Connection reset by peer: socket write error
2024-07-04 17:08:57.962 [,,,,Not Auth Request!] [catalogWatchTaskScheduler-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://**************:18500
2024-07-04 17:08:57.966 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=**********, epoch=INITIAL) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 17:08:58.022 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:08:58.061 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=**********, epoch=INITIAL) to node 0: org.apache.kafka.common.errors.DisconnectException.
2024-07-04 17:09:00.232 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:00.576 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:00.659 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:00.668 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:00.995 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.023 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.195 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-9819ec84-14a3-4851-9a37-eb29bcaab112 is not valid.
2024-07-04 17:09:01.195 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.195 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.196 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.214 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 17:09:01.307 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.308 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 17:09:01.318 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:09:01.455 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 39
2024-07-04 17:09:01.474 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:09:01.511 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.607 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:09:01.660 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.809 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.841 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.841 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:01.946 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:01.947 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:02.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:02.121 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:02.314 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:02.314 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:02.423 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:02.423 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:02.532 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:02.864 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.050 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.050 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.157 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.157 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.263 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.263 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.372 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.372 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.423 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.478 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.478 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.585 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.585 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.677 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.677 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.692 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.692 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.800 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.784 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.825 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:03.937 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:03.937 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.148 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.166 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.166 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.274 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.274 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.305 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.305 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.381 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.409 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.410 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.515 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.526 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.638 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.639 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.724 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.746 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.746 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.855 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.855 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.887 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.887 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.963 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.963 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:04.995 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:04.995 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.070 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.100 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.100 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.329 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.448 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.575 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.576 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.656 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.686 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.810 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:05.919 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:05.919 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.028 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.028 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.008 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.136 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.137 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.161 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.161 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.243 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.243 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.273 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.273 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.351 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.351 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.382 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.382 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.457 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.488 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.458 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.488 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:06.936 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.937 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:06.937 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.279 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.549 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.549 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.657 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.657 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.766 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.766 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.873 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.873 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:07.903 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:07.904 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.011 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.012 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.118 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.119 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.334 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.334 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.408 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.442 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.552 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.552 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.655 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.662 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.783 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.871 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.871 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:08.980 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:08.980 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.073 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.073 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.089 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.089 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.183 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.183 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.198 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.198 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.289 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.289 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.305 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.327 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.415 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.415 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.431 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.522 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.523 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.675 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.676 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.783 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.783 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.794 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.889 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.890 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.934 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:09.934 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:09.997 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.150 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.151 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.260 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.261 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.320 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.368 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.378 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.491 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.492 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.615 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.615 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.709 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.710 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.725 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.725 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.823 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.823 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.833 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.834 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.928 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.928 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:10.973 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:10.973 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.037 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.080 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.080 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.190 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.190 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.298 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.468 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.654 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.655 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.762 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.762 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.781 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.870 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.871 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.972 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.978 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:11.978 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:11.982 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.088 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.089 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.197 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.197 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.197 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.197 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.306 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.306 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.306 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.306 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.414 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.414 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.414 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.415 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.526 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.526 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.527 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.527 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.642 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.742 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.742 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.851 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.851 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.961 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:12.961 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:12.996 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.068 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.149 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.149 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.253 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.253 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.363 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.363 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.394 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.417 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.433 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.518 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.519 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.575 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.575 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.626 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.626 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.689 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.689 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.734 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.734 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.842 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.842 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.920 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.920 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:13.950 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:13.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.027 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.027 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.059 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.059 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.136 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.136 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.168 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.245 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.245 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.367 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.549 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.673 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.673 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.770 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.782 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.782 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.872 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.872 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.890 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.890 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.980 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.981 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:14.995 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:14.995 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.102 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.102 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.105 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.105 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.211 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.211 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.318 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.319 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.319 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.319 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.534 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.535 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.642 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.777 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.932 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:15.932 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:15.945 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.146 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.146 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.175 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.176 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.250 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.250 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.281 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.281 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.358 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.358 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.388 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.389 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.465 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.483 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.589 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.589 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.605 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.698 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.698 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.714 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.714 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.808 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.825 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:16.825 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:16.930 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.192 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.294 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.312 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.312 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.417 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.417 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.436 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.436 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.526 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.526 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.542 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.637 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.637 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.650 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.650 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.745 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.745 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.760 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.761 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.852 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.853 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.866 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.866 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.957 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.957 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:17.972 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:17.972 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:18.064 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:18.079 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:18.079 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:18.186 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:18.186 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:18.293 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:18.311 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:18.421 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:09:21.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-b2b98c84-558b-4ea9-bc58-df100b39283a is not valid.
2024-07-04 17:09:21.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 17:09:21.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 17:09:21.086 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:09:21.432 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-4d1620af-c011-4b61-b7e5-33619a797fd5 is not valid.
2024-07-04 17:09:21.432 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 17:09:21.433 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 17:09:21.433 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:09:22.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 17:09:22.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:09:22.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:09:22.547 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:09:22.555 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 40
2024-07-04 17:09:22.561 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 40
2024-07-04 17:09:22.561 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 17:09:22.565 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 17:09:22.566 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 40
2024-07-04 17:09:22.566 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 17:09:22.570 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 17:09:22.631 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 17:09:22.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 17:09:58.150 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:58.171 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:09:58.488 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:01.671 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:01.671 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:01.690 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.029 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.033 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.034 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.190 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.197 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.203 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.531 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.532 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.536 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.695 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.695 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.703 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:02.706 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:02.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.052 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.136 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.166 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.316 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.316 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.324 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.325 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.327 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.327 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.416 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.416 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.431 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.433 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.431 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.443 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.528 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.544 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.544 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.638 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.653 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:03.895 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:03.974 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.020 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.051 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.051 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.142 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.142 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.158 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.158 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.164 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.164 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.256 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.256 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.313 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.314 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.314 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.314 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.365 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.365 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.428 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.429 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.428 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.430 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.476 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.476 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.540 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.540 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.540 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.540 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.586 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.586 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.646 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.646 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.646 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.647 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.694 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.694 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:04.755 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.755 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:04.802 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.106 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.109 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.122 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.260 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.260 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.260 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.268 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.307 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.307 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.409 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.410 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.485 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.485 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.486 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.486 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.517 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.517 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.595 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.595 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.595 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.595 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.658 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.658 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.702 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.812 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.812 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.813 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.813 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.922 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.922 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.922 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.922 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:05.987 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:05.987 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.031 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.033 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.047 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.047 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.095 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.143 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.143 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.158 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.251 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.437 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.496 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.599 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.599 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.616 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.662 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.662 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.715 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.716 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.757 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.757 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.778 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.778 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.823 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.823 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.870 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.871 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.886 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.887 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.932 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.932 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.978 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.979 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:06.993 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:06.994 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.040 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.041 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.101 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.101 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.087 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.119 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.147 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.166 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.210 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.225 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.225 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.270 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.270 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.378 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.378 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.438 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.458 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.486 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.487 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.562 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.562 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.593 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.593 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.670 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.699 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.781 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:07.944 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:07.944 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.014 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.026 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.054 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.162 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.162 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.174 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.176 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.227 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.269 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.269 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.285 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.285 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.331 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.331 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.379 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.391 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.391 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.391 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.392 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.500 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.546 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.605 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.652 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.653 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.713 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.713 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.713 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.759 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.768 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.821 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.822 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.868 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.868 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.884 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.884 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:08.976 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.991 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:08.991 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.061 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.102 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.217 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.218 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.304 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.333 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.333 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.440 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.442 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.480 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.480 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.549 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.549 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.594 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.594 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.606 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.658 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.658 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.703 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.703 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.718 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.719 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.765 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.811 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.827 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.827 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.875 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.921 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.922 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.936 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.936 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:09.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:09.985 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.029 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.030 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.090 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.124 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.136 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.136 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.152 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.160 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.229 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.246 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.246 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.276 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.276 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.355 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.387 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.387 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.493 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.494 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.559 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.603 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.708 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.734 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.748 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.861 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.861 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.892 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.892 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.960 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:10.970 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:10.970 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.001 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.002 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.078 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.078 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.108 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.109 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.109 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.119 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.185 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.185 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.216 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.216 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.232 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.233 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.294 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.294 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.325 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.325 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.339 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.339 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.404 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.404 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.434 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.449 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.509 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.509 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.555 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.555 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.616 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.617 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.663 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.724 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.771 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.771 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.792 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.888 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.888 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.938 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:11.938 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:11.992 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.045 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.054 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.163 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.163 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.229 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.251 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.273 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.279 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.353 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.366 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.366 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.382 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.382 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.477 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.477 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.492 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.492 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.497 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.498 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.585 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.585 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.600 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.696 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.696 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.711 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.711 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.711 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.804 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.804 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.820 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.820 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:12.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.929 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:12.929 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.036 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.036 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.066 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.145 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.145 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.215 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.216 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.234 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.253 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.253 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.330 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.331 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.361 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.418 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.418 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.439 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.529 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.529 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.543 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.544 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.573 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.573 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.635 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.678 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.678 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.741 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.742 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.804 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.804 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.850 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.851 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.912 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:13.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:13.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:14.022 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:14.022 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:14.070 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:14.084 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:14.131 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:14.131 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:14.132 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:14.131 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-04 17:10:14.238 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:14.238 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-04 17:10:16.376 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-c52fb118-5363-42b1-b436-6b68a8acf121 is not valid.
2024-07-04 17:10:16.376 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-2]
2024-07-04 17:10:16.376 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-2]
2024-07-04 17:10:16.376 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:10:16.406 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 42
2024-07-04 17:10:16.406 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:10:16.422 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:10:17.247 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-a2fc82f9-7164-432b-8661-5acf836beb62 is not valid.
2024-07-04 17:10:17.247 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-04 17:10:17.248 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-04 17:10:17.248 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:10:17.247 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-6a1dd870-af47-43f7-ace9-5fdcdf8365e1 is not valid.
2024-07-04 17:10:17.248 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-1]
2024-07-04 17:10:17.248 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-1]
2024-07-04 17:10:17.248 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:10:19.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-04 17:10:19.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:10:19.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0, saas-sys-apps-1, saas-sys-apps-2]
2024-07-04 17:10:19.419 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-04 17:10:19.425 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 43
2024-07-04 17:10:19.426 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-04 17:10:19.426 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 43
2024-07-04 17:10:19.426 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-2]
2024-07-04 17:10:19.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 43
2024-07-04 17:10:19.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-1]
2024-07-04 17:10:19.429 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-04 17:10:19.430 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-2]
2024-07-04 17:10:19.431 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-1]
2024-07-04 17:15:00.013 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 17:00:00,003 to 2024-07-04 17:15:00,012
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 17:30:00.024 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 17:15:00,012 to 2024-07-04 17:30:00,011
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-04 17:45:00.014 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-04 17:30:00,011 to 2024-07-04 17:45:00,014
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


package cn.hy.auth.custom.common.domain;

import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Optional;

/**
 * 登录状态信息
 *
 * <AUTHOR>
 * @date 2020-12-02 15:36
 **/
@Data
public class LoginStateDTO implements Serializable {

    private static final long serialVersionUID = -1785525058524950184L;
    /**
     * 登录类型
     */
    private LoginTypeEnum loginType;
    /**
     * 登录的端类型
     */
    private ClientTypeEnum clientType;

    /**
     * 获取登录类型的字符串值
     *
     * @return
     */
    public String getLoginTypeString() {
        return Optional.ofNullable(loginType).map(Enum::toString).orElse(StringUtils.EMPTY);
    }
}

package cn.hy.auth.common.security.core.authentication.mobile.event;

import org.springframework.context.ApplicationEvent;

public class LockUserAccountEvent extends ApplicationEvent {

    private String mobile;

    public LockUserAccountEvent(String mobile) {
        super(mobile);
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}

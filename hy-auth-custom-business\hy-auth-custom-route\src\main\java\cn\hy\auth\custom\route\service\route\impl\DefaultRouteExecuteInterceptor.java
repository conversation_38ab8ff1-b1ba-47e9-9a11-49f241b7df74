package cn.hy.auth.custom.route.service.route.impl;

import cn.hy.auth.custom.route.domain.OauthRequestRouting;
import cn.hy.auth.custom.common.script.ScriptEngine;
import cn.hy.auth.custom.route.service.route.RouteExecuteInterceptor;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/25 12:02
 **/
@Component
@Slf4j
public class DefaultRouteExecuteInterceptor implements RouteExecuteInterceptor {
    private static final String OAUTH_REQUEST_ROUTING = "oauthRequestRouting";
    private static final String SERVLET_REQUEST = "requestWrapper";
    private static final String SERVLET_RESPONSE = "servletResponse";
    private static final String HTTP_RESPONSE = "httpResponse";
    private static final String CACHE = "cache";
    private static final String JDBCTEMPLATE = "jdbcTemplate";
    private ScriptEngine scriptEngine;
    /**
     * cache 过期时间，默认是7天
     */
    @Value("${hy.security.oauth2.route.cache.expireTime:604800}")
    private long cacheExpireTimeMs = 7 * 24 * 60 * 60L;
    private final JdbcTemplate jdbcTemplate;
    private final Cache<String, Map<String, Object>> cache = Caffeine.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(cacheExpireTimeMs, TimeUnit.SECONDS)
            .recordStats().build();

    public DefaultRouteExecuteInterceptor(ScriptEngine scriptEngine, JdbcTemplate jdbcTemplate) {
        this.scriptEngine = scriptEngine;
        this.jdbcTemplate = jdbcTemplate;

    }

    @Override
    public void preExe(OauthRequestRouting oauthRequestRouting, HttpServletRequest request, HttpServletResponse response) {
        String preRouteScript = oauthRequestRouting.getPreRouteScript();
        if (StringUtils.isEmpty(preRouteScript)) {
            return;
        }

        scriptEngine.run(preRouteScript, ImmutableMap.<String, Object>builder()
                .put(OAUTH_REQUEST_ROUTING, oauthRequestRouting)
                .put(SERVLET_REQUEST, request)
                .put(SERVLET_RESPONSE, response)
                .put(CACHE, cache)
                .put(JDBCTEMPLATE, jdbcTemplate)
                .build());
    }

    @Override
    public void postExe(OauthRequestRouting oauthRequestRouting, HttpServletRequest request, HttpServletResponse response, HttpResponse httpResponse) {
        String scriptText;

        if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            scriptText = oauthRequestRouting.getSuccessScript();
        } else {
            scriptText = oauthRequestRouting.getFailureScript();
        }

        if (StringUtils.isEmpty(scriptText)) {
            return;
        }

        scriptEngine.run(scriptText, ImmutableMap.<String, Object>builder()
                .put(OAUTH_REQUEST_ROUTING, oauthRequestRouting)
                .put(SERVLET_REQUEST, request)
                .put(SERVLET_RESPONSE, response)
                .put(HTTP_RESPONSE, httpResponse)
                .put(CACHE, cache)
                .put(JDBCTEMPLATE, jdbcTemplate)
                .build());
    }

    /**
     * UUID转为唯一的Long id
     * （提供给groovy脚本或其他地方调用）
     * https://www.thinbug.com/q/********
     * @param uuid .
     * @return Long id
     */
    public static Long getLongId(String uuid) {
        if (uuid == null) {
            return null;
        }
        if (uuid.length() < 32) {
            log.info("uuid = {} 是非标准32位,直接通过hashCode转为Long,有较大概率会重复" , uuid);
            return Long.valueOf(String.valueOf(uuid.hashCode()));
        }
        try {
            if (!uuid.contains("-")) {
                StringBuilder tempUuid = new StringBuilder();
                int length = uuid.length();
                for (int i = 0; i < length; i++) {
                    tempUuid.append(uuid.charAt(i));
                    if (i == 7 || i == 11 || i == 15 || i == 19) {
                        tempUuid.append("-");
                    }
                }
                return UUID.fromString(tempUuid.toString()).getMostSignificantBits() & Long.MAX_VALUE;
            } else {
                return UUID.fromString(uuid).getMostSignificantBits() & Long.MAX_VALUE;
            }
        }catch (Exception e){
            log.info("uuid = {} 转换失败,直接通过hashCode转为Long,有较大概率会重复. e = {}" , uuid,e);
            return Long.valueOf(String.valueOf(uuid.hashCode()));
        }
    }

    public static void main(String[] args) {
        Map<String,Object> map = new HashMap<>();
        map.put("a",null);
        JSONObject jsonObject = new JSONObject();
        System.out.println(map);
        try {

         } catch (Exception e) {

         }
    }

}

spring:
  datasource:
    url: jdbc:h2:mem:;MODE=MySQL;DATABASE_TO_UPPER=FALSE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    driver-class-name: org.h2.Driver
    platform: h2
  autoconfigure:
    exclude: cn.hy.dataengine.interceptor.extpoint.ExecuteProcessorChainAutoConfigure

logging.level.root: debug

hy:
  security:
    oauth2:
      clientInMemory: true
      clients[0]:
        clientId: client_hy_web
        clientSecret: 3LfsJMVt/chTqNgo85Gl/w==
        accessTokenValiditySeconds: 3600
    web:
      ignore:
        pattern:
          - /code/sms
          - /**/actuator/**
    #请求信息分类，用于按不同方式提取租户、应用编码
    request-type:
      authenticate:
        - /**/user/current
      non-business:
        - /**/actuator/**

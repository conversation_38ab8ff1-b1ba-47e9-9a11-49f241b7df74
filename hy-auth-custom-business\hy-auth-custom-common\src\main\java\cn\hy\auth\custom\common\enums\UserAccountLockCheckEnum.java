package cn.hy.auth.custom.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserAccountLockCheckEnum {

    MOBILE("0", "手机号"),

    EMAIL("1", "邮箱"),

    UNKNOWN("99", "未知");

    private String type;

    private String desc;

    public static UserAccountLockCheckEnum findByType(String type) {
        for (UserAccountLockCheckEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNKNOWN;
    }

}

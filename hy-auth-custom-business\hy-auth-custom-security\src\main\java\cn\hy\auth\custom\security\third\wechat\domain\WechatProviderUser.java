package cn.hy.auth.custom.security.third.wechat.domain;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import lombok.Setter;

/**
 * 企业微信用户信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
public class Wechat<PERSON><PERSON>iderUser implements ProviderUserInfo {

    private String providerId;
    private String providerUserid;
    private String userDisplayName;
    private String imageUrl;
    private String email;
    private String phone;
    private String userInfoJson;
    private String unionId;

    @Override
    public String getProviderId() {
        return providerId;
    }

    @Override
    public String getProviderUserid() {
        return providerUserid;
    }

    @Override
    public String getUnionId() {
        return unionId;
    }

    @Override
    public String getUserDisplayName() {
        return userDisplayName;
    }

    @Override
    public String getImageUrl() {
        return imageUrl;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public String getPhone() {
        return phone;
    }

    @Override
    public String getUserInfoJson() {
        return userInfoJson;
    }
}

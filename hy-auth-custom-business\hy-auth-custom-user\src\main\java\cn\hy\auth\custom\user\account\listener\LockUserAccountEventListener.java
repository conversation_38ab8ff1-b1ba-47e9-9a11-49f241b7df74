package cn.hy.auth.custom.user.account.listener;

import cn.hy.auth.common.security.core.authentication.mobile.event.LockUserAccountEvent;
import cn.hy.auth.custom.common.enums.UserAccountLockType;
import cn.hy.auth.custom.user.account.service.UserAccountLockService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class LockUserAccountEventListener implements ApplicationListener<LockUserAccountEvent> {

    @Autowired
    private UserAccountLockService userAccountLockService;

    @Override
    public void onApplicationEvent(LockUserAccountEvent logEvent) {
        userAccountLockService.lockUserAccount("", logEvent.getMobile(), UserAccountLockType.MOBILE_LOGIN.getType());
    }
}

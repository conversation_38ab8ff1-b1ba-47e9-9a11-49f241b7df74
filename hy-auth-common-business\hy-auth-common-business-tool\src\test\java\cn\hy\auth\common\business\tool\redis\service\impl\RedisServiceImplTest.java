package cn.hy.auth.common.business.tool.redis.service.impl;

import cn.hy.auth.common.business.tool.HyToolTest;
import cn.hy.auth.common.business.tool.redis.service.RedisService;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Collections;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * 类描述:redis 测试类
 *
 * <AUTHOR>
 * @date ：创建于 2021/2/1
 */
@Import(value = {RedisServiceImpl.class})
public class RedisServiceImplTest extends HyToolTest {

    @Autowired
    private RedisService redisService;

    @MockBean
    private RedisTemplate<String, Object> redisTemplate;

    @MockBean
    private ValueOperations valueOperations;
    @MockBean
    private ListOperations listOperations;
    @MockBean
    private HashOperations hashOperations;

    @Test
    public void set() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.set("test", "test");
    }

    @Test
    public void get() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.get("test");
    }

    @Test
    public void multiGet() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.multiGet(Collections.singletonList("test"));
    }

    @Test
    public void setHash() {
        Mockito.when(redisTemplate.opsForHash()).thenReturn(hashOperations);
        redisService.setHash("test", Collections.singletonMap("test", "test"));
    }

    @Test
    public void setList() {
        Mockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        redisService.setList("test", Collections.singletonList("test"));
    }

    @Test
    public void existsKey() {
        redisService.existsKey("test");
    }

    @Test
    public void renameKey() {
        redisService.renameKey("test", "test1");
    }

    @Test
    public void renameKeyNotExist() {
        redisService.renameKeyNotExist("test", "test1");
    }

    @Test
    public void deleteKey() {
        redisService.deleteKey("test");
        redisService.deleteKey("test", "test1");
        redisService.deleteKey(Collections.singletonList("test1"));
    }

    @Test
    public void expireKey() {
        redisService.expireKey("test", 1, TimeUnit.MILLISECONDS);
    }

    @Test
    public void expireKeyAt() {
        redisService.expireKeyAt("test", new Date());
    }

    @Test
    public void getKeyExpire() {
        redisService.getKeyExpire("test", TimeUnit.MILLISECONDS);
    }

    @Test
    public void persistKey() {
        redisService.persistKey("test");
    }

    @Test
    public void publish() {
        redisService.publish("test", "test");
    }

    @Test
    public void getKeys() {
        redisService.getKeys("test_");
    }

    @Test
    public void getKeysBySuffix() {
        redisService.getKeysBySuffix("test_");
    }

    @Test
    public void getValuesByPrefix() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.getValuesByPrefix("test_");
    }

    @Test
    public void getValuesBySuffix() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.getValuesBySuffix("test_");
    }

    @Test
    public void getValuesByAffix() {
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        redisService.getValuesByAffix("test_");
    }
}
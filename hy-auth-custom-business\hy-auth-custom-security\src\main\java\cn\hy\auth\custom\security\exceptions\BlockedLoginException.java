package cn.hy.auth.custom.security.exceptions;

import org.springframework.security.core.AuthenticationException;

/**
 * 已存在登录状态，阻止当前登录请求
 *
 * <AUTHOR>
 * @date 2020-11-27 11:23
 **/
public class BlockedLoginException extends AuthenticationException {
    /**
     * Constructs an {@code AuthenticationException} with the specified message and root
     * cause.
     *
     * @param msg the detail message
     * @param t   the root cause
     */
    public BlockedLoginException(String msg, Throwable t) {
        super(msg, t);
    }

    /**
     * Constructs an {@code AuthenticationException} with the specified message and no
     * root cause.
     *
     * @param msg the detail message
     */
    public BlockedLoginException(String msg) {
        super(msg);
    }
}

package cn.hy.auth.common.security.oauth2.extend;

import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.oauth2.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.oauth2.provider.error.OAuth2AccessDeniedHandler;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述：用户访问没有权限资源的错误处理
 * 引入OAUTH2之后,可能不需要该类也可以实现OAUTH2没有权限资源的错误处理流程.
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 12:25
 **/
@Slf4j
public class HyAccessDeniedHandler extends AccessDeniedHandlerImpl {
    /**
     * oauth2的错误处理实现
     */
    private AccessDeniedHandler oAuth2AccessDeniedHandler = new OAuth2AccessDeniedHandler();
    @Autowired(required = false)
    private SecurityProperties securityProperties = new SecurityProperties();

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       AccessDeniedException accessDeniedException) throws IOException,
            ServletException {
        log.warn("用户无访问权限.{},{} : ", RequestUtil.getRequestUrl(request), RequestUtil.getAllRequestParam(request, securityProperties.getFormAuth().getPasswordParameter(), "password", "client_secret"), accessDeniedException);
        // JSON 格式的返回
        if (securityProperties.getResponse().isJsonFormat()) {
            oAuth2AccessDeniedHandler.handle(request, response, accessDeniedException);
        } else {
            super.handle(request, response, accessDeniedException);
        }
    }

}

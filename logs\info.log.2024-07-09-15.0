2024-07-09 15:09:06.835 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://127.0.0.1:8500, dubbo.registry.parameters.token=, dubbo.registry.timout=10000}
2024-07-09 15:09:06.838 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-09 15:09:07.056 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-09 15:09:07.085 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-09 15:09:14.126 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:09:14.126 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:09:14.300 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163ms. Found 12 repository interfaces.
2024-07-09 15:09:14.536 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-09 15:09:14.537 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-09 15:09:14.538 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-09 15:09:14.539 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-09 15:09:14.540 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-09 15:09:15.769 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:09:15.771 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:09:16.094 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-09 15:09:16.095 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-09 15:09:16.095 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-09 15:09:16.095 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-09 15:09:16.096 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-09 15:09:16.096 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-09 15:09:16.097 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-09 15:09:16.097 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-09 15:09:16.097 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-09 15:09:16.098 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-09 15:09:16.098 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-09 15:09:16.099 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-09 15:09:16.107 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 312ms. Found 0 repository interfaces.
2024-07-09 15:09:17.061 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:09:17.061 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-09 15:09:17.062 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:09:17.062 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:09:17.062 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:09:17.458 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:17.458 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:17.546 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:17.546 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:17.549 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-09 15:09:17.724 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-09 15:09:17.940 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$c837a2cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.013 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$ea49296e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.135 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$cd56868d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.318 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ed5460] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.344 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7ff399d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.415 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f4859149] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:18.587 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$109f9446] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:09:19.375 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-09 15:09:19.408 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-09 15:09:19.423 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-09 15:09:19.423 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-09 15:09:19.971 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-09 15:09:19.972 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 12821 ms
2024-07-09 15:09:20.305 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【绑定机器码方式】获取机器编码.
2024-07-09 15:09:20.322 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从文件中读取license内容
2024-07-09 15:09:20.843 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:09:20.873 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7f36cd8f
2024-07-09 15:09:21.175 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-09 15:09:23.437 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-09 15:09:24.787 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-09 15:09:25.191 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-09 15:09:25.362 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-09 15:09:25.473 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-09 15:09:25.476 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-09 15:09:25.802 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-09 15:09:26.120 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-09 15:09:27.519 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:09:28.177 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-09 15:09:31.044 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-09 15:09:31.228 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-09 15:09:31.446 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:23, workerId:21
2024-07-09 15:09:32.250 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:09:32.250 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:09:33.175 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:09:33.175 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:09:34.569 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:09:34.570 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-09 15:09:36.590 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-09 15:09:36.865 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@49d52df2, org.springframework.security.web.context.SecurityContextPersistenceFilter@3cbe4472, org.springframework.security.web.header.HeaderWriterFilter@71d81a3e, org.springframework.security.web.authentication.logout.LogoutFilter@7647d789, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@50a4e4e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c98017f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c007713, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a4cf2d1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b05c2e8, org.springframework.security.web.session.SessionManagementFilter@26d47eba, org.springframework.security.web.access.ExceptionTranslationFilter@1db33525, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@791a53c7]
2024-07-09 15:09:36.922 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@3c69b875, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1c49ae29, org.springframework.security.web.context.SecurityContextPersistenceFilter@78e52e87, org.springframework.security.web.header.HeaderWriterFilter@196cfaed, org.springframework.security.web.authentication.logout.LogoutFilter@3dbdcfa2, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@43fe325e, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@27879b4e, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@2ddb0d1a, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@646599c6, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@153e2f2f, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@4457e347, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@5f914d68, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@53b464b9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2d7157b0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@177244f5, org.springframework.security.web.session.SessionManagementFilter@91339ed, org.springframework.security.web.access.ExceptionTranslationFilter@1946579d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@195b4f4f]
2024-07-09 15:09:36.939 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@399b9537, org.springframework.security.web.context.SecurityContextPersistenceFilter@5fbb2225, org.springframework.security.web.header.HeaderWriterFilter@17dfa412, org.springframework.security.web.csrf.CsrfFilter@1763a8a1, org.springframework.security.web.authentication.logout.LogoutFilter@3fb732ee, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c9cc591, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@36891524, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3117fd8, org.springframework.security.web.session.SessionManagementFilter@32c77e0d, org.springframework.security.web.access.ExceptionTranslationFilter@2324100b]
2024-07-09 15:09:37.216 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:09:37.774 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-09 15:09:39.898 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-09 15:09:40.086 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:09:40.596 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:09:40.698 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application hostname="hy" qosEnable="false" name="HY-AUTH-DUBBO-PROVIDER" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-09 15:09:40.713 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] have been binding by the configuration properties [{address=consul://127.0.0.1:8500, timout=10000, parameters.token=}]
2024-07-09 15:09:40.721 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-09 15:09:40.737 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-09 15:09:40.749 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-09 15:09:41.320 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:09:41.525 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:09:41.526 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:09:41.885 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:09:41.903 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:09:41.907 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:09:41.908 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:09:41.913 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:09:41.918 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:09:41.924 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:09:41.924 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:09:41.924 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:09:41.925 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:09:41.931 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:09:41.932 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:09:41.932 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:09:41.933 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:09:41.934 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:09:41.934 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:09:41.934 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:09:41.939 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:09:41.939 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:09:41.940 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:09:41.941 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:09:41.941 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:09:41.941 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:09:41.988 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:41.988 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:42.000 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-09 15:09:42.000 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-09 15:09:42.001 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:09:42.001 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:09:42.001 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 1
2024-07-09 15:09:42.014 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:09:42.022 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:09:42.022 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:09:42.061 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.consumer.internals.Fetcher - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Resetting offset for partition saas-sys-apps-0 to offset 394.
2024-07-09 15:09:42.081 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:09:44.244 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-09 15:09:44.262 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-09 15:09:44.271 [,,,,Auth is starting] [main] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:44.286 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is about to shutdown..., dubbo version: 2.7.8, current host: **********
2024-07-09 15:09:44.302 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:09:44.302 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:09:44.302 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:09:44.313 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:09:44.314 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:09:44.314 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:09:44.315 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:09:44.316 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:09:44.319 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-07-09 15:09:44.336 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2024-07-09 15:09:44.342 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:09:44.394 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-09 15:09:44.394 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-07-09 15:09:44.407 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-07-09 15:12:55.641 [,,,,Auth is starting] [Thread-70] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-09 15:12:55.641 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-09 15:12:55.642 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-09 15:12:55.643 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:06.073 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://***********:18500, dubbo.registry.parameters.token=, dubbo.registry.timout=10000}
2024-07-09 15:13:06.076 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-09 15:13:06.274 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-09 15:13:06.300 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-09 15:13:08.540 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:13:08.541 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:13:08.632 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85ms. Found 12 repository interfaces.
2024-07-09 15:13:08.717 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-09 15:13:08.717 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-09 15:13:08.718 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-09 15:13:08.719 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-09 15:13:08.719 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-09 15:13:09.066 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:13:09.067 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:13:09.246 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-09 15:13:09.246 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-09 15:13:09.246 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-09 15:13:09.247 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-09 15:13:09.247 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-09 15:13:09.247 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-09 15:13:09.248 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-09 15:13:09.248 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-09 15:13:09.248 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-09 15:13:09.249 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-09 15:13:09.249 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-09 15:13:09.249 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-09 15:13:09.255 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180ms. Found 0 repository interfaces.
2024-07-09 15:13:09.498 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:13:09.498 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-09 15:13:09.498 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:13:09.498 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:13:09.499 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:13:09.778 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:09.779 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:09.828 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:09.828 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:09.831 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-09 15:13:09.978 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-09 15:13:10.127 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$5240abf3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.202 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$74523295] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.273 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$575f8fb4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.410 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8af65d87] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.433 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9fca2ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.462 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7e8e9a70] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:10.588 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9aa89d6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:13:11.144 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-09 15:13:11.162 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-09 15:13:11.173 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-09 15:13:11.173 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-09 15:13:11.259 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-09 15:13:11.259 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4931 ms
2024-07-09 15:13:11.475 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【绑定机器码方式】获取机器编码.
2024-07-09 15:13:11.491 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从文件中读取license内容
2024-07-09 15:13:11.856 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:13:11.869 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3a5c6009
2024-07-09 15:13:12.133 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-09 15:13:13.920 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-09 15:13:14.618 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-09 15:13:14.731 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-09 15:13:14.873 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-09 15:13:14.946 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-09 15:13:14.949 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-09 15:13:15.149 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-09 15:13:15.334 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-09 15:13:16.314 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:13:16.661 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-09 15:13:19.689 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-09 15:13:19.844 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-09 15:13:19.985 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:12, workerId:10
2024-07-09 15:13:20.572 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:13:20.572 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:13:21.276 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:13:21.276 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:13:22.621 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:13:22.621 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-09 15:13:24.318 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-09 15:13:24.545 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@195b4f4f, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ae2721b, org.springframework.security.web.header.HeaderWriterFilter@1bee92f9, org.springframework.security.web.authentication.logout.LogoutFilter@23b2ddc4, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@3c69b875, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@420cf372, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7d2fc711, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7e21f10e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c49ae29, org.springframework.security.web.session.SessionManagementFilter@52c3f893, org.springframework.security.web.access.ExceptionTranslationFilter@143a4506, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6f710da7]
2024-07-09 15:13:24.605 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@10cd5b30, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@82b778b, org.springframework.security.web.context.SecurityContextPersistenceFilter@17dfa412, org.springframework.security.web.header.HeaderWriterFilter@2988933b, org.springframework.security.web.authentication.logout.LogoutFilter@5c9cc591, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@32cc87e0, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@70832ddb, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@23b824b3, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@6b5e1db0, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@11b0cf30, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@427475a2, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@2bf065c2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@32c77e0d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5fbb2225, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@30584e91, org.springframework.security.web.session.SessionManagementFilter@2324100b, org.springframework.security.web.access.ExceptionTranslationFilter@a55ed01, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@28466003]
2024-07-09 15:13:24.631 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46c2ed8d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1db7acc9, org.springframework.security.web.header.HeaderWriterFilter@24ad12ff, org.springframework.security.web.csrf.CsrfFilter@3dacb927, org.springframework.security.web.authentication.logout.LogoutFilter@66b3c476, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e08c557, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@27e1c83, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ee8ba7f, org.springframework.security.web.session.SessionManagementFilter@7963a94b, org.springframework.security.web.access.ExceptionTranslationFilter@1c59f6ae]
2024-07-09 15:13:24.835 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:13:25.272 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-09 15:13:27.031 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-09 15:13:27.235 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:13:27.696 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:13:27.773 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application hostname="hy" qosEnable="false" name="HY-AUTH-DUBBO-PROVIDER" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-09 15:13:27.785 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] have been binding by the configuration properties [{address=consul://127.0.0.1:8500, timout=10000, parameters.token=}]
2024-07-09 15:13:27.792 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-09 15:13:27.803 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-09 15:13:27.812 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-09 15:13:28.240 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:13:28.379 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:13:28.379 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:13:28.659 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:13:28.674 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:13:28.679 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:13:28.680 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:13:28.684 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:13:28.689 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:13:28.694 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:13:28.694 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:13:28.695 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:13:28.695 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:13:28.702 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:13:28.702 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:13:28.702 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:13:28.704 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:13:28.705 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:13:28.707 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:13:28.709 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:13:28.710 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:13:28.713 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:13:28.715 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:13:28.715 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:13:28.715 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:13:28.715 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:13:28.739 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 3
2024-07-09 15:13:28.740 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 3
2024-07-09 15:13:28.740 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 3
2024-07-09 15:13:28.740 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:13:28.742 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:13:28.743 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:13:28.745 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:13:28.745 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:13:28.757 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:13:28.758 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:28.758 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:30.950 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-09 15:13:30.962 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-09 15:13:30.970 [,,,,Auth is starting] [main] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:30.983 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is about to shutdown..., dubbo version: 2.7.8, current host: **********
2024-07-09 15:13:30.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:13:30.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:13:30.999 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:13:31.009 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:13:31.009 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:13:31.009 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:13:31.012 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:13:31.013 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:13:31.016 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-07-09 15:13:31.021 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2024-07-09 15:13:31.025 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:13:31.050 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-09 15:13:31.050 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-07-09 15:13:31.061 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-07-09 15:14:15.768 [,,,,Auth is starting] [Thread-44] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-09 15:14:15.768 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:15.769 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-09 15:14:15.770 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:20.790 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://***********:18500, dubbo.registry.parameters.token=, dubbo.registry.timout=10000}
2024-07-09 15:14:20.793 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-09 15:14:20.954 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-09 15:14:20.979 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-09 15:14:22.976 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:14:22.976 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:14:23.071 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 89ms. Found 12 repository interfaces.
2024-07-09 15:14:23.157 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-09 15:14:23.157 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-09 15:14:23.158 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-09 15:14:23.159 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-09 15:14:23.160 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-09 15:14:23.471 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:14:23.472 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:14:23.628 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-09 15:14:23.628 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-09 15:14:23.628 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-09 15:14:23.629 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-09 15:14:23.629 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-09 15:14:23.629 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-09 15:14:23.630 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-09 15:14:23.630 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-09 15:14:23.630 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-09 15:14:23.631 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-09 15:14:23.631 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-09 15:14:23.632 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-09 15:14:23.641 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 162ms. Found 0 repository interfaces.
2024-07-09 15:14:23.875 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:14:23.875 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-09 15:14:23.875 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:14:23.875 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:14:23.875 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:14:24.155 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:24.155 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:24.207 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:24.208 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:24.211 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-09 15:14:24.379 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-09 15:14:24.562 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$e55e3127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:24.639 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$76fb7c9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:24.707 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$ea7d14e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:24.858 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1e13e2bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:24.885 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9d1a2833] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:24.939 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$11ac1fa4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:25.076 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2dc622a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:14:25.599 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-09 15:14:25.622 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-09 15:14:25.635 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-09 15:14:25.636 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-09 15:14:25.749 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-09 15:14:25.749 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4745 ms
2024-07-09 15:14:25.975 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【绑定机器码方式】获取机器编码.
2024-07-09 15:14:25.991 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从文件中读取license内容
2024-07-09 15:14:26.363 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:14:26.375 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@230e163b
2024-07-09 15:14:26.635 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-09 15:14:28.320 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-09 15:14:29.053 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-09 15:14:29.177 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-09 15:14:29.319 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-09 15:14:29.387 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-09 15:14:29.390 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-09 15:14:29.580 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-09 15:14:29.754 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-09 15:14:30.752 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:14:31.105 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-09 15:14:33.743 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-09 15:14:33.893 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-09 15:14:34.035 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:20, workerId:0
2024-07-09 15:14:34.790 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:14:34.791 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:14:35.544 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:14:35.544 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:14:36.795 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:14:36.795 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-09 15:14:38.476 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-09 15:14:38.477 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-09 15:14:38.729 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3065a6ee, org.springframework.security.web.context.SecurityContextPersistenceFilter@67c492fc, org.springframework.security.web.header.HeaderWriterFilter@648b4b14, org.springframework.security.web.authentication.logout.LogoutFilter@4c2f3f41, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@7d2fc711, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@52c3f893, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@435df9a1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@39783d83, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@a741f59, org.springframework.security.web.session.SessionManagementFilter@39ab2bf3, org.springframework.security.web.access.ExceptionTranslationFilter@66ba2c88, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21ff56e7]
2024-07-09 15:14:38.784 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@45ec7633, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12c338fe, org.springframework.security.web.context.SecurityContextPersistenceFilter@32a7f41b, org.springframework.security.web.header.HeaderWriterFilter@5679e277, org.springframework.security.web.authentication.logout.LogoutFilter@24e2d0d1, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@60144979, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@27e4aacc, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4e85c657, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@316a03cf, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@1711b63f, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@1c1e78eb, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@1eae036e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@87852fb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@118b73d9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fbb0f0f, org.springframework.security.web.session.SessionManagementFilter@23b2ddc4, org.springframework.security.web.access.ExceptionTranslationFilter@680a5de1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@25b665ca]
2024-07-09 15:14:38.804 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@11786a43, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c9c7f1e, org.springframework.security.web.header.HeaderWriterFilter@3dbdcfa2, org.springframework.security.web.csrf.CsrfFilter@196cfaed, org.springframework.security.web.authentication.logout.LogoutFilter@6e639c1e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3b7777ea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68e47ff9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@11b097a1, org.springframework.security.web.session.SessionManagementFilter@6c7d5f84, org.springframework.security.web.access.ExceptionTranslationFilter@2d7157b0]
2024-07-09 15:14:38.994 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:14:39.411 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-09 15:14:41.171 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-09 15:14:41.358 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:14:41.837 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:14:41.912 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-09 15:14:41.925 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] have been binding by the configuration properties [{address=consul://127.0.0.1:8500, timout=10000, parameters.token=}]
2024-07-09 15:14:41.932 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-09 15:14:41.944 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-09 15:14:41.953 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-09 15:14:42.366 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:14:42.487 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:14:42.487 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:14:42.778 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:14:42.798 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:14:42.805 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:14:42.805 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:14:42.810 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:14:42.814 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:14:42.819 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:14:42.819 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:14:42.819 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:14:42.821 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:14:42.848 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:14:42.848 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:14:42.848 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:14:42.850 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:14:42.850 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:14:42.851 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:14:42.851 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:14:42.856 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:14:42.857 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:14:42.857 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:14:42.857 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:14:42.879 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 5
2024-07-09 15:14:42.879 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 5
2024-07-09 15:14:42.880 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 5
2024-07-09 15:14:42.881 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:14:42.881 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:14:42.883 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:14:42.884 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:14:42.884 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:14:42.896 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:14:42.909 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:42.910 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://127.0.0.1:8500" protocol="consul" port="8500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:45.107 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-07-09 15:14:45.118 [,,,,Auth is starting] [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-09 15:14:45.123 [,,,,Auth is starting] [main] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [], dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:45.134 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is about to shutdown..., dubbo version: 2.7.8, current host: **********
2024-07-09 15:14:45.149 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:14:45.149 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:14:45.149 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService
2024-07-09 15:14:45.157 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:14:45.158 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:14:45.158 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer - Consumer stopped
2024-07-09 15:14:45.159 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:14:45.161 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:14:45.164 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-07-09 15:14:45.169 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2024-07-09 15:14:45.173 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:14:45.198 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
2024-07-09 15:14:45.199 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-07-09 15:14:45.210 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-07-09 15:19:07.352 [,,,,Auth is starting] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:07.352 [,,,,Auth is starting] [Thread-42] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-09 15:19:07.355 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-09 15:19:07.356 [,,,,Auth is starting] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:12.664 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://***********:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-09 15:19:12.666 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-09 15:19:12.806 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-09 15:19:12.830 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-09 15:19:14.715 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:19:14.715 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:19:14.805 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84ms. Found 12 repository interfaces.
2024-07-09 15:19:14.892 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-09 15:19:14.892 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-09 15:19:14.893 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-09 15:19:14.893 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-09 15:19:14.894 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-09 15:19:15.215 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:19:15.216 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:19:15.363 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-09 15:19:15.363 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-09 15:19:15.363 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-09 15:19:15.363 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-09 15:19:15.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-09 15:19:15.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-09 15:19:15.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-09 15:19:15.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-09 15:19:15.364 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-09 15:19:15.366 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-09 15:19:15.366 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-09 15:19:15.366 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-09 15:19:15.371 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148ms. Found 0 repository interfaces.
2024-07-09 15:19:15.619 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:19:15.620 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-09 15:19:15.620 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:19:15.620 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:19:15.621 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:19:15.903 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:15.905 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:15.957 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:15.957 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:15.960 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-09 15:19:16.119 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-09 15:19:16.284 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$845081c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.354 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$a6620863] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.416 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$896f6582] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.560 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$bd063355] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.584 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3c0c78cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.614 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b09e703e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:16.735 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ccb8733b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:19:17.275 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-09 15:19:17.296 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-09 15:19:17.308 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-09 15:19:17.308 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-09 15:19:17.399 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-09 15:19:17.399 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4548 ms
2024-07-09 15:19:17.624 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【绑定机器码方式】获取机器编码.
2024-07-09 15:19:17.643 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从文件中读取license内容
2024-07-09 15:19:18.007 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:19:18.020 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@20040c6e
2024-07-09 15:19:18.282 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-09 15:19:19.947 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-09 15:19:20.663 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-09 15:19:20.794 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-09 15:19:20.922 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-09 15:19:20.993 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-09 15:19:20.995 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-09 15:19:21.187 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-09 15:19:21.358 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-09 15:19:22.373 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:19:22.757 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-09 15:19:25.462 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-09 15:19:25.603 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-09 15:19:25.755 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:27, workerId:11
2024-07-09 15:19:26.382 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:19:26.382 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:19:27.147 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:19:27.147 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:19:28.462 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:19:28.462 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:19:30.267 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-09 15:19:30.268 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-09 15:19:30.548 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@63cd8f26, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ebb8383, org.springframework.security.web.header.HeaderWriterFilter@7679a54b, org.springframework.security.web.authentication.logout.LogoutFilter@79a4f733, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@29923d3d, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@118b73d9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@afcfc63, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@582679fd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1bc41486, org.springframework.security.web.session.SessionManagementFilter@30b3d9ef, org.springframework.security.web.access.ExceptionTranslationFilter@3c07b046, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1ca9fd34]
2024-07-09 15:19:30.616 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@11786a43, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@11b097a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@50a4e4e, org.springframework.security.web.header.HeaderWriterFilter@2982f1eb, org.springframework.security.web.authentication.logout.LogoutFilter@45ec7633, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@196cfaed, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@5a6029d9, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@53b464b9, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@2811cde7, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@7a6980d4, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@6f710da7, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@3404df71, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b05c2e8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25b665ca, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@125c42e3, org.springframework.security.web.session.SessionManagementFilter@49d52df2, org.springframework.security.web.access.ExceptionTranslationFilter@747d4d9a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6e639c1e]
2024-07-09 15:19:30.633 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f914d68, org.springframework.security.web.context.SecurityContextPersistenceFilter@27879b4e, org.springframework.security.web.header.HeaderWriterFilter@3c69b875, org.springframework.security.web.csrf.CsrfFilter@37ca8b4c, org.springframework.security.web.authentication.logout.LogoutFilter@4457e347, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@646599c6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@153e2f2f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@177244f5, org.springframework.security.web.session.SessionManagementFilter@1c49ae29, org.springframework.security.web.access.ExceptionTranslationFilter@195b4f4f]
2024-07-09 15:19:30.821 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:19:31.278 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-09 15:19:33.034 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-09 15:19:33.216 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:19:33.676 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:19:33.754 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-09 15:19:33.766 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{address=consul://***********:18500, timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210}]
2024-07-09 15:19:33.773 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-09 15:19:33.784 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-09 15:19:33.794 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-09 15:19:34.226 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:19:34.347 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:19:34.347 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:19:34.617 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:19:34.634 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:19:34.639 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:19:34.639 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:19:34.645 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:19:34.649 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:19:34.654 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:19:34.654 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:19:34.654 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:19:34.654 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:19:34.661 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:19:34.661 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:19:34.661 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:19:34.664 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:19:34.664 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:19:34.664 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:19:34.664 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:19:34.668 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:19:34.669 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:19:34.669 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:19:34.669 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:19:34.669 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:19:34.669 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:19:34.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 7
2024-07-09 15:19:34.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 7
2024-07-09 15:19:34.688 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 7
2024-07-09 15:19:34.689 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:19:34.690 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:19:34.691 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:19:34.692 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:19:34.692 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:19:34.703 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:19:34.715 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:34.715 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:34.911 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.127 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.160 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.184 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.185 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.238 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.238 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.305 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.387 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.388 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=27748&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.403 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.440 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.505 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.507 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.508 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:35.992 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /***********:20881, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.025 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.086 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@5aac64e4; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.173 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.191 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.193 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: **********
2024-07-09 15:19:36.209 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-09 15:19:36.263 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-09 15:19:36.285 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 25.789 seconds (JVM running for 26.833)
2024-07-09 15:19:36.342 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-09 15:19:36.342 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-09 15:19:36.359 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-09 15:19:36.359 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-09 15:19:36.388 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：******************************************************************************************************************************************************************************,username = iotmp
2024-07-09 15:19:37.025 [,,,,Not Auth Request!] [RMI TCP Connection(4)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-09 15:19:37.026 [,,,,Not Auth Request!] [RMI TCP Connection(4)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-09 15:19:37.063 [,,,,Not Auth Request!] [RMI TCP Connection(4)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 37 ms
2024-07-09 15:22:13.604 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_auth_config]的加载时间：75ms
2024-07-09 15:22:13.837 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_user_account]的加载时间：4ms
2024-07-09 15:22:13.852 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【hy】-【flow】
2024-07-09 15:22:13.877 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_zhangsan
2024-07-09 15:22:13.889 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_zhangsan是否需要验证码，失败次数0
2024-07-09 15:22:13.893 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:22:13.894 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:22:14.022 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=hy, pwd_encryption_type=3, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_zhangsan, app_code=flow} 登录失败. 用户名或密码错误 
2024-07-09 15:22:14.213 [hy,flow,/login,,99a9a768863741afa68f4279ab1d8a3b] [http-nio-6060-exec-2] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_user_login_info]的加载时间：4ms
2024-07-09 15:22:42.205 [hy,flow,/login,,8755bffbfbe74cef9c6633db0d2b94c7] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_zhangsan
2024-07-09 15:22:42.206 [hy,flow,/login,,8755bffbfbe74cef9c6633db0d2b94c7] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_zhangsan是否需要验证码，失败次数1
2024-07-09 15:23:04.776 [hy,flow,/login,,c6c6f6bb25f5423da9af3e7d9416544a] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:hy_Zhangsan
2024-07-09 15:23:04.777 [hy,flow,/login,,c6c6f6bb25f5423da9af3e7d9416544a] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - hy_Zhangsan是否需要验证码，失败次数0
2024-07-09 15:23:04.777 [hy,flow,/login,,c6c6f6bb25f5423da9af3e7d9416544a] [http-nio-6060-exec-3] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:23:04.777 [hy,flow,/login,,c6c6f6bb25f5423da9af3e7d9416544a] [http-nio-6060-exec-3] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:23:04.783 [hy,flow,/login,,c6c6f6bb25f5423da9af3e7d9416544a] [http-nio-6060-exec-3] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=hy, pwd_encryption_type=2, client_secret=hy123456, client_type=4, client_id=client_hy_a_web, clientNam=str, username=hy_Zhangsan, app_code=flow} 登录失败. 用户名或密码错误 
2024-07-09 15:23:10.952 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:Zhangsan
2024-07-09 15:23:10.953 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - Zhangsan是否需要验证码，失败次数0
2024-07-09 15:23:10.953 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:23:10.953 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:23:11.013 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【Zhangsan】的密码状态正常。
2024-07-09 15:23:11.041 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_a_web,从数据库加载并放入缓存中
2024-07-09 15:23:11.047 [hy,flow,/login,,452c4f402d984fafb13ae3accac5ac55] [http-nio-6060-exec-5] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_oauth_client_details]的加载时间：5ms
2024-07-09 15:24:10.160 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:Zhangsan
2024-07-09 15:24:10.160 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - Zhangsan是否需要验证码，失败次数0
2024-07-09 15:24:10.160 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:24:10.160 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:24:10.168 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【Zhangsan】的密码状态正常。
2024-07-09 15:24:10.168 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_web,从数据库加载并放入缓存中
2024-07-09 15:24:10.345 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_role_user]的加载时间：4ms
2024-07-09 15:24:10.347 [hy,flow,/login,,7c853d6f0e63498f8f3bd8ec8fd30d45] [http-nio-6060-exec-4] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_role_info]的加载时间：2ms
2024-07-09 15:27:51.896 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:51.897 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:51.897 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:51.913 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:27:52.028 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:52.028 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:52.028 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-d0b55492-726c-4dc1-86ad-8e4753cfdeb7 is not valid.
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-72383d95-4033-4ae1-abc7-34d6cae78ec4 is not valid.
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:27:55.043 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-62e239a9-cfc0-431c-8c8d-1758f486936a is not valid.
2024-07-09 15:27:55.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:27:55.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:27:55.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:27:55.044 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:27:55.045 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:27:55.049 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:27:55.050 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 10
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 10
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 10
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:27:55.055 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:27:55.066 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:31:58.455 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:31:58.455 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:31:58.455 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:32:03.580 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:32:17.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:32:17.634 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:32:29.156 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:33:02.522 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:33:02.522 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=1613902771, epoch=956) to node 1001: org.apache.kafka.common.errors.DisconnectException.
2024-07-09 15:33:02.522 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:33:04.673 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:33:04.673 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:34:18.946 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:34:18.957 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-09 15:19:25,455 to 2024-07-09 15:31:58,454
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.05| 56.25%|            32|            18|             0|             0|       14.6|         79
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-988e6933-6d30-4e0a-b00d-e62eaa7bbfd3 is not valid.
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-edc98a53-07ae-40ed-9a8f-3e2e907f5d24 is not valid.
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-faee0ce8-664b-408d-be06-b0f38f05be5d is not valid.
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:34:21.953 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 12
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 12
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 12
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:34:21.959 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:34:21.962 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:35:24.418 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:35:24.420 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:35:24.420 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  org.apache.kafka.clients.FetchSessionHandler - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Error sending fetch request (sessionId=916902696, epoch=12) to node 1001: org.apache.kafka.common.errors.DisconnectException.
2024-07-09 15:35:24.420 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:35:24.426 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:35:24.427 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:35:24.429 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:35:24.429 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:35:31.690 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:35:31.690 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:16.865 [,,,,Not Auth Request!] [JetCacheDefaultExecutor] INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2024-07-09 15:31:58,454 to 2024-07-09 15:46:16,864
cache   |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
md_table|      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
--------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2024-07-09 15:46:16.866 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:16.866 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:16.872 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:16.892 [,,,,Not Auth Request!] [Ttl-Consul-Check-Executor-thread-1] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry - 检测到dubbo服务实例掉线，第【1】次尝试重新注册服务实例到consul成功！！！  retry register for url: "dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&release=2.7.8&side=provider&timestamp=*************", check id is: ef89443c
2024-07-09 15:46:16.933 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:16.934 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:16.979 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:19.950 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-68a005d1-61ec-4fc2-b088-c2ae9af1ef80 is not valid.
2024-07-09 15:46:19.950 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-4b8debb7-783d-4141-85d5-0f6dd66146f2 is not valid.
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:46:19.951 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:19.956 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 14
2024-07-09 15:46:19.956 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 14
2024-07-09 15:46:19.956 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:46:19.956 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:19.956 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:46:19.960 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:46:19.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-8c8db43e-f189-4472-b949-3ca50e96fdd2 is not valid.
2024-07-09 15:46:19.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:19.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:46:19.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:26.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-09 15:46:26.377 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed since group is rebalancing
2024-07-09 15:46:26.378 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:26.378 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:46:27.337 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:46:27.337 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:46:27.337 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:27.338 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:42.205 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-09 15:46:42.206 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-09 15:46:42.205 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 15
2024-07-09 15:46:42.207 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:46:42.207 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:42.207 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:46:44.340 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:46:44.938 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:46:52.846 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.846 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.872 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.874 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.878 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.878 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:46:52.896 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  org.apache.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.896 [,,,,Not Auth Request!] [Thread-43] INFO  cn.hy.mdatasource.HyMutipleDataSourcesHolder - 开始关闭副数据源...
2024-07-09 15:46:52.898 [,,,,Not Auth Request!] [Thread-57] INFO  o.a.dubbo.registry.support.AbstractRegistryFactory -  [DUBBO] Close all registries [consul://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=27748&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210], dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.898 [,,,,Not Auth Request!] [Thread-57] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy registry:consul://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&interface=org.apache.dubbo.registry.RegistryService&pid=27748&qos.enable=false&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.898 [,,,,Not Auth Request!] [Thread-57] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Unregister: dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.898 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.s.b.c.e.AwaitingNonWebApplicationListener -  [Dubbo] Current Spring Boot Application is about to shutdown...
2024-07-09 15:46:52.899 [,,,,Not Auth Request!] [DubboShutdownHook] INFO  o.a.d.config.event.listener.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.908 [,,,,Not Auth Request!] [Thread-57] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Destroy unregister url dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=27748&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.910 [,,,,Not Auth Request!] [Thread-57] INFO  org.apache.dubbo.rpc.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /***********:20881, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.912 [,,,,Not Auth Request!] [Thread-57] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /***********:20881, dubbo version: 2.7.8, current host: **********
2024-07-09 15:46:52.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.983 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:46:52.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:52.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:46:52.984 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:46:52.991 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 17
2024-07-09 15:46:52.991 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:46:52.995 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:47:05.920 [,,,,Auth is starting] [main] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.id=HY-AUTH-DUBBO-PROVIDER, dubbo.application.name=HY-AUTH-DUBBO-PROVIDER, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.protocol.name=dubbo, dubbo.protocol.port=-1, dubbo.registry.address=consul://***********:18500, dubbo.registry.parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo.registry.timout=10000}
2024-07-09 15:47:05.923 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.init.InitDeploy - 启动参数，args = 【】
2024-07-09 15:47:06.070 [,,,,Auth is starting] [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: CompositePropertySource {name='consul', propertySources=[ConsulPropertySource {name='config/HY-AUTH,prod/'}, ConsulPropertySource {name='config/HY-AUTH/'}, ConsulPropertySource {name='config/application,prod/'}, ConsulPropertySource {name='config/application/'}]}
2024-07-09 15:47:06.095 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - The following profiles are active: prod
2024-07-09 15:47:08.778 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:47:08.778 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:47:08.887 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103ms. Found 12 repository interfaces.
2024-07-09 15:47:08.989 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2024-07-09 15:47:08.991 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2024-07-09 15:47:08.992 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2024-07-09 15:47:08.993 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2024-07-09 15:47:08.994 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2024-07-09 15:47:09.358 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-07-09 15:47:09.359 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-07-09 15:47:09.591 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DataSourcesRepository.
2024-07-09 15:47:09.592 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.DynamicFieldRepository.
2024-07-09 15:47:09.592 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.FieldRepository.
2024-07-09 15:47:09.593 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.ForeignKeyRepository.
2024-07-09 15:47:09.593 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.IndexRepository.
2024-07-09 15:47:09.593 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.JsonClipsRepository.
2024-07-09 15:47:09.593 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperRelationRepository.
2024-07-09 15:47:09.594 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.SuperTableRepository.
2024-07-09 15:47:09.594 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableMappingRepository.
2024-07-09 15:47:09.595 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TableRepository.
2024-07-09 15:47:09.595 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.core.repository.TriggerRepository.
2024-07-09 15:47:09.595 [,,,,Auth is starting] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.hy.metadata.engine.revision.db.repository.TableMetaRevisionChangeRepository.
2024-07-09 15:47:09.603 [,,,,Auth is starting] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 225ms. Found 0 repository interfaces.
2024-07-09 15:47:09.848 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : HY-AUTH-DUBBO-PROVIDER, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:47:09.848 [,,,,Auth is starting] [main] INFO  com.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2024-07-09 15:47:09.849 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:47:09.849 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:47:09.850 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2024-07-09 15:47:10.181 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:10.181 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:10.262 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:cn.hy.auth.custom.common.api.UserAccountApi, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:10.263 [,,,,Auth is starting] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationBeanPostProcessor -  [DUBBO] 1 annotated Dubbo's @Service Components { [Bean definition with name 'userAccountDubboService': Generic bean: class [cn.hy.auth.custom.user.account.dubbo.UserAccountDubboService]; scope=; abstract=false; lazyInit=false; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-user\target\classes\cn\hy\auth\custom\user\account\dubbo\UserAccountDubboService.class]] } were scanned under package[cn.hy.auth.custom], dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:10.266 [,,,,Auth is starting] [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2024-07-09 15:47:10.455 [,,,,Auth is starting] [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=9c2cbea0-1523-3820-b057-e7607de16b2a
2024-07-09 15:47:10.612 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$d2b83da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:10.679 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$2f3d0a7c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:10.770 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration' of type [cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaAutoConfiguration$$EnhancerBySpringCGLIB$$124a679b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:10.896 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$45e1356e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:10.919 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c4e77ae6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:10.950 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$39797257] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:11.086 [,,,,Auth is starting] [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$55937554] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-07-09 15:47:11.735 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6060 (http)
2024-07-09 15:47:11.757 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6060"]
2024-07-09 15:47:11.769 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-07-09 15:47:11.770 [,,,,Auth is starting] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.17]
2024-07-09 15:47:11.893 [,,,,Auth is starting] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-07-09 15:47:11.894 [,,,,Auth is starting] [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5776 ms
2024-07-09 15:47:12.151 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 启用【绑定机器码方式】获取机器编码.
2024-07-09 15:47:12.172 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.LicenseConfiguration - 从文件中读取license内容
2024-07-09 15:47:12.579 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:47:12.600 [,,,,Auth is starting] [main] INFO  com.netflix.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4509414e
2024-07-09 15:47:12.881 [,,,,Auth is starting] [main] INFO  c.h.m.e.c.cache.HyMetadataCoreCacheConfiguration - 元数据加载 caffeine cacheManager 配置
2024-07-09 15:47:14.813 [,,,,Auth is starting] [main] INFO  c.t.c.generator.AbstractImageCaptchaGenerator - 图片验证码[SpringMultiImageCaptchaGenerator]初始化...
2024-07-09 15:47:15.860 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-07-09 15:47:16.046 [,,,,Auth is starting] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-07-09 15:47:16.201 [,,,,Auth is starting] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: default
	...]
2024-07-09 15:47:16.327 [,,,,Auth is starting] [main] INFO  org.hibernate.Version - HHH000412: Hibernate Core {5.3.9.Final}
2024-07-09 15:47:16.331 [,,,,Auth is starting] [main] INFO  org.hibernate.cfg.Environment - HHH000206: hibernate.properties not found
2024-07-09 15:47:16.782 [,,,,Auth is starting] [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
2024-07-09 15:47:17.062 [,,,,Auth is starting] [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL57Dialect
2024-07-09 15:47:18.366 [,,,,Auth is starting] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-09 15:47:18.810 [,,,,Auth is starting] [main] INFO  o.h.hql.internal.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
2024-07-09 15:47:21.842 [,,,,Auth is starting] [main] INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2024-07-09 15:47:22.005 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.config.JacksonConfig - 设置LocalDateTime 序列化配置
2024-07-09 15:47:22.160 [,,,,Auth is starting] [main] INFO  cn.hy.id.generator.SnowflakeIdWorker - zoneId:9, workerId:8
2024-07-09 15:47:22.875 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:47:22.876 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil.applicationContext
2024-07-09 15:47:23.679 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:47:23.679 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.auth.custom.common.utils.AuthSpringUtil.applicationContext
2024-07-09 15:47:25.120 [,,,,Auth is starting] [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:47:25.120 [,,,,Auth is starting] [main] INFO  c.a.j.a.f.CreateCacheAnnotationBeanPostProcessor - Autowired annotation is not supported on static fields: private static org.springframework.context.ApplicationContext cn.hy.metadata.engine.common.utils.SpringUtil.applicationContext
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/sendSmsCode'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/check'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/user/forget/pwd'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/code/sms'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/actuator/**'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/route/cache/clear'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/log/cache/clear'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/*/cache/clear'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/license/*'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/verifyCode/sliderCaptcha/**'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/appInfo/status'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/meta/data/cache/clear'], []
2024-07-09 15:47:26.975 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: Ant [pattern='/**/user/checkUserAccount'], []
2024-07-09 15:47:27.233 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@c98017f, org.springframework.security.web.context.SecurityContextPersistenceFilter@7546033d, org.springframework.security.web.header.HeaderWriterFilter@1db33525, org.springframework.security.web.authentication.logout.LogoutFilter@427b8fe4, cn.hy.auth.common.security.oauth2.extend.CustomClientCredentialsTokenEndpointFilter@747d4d9a, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3b1870b6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@d86a616, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7f57925d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2982f1eb, org.springframework.security.web.session.SessionManagementFilter@12973dd3, org.springframework.security.web.access.ExceptionTranslationFilter@7d332e20, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@75b9bbab]
2024-07-09 15:47:27.294 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@708a0150, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a282bad, org.springframework.security.web.context.SecurityContextPersistenceFilter@1946579d, org.springframework.security.web.header.HeaderWriterFilter@29173f02, org.springframework.security.web.authentication.logout.LogoutFilter@78e52e87, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@3404df71, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter@195b4f4f, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@43fe325e, cn.hy.auth.common.security.core.authentication.external.HyExternalAuthenticationFilter@3c69b875, cn.hy.auth.common.security.core.authentication.face.HyFaceAuthenticationFilter@1c49ae29, cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationFilter@27879b4e, cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationFilter@646599c6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@196cfaed, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@91339ed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@153e2f2f, org.springframework.security.web.session.SessionManagementFilter@1b4d4949, org.springframework.security.web.access.ExceptionTranslationFilter@4308744c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@420cf372]
2024-07-09 15:47:27.313 [,,,,Auth is starting] [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c9cc591, org.springframework.security.web.context.SecurityContextPersistenceFilter@2324100b, org.springframework.security.web.header.HeaderWriterFilter@a55ed01, org.springframework.security.web.csrf.CsrfFilter@21833d72, org.springframework.security.web.authentication.logout.LogoutFilter@5fbb2225, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17dfa412, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@32c77e0d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@36891524, org.springframework.security.web.session.SessionManagementFilter@2988933b, org.springframework.security.web.access.ExceptionTranslationFilter@2fb37f92]
2024-07-09 15:47:27.524 [,,,,Auth is starting] [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-07-09 15:47:28.004 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-07-09 15:47:29.976 [,,,,Auth is starting] [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-07-09 15:47:30.242 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'catalogWatchTaskScheduler'
2024-07-09 15:47:30.757 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'configWatchTaskScheduler'
2024-07-09 15:47:30.858 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="HY-AUTH-DUBBO-PROVIDER" hostname="hy" qosEnable="false" />] have been binding by the configuration properties [{name=HY-AUTH-DUBBO-PROVIDER, id=HY-AUTH-DUBBO-PROVIDER, qos-enable=false}]
2024-07-09 15:47:30.872 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] have been binding by the configuration properties [{address=consul://***********:18500, timout=10000, parameters.token=6357edb7-ebd4-4ea7-854d-553697d0f210}]
2024-07-09 15:47:30.883 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{port=-1, name=dubbo}]
2024-07-09 15:47:30.897 [,,,,Auth is starting] [main] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false}]
2024-07-09 15:47:30.908 [,,,,Auth is starting] [main] INFO  c.h.a.c.user.account.dubbo.UserAccountDubboService - 创建了UserAccountDubboService
2024-07-09 15:47:31.541 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:47:31.694 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:47:31.694 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:47:32.028 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:47:32.047 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:47:32.054 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:47:32.054 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:47:32.060 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:47:32.065 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:47:32.071 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:47:32.071 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:47:32.072 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:47:32.072 [,,,,Auth is starting] [main] INFO  org.apache.kafka.clients.consumer.ConsumerConfig - ConsumerConfig values: 
	auto.commit.interval.ms = 2000
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092]
	check.crcs = true
	client.id = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = ********
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = hy-auth-sys-*********-6060
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.1, TLSv1]
	ssl.endpoint.identification.algorithm = https
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLS
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2024-07-09 15:47:32.078 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka version : 2.0.1
2024-07-09 15:47:32.078 [,,,,Auth is starting] [main] INFO  org.apache.kafka.common.utils.AppInfoParser - Kafka commitId : fa14705e51bd2ce5
2024-07-09 15:47:32.078 [,,,,Auth is starting] [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-07-09 15:47:32.086 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:47:32.086 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:47:32.086 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  org.apache.kafka.clients.Metadata - Cluster ID: pi6p5SrxT7G0R-F5-PoHmQ
2024-07-09 15:47:32.086 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:47:32.086 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:47:32.087 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:47:32.091 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:47:32.091 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:47:32.091 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:47:32.092 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:47:32.111 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:47:32.111 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:47:32.123 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 20
2024-07-09 15:47:32.123 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 20
2024-07-09 15:47:32.124 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:47:32.124 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:47:32.127 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:47:32.127 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:47:32.136 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 20
2024-07-09 15:47:32.139 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:47:32.189 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : consul] supports as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.190 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] will be used as the config center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.191 [,,,,Auth is starting] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:47:32.417 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.613 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/HY-AUTH-DUBBO-PROVIDER/dubbo.properties, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.646 [,,,,Auth is starting] [main] INFO  o.apache.dubbo.config.utils.ConfigValidationUtils -  [DUBBO] There's no valid monitor config found, if you want to open monitor statistics for Dubbo, please make sure your monitor is configured properly., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.671 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : consul] supports as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.671 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry address="consul://***********:18500" protocol="consul" port="18500" />] will be used as the metadata center, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.825 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.825 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.900 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to find valid host from DNS., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.970 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service cn.hy.auth.custom.common.api.UserAccountApi to local registry url : injvm://127.0.0.1/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=13236&qos.enable=false&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.971 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service cn.hy.auth.custom.common.api.UserAccountApi url dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&bind.ip=*********&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=13236&qos.enable=false&release=2.7.8&side=provider&timestamp=************* to registry registry://***********:18500/org.apache.dubbo.registry.RegistryService?application=HY-AUTH-DUBBO-PROVIDER&dubbo=2.0.2&pid=13236&qos.enable=false&registry=consul&release=2.7.8&timestamp=*************&token=6357edb7-ebd4-4ea7-854d-553697d0f210, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:32.983 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ProviderConfigurationListener for config with key: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:33.009 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/HY-AUTH-DUBBO-PROVIDER.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:33.049 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.qos.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:33.052 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] register listener class org.apache.dubbo.registry.integration.RegistryProtocol$ServiceConfigurationListener for config with key: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:33.053 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.ConsulDynamicConfiguration -  [DUBBO] getting config from: /dubbo/config/dubbo/cn.hy.auth.custom.common.api.UserAccountApi::.configurators, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.019 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /***********:20881, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.055 [,,,,Auth is starting] [main] INFO  cn.hy.auth.custom.dubbo.HyConsulRegistry -  [DUBBO] Register: dubbo://***********:20881/cn.hy.auth.custom.common.api.UserAccountApi?anyhost=true&application=HY-AUTH-DUBBO-PROVIDER&consul-deregister-critical-service-after=300s&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hy.auth.custom.common.api.UserAccountApi&metadata-type=remote&methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount&pid=13236&release=2.7.8&side=provider&timestamp=*************, dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.175 [,,,,Auth is starting] [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.r.support.ConfigCenterBasedMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@280a9330; definition: FullServiceDefinition{parameters={side=provider, release=2.7.8, methods=getCurrentUserAccount,getCurrentUserWithLoginInfo,getLastSuccessLoginInfo,getCurrentAssociationUser,checkToken,getCurrentAccount, deprecated=false, dubbo=2.0.2, interface=cn.hy.auth.custom.common.api.UserAccountApi, qos.enable=false, generic=false, metadata-type=remote, application=HY-AUTH-DUBBO-PROVIDER, dynamic=true, anyhost=true}} ServiceDefinition [canonicalName=cn.hy.auth.custom.common.api.UserAccountApi, codeSource=file:/D:/flowservice/hy-authentication-center/hy-auth-custom-business/hy-auth-custom-common/target/classes/, methods=[MethodDefinition [name=checkToken, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentUserAccount, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserAccountDTO], MethodDefinition [name=getCurrentUserWithLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.LoginUserHistoryDTO], MethodDefinition [name=getCurrentAssociationUser, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getCurrentAccount, parameterTypes=[java.lang.String], returnType=java.util.Map<java.lang.String,java.lang.Object>], MethodDefinition [name=getLastSuccessLoginInfo, parameterTypes=[java.lang.String], returnType=cn.hy.auth.custom.common.domain.UserLoginInfoDTO]]], dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.233 [,,,,Auth is starting] [main] INFO  o.a.d.m.DynamicConfigurationServiceNameMapping -  [DUBBO] Dubbo service[null] mapped to interface name[cn.hy.auth.custom.common.api.UserAccountApi]., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.244 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.244 [,,,,Auth is starting] [main] INFO  org.apache.dubbo.config.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.8, current host: **********
2024-07-09 15:47:34.261 [,,,,Auth is starting] [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6060"]
2024-07-09 15:47:34.305 [,,,,Auth is starting] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 6060 (http) with context path ''
2024-07-09 15:47:34.332 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.AuthApplication - Started AuthApplication in 31.201 seconds (JVM running for 32.311)
2024-07-09 15:47:34.337 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->从file:/D:/flowservice/hy-authentication-center/hy-auth-center-boot/target/classes/verifycode/imgs/00325.jpg加载验证码背景图
2024-07-09 15:47:34.337 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图->加载了15个验证码背景图
2024-07-09 15:47:34.342 [,,,,Auth is starting] [main] INFO  c.h.a.c.s.verifycode.VerifyBgImgInitFromLocal - 从本地初始加载验证码背景图，加载完成
2024-07-09 15:47:34.342 [,,,,Auth is starting] [main] INFO  cn.hy.license.sdk.ApplicationLicenseListener - 已停用license功能
2024-07-09 15:47:34.370 [,,,,Auth is starting] [main] INFO  cn.hy.auth.boot.InitAuth - auth 平台启动成功!数据库连接信息：******************************************************************************************************************************************************************************,username = iotmp
2024-07-09 15:47:34.996 [,,,,Not Auth Request!] [RMI TCP Connection(3)-*********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-07-09 15:47:35.001 [,,,,Not Auth Request!] [RMI TCP Connection(3)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-07-09 15:47:35.067 [,,,,Not Auth Request!] [RMI TCP Connection(3)-*********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 66 ms
2024-07-09 15:49:12.208 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_auth_config]的加载时间：73ms
2024-07-09 15:49:12.426 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_user_account]的加载时间：5ms
2024-07-09 15:49:12.440 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.c.multi.systable.AppInvolvedTableServiceImpl - 初始化涉及的应用信息表--【hy】-【flow】
2024-07-09 15:49:12.451 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:Zhangsan
2024-07-09 15:49:12.452 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - Zhangsan是否需要验证码，失败次数0
2024-07-09 15:49:12.455 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:49:12.455 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:49:12.508 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.p.PwdExpirationAndForceModifyProviderer - 检查用户账号密码状态-->用户【Zhangsan】的密码状态正常。
2024-07-09 15:49:12.521 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.a.c.s.o.client.OauthClientDetailsServiceImpl - clientId:client_hy_web,从数据库加载并放入缓存中
2024-07-09 15:49:12.527 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_oauth_client_details]的加载时间：5ms
2024-07-09 15:49:12.579 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_role_user]的加载时间：3ms
2024-07-09 15:49:12.582 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_role_info]的加载时间：3ms
2024-07-09 15:49:12.640 [hy,flow,/login,,12c2b2e9c9414737bfc7086271784643] [http-nio-6060-exec-1] INFO  c.h.m.e.a.md.service.impl.MetaDataQueryManagerImpl - 元数据表：[hy_flow_user_login_info]的加载时间：3ms
2024-07-09 15:49:47.525 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:47.525 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:47.525 [,,,,Not Auth Request!] [kafka-coordinator-heartbeat-thread | hy-auth-sys-*********-6060] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:47.531 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:47.531 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:47.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:47.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:47.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:47.545 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Group coordinator *************:9092 (id: ********** rack: null) is unavailable or invalid, will attempt rediscovery
2024-07-09 15:49:48.475 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:48.476 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:48.476 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Discovered group coordinator *************:9092 (id: ********** rack: null)
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-4-013dd0bc-7fb1-46ad-bdd0-1e1b573df646 is not valid.
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-3-df889553-f607-45f0-80b4-4790242875fa is not valid.
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Attempt to heartbeat failed for since member id consumer-2-22dc9eb9-59c5-442a-913b-dc4f9577adcd is not valid.
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions [saas-sys-apps-0]
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Revoking previously assigned partitions []
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: [saas-sys-apps-0]
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions revoked: []
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:49:52.511 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] (Re-)joining group
2024-07-09 15:49:52.537 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 22
2024-07-09 15:49:52.537 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-4, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:49:52.537 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:49:52.537 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 22
2024-07-09 15:49:52.538 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-3, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions []
2024-07-09 15:49:52.538 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: []
2024-07-09 15:49:52.538 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.AbstractCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Successfully joined group with generation 22
2024-07-09 15:49:52.538 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.consumer.internals.ConsumerCoordinator - [Consumer clientId=consumer-2, groupId=hy-auth-sys-*********-6060] Setting newly assigned partitions [saas-sys-apps-0]
2024-07-09 15:49:52.543 [,,,,Not Auth Request!] [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.kafka.listener.KafkaMessageListenerContainer - partitions assigned: [saas-sys-apps-0]
2024-07-09 15:50:35.820 [hy,flow,/login,,6c8d48b6a180454b9e8708e92d295653] [http-nio-6060-exec-6] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - 登陆时再检查验证结果是否过期，验证码ID：null，loginName:Zhangsan
2024-07-09 15:50:35.820 [hy,flow,/login,,6c8d48b6a180454b9e8708e92d295653] [http-nio-6060-exec-6] INFO  c.h.a.custom.security.verifycode.VerifyCodeCreator - Zhangsan是否需要验证码，失败次数0
2024-07-09 15:50:35.820 [hy,flow,/login,,6c8d48b6a180454b9e8708e92d295653] [http-nio-6060-exec-6] INFO  c.h.a.c.security.filter.LoginSupportTypeFilter - 识别到的登录类型：【USERNAME_PASSWORD】
2024-07-09 15:50:35.820 [hy,flow,/login,,6c8d48b6a180454b9e8708e92d295653] [http-nio-6060-exec-6] INFO  c.h.a.custom.security.filter.LoginStateInitFilter - 登录请求，校验登录扩展参数。URL:【http://127.0.0.1:6060/login】
2024-07-09 15:50:35.839 [hy,flow,/login,,6c8d48b6a180454b9e8708e92d295653] [http-nio-6060-exec-6] INFO  c.h.a.c.s.o.extend.HyAuthenticationFailureHandler - /login,{lessee_code=hy, pwd_encryption_type=2, client_secret=hy123456, client_type=4, clientNam=str, client_id=client_hy_web, app_code=flow, username=Zhangsan} 登录失败. 用户名或密码错误 

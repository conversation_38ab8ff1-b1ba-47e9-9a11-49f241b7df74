package cn.hy.auth.common.security.oauth2.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * oauth2 的客户端配置数组
 *
 * <AUTHOR>
 * @date 2020-11-12
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "hy.security.oauth2")
public class ClientLoadProperties {
    /**
     * 客户端内存模式
     */
    private boolean clientInMemory = false;
    /**
     * 客户端clientId和clientSecret配置
     */
    private ClientProperties[] clients = {};
}
package cn.hy.auth.common.security.core.authentication.mobile.service.impl;


import cn.hy.auth.common.security.core.authentication.mobile.KafkaSmsCodeMessage;
import cn.hy.auth.common.security.core.authentication.mobile.service.EmailCodeSender;
import cn.hy.auth.common.security.core.authentication.mobile.vo.SmsLoginVO;
import cn.hy.auth.common.security.core.authentication.util.AppConfigParamUtil;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import cn.hy.id.IdWorker;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title: EmailCodeSenderImpl
 * @description: 邮箱
 * @date 2024/10/17
 */
@Slf4j
@Service
public class EmailCodeSenderImpl implements EmailCodeSender {

    public Cache<String, Integer> emailSendNumberCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    @Value("${message.topic:auth_captcha_message_topic}")
    private String emailCodeKafkaTopic;

    @Value("${message.emailTitle:邮箱验证码}")
    private String emailTitle;

    private final IdWorker idWorker;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final AppConfigParamUtil appConfigParamUtil;

    @Autowired
    public EmailCodeSenderImpl(IdWorker idWorker, KafkaTemplate<String, Object> kafkaTemplate, AppConfigParamUtil appConfigParamUtil) {
        this.idWorker = idWorker;
        this.kafkaTemplate = kafkaTemplate;
        this.appConfigParamUtil = appConfigParamUtil;
    }

    @Override
    public String send(String email, String code, String appCode, String lesseeCode, SmsLoginVO smsLoginVO, String appParamKey) {
        Integer number = emailSendNumberCache.getIfPresent(appParamKey + email);
        number = number == null ? 0 : number;
        Object limitobj = appConfigParamUtil.getByKey(lesseeCode, appCode, appParamKey);
        int limit = limitobj == null ? 10 : Integer.parseInt(limitobj.toString());
        if (limit <= number) {
            return LocaleUtil.getMessage("EmailCodeSenderImpl.send.msg1", "");
        }
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("email", email);
        paramMap.put("captcha", code);
        Map<String, Object> extendVariables = smsLoginVO.getExtendVariables();
        paramMap.put("extendVariables", extendVariables == null ? new HashMap<>() : extendVariables);
        KafkaSmsCodeMessage msg = KafkaSmsCodeMessage.getEmailCodeMessage(idWorker.nextId() + "");
        msg.setPayload(paramMap);
        String eventMsg = JSON.toJSONString(msg);
        kafkaTemplate.send(emailCodeKafkaTopic, eventMsg)
                .addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                    @Override
                    public void onFailure(Throwable ex) {
                        log.error("发送邮件验证码的kafka消息失败，邮箱：{}，验证码：{}，错误：{}", email, code, ex.getMessage(), ex);
                    }

                    @Override
                    public void onSuccess(SendResult<String, Object> result) {
                        log.info("发送邮件验证码的kafka消息成功，邮箱：{}，验证码：{}", email, code);
                    }
                });
        log.info("成功发送邮件验证码: email = {}, code = {}", email, code);
        emailSendNumberCache.put(appParamKey + email, number + 1);
        return "";
    }

}

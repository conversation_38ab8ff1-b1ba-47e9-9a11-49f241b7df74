package cn.hy.auth.custom.security.oauth2.token;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.util.OAuth2Utils;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.DefaultAuthenticationKeyGenerator;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeSet;

/**
 * 类描述：自定义的 Authentication key 生成器
 * <p>
 * 重新登录能否重用已有token的因素，默认实现是3大因素 client_id、username 和scope。
 * 此类的实现逻辑，影响的因素有2种情况：
 * 情况1：适合非oauth2的client_credentials登录模式, client_id、scope、user_name、client_type 、client_name、租户编号和应用编号。
 * 情况2：oauth2的client_credentials登录模式, client_id、scope,租户编号和应用编号
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/14 16:18
 **/
@Slf4j
public class HyAuthenticationKeyGenerator extends DefaultAuthenticationKeyGenerator {
    private static final String CLIENT_ID = "client_id";

    private static final String SCOPE = "scope";

    private static final String USERNAME = "username";

    private static final String CLIENT_TYPE = "client_type";

    private static final String CLIENT_NAME = "client_name";

    private static final String LESSEE_CODE = "lessee_code";

    private static final String APP_CODE = "app_code";

    private static final String BROWSER_ID = "browser_id";

    private static final String PAGE_ID = "page_id";

    @Override
    public String extractKey(OAuth2Authentication authentication) {
        Map<String, String> values = new LinkedHashMap<>();
        OAuth2Request authorizationRequest = authentication.getOAuth2Request();
        if (authorizationRequest != null) {
            values.put(CLIENT_ID, authorizationRequest.getClientId());
            if (authorizationRequest.getScope() != null) {
                values.put(SCOPE, OAuth2Utils.formatParameterList(new TreeSet<>(authorizationRequest.getScope())));
            }
        }
        // 非oauth2的client_credentials 模式
        if (!authentication.isClientOnly()) {
            setNotClientCredentialsValues(authentication, values);
        } else {
            // oauth2的client_credentials 模式
            setClientCredentialsValues(authorizationRequest, values);
        }

        return generateKey(values);
    }

    /**
     * 设置非oauth2的client_credentials 模式的values
     * username,client_type,client_name,lessee_code,app_code
     *
     * @param authentication .
     * @param values         .
     */
    private void setNotClientCredentialsValues(OAuth2Authentication authentication, Map<String, String> values) {
        values.put(USERNAME, authentication.getName());
        Authentication userAuthentication = authentication.getUserAuthentication();
        if (userAuthentication == null) {
            log.warn("userAuthentication is null,无法设置client_type,client_name,lessee_code和app_code的值");
            return;
        }
        Object details = userAuthentication.getDetails();
        if (details instanceof Map) {
            Map<String, Object> detailsMap = (Map<String, Object>) details;
            Object clientType = detailsMap.get(CLIENT_TYPE);
            if (clientType != null && StringUtils.isNotEmpty(clientType.toString())) {
                values.put(CLIENT_TYPE, clientType.toString());
            }
            Object clientName = detailsMap.get(CLIENT_NAME);
            if (clientName != null && StringUtils.isNotEmpty(clientName.toString())) {
                values.put(CLIENT_NAME, clientName.toString());
            }
            Object lesseeCode = detailsMap.get(LESSEE_CODE);
            if (lesseeCode != null && StringUtils.isNotEmpty(lesseeCode.toString())) {
                values.put(LESSEE_CODE, lesseeCode.toString());
            }
            Object appCode = detailsMap.get(APP_CODE);
            if (appCode != null && StringUtils.isNotEmpty(appCode.toString())) {
                values.put(APP_CODE, appCode.toString());
            }
            Object browserId = detailsMap.get(BROWSER_ID);
            if (browserId != null && StringUtils.isNotEmpty(browserId.toString())) {
                values.put(BROWSER_ID, browserId.toString());
            }
            Object pageId = detailsMap.get(PAGE_ID);
            if (pageId != null && StringUtils.isNotEmpty(pageId.toString())) {
                values.put(PAGE_ID, pageId.toString());
            }
        }

    }

    private void setClientCredentialsValues(OAuth2Request authorizationRequest, Map<String, String> values) {
        if (authorizationRequest == null) {
            log.warn("authorizationRequest is null,无法设置LESSEE_CODE 和 APP_CODE");
            return;
        }
        Map<String, String> requestParameters = authorizationRequest.getRequestParameters();
        String lesseeCode = requestParameters.get(LESSEE_CODE);
        if (StringUtils.isNotEmpty(lesseeCode)) {
            values.put(LESSEE_CODE, lesseeCode);
        }
        String appCode = requestParameters.get(APP_CODE);
        if (StringUtils.isNotEmpty(appCode)) {
            values.put(APP_CODE, appCode);
        }
    }
}

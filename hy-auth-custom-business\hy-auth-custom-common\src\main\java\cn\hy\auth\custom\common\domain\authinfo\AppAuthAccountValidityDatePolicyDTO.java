package cn.hy.auth.custom.common.domain.authinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 账号过期策略
 *
 * <AUTHOR>
 * @date 2020-12-03 16:37
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppAuthAccountValidityDatePolicyDTO extends BasePolicyDTO {
    private static final long serialVersionUID = 3499945692247725699L;

    /**
     * 有效开始时间
     */
    private String startEffectiveTime;
    /**
     * 有效截止时间
     */
    private String endEffectiveTime;

    /**
     * 解析有效开始时间戳
     *
     * @param data 数据集
     * @return 账号有效开始时间戳
     */
    public Long parseStartTime(Map<String, Object> data) {
        return parseTimestamp(data, startEffectiveTime);
    }

    /**
     * 解析有效截止时间戳
     *
     * @param data 数据集
     * @return 账号有效截止时间戳
     */
    public Long parseEndTime(Map<String, Object> data) {
        return parseTimestamp(data, endEffectiveTime);
    }
}

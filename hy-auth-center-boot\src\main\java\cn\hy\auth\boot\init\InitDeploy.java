package cn.hy.auth.boot.init;

import cn.hy.auth.custom.common.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 初始化平台部署数据
 *
 * <AUTHOR>
 * @date 2021-09-26 9:31
 */
@Slf4j
public class InitDeploy implements ApplicationListener<ApplicationEnvironmentPreparedEvent>, Ordered {

    private final List<String> defaultYmls = Arrays.asList("/config/application-prod.yml", "/config/application-prod.yaml");
    private final ResourceLoader resourceLoader = new DefaultResourceLoader();
    private final YamlPropertySourceLoader yamlPropertySourceLoader = new YamlPropertySourceLoader();
    // 说明此时yml配置的环境变量还没有变成最终的变量值
    private static final String MYSQL_HOST_REGX = "(\\$\\{MYSQL_HOST:\\S+?\\})";
    private static final String MYSQL_PORT_REGX = "(\\$\\{MYSQL_PORT:\\S+?\\})";
    private static final String X2_AUTH_CENTER = "(\\$\\{X2_AUTH_CENTER:\\S+?\\})";
    private static final String DATABASE_NAME_REGX = "(\\$\\{DATABASE_NAME:\\S+?\\})";
    private static final String DATABASE_USER_REGX = "(\\$\\{DATABASE_USER:\\S+?\\})";
    private static final String DATABAE_PWD_REGX = "(\\$\\{DATABASE_PWD:\\S+?\\})";
    private static final String APP_NAME_REGX = "(\\$\\{APP_NAME:(\\S+)?\\})";
    private static final String AUTH_ADDRESS_REGX = "(\\$\\{AUTH_ADDRESS:(\\S+)?\\})";


    private boolean isInitYml = false;
    private boolean isInitDatabase = false;
    private final Map<String, Object> bootstrapMap = bootstrapMap();
    private static final String APP_NAME = "spring.application.name";
    private static final String CONFIG_PRE_FIX = "spring.cloud.consul.config.prefix";

    private final String initYml = "init-yml";
    private final String initDatabase = "init-database";
    private final String deploy = "deploy";
    static final String IS_DEPLOY_KEY = "deploying";
    static final String IS_DEPLOY_VAL = "is";

    /**
     * 标记初始化模式
     *
     * @param args .
     */
    private void markInitModel(String args) {
        if (initYml.equalsIgnoreCase(args)) {
            isInitYml = true;
        } else if (initDatabase.equalsIgnoreCase(args)) {
            isInitDatabase = true;
        }
    }

    /**
     * 返回bootstrap配置 map
     *
     * @return .
     */
    private Map<String, Object> bootstrapMap() {
        try {
            Resource[] bootstrap = ResourcePatternUtils.getResourcePatternResolver(resourceLoader)
                    .getResources("classpath:/config/bootstrap.yml");
            List<PropertySource<?>> bootstrapPropertySources = yamlPropertySourceLoader.load("bootstrap", bootstrap[0]);
            Map bootStrap = (Map) bootstrapPropertySources.get(0).getSource();
            log.info("bootStrap  {} ", bootStrap);
            return bootStrap;
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 如果环境变量配置了appname，它的优先级更高
     *
     * @param confAppName 配置文件里面的appName
     * @param evnAppName  环境变量appName
     * @return appName
     */
    private String getAppName(String confAppName, String evnAppName) {

        Pattern p = Pattern.compile(APP_NAME_REGX);
        Matcher m = p.matcher(confAppName);
        //只有配置文件使用了${}模式，才去处理环境变量
        if (m.find()) {
            //环境变量配置的应用名字优先级更高
            confAppName = evnAppName == null ? m.group(2) : evnAppName;
        }
        return confAppName;
    }


    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {

        String[] args = event.getArgs();

        log.info("启动参数，args = 【{}】", org.apache.commons.lang3.StringUtils.join(args));


        //检查参数
        if (args.length == 1 && deploy.equalsIgnoreCase(args[0].trim())) {
            log.error("deploy命令需要提供最少一个参数：init-yml或init-database");
            log.error("部署失败！");
            System.exit(0);
        } else if (args.length > 1 && deploy.equalsIgnoreCase(args[0].trim())) {
            if (args.length == 2) {
                markInitModel(args[1].trim());
            } else if (args.length == 3) {
                markInitModel(args[1].trim());
                markInitModel(args[2].trim());
            }

            System.setProperty(IS_DEPLOY_KEY, IS_DEPLOY_VAL);
            log.info("################################################");
            log.info("#                 当前为部署模式                  #");
            log.info("################################################");
            for (String arg : event.getArgs()) {
                log.info("运行参数:                 {}", arg);
            }

            log.info("环境变量 CONSUL_HOST:     {}", event.getEnvironment().getProperty("CONSUL_HOST"));
            log.info("环境变量 CONSUL_PORT:     {}", event.getEnvironment().getProperty("CONSUL_PORT"));
            log.info("环境变量 MYSQL_HOST:      {}", event.getEnvironment().getProperty("MYSQL_HOST"));
            log.info("环境变量 MYSQL_PORT:      {}", event.getEnvironment().getProperty("MYSQL_PORT"));
            log.info("环境变量 DATABASE_NAME:   {}", event.getEnvironment().getProperty("DATABASE_NAME"));
            log.info("环境变量 DATABASE_USER:   {}", event.getEnvironment().getProperty("DATABASE_USER"));
            log.info("环境变量 DATABASE_PWD:    {}", event.getEnvironment().getProperty("DATABASE_PWD"));
            log.info("环境变量 X2_AUTH_CENTER:    {}", event.getEnvironment().getProperty("X2_AUTH_CENTER"));
            String activeEnv = org.apache.commons.lang3.StringUtils.isAllBlank(event.getEnvironment().getProperty("ENV"))?"prod":event.getEnvironment().getProperty("ENV");
            log.info("环境变量 activeEnv:    {}", activeEnv);

            if (isInitYml) {
                try {
                    MutablePropertySources mp = event.getEnvironment().getPropertySources();
                    for (String defaultYml : defaultYmls) {
                        for (PropertySource<?> ps : mp) {
                            if (ps.getName().contains(defaultYml)) {

                                Resource[] resources = ResourcePatternUtils.getResourcePatternResolver(resourceLoader).getResources(pickYmlPath(ps.getName()));
                                for (Resource resource : resources) {
                                    if (!resource.exists()) {
                                        continue;
                                    }
                                    // bootstrapMap.get(APP_NAME)获取到的是字符串 ${APP_NAME:HY-AUTH}
                                    //看看应用名长啥样
                                    String evnAppName = event.getEnvironment().getProperty("APP_NAME");
                                    String consulConfigHoldPath = bootstrapMap.get(CONFIG_PRE_FIX) + "/" +
                                            getAppName(bootstrapMap.get(APP_NAME).toString(), evnAppName) + ","
                                            + activeEnv + "/data";
                                    //获取标准配置模板
                                    String yml = preHandleYml(IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8),
                                            event.getEnvironment().getProperty("MYSQL_HOST"),
                                            event.getEnvironment().getProperty("MYSQL_PORT"),
                                            event.getEnvironment().getProperty("DATABASE_NAME"),
                                            event.getEnvironment().getProperty("DATABASE_USER"),
                                            event.getEnvironment().getProperty("DATABASE_PWD"),
                                            event.getEnvironment().getProperty("AUTH_HOST"),
                                            event.getEnvironment().getProperty("X2_AUTH_CENTER")
                                    );

                                    boolean flag = postYmlToConsul(event.getEnvironment().getProperty("CONSUL_HOST"),
                                            event.getEnvironment().getProperty("CONSUL_PORT"),
                                            consulConfigHoldPath,
                                            yml
                                    );

                                    if (flag) {
                                        log.info("YML导入到consul成功! [{}]", consulConfigHoldPath);
                                    }

                                    break;
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    throw new IllegalStateException(e);
                }
            }
        }


    }

    /**
     * 对yml模板进行预处理，把数据库信息替换成常量
     *
     * @param yml .
     * @return .
     */
    public String preHandleYml(String yml, String mysqlHost, String mysqlPort, String databaseName,
                                String databaseUser, String databasePwd, String authAddress,String x2AuthCenterAddress) {

        Assert.hasText(yml, LocaleUtil.getMessage("InitDeploy.assert.msg1", null));
        Assert.hasText(mysqlHost, LocaleUtil.getMessage("InitDeploy.assert.msg2", null));
        Assert.hasText(mysqlPort, LocaleUtil.getMessage("InitDeploy.assert.msg3", null));
        Assert.hasText(databaseName, LocaleUtil.getMessage("InitDeploy.assert.msg4", null));
        Assert.hasText(databaseUser, LocaleUtil.getMessage("InitDeploy.assert.msg5", null));
        Assert.hasText(databasePwd, LocaleUtil.getMessage("InitDeploy.assert.msg6", null));

        yml = yml.replaceAll(MYSQL_HOST_REGX, mysqlHost);
        yml = yml.replaceAll(MYSQL_PORT_REGX, mysqlPort);
        yml = yml.replaceAll(DATABASE_NAME_REGX, databaseName);
        yml = yml.replaceAll(DATABAE_PWD_REGX, databasePwd);
        yml = yml.replaceAll(DATABASE_USER_REGX, databaseUser);
        yml = yml.replaceAll(AUTH_ADDRESS_REGX, authAddress);
        if (x2AuthCenterAddress != null){
            yml = yml.replaceAll(X2_AUTH_CENTER, x2AuthCenterAddress);
        }
        log.info("预处理后的yml:\n {}", yml);

        return yml;
    }


    /**
     * 提取yaml路径
     *
     * @param s .
     * @return .
     */
    private String pickYmlPath(String s) {
        s = s.split("\\[")[1];
        s = s.substring(0, s.length() - 1);
        return s;
    }

    /**
     * 把配置数据写入到consul
     *
     * @param host .
     * @param port .
     * @param yml  .
     * @return .
     */
    private boolean postYmlToConsul(String host, String port, String consulConfigHoldPath, String yml) {

        Assert.hasText(host, LocaleUtil.getMessage("InitDeploy.assert.msg7", null));
        Assert.hasText(port, LocaleUtil.getMessage("InitDeploy.assert.msg8", null));
        Assert.hasText(yml, LocaleUtil.getMessage("InitDeploy.assert.msg1", null));
        String url = "http://" + host + ":" + port + "/v1/kv/" + consulConfigHoldPath;
        log.info("开始写入yml配置到consul... {}", url);

        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder().url(url)
                .method("PUT", RequestBody.create(
                        MediaType.parse("text/plain; charset=utf-8"),
                        yml))
                .build();

        Call call = okHttpClient.newCall(request);
        try {
            //同步调用,返回Response,会抛出IO异常
            Response response = call.execute();
            String respMsg = response.body().string();
            return StringUtils.hasText(respMsg) && "true".equalsIgnoreCase(respMsg);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }

    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }


    /**
     * 初始化数据库
     */
    public class InitDatabase implements ApplicationListener<ApplicationReadyEvent> {

        @Override
        public void onApplicationEvent(ApplicationReadyEvent event) {

            String isDeploy = System.getProperty(IS_DEPLOY_KEY);
            if (IS_DEPLOY_VAL.equalsIgnoreCase(isDeploy) && isInitDatabase) {

                log.info("开始初始化数据库...");

                try {
                    Resource[] appSqlResources = ResourcePatternUtils.getResourcePatternResolver(resourceLoader).getResources("classpath:/deploy-sql/auth.sql");

                    String appSql = IOUtils.toString(appSqlResources[0].getInputStream(), StandardCharsets.UTF_8);

                    JdbcTemplate jdbcTemplate = event.getApplicationContext().getBean(JdbcTemplate.class);

                    log.info("执行SQL: {}", appSql);
                    jdbcTemplate.update(appSql);


                } catch (Exception e) {
                    throw new IllegalStateException(e);
                }

                log.info("数据库初始化完成！");
                log.info("退出应用部署！");
                System.exit(0);
            }


        }
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.user.account.dao.UserSecurityManageDao">

    <sql id="Pwd_Change_Interval_Base_Column_List">
        id, user_name as userName, user_account_name as userAccountName, user_valid_start_time as userValidStartTime,
        user_valid_end_time as userValidEndTime, last_pwd_change_time as lastPwdChangeTime,
        pwd_change_interval as pwdChangeInterval, user_account_id as userAccountId
    </sql>

    <select id="selectParamCfgByCode" resultType="cn.hy.auth.custom.user.account.domain.BizgwPluginBusinessCfgDTO">
        select
        bus_cfg_key as code, bus_cfg_name as name, address as value, bus_cfg_desc as descr
        from bizgw_plugin_driver_business_cfg
        where bus_cfg_key = #{code}
    </select>

    <select id="selectPwdWhiteListByUser"
            resultType="cn.hy.auth.custom.user.account.domain.UserPwdChangeWhiteListDTO">
        select
        id, user_name as userName, user_account_name as userAccountName
        from sysdev_user_pwd_change_white_list
        where user_account_name = #{userAccountName}
    </select>

    <select id="selectPwdChangeIntervalByUser"
            resultType="cn.hy.auth.custom.user.account.domain.UserPwdChangeIntervalDTO">
        select
        <include refid="Pwd_Change_Interval_Base_Column_List"/>
        from sysdev_user_pwd_change_interval
        where user_account_name = #{userAccountName}
    </select>

    <select id="selectPwdChangeIntervalByUserId"
            resultType="cn.hy.auth.custom.user.account.domain.UserPwdChangeIntervalDTO">
        select
        <include refid="Pwd_Change_Interval_Base_Column_List"/>
        from sysdev_user_pwd_change_interval
        where user_account_id = #{userAccountId}
    </select>

    <select id="selectLoginBindIpByUser" resultType="cn.hy.auth.custom.user.account.domain.UserLoginBindIpDTO">
        select
        id, user_name as userName, user_account_name as userAccountName, restrict_login_address restrictLoginAddress,
        user_account_id as userAccountId
        from sysdev_user_login_bind_ip
        where user_account_name = #{userAccountName}
    </select>

    <select id="selectLoginBindIpListByFid"
            resultType="cn.hy.auth.custom.user.account.domain.UserLoginBindIpSublistDTO">
        select
        id, fid, initial_address initialAddress, final_address finalAddress
        from sysdev_user_login_bind_ip_sublist
        where fid in
        <foreach collection="fids" item="fid" open="(" separator="," close=")">
            #{fid}
        </foreach>
    </select>

    <update id="updatePwdChangeTimeStatus">
        update
        sysdev_user_pwd_change_interval
        set pwd_change_status = #{pwdChangeStatus},
        last_pwd_change_time = #{lastPwdChangeTime}
        where user_account_id = #{userAccountId}
    </update>

    <update id="updatePwdChangeStatus">
        update
        sysdev_user_pwd_change_interval
        set pwd_change_status = #{pwdChangeStatus}
        where user_account_name = #{userAccountName}
    </update>

    <insert id="insertPwdChangeIntervalInfo">
        insert into sysdev_user_pwd_change_interval
        (
        id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name,
        last_update_time, sequence, user_name, user_account_name, user_valid_start_time, user_valid_end_time,
        last_pwd_change_time, pwd_change_interval, _pwd_change_intervalname, user_account_id, pwd_change_status,
        _pwd_change_statusname, next_pwd_change_time
        )
        values
        (
        #{id,jdbcType=DECIMAL}, #{dataVersion,jdbcType=VARCHAR}, #{createUserId,jdbcType=DECIMAL},
        #{createUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=DECIMAL},
        #{lastUpdateUserName,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{sequence,jdbcType=BIGINT},
        #{userName,jdbcType=VARCHAR}, #{userAccountName,jdbcType=VARCHAR}, #{userValidStartTime,jdbcType=TIMESTAMP},
        #{userValidEndTime,jdbcType=TIMESTAMP}, #{lastPwdChangeTime,jdbcType=TIMESTAMP},
        #{pwdChangeInterval,jdbcType=VARCHAR}, #{pwdChangeIntervalName,jdbcType=VARCHAR},
        #{userAccountId,jdbcType=DECIMAL}, #{pwdChangeStatus,jdbcType=VARCHAR},
        #{pwdChangeStatusName,jdbcType=VARCHAR}, #{nextPwdChangeTime,jdbcType=TIMESTAMP}
        )
    </insert>

</mapper>
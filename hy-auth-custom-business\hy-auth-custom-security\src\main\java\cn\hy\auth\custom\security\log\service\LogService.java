package cn.hy.auth.custom.security.log.service;

import cn.hy.auth.custom.common.log.domain.LogDTO;
import cn.hy.auth.custom.common.log.event.LogEvent;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/6/2 11:08
 **/
public interface LogService {
    /**
     *  保存日志对象
     * @param logEvent .
     * @return .
     */
    void saveLogDto(LogEvent logEvent);

    /**
     * 清除缓存
     */
    void clearCache();
}

package cn.hy.auth.custom.route.indentify.impl;

import cn.hy.auth.custom.route.indentify.ThirdIndentifyClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 类描述：用于获取IAM系统TOKEN标识
 *
 * <AUTHOR>
 * @date 2022/11/17 14:12
 **/
@Service
@Slf4j
public class IAMThirdIndectifyServiceImpl implements ThirdIndentifyClient {

    public static final  String IAM_CODE="IAM_CODE";
    public static final  String IAM_TOKEN="IAM_TOKEN";

    @Override
    public String getThirdIndentify(String token) {
        if (token.length()<= 32 ) {
            log.info("THIRD REQUEST------> IAM_CODE");
            return IAM_CODE;
        } else {
            log.info("THIRD REQUEST------> IAM_TOKEN");
            return IAM_TOKEN;
        }
    }
}

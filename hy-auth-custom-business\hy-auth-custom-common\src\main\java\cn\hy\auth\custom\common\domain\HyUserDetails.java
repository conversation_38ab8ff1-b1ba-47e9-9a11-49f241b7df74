package cn.hy.auth.custom.common.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-26 15:42
 **/
@Accessors(chain = true)
public class HyUserDetails extends User {

    private static final long serialVersionUID = 8032100268994219950L;

    /**
     * 用户主键
     */
    @Getter
    @Setter
    private Long userId;
    /**
     * 用户账号表里的完整信息
     */
    @Getter
    @Setter
    private Map<String,Object> account;
    public HyUserDetails(String username, String password, boolean enabled, boolean accountNonExpired,
                         boolean credentialsNonExpired, boolean accountNonLocked) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked,
                Collections.emptyList());
    }

    public HyUserDetails(String username, String password) {
        super(username, password, true, true, true, true, Collections.emptyList());
    }

    /**
     * Calls the more complex constructor with all boolean arguments set to {@code true}.
     *
     * @param username
     * @param password
     * @param authorities
     */
    public HyUserDetails(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }

    /**
     * 根据UserAccountDTO构造HyUserDetails对象
     *
     * @param account 用户帐号信息
     * @return OAuth2的用户详情对象
     */
    public static HyUserDetails builderOf(@lombok.NonNull UserAccountDTO account) {
        return new HyUserBuilder().username(account.getUserAccountName())
                .password(account.getPassword())
                .isEnable(account.isEnabled())
                .isAccountNonExpired(account.isAccountNonExpired())
                .isAccountNonLocked(account.isAccountNonLocked())
                .isCredentialsNonExpired(account.isPasswordNonExpired())
                .build()
                .setUserId(account.getId())
                .setAccount(account.getAccount());
    }

    /**
     * 用户详情对象构造器
     */
    public static class HyUserBuilder {
        private HyUserBuilder() {
        }

        private String username;
        private String password;
        private boolean isAccountNonExpired;
        private boolean isAccountNonLocked;
        private boolean isCredentialsNonExpired;
        private boolean isEnable;
        public HyUserBuilder username(String username) {
            Assert.notNull(username, "username cannot be null");
            this.username = username;
            return this;
        }

        public HyUserBuilder password(String password) {
            Assert.notNull(password, "password cannot be null");
            this.password = password;
            return this;
        }

        /**
         * 用户是否为启用状态
         *
         * @return true:启用；false:禁用
         */
        public HyUserBuilder isEnable(boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        /**
         * 账号是否没有过期
         *
         * @return true:没过期；false:已过期
         */
        public HyUserBuilder isAccountNonExpired(boolean isAccountNonExpired) {
            this.isAccountNonExpired = isAccountNonExpired;
            return this;
        }

        /**
         * 账号是否没被锁定
         *
         * @return true:没锁定；false:已被锁定
         */
        public HyUserBuilder isAccountNonLocked(boolean isAccountNonLocked) {
            this.isAccountNonLocked = isAccountNonLocked;
            return this;
        }

        /**
         * 账号密码是否没有过期
         *
         * @return true:没过期；false:已过期
         */
        public HyUserBuilder isCredentialsNonExpired(boolean isCredentialsNonExpired) {
            this.isCredentialsNonExpired = isCredentialsNonExpired;
            return this;
        }

        public HyUserDetails build() {
            return new HyUserDetails(username, password, isEnable, isAccountNonExpired, isCredentialsNonExpired,
                    isAccountNonLocked);
        }
    }
}

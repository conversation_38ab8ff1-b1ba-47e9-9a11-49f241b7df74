management:
  health:
    defaults:
      enabled: false
    refresh:
      enabled: false
    hystrix:
      enabled: false
    diskSpace:
      enabled: false
    db:
      enabled: false
  endpoint:
    health:
      # 设置健康检查的详细信息始终可见
      show-details: always
#-----consul配置，此处参数从pom文件传入或者启动时指定新值-----
# 这个参数可以在docker启动时重新指定,默认值从pom文件读取
spring:
  profiles:
    active: ${ENV:prod}
  application:
    name: ${APP_NAME:HY-AUTH}
  cloud:
    discovery:
      client:
        health-indicator:
          enabled: false
    consul:
      host: ${CONSUL_HOST:**************}
      port: ${CONSUL_PORT:18500}
      config:
        enabled: true #false禁用Consul配置，默认true
        format: YAML    # 表示consul上面文件的格式 有四种 YAML PROPERTIES KEY-VALUE FILES
        data-key: ${REMOTE_CONF_DATA_KEY:data}    #表示consul上面的KEY值(或者说文件的名字) 默认是data
        prefix: config
        #prefix设置配置值的基本文件夹
        #defaultContext设置所有应用程序使用的文件夹名称
        #profileSeparator设置用于使用配置文件在属性源中分隔配置文件名称的分隔符的值
        acl-token: ${ACL_TOKEN:}
      discovery:
        register: false
        serviceName: ${APP_NAME:HY-AUTH}
        #prefer-ip-address: true
        hostname: ${AUTH_HOST:127.0.0.1}
        health-check-path: /actuator/health
        acl-token: ${ACL_TOKEN:}
        instanceId: ${spring.application.name}-${spring.cloud.consul.discovery.hostname}-${server.port}



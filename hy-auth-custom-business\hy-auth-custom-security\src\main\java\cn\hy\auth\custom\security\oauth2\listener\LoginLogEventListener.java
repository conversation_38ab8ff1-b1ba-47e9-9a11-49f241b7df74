package cn.hy.auth.custom.security.oauth2.listener;

import cn.hutool.core.date.DateUtil;
import cn.hy.auth.common.security.oauth2.util.TokenUtil;
import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import cn.hy.auth.custom.common.enums.LoginOutTypeEnum;
import cn.hy.auth.custom.common.log.AuthLogAop;
import cn.hy.auth.custom.common.log.domain.LogDTO;
import cn.hy.auth.custom.common.log.enums.ModuleNameEnum;
import cn.hy.auth.custom.common.log.enums.OperationResultEnum;
import cn.hy.auth.custom.common.log.enums.OperationTypeEnum;
import cn.hy.auth.custom.common.log.event.LogEvent;
import cn.hy.auth.custom.security.oauth2.event.LoginFailureLogEvent;
import cn.hy.auth.custom.security.oauth2.event.LoginSuccessLogEvent;
import cn.hy.auth.custom.user.history.service.AuthLoginInfoService;
import cn.hy.id.IdWorker;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * 登录日志处理
 *
 * <AUTHOR>
 * @date 2020-12-08 15:59
 **/
@Slf4j
@Component
@AllArgsConstructor
public class LoginLogEventListener {
    private final IdWorker idWorker;

    private final AuthLoginInfoService authLoginInfoService;
    private final ApplicationContext applicationContext;

    //@Async
    @EventListener
    public void onLoginSuccessLogEvent(LoginSuccessLogEvent event) {
        saveLoginInfo(event.getLoginInfo());
        publishLogEvent(event.getLoginInfo(),event.getLesseeCode(),event.getAppCode());
    }



    //@Async
    @EventListener
    public void onLoginFailureLogEvent(LoginFailureLogEvent event) {
        saveLoginInfo(event.getLoginInfo());
        publishLogEvent(event.getLoginInfo(),event.getLesseeCode(),event.getAppCode());
    }

    private void saveLoginInfo(UserLoginInfoDTO loginInfoDTO) {
        try {
            loginInfoDTO.setId(idWorker.nextId());
            loginInfoDTO.setCreateTime(DateUtil.date());
            loginInfoDTO.setLastUpdateTime(DateUtil.date());

            //版本
            loginInfoDTO.setDataVersion("1");

            //排序号
            //loginInfoDTO.setSequence(authLoginInfoService.getMaxSequence());

            //插入数据
            authLoginInfoService.insert(loginInfoDTO);
        } catch (Exception e) {
            log.error("记录登录日志失败,{} ", loginInfoDTO, e);
        }
    }

    /**
     *  发布日志记录时间
     * @param loginInfo .
     * @param lesseeCode .
     * @param appCode .
     */
    private void publishLogEvent(UserLoginInfoDTO loginInfo,String lesseeCode,String appCode) {
        LogDTO logDTO = LogDTO.builder()
                .ip(loginInfo.getCurrentIp())
                .appCode(appCode)
                .lesseeCode(lesseeCode)
                .beginTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .elapsedTime("0")
                .id(idWorker.nextId())
                .moduleName(ModuleNameEnum.USER_MANAGE.getMsg())
                .transactionId(AuthLogAop.getIncreaseUuid())
                .dataVersion(1L)
                .createUserId(loginInfo.getUserId())
                .userAccount(loginInfo.getUserAccountName())
                .userId(String.valueOf(loginInfo.getUserId()))
                .clientType(String.valueOf(loginInfo.getClientType()))
                .operationObj("登录")
                .operationType(OperationTypeEnum.LOGIN.getMsg())
                .createUserId(loginInfo.getUserId())
                .operationResult(OperationResultEnum.SUCCESS.getMsg())
                .operationResultDesc("操作成功")
                .build();
        if (LoginOutTypeEnum.LOGIN_FAILURE.getCode().equals(loginInfo.getType())) {
            // 登录失败
            logDTO.setOperationResult(OperationResultEnum.FAILURE.getMsg());
            logDTO.setOperationResultDesc(loginInfo.getErrorMessage());
        }
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            // 请求全路径
            logDTO.setUrl(request.getRequestURI());
            logDTO.setQueryString(request.getQueryString());
            logDTO.setRequiredMethod(request.getMethod());
        }
        applicationContext.publishEvent(new LogEvent(this, logDTO,true, TokenUtil.getTokenID()));
    }

}

package cn.hy.auth.custom.security.log.domain;

import cn.hy.auth.custom.common.log.domain.LogDTO;
import lombok.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * hy_8test8_auth_sys_log
 * 认证中心系统日志表
 * <AUTHOR>
 * @date   2022/06/02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AuthSysLog  {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人主键
     */
    private Long createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 排序序号
     */
    private Long sequence;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 最后修改人主键
     */
    private Long lastUpdateUserId;

    /**
     * 最后修改人名称
     */
    private String lastUpdateUserName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 业务流水号
     */
    private String transactionId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作对象
     */
    private String operationObj;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * ip
     */
    private String ip;

    /**
     * 操作结果
     */
    private String operationResult;

    /**
     * 操作结果描述
     */
    private String operationResultDesc;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求方式
     */
    private String requiredMethod;

    /**
     * 耗时
     */
    private String elapsedtime;

    /**
     * get方式的请求参数
     */
    private String queryString;

    /**
     * 备注描述
     */
    private String description;
    /**
     *  客户端标识
     */
    private String  clientType;
    /**
     *  表名
     */
    //private String tableName;

    public static AuthSysLog toAuthSysLog(LogDTO logDTO){
        return AuthSysLog.builder()
                .id(logDTO.getId())
                .createTime(logDTO.getCreateTime())
                .lastUpdateTime(logDTO.getLastUpdateTime())
                .createUserId(logDTO.getCreateUserId())
                .lastUpdateUserId(logDTO.getLastUpdateUserId())
                .transactionId(logDTO.getTransactionId())
                .userAccount(logDTO.getUserAccount())
                .operationType(logDTO.getOperationType())
                .operationObj(logDTO.getOperationObj())
                .operationResult(logDTO.getOperationResult())
                .operationResultDesc(logDTO.getOperationResultDesc())
                .description(logDTO.getDescription())
                .ip(logDTO.getIp())
                .url(logDTO.getUrl())
                .requiredMethod(logDTO.getRequiredMethod())
                .moduleName(logDTO.getModuleName())
                .queryString(logDTO.getQueryString())
                .elapsedtime(logDTO.getElapsedTime())
                .dataVersion(String.valueOf(logDTO.getDataVersion()))
                //.tableName(tableName)
                .clientType(logDTO.getClientType())
                .build();
    }
}
<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hy-authentication-center</artifactId>
        <groupId>cn.hy.auth</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hy-auth-custom-business</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>hy-auth-custom-security</module>
        <module>hy-auth-custom-common</module>
        <module>hy-auth-custom-multi-app</module>
        <module>hy-auth-custom-user</module>
        <module>hy-auth-custom-route</module>
    </modules>

    <name>hy-auth-custom-business</name>
    <url>http://www.example.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hy.auth</groupId>
            <artifactId>hy-auth-common-security-oauth2-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--依赖库表引擎-->
        <dependency>
            <groupId>cn.hy.dataengine</groupId>
            <artifactId>hy-data-engine-core</artifactId>
            <version>1.1.9-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>hy-metadata-engine</artifactId>
                    <groupId>cn.hy</groupId>
                </exclusion>

                <exclusion>
                    <groupId>javax.persistence</groupId>
                    <artifactId>persistence-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hy.dataengine.extend</groupId>
            <artifactId>hy-data-engine-metadata-extend</artifactId>
            <version>1.1.9-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>cn.hy.dataengine</groupId>
                    <artifactId>hy-data-engine-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>



        <dependency>
            <groupId>com.szgx.scmp</groupId>
            <artifactId>scmp-gateway-sdk-java</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.5</version>
        </dependency>

        <!-- https://search.maven.org/artifact/org.springframework.boot/spring-boot-starter-websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->

        </pluginManagement>
    </build>
</project>

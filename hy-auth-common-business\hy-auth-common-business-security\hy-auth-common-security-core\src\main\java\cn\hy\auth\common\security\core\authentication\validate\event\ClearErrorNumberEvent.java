package cn.hy.auth.common.security.core.authentication.validate.event;

import org.springframework.context.ApplicationEvent;

public class ClearErrorNumberEvent extends ApplicationEvent {

    private String mobile;

    private String mobileCipherText;

    private String accountName;

    private String lesseeCode;

    private String appCode;

    public ClearErrorNumberEvent(Object source) {
        super(source);
    }

    public ClearErrorNumberEvent(Object source, String mobile, String accountName, String mobileCipherText, String lesseeCode, String appCode) {
        super(source);
        this.mobile = mobile;
        this.accountName = accountName;
        this.mobileCipherText = mobileCipherText;
        this.lesseeCode = lesseeCode;
        this.appCode = appCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getMobileCipherText() {
        return mobileCipherText;
    }

    public void setMobileCipherText(String mobileCipherText) {
        this.mobileCipherText = mobileCipherText;
    }

    public String getLesseeCode() {
        return lesseeCode;
    }

    public void setLesseeCode(String lesseeCode) {
        this.lesseeCode = lesseeCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }
}

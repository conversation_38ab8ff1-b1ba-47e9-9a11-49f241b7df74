package cn.hy.auth.custom.user.cache.service.impl;

import cn.hy.auth.custom.user.cache.expiry.CacheExpiryFunction;
import cn.hy.auth.custom.user.cache.service.ForgetPasswordCacheManage;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class ForgetPasswordMapCacheManage implements ForgetPasswordCacheManage {

    protected final Cache<String, Object> cache = Caffeine.newBuilder()
            .expireAfter(new CacheExpiryFunction())
            .build();


    @Override
    public void put(String key, Object value) {
        cache.put(key, value);
    }

    @Override
    public void put(String key, Object value, Date expireDate) {
        cache.put(key, value);
    }

    @Override
    public void put(String key, Object value, long timeout, TimeUnit unit) {
        cache.put(key, value);
    }

    @Override
    public Object get(String key) {
        return cache.getIfPresent(key);
    }

    @Override
    public boolean invalidate(String key) {
        cache.invalidate(key);
        return true;
    }
}

package cn.hy.auth.common.security.oauth2.event;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.TokenRequest;

/**
 * 类描述：token 成功刷新事件
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/18 10:56
 **/
@ToString
public class TokenRefreshedSuccessEvent extends ApplicationEvent {

    /**
     * 请求参数封装
     */
    private TokenRequest tokenRequest;

    /**
     * 刷新后的token对象
     */
    private OAuth2AccessToken oAuth2AccessToken;

    public TokenRefreshedSuccessEvent(String refreshTokenValue, TokenRequest tokenRequest, OAuth2AccessToken oAuth2AccessToken) {
        super(refreshTokenValue);
        this.tokenRequest = tokenRequest;
        this.oAuth2AccessToken = oAuth2AccessToken;
    }

    /**
     * 刷新的原token值
     */
    public String getRefreshTokenValue() {
        return (String) getSource();
    }


    public TokenRequest getTokenRequest() {
        return tokenRequest;
    }


    public OAuth2AccessToken getoAuth2AccessToken() {
        return oAuth2AccessToken;
    }

}

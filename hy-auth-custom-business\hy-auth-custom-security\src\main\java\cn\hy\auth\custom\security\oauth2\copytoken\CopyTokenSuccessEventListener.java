package cn.hy.auth.custom.security.oauth2.copytoken;

import cn.hy.auth.common.business.tool.redis.service.RedisService;
import cn.hy.auth.common.security.oauth2.event.CopyTokenSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * CopyTokenSuccessEventListener：
 *
 * <AUTHOR>
 * @version 2024/12/05 20:40
 **/
@Slf4j
@Component
public class CopyTokenSuccessEventListener implements ApplicationListener<CopyTokenSuccessEvent> {
    private final CopyTokenService copyTokenService;

    public CopyTokenSuccessEventListener(CopyTokenService copyTokenService) {
       this.copyTokenService = copyTokenService;
    }

    @Override
    public void onApplicationEvent(CopyTokenSuccessEvent event) {
        String token = event.getToken().getValue();
        String name = event.getAuthentication().getName();
        Map<String,Object> details = (Map<String, Object>) event.getAuthentication().getDetails();
        String lesseeCode = (String) details.get("lessee_code");
        String appCode = (String) details.get("app_code");
        copyTokenService.cacheCopyToken(lesseeCode, appCode, name, token,event.getToken().getExpiresIn());
        copyTokenService.cacheCopyRefreshTokenToAccessToken(event.getToken().getRefreshToken().getValue(), token,event.getToken().getExpiresIn());
    }
}

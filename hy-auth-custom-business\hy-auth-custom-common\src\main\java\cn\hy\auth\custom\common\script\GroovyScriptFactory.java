package cn.hy.auth.custom.common.script;

import cn.hutool.crypto.digest.DigestUtil;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类描述：脚本工厂
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/10/12 14:18
 **/
@Slf4j
public class GroovyScriptFactory {
    private static final Map<String, GenericObjectPool<Script>> SCRIPT_MAP = new ConcurrentHashMap<>();
    private final GroovyShell groovyShell;
    public GroovyScriptFactory(){
        groovyShell = new GroovyShell();
    }

    public Script borrowScript(String scriptText){
        String key = DigestUtil.md5Hex(scriptText);
        GenericObjectPool<Script> scriptGenericObjectPool = SCRIPT_MAP.computeIfAbsent(key, k -> new GenericObjectPool<>(new ScriptPooledObjectFactory(scriptText,groovyShell), createGenericObjectPoolConfig()));
        try {
            return scriptGenericObjectPool.borrowObject();
        } catch (Exception e) {
           log.info("从对象池里获取Script对象失败，额外创建Script对象。scriptText：{}。errMsg: {}",scriptText,e.getMessage());
           return groovyShell.parse(scriptText);
        }
    }

    public void returnScript(String scriptText,Script script){
        //System.out.println("return "+script.toString());
        String key = DigestUtil.md5Hex(scriptText);
        GenericObjectPool<Script> scriptGenericObjectPool = SCRIPT_MAP.get(key);
        if (scriptGenericObjectPool!= null){
            scriptGenericObjectPool.returnObject(script);
        }
    }
    public static class ScriptPooledObjectFactory extends BasePooledObjectFactory<Script> {
        private final String scriptText;
        private final GroovyShell groovyShell;
        public ScriptPooledObjectFactory(String scriptText, GroovyShell groovyShell){
            this.scriptText = scriptText;
            this.groovyShell = groovyShell;
        }

        @Override
        public Script create() throws Exception {
            Script script = this.groovyShell.parse(scriptText);
          //  System.out.println("create "+script.toString());
            return script;
        }

        @Override
        public PooledObject<Script> wrap(Script obj) {
            return new DefaultPooledObject<>(obj);
        }
        /**
         * When an object is returned to the pool, clear the buffer.
         */
        @Override
        public void passivateObject(PooledObject<Script> pooledObject) {
            pooledObject.getObject().setBinding(new Binding());
        }

    }
    /**
     * 最大空闲数
     */
    @Value("${hy.auth.script.pool.maxIdel:500}")
    private int maxIdel = 400;
    /**
     * 最小空闲数, 池中只有一个空闲对象的时候，池会在创建一个对象，并借出一个对象，从而保证池中最小空闲数为1
     */
    @Value("${hy.auth.script.pool.minIdle:1}")
    private int minIdle = 1;
    /**
     * // 最大池对象总数
     */
    @Value("${hy.auth.script.pool.maxTotal:500}")
    private int maxTotal = 400;
    /**
     * // 逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
     */
    @Value("${hy.auth.script.pool.minEvictableIdleTimeMillis:-1}")
    private long minEvictableIdleTimeMillis = -1;
    /**
     * 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程
     */
    @Value("${hy.auth.script.pool.timeBetweenEvictionRunsMillis:-1}")
    private long timeBetweenEvictionRunsMillis = -1;
    /**
     * // 最大等待时间，毫秒单位 默认的值为-1，表示无限等待。
     */
    @Value("${hy.auth.script.pool.maxWaitMillis:6000}")
    private long maxWaitMillis = 6000;
    /**
     *  // 每次逐出检查时 逐出的最大数目 默认3
     */
    @Value("${hy.auth.script.pool.numTestsPerEvictionRun:1}")
    private int numTestsPerEvictionRun = 3;

    private GenericObjectPoolConfig<Script> createGenericObjectPoolConfig(){
        GenericObjectPoolConfig<Script> poolConfig = new GenericObjectPoolConfig<>();
        // 最大空闲数
        poolConfig.setMaxIdle(maxIdel);
        // 最小空闲数, 池中只有一个空闲对象的时候，池会在创建一个对象，并借出一个对象，从而保证池中最小空闲数为1
        poolConfig.setMinIdle(minIdle);
        // 最大池对象总数
        poolConfig.setMaxTotal(maxTotal);
        // 逐出连接的最小空闲时间 默认1800000毫秒(30分钟),-1表示对象不会被逐出资源池
        poolConfig.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        // 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
        poolConfig.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        // 在获取对象的时候检查有效性, 默认false
        poolConfig.setTestOnBorrow(true);
        // 在归还对象的时候检查有效性, 默认false
        poolConfig.setTestOnReturn(false);
        // 在空闲时检查有效性, 默认false
        poolConfig.setTestWhileIdle(false);
        // 最大等待时间，毫秒单位 默认的值为-1，表示无限等待。
        poolConfig.setMaxWaitMillis(maxWaitMillis);
        // 是否启用后进先出, 默认true
        poolConfig.setLifo(true);
        // 连接耗尽时是否阻塞, false报异常,true阻塞直到超时, 默认true
        poolConfig.setBlockWhenExhausted(true);
        // 每次逐出检查时 逐出的最大数目 默认3
        poolConfig.setNumTestsPerEvictionRun(numTestsPerEvictionRun);
        return poolConfig;
    }
}

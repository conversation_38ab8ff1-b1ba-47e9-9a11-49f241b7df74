package cn.hy.auth.custom.common.handle;

import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.error.AbstractOAuth2SecurityExceptionHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 类描述: 自定义认证异常抛出
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/16
 */
public class Oauth2SecurityExceptionHandler extends AbstractOAuth2SecurityExceptionHandler {

    public void commence(HttpServletRequest request, HttpServletResponse response, AuthBusinessException exception)
            throws IOException, ServletException {
        InvalidTokenException e400 = new InvalidTokenException(exception.getMessage()) {
            @Override
            public int getHttpErrorCode() {
                return 400;
            }
        };
        doHandle(request, response, e400);
    }
}

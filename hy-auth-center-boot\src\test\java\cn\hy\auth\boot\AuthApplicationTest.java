package cn.hy.auth.boot;

import cn.hy.auth.common.business.oauth2.starter.CommonAuthAutoConfiguration;
import cn.hy.auth.common.business.oauth2.starter.TokenAutoConfiguration;
import cn.hy.logging.autoconfigure.EnableHyLogging;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Unit test for simple App.
 */
@TestPropertySource(properties = "spring.jpa.hibernate.ddl=update")
@EnableHyLogging
@SpringBootApplication
@ComponentScan(basePackages = {"cn.hy.auth", "cn.hy.id", "cn.hy.dataengine", "cn.hy.metadata"})
@MapperScan({"cn.hy.auth.custom.user.**.dao"})
@ImportAutoConfiguration({CommonAuthAutoConfiguration.class, TokenAutoConfiguration.class})
@Slf4j
@EnableTransactionManagement
public class AuthApplicationTest {


    public static void main(String[] args) {
        SpringApplication.run(AuthApplicationTest.class, args);
    }
}

package cn.hy.auth.custom.security.oauth2.client;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.AuthSpringUtil;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import cn.hy.auth.custom.security.oauth2.client.dao.OauthClientDetailsMapper;
import cn.hy.auth.custom.security.oauth2.client.domain.OauthClientDetailsDO;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.core.env.Environment;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/18 17:25
 **/
@Slf4j
public class OauthClientDetailsServiceImpl implements ClientDetailsService {
    private final OauthClientDetailsMapper oauthClientDetailsMapper;
    private final ObjectMapper objectMapper;
    /**
     * 缓存实现类，该处已实现租户应用间的隔离
     */
    private final CacheManager cacheManager;
    /**
     * 跟主键标识
     */
    static final String ROOT_CACHE_NAME = "hy_auth_oauth_client";
    private final ConcurrentHashMap<String,Object> parallelLockMap = new ConcurrentHashMap<>();

    public OauthClientDetailsServiceImpl(OauthClientDetailsMapper oauthClientDetailsMapper, ObjectMapper objectMapper, CacheManager cacheManager) {
        this.oauthClientDetailsMapper = oauthClientDetailsMapper;
        this.objectMapper = objectMapper;
        this.cacheManager = cacheManager;
    }


    @Override
    public ClientDetails loadClientByClientId(String clientId) {
        OauthClientDetailsDO oauthClientDetailsDO = getOauthClientDetailsDoFromCache(clientId);
        if (oauthClientDetailsDO == null){
            String key = AuthContext.getContext().getLesseeCode()+"_"+AuthContext.getContext().getAppCode();
            //应用编码
            synchronized (getLock(key)){
                oauthClientDetailsDO = getOauthClientDetailsDoFromCache(clientId);
                if (oauthClientDetailsDO == null){
                    log.info("clientId:{},从数据库加载并放入缓存中",clientId);
                    oauthClientDetailsDO = loadClientByClientIdFromDb(clientId);
                    getRootCache().put(clientId,JSON.toJSONString(oauthClientDetailsDO));
                }
                removeLock(key);
            }

        }
        return createBaseClientDetail(oauthClientDetailsDO);
    }

    private OauthClientDetailsDO getOauthClientDetailsDoFromCache(String clientId) {
        String clientDetailsStr = getRootCache().get(clientId, String.class);
        OauthClientDetailsDO oauthClientDetailsDO = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(clientDetailsStr)){
            try {
                oauthClientDetailsDO = JSON.parseObject(clientDetailsStr,OauthClientDetailsDO.class);
             } catch (Exception e) {
                 log.warn("clientId:{},从缓存获取解析错误了。{}", clientId,e.getMessage(),e);
             }
        }
        return oauthClientDetailsDO;
    }

    /**
     * 获取根部缓存信息
     *
     * @return 返回缓存对象
     */
    private Cache getRootCache() {
        //租户编码
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        //应用编码
        String appCode = AuthContext.getContext().getAppCode();
        StringJoiner stringJoiner = new StringJoiner("_");
        stringJoiner.add(lesseeCode).add(appCode);
        String cacheName = stringJoiner+"_"+ROOT_CACHE_NAME;
        log.debug("OauthClientDetailsServiceImpl cacheName: {}",cacheName);
        Boolean autoAttachCachePrefix = CachePrefixUtil.isAutoAttachCcahePrefix();
        try {
            CachePrefixUtil.setAutoAttachCachePrefix(false);
            return cacheManager.getCache(cacheName);
        }finally {
            // 恢复原样
            CachePrefixUtil.setAutoAttachCachePrefix(autoAttachCachePrefix);
        }
    }

    private OauthClientDetailsDO loadClientByClientIdFromDb(String clientId) {
        // 加载默认的内置表
        OauthClientDetailsDO oauthClientDetailsDO = oauthClientDetailsMapper.selectByClientId(clientId);
        if (oauthClientDetailsDO == null) {
            throw new NoSuchClientException("No client with requested id: [" + clientId + "]");
        }
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        //应用编码
        String appCode = AuthContext.getContext().getAppCode();
        // 需要读取应用配置关于token的设置信息
        String clientIdStr = AuthSpringUtil.getBean(Environment.class).getProperty("hy.security.oauth2.useTokenConfig.clientId", "client_hy_web");
        Set<String> clientIds = new HashSet<>(Arrays.asList(clientIdStr.split(",")));
        if (clientIds.contains(oauthClientDetailsDO.getClientId())) {
            // 1、应用配置
                // "token_validate_time","auto_logout_no_opt"
                try {
                    List<Map<String, Object>> appTokenConfig = oauthClientDetailsMapper.selectAppTokenConfig();
                    if (appTokenConfig !=null){
                        Object tokenValidateTime = getFromMapList(appTokenConfig,"cfg_key","token_validate_time","cfg_val");
                        if (tokenValidateTime != null){
                            int accessTokenValidity = Integer.parseInt(tokenValidateTime.toString()) * 60;
                            if (accessTokenValidity>0){
                                oauthClientDetailsDO.setAccessTokenValidity(accessTokenValidity);
                                log.info("{}_{} 使用应用级别配置的token配置，token有效时间：{}s",lesseeCode,appCode,accessTokenValidity);
                                return oauthClientDetailsDO;
                            }
                            log.info("{}_{} 应用级别配置的token配置，token有效时间：{}s,不符合大于0的要求，舍弃不使用",lesseeCode,appCode,accessTokenValidity);
                        }
                    }
                }catch (Exception e){
                    log.warn("{}_{} 使用应用级别配置的token配置时出错了。",lesseeCode,appCode,e);
                }
                try {
                    // 2、全局配置
                    AuthContext.getContext().setAppCode("appmanage");
                    List<Map<String, Object>> globalTokenConfig = oauthClientDetailsMapper.selectGlobalTokenConfig();
                    Object tokenValidateTime = getFromMapList(globalTokenConfig,"bus_cfg_key","token_validate_time","address");
                    if (tokenValidateTime != null){
                        int accessTokenValidity = Integer.parseInt(tokenValidateTime.toString()) * 60;
                        if (accessTokenValidity > 0){
                            oauthClientDetailsDO.setAccessTokenValidity(accessTokenValidity);
                            log.info("{}_{} 使用全局级别配置的token配置，token有效时间：{}s",lesseeCode,appCode,accessTokenValidity);
                            return oauthClientDetailsDO;
                        }
                        log.info("{}_{} 全局级别配置的token配置，token有效时间：{}s,不符合大于0的要求，舍弃不使用",lesseeCode,appCode,accessTokenValidity);
                    }
                }catch (Exception e){
                    log.warn("{}_{} 使用全局级别配置的token配置时出错了。",lesseeCode,appCode,e);

                }finally {
                    AuthContext.getContext().setAppCode(appCode);
                }
        }
        log.info("{}_{} 使用默認的token有效期配置：{}",lesseeCode,appCode,oauthClientDetailsDO.getAccessTokenValidity());
        return oauthClientDetailsDO;
    }


    private Object getFromMapList(List<Map<String, Object>> mapList,String key,String keyValue,String value){
        if (CollectionUtils.isEmpty(mapList)){
            return null;
        }
        for (Map<String, Object> objectMap : mapList) {
            if (Objects.equals(objectMap.get(key),keyValue)) {
                return objectMap.get(value);
            }
        }
        return null;
    }
    private BaseClientDetails createBaseClientDetail(OauthClientDetailsDO oauthClientDetailsDO) {
        BaseClientDetails clientDetails = new BaseClientDetails(oauthClientDetailsDO.getClientId(),
                oauthClientDetailsDO.getResourceIds(),
                oauthClientDetailsDO.getScope(),
                oauthClientDetailsDO.getAuthorizedGrantTypes(),
                oauthClientDetailsDO.getAuthorities(),
                oauthClientDetailsDO.getWebServerRedirectUri()
        );

        clientDetails.setClientSecret(oauthClientDetailsDO.getClientSecret());
        if (oauthClientDetailsDO.getAccessTokenValidity() != null) {
            clientDetails.setAccessTokenValiditySeconds(oauthClientDetailsDO.getAccessTokenValidity());
        }
        if (oauthClientDetailsDO.getRefreshTokenValidity() != null) {
            clientDetails.setRefreshTokenValiditySeconds(oauthClientDetailsDO.getRefreshTokenValidity());
        }
        try {
            if (!StringUtils.isEmpty(oauthClientDetailsDO.getAdditionalInformation())) {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = objectMapper.readValue(oauthClientDetailsDO.getAdditionalInformation(), Map.class);
                clientDetails.setAdditionalInformation(map);
            }
        } catch (IOException e) {
            log.error("{} additionalInformation 字段内容有错误，无法序列化为Map", oauthClientDetailsDO.getClientId(), e);
        }
        String autoapproves = oauthClientDetailsDO.getAutoapprove();
        if (autoapproves != null) {
            clientDetails.setAutoApproveScopes(StringUtils.commaDelimitedListToSet(autoapproves));
        }
        return clientDetails;
    }

    private Object getLock(String identify) {
        // 线程安全
        return parallelLockMap.computeIfAbsent(identify, t -> new Object());

    }

    private void removeLock(String identify) {
        // 线程安全
        parallelLockMap.remove(identify);
    }

}

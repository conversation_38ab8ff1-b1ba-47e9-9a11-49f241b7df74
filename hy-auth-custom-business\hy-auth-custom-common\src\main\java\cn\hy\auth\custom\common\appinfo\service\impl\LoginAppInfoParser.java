package cn.hy.auth.custom.common.appinfo.service.impl;

import cn.hy.auth.custom.common.appinfo.domain.AppInfoDTO;
import cn.hy.auth.custom.common.appinfo.service.BaseAppInfoParser;
import cn.hy.auth.custom.common.constant.AuthenticateUriConst;
import cn.hy.auth.custom.common.constant.LoginParamterConts;
import cn.hy.auth.custom.common.enums.RequestTypeEnum;
import com.google.common.collect.Lists;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 登录类型请求的解析器
 *
 * <AUTHOR>
 * @date 2020-12-13 10:17
 **/
@Service
public class LoginAppInfoParser extends BaseAppInfoParser {

    /**
     * 登录类型的grantType
     */
    private static final List<String> LOGIN_GRANT_TYPES = Lists.newArrayList("client_credentials", "password");

    /**
     * 解析得到租户、应用编码信息已经请求分类
     *
     * @param request 请求信息
     * @return 第一个属性：租户、应用编码；第二个属性：请求类型
     */
    @Override
    public ImmutablePair<AppInfoDTO, RequestTypeEnum> parse(HttpServletRequest request) {

        if (supports(request)) {
            String lesseeCode = obtainParameter(request, LoginParamterConts.LESSEE_CODE);
            String appCode = obtainParameter(request, LoginParamterConts.APP_CODE);
            return ImmutablePair.of(AppInfoDTO.builder().lesseeCode(lesseeCode).appCode(appCode).build(),
                    RequestTypeEnum.LOGIN);
        }
        return null;
    }

    /**
     * 解析请求类型信息
     *
     * @param request 请求信息
     * @return 请求分类
     */
    @Override
    public RequestTypeEnum parseType(HttpServletRequest request) {
        if (supports(request)) {
            return RequestTypeEnum.LOGIN;
        }
        return null;
    }

    /**
     * 排序值，越小越靠前
     *
     * @return 排序值
     */
    @Override
    protected int order() {
        return 1;
    }

    /**
     * 支持的uri模板列表
     *
     * @return 支持的uri模板列表
     */
    @Override
    protected @NonNull List<String> uriPatterns() {
        return new ArrayList<>(requestTypeProperties.getLogin());
    }

    /**
     * 是否支持该处理
     *
     * @param request 请求信息
     * @return 当前实例是否支持对该请求处理
     */
    @Override
    protected boolean supports(@NonNull HttpServletRequest request) {
        boolean isSupportUri = super.supports(request);
        boolean isMatchGrantType = true;
        if (pathMatcher.match(AuthenticateUriConst.LOGIN_OAUTH2, request.getRequestURI())) {
            String grantType = obtainParameter(request, GRANT_TYPE_PARAM);
            isMatchGrantType = LOGIN_GRANT_TYPES.contains(grantType.toLowerCase());
        }

        return isSupportUri && isMatchGrantType;
    }
}

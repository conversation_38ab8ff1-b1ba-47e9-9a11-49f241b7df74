package cn.hy.auth.custom.route.service.impl;

import cn.hy.auth.custom.route.properties.RouteProperties;
import cn.hy.auth.custom.route.service.UrlPathMatchService;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/23 12:16
 **/
@Component
public class DefaultUrlPathMatchServiceImpl implements UrlPathMatchService {
    private RouteProperties routeProperties;
    /**
     * URL匹配验证工具类
     */
    private final AntPathMatcher pathMatcher;
    public DefaultUrlPathMatchServiceImpl(RouteProperties routeProperties) {
        this.routeProperties = routeProperties;
        this.pathMatcher = new AntPathMatcher();
    }

    @Override
    public boolean shouldFilter(HttpServletRequest request) {
        if (CollectionUtils.isEmpty(routeProperties.getFilterPattern())){
            return false;
        }
        for (String pattern : routeProperties.getFilterPattern()){
            if (pathMatcher.match(pattern,request.getRequestURI())){
                return true;
            }
        }
        return false;
    }
}

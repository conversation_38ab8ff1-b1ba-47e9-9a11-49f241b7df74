package cn.hy.auth.custom.security.oauth2.copytoken;

import cn.hy.auth.common.business.tool.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * CopyTokenService：
 *
 * <AUTHOR>
 * @version 2024/12/06 14:22
 **/
@Service
public class CopyTokenService {
    private final RedisService redisService;
    private int expireTime = 2 * 60 * 60;

    public CopyTokenService(RedisService redisService, @Value("${auth.copyToken.expireTime:0}") int expireTime) {
        this.redisService = redisService;
        this.expireTime = expireTime;
    }

    public void cacheCopyToken(String lesseeCode, String appCode, String name, String copyToken,int expiresIn) {
        String redisKey = createRedisKey(lesseeCode, appCode, name);
        List<Object> copyTokens = redisService.getList(redisKey);
        if (copyTokens == null) {
            copyTokens = new ArrayList<>();
        }
        if (!copyTokens.contains(copyToken)) {
            copyTokens.add(copyToken);
            redisService.setList(redisKey, copyTokens);
            if (expireTime > 0){
                expiresIn = expireTime;
            }
            redisService.expireKey(redisKey, expiresIn, TimeUnit.SECONDS);
        }
    }

    private String createRedisKey(String lesseeCode, String appCode, String name) {
        return String.format("copy_token:%s#%s#%s", lesseeCode, appCode, name);
    }

    /**
     * 从缓存中移除某个copyToken
     *
     * @param lesseeCode .
     * @param appCode    .
     * @param name       .
     * @param copyToken  .
     */
    public void removeCopyToken(String lesseeCode, String appCode, String name, String copyToken) {
        String redisKey = createRedisKey(lesseeCode, appCode, name);
        List<Object> copyTokens =  redisService.getList(redisKey);
        if (copyTokens != null && !copyTokens.isEmpty()) {
            copyTokens.remove(copyToken);
            if (copyTokens.isEmpty()){
                redisService.deleteKey(redisKey);
            } else {
                redisService.setList(redisKey,copyTokens);
            }
        }
    }

    public void removeAllCopyTokens(String lesseeCode, String appCode, String name) {
        String redisKey = createRedisKey(lesseeCode, appCode, name);
        redisService.deleteKey(redisKey);
    }

    public List<Object> getAllCopyTokens(String lesseeCode, String appCode, String name) {
        String redisKey = createRedisKey(lesseeCode, appCode, name);
        return  redisService.getList(redisKey);
    }

    public void cacheCopyRefreshTokenToAccessToken(String refreshToken, String token,int expireIn) {
        String key = "copy_token_refreshToken_to_AccessToken:"+refreshToken;
        redisService.set(key,token);
        if (expireTime > 0){
            expireIn = expireTime;
        }
        redisService.expireKey(key,expireIn,TimeUnit.SECONDS);
    }

       public String getCopyAccessToken(String refreshToken) {
        String key = "copy_token_refreshToken_to_AccessToken:"+refreshToken;
        return (String)redisService.get(key);
    }

        public void removeRefreshCopyAccessToken(String refreshToken) {
        String key = "copy_token_refreshToken_to_AccessToken:"+refreshToken;
        redisService.deleteKey(key);
    }
}

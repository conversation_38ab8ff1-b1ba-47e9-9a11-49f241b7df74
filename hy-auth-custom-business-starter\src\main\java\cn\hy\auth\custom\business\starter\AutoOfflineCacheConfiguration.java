package cn.hy.auth.custom.business.starter;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import cn.hy.auth.custom.common.utils.CachePrefixUtil;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 类描述: 自动下线缓存动态切换工具类
 * 暂时注释，不实现该功能
 * <AUTHOR>
 * @date ：创建于 2022/01/11
 */
@Slf4j
//@Configuration
public class AutoOfflineCacheConfiguration {

    /**
     * 过期时间(s),没有配置则为1小时
     */
    @Value(value = "${auth.cache.autoOffline.expire_time:3600}")
    private Long expireTime;

    @Bean
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "caffeine"
    )
    @Qualifier("autoOfflineCacheManager")
    public CacheManager cacheManagerWithCaffeine() {
        log.debug("autoOfflineCacheManager caffeine cache 配置");
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        Caffeine caffeine = Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(100)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight不可以同时使用，
                //.maximumSize(5000)
                .expireAfterWrite(expireTime, TimeUnit.SECONDS);
        cacheManager.setCaffeine(caffeine);
        //不允许设置空值
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    @Bean
    @ConditionalOnMissingBean(CacheManager.class)
    @ConditionalOnProperty(
            prefix = "auth.cache",
            name = "type",
            havingValue = "redis"
    )
    @Qualifier("autoOfflineCacheManager")
    public CacheManager cacheManagerWithRedis(RedisConnectionFactory redisConnectionFactory) {
        log.debug("autoOfflineCacheManager redis cache 配置");

        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                //不允许存入空值
                .disableCachingNullValues()
                .computePrefixWith(compute -> CachePrefixUtil.get() + compute + "::")
                //设置默认超过期时间是3600秒
                .entryTtl(Duration.ofSeconds(expireTime));
        //初始化RedisCacheManager
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig);
    }



}

package cn.hy.auth.custom.common.utils;

import cn.hy.auth.custom.common.constant.AuthenticateUriConst;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 登录类型匹配器
 *
 * <AUTHOR>
 * @date 2020-12-02 14:25
 **/
@Component
@Slf4j
public class LoginTypeMatcher {

    /**
     * 登录类型解析逻辑分配器
     */
    private Map<String, Function<HttpServletRequest, LoginTypeEnum>> parserDispatcher = new HashMap<>(4);
    private Map<String, LoginTypeEnum> grantTypePatterns = new HashMap<>();
    private final Map<String, LoginTypeEnum> clientNamePatterns = new HashMap<>();
    /**
     * URL匹配验证工具类
     */
    private AntPathMatcher pathMatcher = new AntPathMatcher();

    @PostConstruct
    private void init() {
        //初始化根据uri解析登录类型的分配器
        parserDispatcher.put(AuthenticateUriConst.LOGIN_USERNAME_PASSWORD,
                httpRequest -> LoginTypeEnum.USERNAME_PASSWORD);
        parserDispatcher.put(AuthenticateUriConst.LOGIN_SMS_CODE, httpRequest -> LoginTypeEnum.SMS_CODE);
        parserDispatcher.put(AuthenticateUriConst.LOGIN_OAUTH2, this::oauthParse);
        parserDispatcher.put(AuthenticateUriConst.LOGIN_RE_AUTH, this::reAuthParse);
        parserDispatcher.put(AuthenticateUriConst.LOGIN_COPY_TOKEN, httpRequest -> LoginTypeEnum.COPY_TOKEN);
        //初始化grantType与登录类型的映射
        grantTypePatterns.put("client_credentials", LoginTypeEnum.OAUTH2_CLIENT);
        grantTypePatterns.put("password", LoginTypeEnum.OAUTH2_PASSWORD);

        // 初始化二次认证中client_name与登录类型的映射
        clientNamePatterns.put("password", LoginTypeEnum.USERNAME_PASSWORD);
        clientNamePatterns.put("usbkey", LoginTypeEnum.USB_KEY);
    }

    /**
     * 匹配登录类型
     *
     * @param httpRequest 请求对象
     * @return 登录类型，匹配不到则返回null
     */
    public LoginTypeEnum match(HttpServletRequest httpRequest) {
        String uri = httpRequest.getRequestURI();

        log.debug("LoginType match, get uri is 【{}】", uri);
        //根据请求参数，解析登录类型
        return parserDispatcher.keySet()
                .stream()
                .filter(key -> pathMatcher.match(key, uri))
                .findFirst()
                .map(key -> parserDispatcher.get(key).apply(httpRequest))
                .orElse(null);
    }

    /**
     * OAuth登录模式下的登录类型判断
     *
     * @param httpRequest 请求对象
     * @return 登录类型，匹配不到则返回null
     */
    private LoginTypeEnum oauthParse(HttpServletRequest httpRequest) {
        String grantType = httpRequest.getParameter("grant_type");
        return MapUtils.getObject(grantTypePatterns, grantType, null);
    }

    private LoginTypeEnum reAuthParse(HttpServletRequest httpRequest) {
        String clientName = httpRequest.getParameter("client_name");
        return MapUtils.getObject(clientNamePatterns, clientName, null);
    }
}

package cn.hy.auth.custom.common.vo;

import cn.hy.auth.custom.common.enums.AuthErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 类描述：统一的响应结构体
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/5/29 18:11
 **/
@Data
@AllArgsConstructor
public class DefaultAuthResponse<R> implements AuthResponse {
    private String code;
    private String msg;
    private long timestamp = System.currentTimeMillis();
    private R result;

    private DefaultAuthResponse(String code, String msg, R result) {
        this.result = result;
        this.code = code;
        this.msg = msg;
    }

    /**
     * 成功返回
     *
     * @param code   业务编号
     * @param msg    提示信息
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultAuthResponse<R> success(String code, String msg, R result) {
        return new DefaultAuthResponse<>(code, msg, result);
    }

    /**
     * 成功返回
     *
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultAuthResponse<R> success(R result) {
        return success(AuthErrorCodeEnum.SUCCESS, result);
    }

    /**
     * 成功返回
     *
     * @param code   提示信息
     * @param result 结果
     * @param <R>    结果的类型
     * @return 响应对象
     */
    public static <R> DefaultAuthResponse<R> success(AuthErrorCodeEnum code, R result) {
        return success(code.code(), code.msg(), result);
    }

    /**
     * 失败返回，结果集不设置
     *
     * @param code 业务编号
     * @param msg  提示信息
     * @return 响应对象
     */
    public static DefaultAuthResponse<String> failure(String code, String msg) {
        return new DefaultAuthResponse<>(code, msg, null);
    }

    /**
     * 失败返回，结果集不设置
     *
     * @param code 业务提示
     * @return 响应对象
     */
    public static DefaultAuthResponse<String> failure(AuthErrorCodeEnum code) {
        return failure(code.code(), code.msg());
    }


    @Override
    public R getResult() {
        return result;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public long getTimestamp() {
        return timestamp;
    }
}

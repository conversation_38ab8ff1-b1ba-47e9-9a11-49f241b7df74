package cn.hy.auth.custom.user.account.dao;

import cn.hy.auth.custom.user.account.domain.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: UserSecurityManageDao
 * @description: 用户安全管理：用户密码变更时间间隔、用户登陆绑定IP
 * @date 2024/1/17
 */
public interface UserSecurityManageDao {

    /**
     * 查询应用设置，判断是否有对应内置功能
     * 用户更换密码开关：user_change_pwd_switch
     * 限制用户ip访问开关：restrict_user_ip_switch
     *
     * @return 结果集
     */
    List<BizgwPluginBusinessCfgDTO> selectParamCfgByCode(@Param("code") String code);

    /**
     * 查询用户密码变更白名单
     *
     * @param userAccountName 账号名
     * @return 结果集
     */
    List<UserPwdChangeWhiteListDTO> selectPwdWhiteListByUser(@Param("userAccountName") String userAccountName);

    /**
     * 查询用户密码变更时间间隔
     *
     * @param userAccountName 账号名
     * @return 结果集
     */
    List<UserPwdChangeIntervalDTO> selectPwdChangeIntervalByUser(@Param("userAccountName") String userAccountName);

    /**
     * 查询用户密码变更时间间隔
     *
     * @param userAccountId 用户账号主键
     * @return 结果集
     */
    List<UserPwdChangeIntervalDTO> selectPwdChangeIntervalByUserId(@Param("userAccountId") Long userAccountId);

    /**
     * 查询用户绑定IP信息
     *
     * @param userAccountName 账号名
     * @return 结果集
     */
    List<UserLoginBindIpDTO> selectLoginBindIpByUser(@Param("userAccountName") String userAccountName);

    /**
     * 查询用户绑定IP子表信息
     *
     * @param fids 用户绑定IP表主键列表
     * @return 结果集
     */
    List<UserLoginBindIpSublistDTO> selectLoginBindIpListByFid(@Param("fids") List<BigDecimal> fids);

    /**
     * 更新用户变更时间间隔信息
     *
     * @param userAccountId     用户账号主键
     * @param pwdChangeStatus   密码更新状态
     * @param lastPwdChangeTime 密码最近修改时间
     */
    void updatePwdChangeTimeStatus(@Param("userAccountId") Long userAccountId,
                                   @Param("pwdChangeStatus") String pwdChangeStatus,
                                   @Param("lastPwdChangeTime") Date lastPwdChangeTime);

    /**
     * 更新密码更新状态
     *
     * @param userAccountName 账号名
     * @param pwdChangeStatus 密码更新状态
     */
    void updatePwdChangeStatus(@Param("userAccountName") String userAccountName,
                               @Param("pwdChangeStatus") String pwdChangeStatus);

    /**
     * 新增用户密码变更时间间隔
     *
     * @param userPwdChangeIntervalDTO 用户密码变更时间间隔对象
     */
    void insertPwdChangeIntervalInfo(UserPwdChangeIntervalDTO userPwdChangeIntervalDTO);

}

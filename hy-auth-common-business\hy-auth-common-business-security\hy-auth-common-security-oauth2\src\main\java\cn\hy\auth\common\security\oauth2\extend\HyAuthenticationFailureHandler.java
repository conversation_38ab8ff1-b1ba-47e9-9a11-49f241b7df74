package cn.hy.auth.common.security.oauth2.extend;

import cn.hy.auth.common.security.core.authentication.mobile.SmsCodeAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationToken;
import cn.hy.auth.common.security.core.properties.SecurityProperties;
import cn.hy.auth.common.security.oauth2.event.LoginFailureEvent;
import cn.hy.auth.common.security.oauth2.util.RequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * * 登录校验失败的处理器（自定义业务抛出异常也会进入此处）
 * * （账号密码、手机短信登录的失败处理器）
 * * 目前确定是支持security 自带登录方式的
 *
 * <AUTHOR>
 * @date 2020/11/11  10:06
 */
@Slf4j
public class HyAuthenticationFailureHandler extends SimpleUrlAuthenticationFailureHandler {
    private ObjectMapper objectMapper;
    private SecurityProperties securityProperties;
    private ApplicationContext applicationContext;
    private WebResponseExceptionTranslator webResponseExceptionTranslator;
    public HyAuthenticationFailureHandler(ObjectMapper objectMapper,
                                          SecurityProperties securityProperties,
                                          WebResponseExceptionTranslator webResponseExceptionTranslator,
                                          ApplicationContext applicationContext) {
        this.objectMapper = objectMapper;
        this.securityProperties = securityProperties;
        this.webResponseExceptionTranslator = webResponseExceptionTranslator;
        this.applicationContext = applicationContext;
    }

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        log.info("{},{} 登录失败. {} ", RequestUtil.getRequestUrl(request), RequestUtil.getAllRequestParam(request, securityProperties.getFormAuth().getPasswordParameter(), "password"), exception.getMessage());
        // JSON 格式的返回
        if (securityProperties.getResponse().isJsonFormat()) {
            response.setContentType("application/json;charset=UTF-8");
            try {
                ResponseEntity responseEntity = webResponseExceptionTranslator.translate(exception);
                response.setStatus(responseEntity.getStatusCode().value());
                for (Map.Entry<String, List<String>> stringListEntry : responseEntity.getHeaders().entrySet()) {
                    String key = stringListEntry.getKey();
                    String value = CollectionUtils.size(stringListEntry.getValue()) > 0?stringListEntry.getValue().get(0):"";
                    response.setHeader(key, value);
                }
                response.getWriter().write(objectMapper.writeValueAsString( responseEntity.getBody()));
            } catch (Exception e) {
                log.error("webResponseExceptionTranslator 转义exception error ",e);
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                Map<String, String> result = new HashMap<>(4);
                result.put("error", OAuth2Exception.INVALID_REQUEST);
                result.put("error_description", exception.getMessage());
                response.getWriter().write(objectMapper.writeValueAsString(result));
            }
        } else {
            super.onAuthenticationFailure(request, response, exception);
        }
        if (!isOnlyToken(request)) {
            publishFailureEvent(createAuthentication(request), request, exception);
        }
    }

    private Authentication createAuthentication(HttpServletRequest request){
        String requestUrl = RequestUtil.getRequestUrl(request);
        if (StringUtils.equals(securityProperties.getFormAuth().getLoginProcessingUrl(), requestUrl)) {
            // 表单密码登录
            // 1 用户账号 2 密码
            return new UsernamePasswordAuthenticationToken(request.getParameter(securityProperties.getFormAuth().getUsernameParameter()),
                    request.getParameter(securityProperties.getFormAuth().getPasswordParameter()));
        } else if (StringUtils.equals(securityProperties.getSmsAuth().getLoginUrl(), requestUrl)) {
            // 手机短信登录
            return new SmsCodeAuthenticationToken(request.getParameter(securityProperties.getSmsAuth().getMobileParameter()));
        } else if (StringUtils.equals(securityProperties.getHySocialProperties().getAuthProcessingUrl(), requestUrl)){
            // 社交登录
            String code = request.getParameter(securityProperties.getHySocialProperties().getCodeParameter());
            String providerId = request.getParameter(securityProperties.getHySocialProperties().getProviderIdParamter());
            String lessCode = request.getParameter(securityProperties.getHySocialProperties().getLesseeCodeParamter());
            String appCode = request.getParameter(securityProperties.getHySocialProperties().getAppCodeParamter());
            String loginType = request.getParameter(securityProperties.getHySocialProperties().getLoginType());
            return new HySocialAuthenticationToken(code,code,providerId,lessCode,appCode,loginType);
        }
        return null;
    }

    private Boolean isOnlyToken(HttpServletRequest request){
        String requestUrl = RequestUtil.getRequestUrl(request);
        if (StringUtils.equals(securityProperties.getReAuthenticationProperties().getAuthProcessingUrl(), requestUrl)) {
            String onlyToken = request.getParameter(securityProperties.getReAuthenticationProperties().getOnlyTokenParameter());
            return "1".equals(onlyToken);
        }
        return false;
    }

    /**
     * 发送登录验证失败的事件
     *
     * @param exception .
     */
    private void publishFailureEvent(Authentication authentication, HttpServletRequest request,AuthenticationException exception) {
        LoginFailureEvent loginFailureEvent = new LoginFailureEvent(exception);
        loginFailureEvent.setAuthentication(authentication);
        loginFailureEvent.setRequest(request);
        applicationContext.publishEvent(loginFailureEvent);
    }

}

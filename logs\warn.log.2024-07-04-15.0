2024-07-04 15:23:12.715 [paas,sys,/login,,5efabc01aada4f20b68ff08090c347a5] [http-nio-6060-exec-4] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.77dbc8dc-ed0f-49df-a300-312426ba0713]获取用户主键失败,获取为空
2024-07-04 15:24:10.779 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m7s119ms873µs200ns).
2024-07-04 15:25:20.065 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m9s285ms720µs700ns).
2024-07-04 15:27:19.286 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53s7ms239µs500ns).
2024-07-04 15:31:14.542 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m55s255ms746µs800ns).
2024-07-04 15:42:26.679 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710]获取用户主键失败,获取为空
2024-07-04 15:42:26.700 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] WARN  c.h.a.c.s.c.a.c.AbstractAuthenticationProvider - Provider has error:{"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"}
cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"}
	at cn.hy.auth.custom.security.oauth2.provider.PwdExpirationAndForceModifyProviderer.doAuthenticate(PwdExpirationAndForceModifyProviderer.java:80)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 15:42:26.705 [paas,sys,/login,,efd3441cad494de1bdb0dd0f0a4847e5] [http-nio-6060-exec-9] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"}
org.springframework.security.authentication.InternalAuthenticationServiceException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"}
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:33)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: cn.hy.auth.custom.common.exceptions.AuthBusinessException: {"code":"A0245","msg":"您的密码为默认密码，请立即修改","token":"paas.sys.4.7cd71183-70fe-4167-b8ba-85ba8db40710"}
	at cn.hy.auth.custom.security.oauth2.provider.PwdExpirationAndForceModifyProviderer.doAuthenticate(PwdExpirationAndForceModifyProviderer.java:80)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	... 109 common frames omitted
2024-07-04 15:48:54.951 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m8s86ms236µs300ns).
2024-07-04 15:50:45.891 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] WARN  c.h.a.c.s.c.a.c.AbstractAuthenticationProvider - Provider has error:用户名或密码错误
org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at cn.hy.auth.custom.security.oauth2.provider.PwdAuthenticationProviderer.doAuthenticate(PwdAuthenticationProviderer.java:97)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 15:50:45.894 [paas,sys,/login,,1152352beb064d62a5d8a2fd8ae86eb4] [http-nio-6060-exec-1] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - 用户名或密码错误
org.springframework.security.authentication.InternalAuthenticationServiceException: 用户名或密码错误
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:33)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:175)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:94)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at cn.hy.auth.common.security.core.authentication.mobile.SmsCodeValidateFilter.doFilterInternal(SmsCodeValidateFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:74)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginStateInitFilter.doMyFilter(LoginStateInitFilter.java:51)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.LoginSupportTypeFilter.doMyFilter(LoginSupportTypeFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.SliderCaptchaFilter.doMyFilter(SliderCaptchaFilter.java:67)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at cn.hy.auth.custom.security.oauth2.provider.PwdAuthenticationProviderer.doAuthenticate(PwdAuthenticationProviderer.java:97)
	at cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider.authenticate(AbstractAuthenticationProvider.java:29)
	... 109 common frames omitted
2024-07-04 15:50:49.150 [paas,sys,/login,,ddadf718fed7497e8ab2bfe8891c2652] [http-nio-6060-exec-3] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - {"code":"A0205","msg":"需要验证码"}
cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler$1: {"code":"A0205","msg":"需要验证码"}
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.commence(Oauth2SecurityExceptionHandler.java:22)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 15:51:06.498 [paas,sys,/login,,4b696a98b90642a5afb19a06d62a6464] [http-nio-6060-exec-2] WARN  c.h.a.c.s.o.e.HyWebResponseExceptionTranslator - {"code":"A0205","msg":"需要验证码"}
cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler$1: {"code":"A0205","msg":"需要验证码"}
	at cn.hy.auth.custom.common.handle.Oauth2SecurityExceptionHandler.commence(Oauth2SecurityExceptionHandler.java:22)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AppAuthInfoEixtFilter.doMyFilter(AppAuthInfoEixtFilter.java:56)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.multi.filter.AuthContextFilter.doMyFilter(AuthContextFilter.java:125)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.OauthEndpointParamValidateFilter.doMyFilter(OauthEndpointParamValidateFilter.java:69)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.security.filter.AppendClientParametersFilter.doMyFilter(AppendClientParametersFilter.java:65)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.auth.custom.route.filter.RequestRouteFilter.doMyFilter(RequestRouteFilter.java:53)
	at cn.hy.auth.custom.common.filter.AbstractAuthFilter.doFilterInternal(AbstractAuthFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.hy.license.sdk.filter.LicenseFilter.doFilter(LicenseFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ddlMapper' and 'cn.hy.metadata.engine.api.md.dao.DdlMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'fieldPropertyMapper' and 'cn.hy.metadata.engine.api.md.dao.FieldPropertyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'foreignKeyMapper' and 'cn.hy.metadata.engine.api.md.dao.ForeignKeyMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'indexMapper' and 'cn.hy.metadata.engine.api.md.dao.IndexMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonClipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonClipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonCustomDictMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonCustomDictMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDictRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDictRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.796 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonDynamicFieldsMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonDynamicFieldsMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'jsonFieldsConditionRelationshipMapper' and 'cn.hy.metadata.engine.api.md.dao.JsonFieldsConditionRelationshipMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'superTableMapper' and 'cn.hy.metadata.engine.api.md.dao.SuperTableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableMapper' and 'cn.hy.metadata.engine.api.md.dao.TableMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tableRevisionChangeMapper' and 'cn.hy.metadata.engine.api.md.dao.TableRevisionChangeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'treeMapper' and 'cn.hy.metadata.engine.api.md.dao.TreeMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'triggerMapper' and 'cn.hy.metadata.engine.api.md.dao.TriggerMapper' mapperInterface. Bean already defined with the same name!
2024-07-04 15:51:27.797 [,,,,Auth is starting] [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.hy.metadata.engine.api.*.dao]' package. Please check your configuration.
2024-07-04 15:51:28.493 [,,,,Auth is starting] [main] WARN  o.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2024-07-04 15:51:30.842 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 15:51:43.681 [,,,,Auth is starting] [main] WARN  c.h.m.e.c.v.service.loader.DefaultVerifyRuleLoader - 业务校验规则初始化--传入规则对象的规则编码为空,丢弃该规则。rule:[cn.hy.metadata.engine.verifier.db.rule.single.DbDecimalSizeRule@3f314bad]
2024-07-04 15:51:45.571 [,,,,Auth is starting] [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-07-04 15:51:46.154 [,,,,Auth is starting] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration$JpaWebMvcConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-04 15:51:48.254 [,,,,Auth is starting] [main] WARN  o.s.cloud.netflix.core.CoreAutoConfiguration - This module is deprecated. It will be removed in the next major release. Please use spring-cloud-netflix-hystrix instead.
2024-07-04 15:51:49.681 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 15:51:49.681 [,,,,Auth is starting] [main] WARN  org.apache.dubbo.common.config.ConfigurationUtils -  [DUBBO] You specified the config center, but there's not even one single config item in it., dubbo version: 2.7.8, current host: *********
2024-07-04 15:52:24.908 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] WARN  c.h.a.c.m.a.service.AppAuthStrategyManagerImpl - 租户编码【paas】,应用编码【sys】,在user_account表不存在登录字段【mobile】,不启用该登录字段. 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
### The error may exist in file [D:\flowservice\hy-authentication-center\hy-auth-custom-business\hy-auth-custom-multi-app\target\classes\mapper\authinfo\AppAuthStrategyMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT mobile FROM paas_sys_user_account WHERE id = 1;
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'mobile' in 'field list'
2024-07-04 15:52:25.020 [paas,sys,/login,,eb7f026b527a4893a43be76b338495e2] [http-nio-6060-exec-1] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.0369b7ed-0842-414d-9f10-6cd47696b6b5]获取用户主键失败,获取为空
2024-07-04 15:53:38.174 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s233ms40µs800ns).
2024-07-04 15:53:49.051 [paas,sys,/login,,af6f7cd32b9c42fdbb27e8f2b5dc5981] [http-nio-6060-exec-10] WARN  c.h.a.c.u.cache.service.impl.UserCacheServiceImpl - 按tokenId[paas.sys.4.ca137a4c-a309-4146-b38f-a7e7ebadf92d]获取用户主键失败,获取为空
2024-07-04 15:54:25.827 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s653ms335µs300ns).
2024-07-04 15:55:21.125 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=55s297ms743µs200ns).
2024-07-04 15:56:24.497 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s372ms201µs700ns).
2024-07-04 15:58:58.662 [,,,,Not Auth Request!] [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m32s70ms314µs700ns).

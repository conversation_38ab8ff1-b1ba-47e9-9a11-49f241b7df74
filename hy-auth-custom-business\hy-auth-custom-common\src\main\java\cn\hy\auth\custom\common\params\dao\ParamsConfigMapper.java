package cn.hy.auth.custom.common.params.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 获取配置参数工具mapper
 * <AUTHOR>
 * @Date 2024/3/13
 */
@Mapper
public interface ParamsConfigMapper {

    /**
     * 获取配置值（bizgw_plugin_driver_business_cfg）
     *
     * @param lesseeCode 租户编码
     * @param appCode 应用编码
     * @param key 配置键
     * @return 。
     */
    String getAddressByBizgw(@Param("lesseeCode")String lesseeCode,@Param("appCode")String appCode,@Param("key")String key);

    /**
     * 获取配置值（app_config）
     *
     * @param lesseeCode 租户编码
     * @param appCode 应用编码
     * @param key 配置键
     * @return 。
     */
    String getAddressByConfig(@Param("lesseeCode")String lesseeCode,@Param("appCode")String appCode,@Param("key")String key);

    /**
     * 获取组户级配置值
     *
     * @param lesseeCode 租户编码
     * @param key 配置键
     * @return 。
     */
    String getLesseeAddress(@Param("lesseeCode")String lesseeCode,@Param("key")String key);
}

package cn.hy.auth.custom.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UserResetPasswordType {

    SECURITY_QUESTION("0", "FORGET_PASSWORD_SECURITY_QUESTION"),

    PHON<PERSON>_SMS_CODE("1", "FORGET_PASSWORD_SMS"),

    EMAIL_CODE("2", "FORGET_PASSWORD_EMAIL");

    private String type;

    private String typeCode;

    public static boolean isIncludeType(String type) {
        for (UserResetPasswordType resetPasswordType : values()) {
            if (resetPasswordType.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }

    public static UserResetPasswordType getByType(String type) {
        for (UserResetPasswordType resetPasswordType : values()) {
            if (resetPasswordType.getType().equals(type)) {
                return resetPasswordType;
            }
        }
        return null;
    }

}

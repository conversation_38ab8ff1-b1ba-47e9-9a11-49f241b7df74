package cn.hy.auth.common.security.oauth2.extend;

import cn.hy.auth.common.security.oauth2.constant.Oauth2Constant;
import cn.hy.auth.common.security.oauth2.event.LoginFailureEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEvent;
import cn.hy.auth.common.security.oauth2.event.LoginSuccessEventType;
import cn.hy.auth.common.security.oauth2.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.exceptions.BadClientCredentialsException;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 重写ClientCredentialsTokenEndpointFilter
 * 1、实现客户端自定义异常处理
 * 2、发出oauth2 password和client 模式登录成功和失败的自定义事件
 * oauth2 里的client_id 和 client_secret都是写死的，不可配置更改
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/12 12:25
 **/
public class CustomClientCredentialsTokenEndpointFilter extends ClientCredentialsTokenEndpointFilter {
    private static final Logger log = LoggerFactory.getLogger(CustomClientCredentialsTokenEndpointFilter.class);
    private AuthorizationServerSecurityConfigurer configurer;
    private AuthenticationEntryPoint authenticationEntryPoint;
    private ApplicationContext applicationContext;

    public CustomClientCredentialsTokenEndpointFilter(AuthorizationServerSecurityConfigurer configurer) {
        this.configurer = configurer;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException, ServletException {
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            throw new HttpRequestMethodNotSupportedException(request.getMethod(), new String[]{"POST"});
        }

        String clientId = request.getParameter("client_id");
        String clientSecret = request.getParameter("client_secret");

        // If the appinfo is already authenticated we can assume that this
        // filter is not needed
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            return authentication;
        }

        if (clientId == null) {
            throw new BadCredentialsException("No client credentials presented");
        }

        if (clientSecret == null) {
            clientSecret = "";
        }

        clientId = clientId.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(clientId,
                clientSecret);
        // fxr 增加request的请求参数(排除password)到UsernamePasswordAuthenticationToken中，
        // 其他内容不变，和父类ClientCredentialsTokenEndpointFilter一样
        authRequest.setDetails(RequestUtil.getAllRequestParam(request, Oauth2Constant.PASSWORD, Oauth2Constant.CLIENT_SECRET));
        return this.getAuthenticationManager().authenticate(authRequest);
    }

    @Override
    public void setAuthenticationEntryPoint(AuthenticationEntryPoint authenticationEntryPoint) {
        super.setAuthenticationEntryPoint(authenticationEntryPoint);
        this.authenticationEntryPoint = authenticationEntryPoint;
    }

    @Override
    public void afterPropertiesSet() {
        setAuthenticationFailureHandler((request, response, exception) -> {
            if (exception instanceof BadCredentialsException) {
                exception = new BadCredentialsException(exception.getMessage(), new BadClientCredentialsException());
            }

            // 客户端发出认证失败消息. password 模式不会因为client_id 和client_scrent 错误发出登录失败事件
            if (Oauth2Constant.CLIENT_CREDENTIALS.equals(request.getParameter(Oauth2Constant.GRANT_TYPE))) {
                // 1 用户账号 2 密码
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(request.getParameter(Oauth2Constant.CLIENT_ID), request.getParameter(Oauth2Constant.CLIENT_SECRET));
                publishFailureEvent(authenticationToken, request, exception);
            }

            log.error(exception.getMessage(), exception);
            authenticationEntryPoint.commence(request, response, exception);
        });
        setAuthenticationSuccessHandler((request, response, authentication) -> {
            // no-op - just allow filter chain to continue to token endpoint

        });
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain, Authentication authResult) throws IOException, ServletException {
        try {
            super.successfulAuthentication(request, response, chain, authResult);
            if (response.getStatus() == HttpStatus.OK.value()) {
                publicOauth2LoginSuccessEvent(request);
            } else {
                String errorMsg = response.getHeader("WWW-Authenticate");
                publicOauth2LoginFailureEvent(request, new Exception(errorMsg));
            }
        } catch (Exception e) {
            //publicOauth2LoginSuccessEvent 或publicOauth2LoginFailureEvent代码不报错情况下，正常流程是不会执行这里的。
            publicOauth2LoginFailureEvent(request, e);
            throw e;
        }
    }

    @Override
    protected AuthenticationManager getAuthenticationManager() {
        return configurer.and().getSharedObject(AuthenticationManager.class);
    }

    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 发出登录成功事件，包含password模式和client模式登录成功事件
     *
     * @param request .
     */
    private void publicOauth2LoginSuccessEvent(HttpServletRequest request) {
        if (Oauth2Constant.PASSWORD.equals(request.getParameter(Oauth2Constant.GRANT_TYPE))) {
            String username = request.getParameter(Oauth2Constant.USERNAME);
            String password = request.getParameter(Oauth2Constant.PASSWORD);
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            authenticationToken.setDetails(RequestUtil.getAllRequestParam(request, Oauth2Constant.PASSWORD, Oauth2Constant.CLIENT_SECRET));
            publishSuccessEvent(authenticationToken);
        } else if (Oauth2Constant.CLIENT_CREDENTIALS.equals(request.getParameter(Oauth2Constant.GRANT_TYPE))) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            publishSuccessEvent(authentication);
        }
    }

    /**
     * 发出登录失败事件, 仅发出账号密码模式的登录失败事件
     *
     * @param request .
     */
    private void publicOauth2LoginFailureEvent(HttpServletRequest request, Exception e) {
        // 账号密码登录失败。发出认证失败消息
        if (Oauth2Constant.PASSWORD.equals(request.getParameter(Oauth2Constant.GRANT_TYPE))) {
            // 1 用户账号 2 密码
            String username = request.getParameter(Oauth2Constant.USERNAME);
            String password = request.getParameter(Oauth2Constant.PASSWORD);
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            authenticationToken.setDetails(RequestUtil.getAllRequestParam(request, Oauth2Constant.PASSWORD, Oauth2Constant.CLIENT_SECRET));
            publishFailureEvent(authenticationToken, request, e);
        }
    }

    /**
     * 发送登录验证成功的时间
     *
     * @param authentication .
     */
    private void publishSuccessEvent(Authentication authentication) {
        if (applicationContext != null) {
            LoginSuccessEvent loginSuccessEvent = new LoginSuccessEvent(authentication, LoginSuccessEventType.OAUTH2_LOGIN);
            applicationContext.publishEvent(loginSuccessEvent);
        }

    }

    /**
     * 发送登录验证失败的事件
     *
     * @param request   .
     * @param exception .
     */
    private void publishFailureEvent(Authentication authentication, HttpServletRequest request, Exception exception) {
        if (applicationContext != null) {
            LoginFailureEvent loginFailureEvent = new LoginFailureEvent(exception);
            loginFailureEvent.setAuthentication(authentication);
            loginFailureEvent.setRequest(request);
            applicationContext.publishEvent(loginFailureEvent);
        }
    }
}
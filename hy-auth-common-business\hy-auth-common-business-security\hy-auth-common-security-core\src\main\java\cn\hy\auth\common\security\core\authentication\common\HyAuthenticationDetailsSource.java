package cn.hy.auth.common.security.core.authentication.common;

import org.springframework.security.authentication.AuthenticationDetailsSource;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 类描述：设置xxxToken的detail里的内容
 * 完全模仿ResourceOwnerPasswordTokenGranter#getOAuth2Authentication 里返回所有参数Map<String,String>
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/11/26 20:20
 **/

public class HyAuthenticationDetailsSource implements
        AuthenticationDetailsSource<HttpServletRequest, Map<String, String>> {
    private Set<String> ignoreParamSet;

    public HyAuthenticationDetailsSource(String... ignoreParam) {
        if (ignoreParam == null) {
            ignoreParam = new String[]{};
        }
        this.ignoreParamSet = new HashSet<>(ignoreParam.length);
        Collections.addAll(this.ignoreParamSet, ignoreParam);
    }

    @Override
    public Map<String, String> buildDetails(HttpServletRequest context) {
        return getAllRequestParam(context, ignoreParamSet);
    }

    /**
     * 获取客户端请求参数中所有的信息
     *
     * @param request        .
     * @param ignoreParamSet 忽略的请求参数
     */
    private Map<String, String> getAllRequestParam(final HttpServletRequest request, Set<String> ignoreParamSet) {
        Map<String, String> res = new HashMap<>(4);
        if (ignoreParamSet == null) {
            ignoreParamSet = new HashSet<>();
        }
        Enumeration<?> temp = request.getParameterNames();
        if (null != temp) {
            while (temp.hasMoreElements()) {
                String en = (String) temp.nextElement();
                String value = request.getParameter(en);
                res.put(en, value);
                //如果字段的值为空，判断若值为空，则删除这个字段>
                if (null == res.get(en) || "".equals(res.get(en)) || ignoreParamSet.contains(en)) {
                    res.remove(en);
                }
            }
        }
        return res;
    }

}

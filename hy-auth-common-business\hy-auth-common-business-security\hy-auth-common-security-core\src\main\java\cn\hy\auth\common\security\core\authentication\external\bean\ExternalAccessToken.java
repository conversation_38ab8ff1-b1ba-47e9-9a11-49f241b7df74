package cn.hy.auth.common.security.core.authentication.external.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * token信息
 *
 * <AUTHOR>
 * @date 2022-11-08 17:34
 */
@Setter
@Getter
public class ExternalAccessToken {

    private String providerId;
    private String lesseeCode;
    private String appCode;
    private String accessToken;
    private int errCode;
    private String errorMsg;
    private Date expiresTime;
    private int expiresIn;
    private String tokenType;
    private String refreshToken;
}

package cn.hy.auth.boot.config;

import okhttp3.ConnectionPool;

import java.util.concurrent.TimeUnit;

/**
 * Okhttp业务常量
 *
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
public class DefaultInitContants {

    /**
     * Http连接超时时间
     */
    public static final int minCONNECT_TIMEOUT = 6000;
    /**
     * Http 写入超时时间
     */
    public static final int minWRITE_TIMEOUT = 6000;
    /**
     * Http Read超时时间
     */
    public static final int minREAD_TIMEOUT = 1200000;
    /**
     * Http Async Call Timeout
     */
    public static final int minCall_TIMEOUT = 10000;
    /**
     * Http连接池
     */
    public static final int connectionPoolSize = 1000;
    /**
     * 静态连接池对象
     */
    public static ConnectionPool mConnectionPool=new ConnectionPool(connectionPoolSize,
            30, TimeUnit.MINUTES);

    /**
     * ContentType(JSON)
     */
    public static final String JSON_TYPE="JSON";

    /**
     * ContentType(form)
     */
    public static final String FORM_TYPE="FORM";
}

package cn.hy.auth.common.security.oauth2.token.store;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hy.auth.common.security.core.authentication.common.AuthCenterSpringUtil;
import cn.hy.auth.common.security.oauth2.event.TokenRevokedEvent;
import cn.hy.auth.common.security.oauth2.token.TokenStoreExtends;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.ExpiringOAuth2RefreshToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.DefaultAuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.store.redis.JdkSerializationStrategy;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStoreSerializationStrategy;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：
 * 新增刷新token时，旧token保留一段时间后再移除的功能，解决刷新token有并发请求报token过期问题。
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/1/11 11:16
 **/
@Slf4j
public class HyRedisTokenStore extends RedisTokenStore implements ApplicationListener<TokenRevokedEvent>, TokenStoreExtends {
    private static final String ACCESS = "access:";
    private static final String REMOVING_ACCESS = "removing:access:";
    private static final String AUTH_TO_ACCESS = "auth_to_access:";
    private static final String AUTH = "auth:";
    private static final String REMOVING_AUTH = "removing:auth:";
    private static final String CLIENT_ID_TO_ACCESS = "client_id_to_access:";
    private static final String REMOVING_CLIENT_ID_TO_ACCESS = "removing:client_id_to_access:";
    private static final String UNAME_TO_ACCESS = "uname_to_access:";
    private static final String REMOVING_UNAME_TO_ACCESS = "removing:uname_to_access:";
    private static final String REFRESH_TO_ACCESS = "refresh_to_access:";
    private static final String ACCESS_TO_REFRESH = "access_to_refresh:";
    private AuthenticationKeyGenerator thisAuthenticationKeyGenerator = new DefaultAuthenticationKeyGenerator();
    private RedisTokenStoreSerializationStrategy thisSerializationStrategy = new JdkSerializationStrategy();
    private String prefix = "";

    private final Method redisConnectionSet2;
    private Method redisConnectionSet_2_0;
    private static final boolean springDataRedis_2_0 = ClassUtils.isPresent(
            "org.springframework.data.redis.connection.RedisStandaloneConfiguration",
            RedisTokenStore.class.getClassLoader());
    private final RedisConnectionFactory connectionFactory;
    /**
     * 已移除token的保留时长，单位ms.默认30s
     */
    private int removingDelta = 30 * 1000;

    private final static ThreadLocal<Boolean> repeatLoginThreadLocal = new ThreadLocal<>();

    private RedisTemplate<String, Object> stringRedisTemplate;

    public HyRedisTokenStore(RedisConnectionFactory connectionFactory) {
        super(connectionFactory);
        this.connectionFactory = connectionFactory;
        this.redisConnectionSet2 = (Method) ReflectUtil.getFieldValue(this, "redisConnectionSet_2_0");
        if (springDataRedis_2_0) {
            this.loadRedisConnectionMethods_2_0();
        }
    }

    public void setRemovingDelta(int removingDelta) {
        Assert.isTrue(removingDelta > 0, "removingDelta must be greater than 0");
        this.removingDelta = removingDelta;
    }

    public void resetRepeatLoginThreadLocal() {
        repeatLoginThreadLocal.remove();
    }

    @Override
    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
        String key = thisAuthenticationKeyGenerator.extractKey(authentication);
        byte[] serializedKey = serializeKey(AUTH_TO_ACCESS + key);
        byte[] bytes = null;
        RedisConnection conn = getConnection();
        try {
            bytes = conn.get(serializedKey);
        } finally {
            conn.close();
        }
        OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
        repeatLoginThreadLocal.remove();
        if (accessToken != null) {
            OAuth2Authentication storedAuthentication = readAuthentication(accessToken.getValue());
            if ((storedAuthentication == null || !key.equals(thisAuthenticationKeyGenerator.extractKey(storedAuthentication)))) {
                // Keep the stores consistent (maybe the same user is
                // represented by this authentication but the details have
                // changed)
                storeAccessToken(accessToken, authentication);
            } else {
                repeatLoginThreadLocal.set(true);
            }
        }
        return accessToken;
    }

    @Override
    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        if (Boolean.TRUE.equals(repeatLoginThreadLocal.get())){
            repeatLoginThreadLocal.remove();
            return;
        }
        byte[] serializedAccessToken = serialize(token);
        byte[] serializedAuth = serialize(authentication);
        byte[] accessKey = serializeKey(ACCESS + token.getValue());
        byte[] authKey = serializeKey(AUTH + token.getValue());
        byte[] authToAccessKey = serializeKey(AUTH_TO_ACCESS + thisAuthenticationKeyGenerator.extractKey(authentication));
        byte[] approvalKey = serializeKey(UNAME_TO_ACCESS + getApprovalKey(token.getValue(),authentication));
        byte[] clientId = serializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());

        RedisConnection conn = getConnection();
        try {
            conn.openPipeline();
            if (springDataRedis_2_0) {
                try {
                    this.redisConnectionSet_2_0.invoke(conn, accessKey, serializedAccessToken);
                    this.redisConnectionSet_2_0.invoke(conn, authKey, serializedAuth);
                    this.redisConnectionSet_2_0.invoke(conn, authToAccessKey, serializedAccessToken);
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            } else {
                conn.set(accessKey, serializedAccessToken);
                conn.set(authKey, serializedAuth);
                conn.set(authToAccessKey, serializedAccessToken);
            }
            if (!authentication.isClientOnly()) {
                conn.sAdd(approvalKey, serializedAccessToken);
                //conn.rPush(approvalKey, serializedAccessToken);
            }
            //conn.rPush(clientId, serializedAccessToken);
            conn.sAdd(clientId, serializedAccessToken);
            if (token.getExpiration() != null) {
                int seconds = token.getExpiresIn();
                conn.expire(accessKey, seconds);
                conn.expire(authKey, seconds);
                conn.expire(authToAccessKey, seconds);
                conn.expire(clientId, seconds);
                conn.expire(approvalKey, seconds);
            }
            OAuth2RefreshToken refreshToken = token.getRefreshToken();
            if (refreshToken != null && refreshToken.getValue() != null) {
                byte[] refresh = serialize(token.getRefreshToken().getValue());
                byte[] auth = serialize(token.getValue());
                byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + token.getRefreshToken().getValue());
                byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + token.getValue());
                if (springDataRedis_2_0) {
                    try {
                        this.redisConnectionSet_2_0.invoke(conn, refreshToAccessKey, auth);
                        this.redisConnectionSet_2_0.invoke(conn, accessToRefreshKey, refresh);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                } else {
                    conn.set(refreshToAccessKey, auth);
                    conn.set(accessToRefreshKey, refresh);
                }
                if (refreshToken instanceof ExpiringOAuth2RefreshToken) {
                    ExpiringOAuth2RefreshToken expiringRefreshToken = (ExpiringOAuth2RefreshToken) refreshToken;
                    Date expiration = expiringRefreshToken.getExpiration();
                    if (expiration != null) {
                        int seconds = Long.valueOf((expiration.getTime() - System.currentTimeMillis()) / 1000L)
                                .intValue();
                        conn.expire(refreshToAccessKey, seconds);
                        conn.expire(accessToRefreshKey, seconds);
                    }
                }
            }
            conn.closePipeline();
        } finally {
            conn.close();
        }
    }

    private void loadRedisConnectionMethods_2_0() {
        this.redisConnectionSet_2_0 = ReflectionUtils.findMethod(
                RedisConnection.class, "set", byte[].class, byte[].class);
    }

    private RedisConnection getConnection() {
        return connectionFactory.getConnection();
    }

    private byte[] serialize(Object object) {
        return thisSerializationStrategy.serialize(object);
    }

    private byte[] serializeKey(String object) {
        return serialize(this.prefix + object);
    }

    private OAuth2AccessToken deserializeAccessToken(byte[] bytes) {
        return thisSerializationStrategy.deserialize(bytes, OAuth2AccessToken.class);
    }


    private byte[] serialize(String string) {
        return thisSerializationStrategy.serialize(string);
    }



    private static String getApprovalKey(String tokenValue,OAuth2Authentication authentication) {
        // 增加租户号前缀，防止不同租户的用户名混淆，比如admin
        String lessCode = tokenValue!=null && tokenValue.split("\\.").length> 3 ? tokenValue.split("\\.")[0]+"_" : "";
        String userName = authentication.getUserAuthentication() == null ? ""
                : lessCode+authentication.getUserAuthentication().getName();
        return getApprovalKey(authentication.getOAuth2Request().getClientId(), userName);
    }

    private static String getApprovalKey(String clientId, String userName) {
        return clientId + (userName == null ? "" : ":" + userName);
    }

    @Override
    public OAuth2Authentication readAuthentication(String token) {
        OAuth2Authentication oAuth2Authentication = super.readAuthentication(token);
        if (oAuth2Authentication == null) {
            byte[] bytes;
            RedisConnection conn = thisGetConnection();
            try {
                bytes = conn.get(thisSerializeKey(REMOVING_AUTH + token));
            } finally {
                conn.close();
            }
            return thisDeserializeAuthentication(bytes);
        }
        return oAuth2Authentication;
    }

    @Override
    public OAuth2AccessToken readAccessToken(String tokenValue) {
        OAuth2AccessToken oAuth2AccessToken = super.readAccessToken(tokenValue);
        if (oAuth2AccessToken == null) {
            log.debug("从待移除集合readAccessToken accessToken,tokenValue:【{}】", tokenValue);
            byte[] key = thisSerializeKey(REMOVING_ACCESS + tokenValue);
            byte[] bytes;
            RedisConnection conn = thisGetConnection();
            try {
                bytes = conn.get(key);
            } finally {
                conn.close();
            }
            return thisDeserializeAccessToken(bytes);
        }
        return oAuth2AccessToken;
    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
        // username需要携带"租户号_"
        String userNameWithdClientIdAndLessCode = getApprovalKey(clientId, userName);
        byte[] approvalKey = serializeKey(UNAME_TO_ACCESS + userNameWithdClientIdAndLessCode);
        List<byte[]> byteList = null;
        RedisConnection conn = getConnection();
        try {
            //byteList = conn.lRange(approvalKey, 0, -1);
            byteList = conn.sRandMember(approvalKey, 100);
        } finally {
            conn.close();
        }
        if (byteList == null || byteList.isEmpty()) {
            return Collections.<OAuth2AccessToken>emptySet();
        }
        List<OAuth2AccessToken> accessTokens = new ArrayList<OAuth2AccessToken>(byteList.size());
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            accessTokens.add(accessToken);
        }
        if (!CollectionUtils.isEmpty(accessTokens)) {
            return Collections.<OAuth2AccessToken>unmodifiableCollection(accessTokens);
        }

        log.debug("从待移除集合中findTokensByClientIdAndUserName accessToken,clientId:【{}】,userName:【{}】 ", clientId, userName);
        approvalKey = thisSerializeKey(REMOVING_UNAME_TO_ACCESS + userNameWithdClientIdAndLessCode);
        try {
            //byteList = conn.lRange(approvalKey, 0, -1);
            byteList = conn.sRandMember(approvalKey, 100);
        } finally {
            conn.close();
        }
        if (byteList == null || byteList.isEmpty()) {
            return Collections.emptySet();
        }
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = thisDeserializeAccessToken(bytes);
            accessTokens.add(accessToken);
        }
        return Collections.<OAuth2AccessToken>unmodifiableCollection(accessTokens);

    }

    @Override
    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
        byte[] key = serializeKey(CLIENT_ID_TO_ACCESS + clientId);
        List<byte[]> byteList = null;
        RedisConnection conn = getConnection();
        try {
            //byteList = conn.lRange(key, 0, -1);
            byteList = conn.sRandMember(key, 100);
        } finally {
            conn.close();
        }
        if (byteList == null || byteList.isEmpty()) {
            return Collections.<OAuth2AccessToken> emptySet();
        }
        List<OAuth2AccessToken> accessTokens = new ArrayList<OAuth2AccessToken>(byteList.size());
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            accessTokens.add(accessToken);
        }
        if (!CollectionUtils.isEmpty(accessTokens)){
            return Collections.<OAuth2AccessToken> unmodifiableCollection(accessTokens);
        }

        log.debug("从待移除集合中findTokensByClientId clientId:【{}】 ", clientId);
        byte[] approvalKey = serializeKey(REMOVING_UNAME_TO_ACCESS + clientId);
        try {
            //byteList = conn.lRange(approvalKey, 0, -1);
            byteList = conn.sRandMember(approvalKey, 100);
        } finally {
            conn.close();
        }
        if (byteList == null || byteList.isEmpty()) {
            return Collections.emptySet();
        }
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            accessTokens.add(accessToken);
        }
        return Collections.unmodifiableCollection(accessTokens);

    }

    @Override
    public void removeAccessToken(String tokenValue) {
        byte[] accessKey = thisSerializeKey(ACCESS + tokenValue);
        byte[] authKey = thisSerializeKey(AUTH + tokenValue);
        byte[] accessToRefreshKey = thisSerializeKey(ACCESS_TO_REFRESH + tokenValue);
        RedisConnection conn = thisGetConnection();
        try {
            conn.openPipeline();
            conn.get(accessKey);
            conn.get(authKey);
            conn.del(accessKey);
            conn.del(accessToRefreshKey);
            // Don't remove the refresh token - it's up to the caller to do that
            conn.del(authKey);
            List<Object> results = conn.closePipeline();
            byte[] access = (byte[]) results.get(0);
            byte[] auth = (byte[]) results.get(1);
            OAuth2Authentication authentication = thisDeserializeAuthentication(auth);
            if (authentication != null) {
                String key = this.thisAuthenticationKeyGenerator.extractKey(authentication);
                byte[] authToAccessKey = thisSerializeKey(AUTH_TO_ACCESS + key);
                byte[] unameKey = thisSerializeKey(UNAME_TO_ACCESS + getApprovalKey(tokenValue,authentication));
                byte[] clientId = thisSerializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
                conn.openPipeline();
                conn.del(authToAccessKey);
                //conn.lRem(unameKey, 1, access);
                conn.sRem(unameKey, access);
                //conn.lRem(clientId, 1, access);
                conn.sRem(clientId, access);
                conn.del(thisSerialize(ACCESS + key));
                conn.closePipeline();
            }
            OAuth2AccessToken oAuth2AccessToken = thisDeserializeAccessToken(access);
            // add by fxr
            if (oAuth2AccessToken != null && !oAuth2AccessToken.isExpired()) {
                log.debug("token：【{}】，【{}】 没有过期，放入待移除区域中。", oAuth2AccessToken.getValue(), oAuth2AccessToken.getExpiresIn());
                ((DefaultOAuth2AccessToken) oAuth2AccessToken).setExpiration(new Date(System.currentTimeMillis() + removingDelta));
                log.debug("token：【{}】，重新设置过期时间为：【{}】", oAuth2AccessToken.getValue(), oAuth2AccessToken.getExpiresIn());
                // 没有过期，存放在removing集合中
                byte[] newAccessToken = thisSerialize(oAuth2AccessToken);
                byte[] removingAccessKey = thisSerializeKey(REMOVING_ACCESS + oAuth2AccessToken.getValue());
                int seconds = oAuth2AccessToken.getExpiresIn();
                conn.openPipeline();
                if (redisConnectionSet2 != null) {
                    try {
                        this.redisConnectionSet2.invoke(conn, removingAccessKey, newAccessToken);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                } else {
                    conn.set(removingAccessKey, newAccessToken);
                }
                conn.expire(removingAccessKey, seconds);
                if (authentication != null) {
                    byte[] removingAuthKey = thisSerializeKey(REMOVING_AUTH + oAuth2AccessToken.getValue());
                    if (redisConnectionSet2 != null) {
                        try {
                            this.redisConnectionSet2.invoke(conn, removingAuthKey, auth);
                        } catch (Exception ex) {
                            throw new RuntimeException(ex);
                        }
                    } else {
                        conn.set(removingAuthKey, auth);
                    }
                    conn.expire(removingAuthKey, removingDelta/1000);
                    byte[] approvalKey = thisSerializeKey(REMOVING_UNAME_TO_ACCESS + getApprovalKey(tokenValue,authentication));
                    byte[] clientId = thisSerializeKey(REMOVING_CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
                    if (!authentication.isClientOnly()) {
                        //conn.rPush(approvalKey, newAccessToken);
                        conn.sAdd(approvalKey, newAccessToken);
                        conn.expire(approvalKey, seconds);
                    }
                    //conn.rPush(clientId, newAccessToken);
                    conn.sAdd(clientId, newAccessToken);
                    conn.expire(clientId, seconds);
                }
                conn.closePipeline();
            }
        } finally {
            conn.close();
        }
    }


    private byte[] thisSerializeKey(String object) {
        return thisSerialize(prefix + object);
    }

    private RedisConnection thisGetConnection() {
        return ReflectUtil.invoke(this, "getConnection");
    }

    private OAuth2Authentication thisDeserializeAuthentication(byte[] bytes) {
        return thisSerializationStrategy.deserialize(bytes, OAuth2Authentication.class);
    }

    private byte[] thisSerialize(String string) {
        return thisSerializationStrategy.serialize(string);
    }

    private byte[] thisSerialize(Object object) {
        return thisSerializationStrategy.serialize(object);
    }

    private OAuth2AccessToken thisDeserializeAccessToken(byte[] bytes) {
        return thisSerializationStrategy.deserialize(bytes, OAuth2AccessToken.class);
    }


    @Override
    public void setAuthenticationKeyGenerator(AuthenticationKeyGenerator authenticationKeyGenerator) {
        super.setAuthenticationKeyGenerator(authenticationKeyGenerator);
        this.thisAuthenticationKeyGenerator = authenticationKeyGenerator;
    }

    @Override
    public void setSerializationStrategy(RedisTokenStoreSerializationStrategy serializationStrategy) {
        super.setSerializationStrategy(serializationStrategy);
        this.thisSerializationStrategy = serializationStrategy;
    }

    @Override
    public void setPrefix(String prefix) {
        super.setPrefix(prefix);
        this.prefix = prefix;
    }

    @Override
    public void onApplicationEvent(TokenRevokedEvent tokenRevokedEvent) {
        log.debug("接收用户退出登录时间:tokenId = [{}],result = [{}]", tokenRevokedEvent.getTokenValue(), tokenRevokedEvent.isRevokeToken());
        if (tokenRevokedEvent.isRevokeToken()) {
            removeRemovingAccessToken(tokenRevokedEvent.getTokenValue());
        }
    }

    private void removeRemovingAccessToken(String tokenValue) {
        byte[] accessKey = thisSerializeKey(REMOVING_ACCESS + tokenValue);
        byte[] authKey = thisSerializeKey(REMOVING_AUTH + tokenValue);
        RedisConnection conn = thisGetConnection();
        try {
            conn.openPipeline();
            conn.get(accessKey);
            conn.get(authKey);
            conn.del(accessKey);
            // Don't remove the refresh token - it's up to the caller to do that
            conn.del(authKey);
            List<Object> results = conn.closePipeline();
            byte[] access = (byte[]) results.get(0);
            byte[] auth = (byte[]) results.get(1);
            OAuth2Authentication authentication = thisDeserializeAuthentication(auth);
            if (authentication != null) {
                String key = this.thisAuthenticationKeyGenerator.extractKey(authentication);
                byte[] unameKey = thisSerializeKey(REMOVING_UNAME_TO_ACCESS + getApprovalKey(tokenValue,authentication));
                byte[] clientId = thisSerializeKey(REMOVING_CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
                conn.openPipeline();
                //conn.lRem(unameKey, 1, access);
                conn.sRem(unameKey, access);
                //conn.lRem(clientId, 1, access);
                conn.sRem(clientId, access);
                conn.del(thisSerialize(REMOVING_ACCESS + key));
                conn.closePipeline();
            }

        } finally {
            conn.close();
        }
    }

    @Override
    public Set<String> getNoExpiredTokenUserNames() {
        if (stringRedisTemplate == null) {
            stringRedisTemplate = (RedisTemplate)AuthCenterSpringUtil.getBean(StringRedisTemplate.class);
        }
        Map<String, byte[]> keyMaps = stringRedisTemplate.execute((RedisCallback<Map<String, byte[]>>) connection -> {
            Set<byte[]> keysBytes = connection.keys((AUTH_TO_ACCESS + "*").getBytes(StandardCharsets.UTF_8));
            if (CollUtil.isEmpty(keysBytes)) {
                return Collections.emptyMap();
            }
            return keysBytes.stream()
                    .collect(Collectors.toMap(
                            key -> new String(key, StandardCharsets.UTF_8),
                            key -> key
                    ));
        });
        Set<String> noExpiredTokenUserNames = new HashSet<>();
        if (CollUtil.isEmpty(keyMaps)){
            return noExpiredTokenUserNames;
        }
        for (Map.Entry<String, byte[]> entry : keyMaps.entrySet()) {
            byte[] serializedKey = serializeKey(entry.getKey());
            byte[] bytes;
            RedisConnection conn = getConnection();
            try {
                bytes = conn.get(serializedKey);
            } finally {
                conn.close();
            }
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            if (accessToken != null) {
                OAuth2Authentication storedAuthentication = readAuthentication(accessToken.getValue());
                if (StrUtil.isNotBlank(storedAuthentication.getName())) {
                    noExpiredTokenUserNames.add(storedAuthentication.getName());
                }
            }
        }
        return noExpiredTokenUserNames;
    }

}

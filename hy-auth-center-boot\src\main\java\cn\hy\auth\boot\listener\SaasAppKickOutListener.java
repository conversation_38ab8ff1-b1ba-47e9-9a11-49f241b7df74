package cn.hy.auth.boot.listener;

import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.user.account.controller.UserAccountController;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 类描述：监听saas安装应用的主题，用于刷新更新缓存
 *
 * <AUTHOR> by fuxinrong
 * @date 2024/4/23 15:49
 **/
@Service
@Slf4j
public class SaasAppKickOutListener {
    private final UserAccountController userAccountController;

    public SaasAppKickOutListener(UserAccountController userAccountController) {
        this.userAccountController = userAccountController;
    }

    @KafkaListener(topics = "${hy.saas.token.kickout.topic:hy-auth-sys-token-kickout}"
    )
    public void processAsyncTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional<Object> message = Optional.ofNullable(record.value());
        try {
            if (message.isPresent()) {
                String msg = (String) message.get();
                log.debug("消费了： Topic:{},Message:{}", record.topic(), msg);
                JSONObject jsonObject = JSON.parseObject(msg);
                String lesseeCode = (String) jsonObject.get("lessee_code");
                String appCode = (String) jsonObject.get("app_code");
                AuthContext.getContext().setLesseeCode(lesseeCode);
                AuthContext.getContext().setAppCode(appCode);
                Object userNameObj = jsonObject.get("username");
                if (userNameObj instanceof List) {
                    List<String> userList = (List<String>) userNameObj;
                    for (String userName : userList) {
                        kickOutUser(userName);
                    }
                } else if (userNameObj instanceof String){
                    kickOutUser(userNameObj.toString());
                }
            }
        } finally {
            ack.acknowledge();
        }
    }
    private void kickOutUser(String username) {
        try {
            userAccountController.kickOutByResetPwd(username);
        } catch (Exception e) {
            log.warn("处理Kafka的应用踢人消息报错，可以忽略不处理。{}", e.getMessage());
        }
    }
}

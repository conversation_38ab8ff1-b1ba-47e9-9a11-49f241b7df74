package cn.hy.auth.custom.security.third.dingtalk.impl;

import cn.hy.auth.common.security.core.authentication.social.HySocialAuthenticationToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderUserInfo;
import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.AppThirdLoginAccount;
import cn.hy.auth.common.security.core.authentication.social.service.MiniProgramFreeLogin;
import cn.hy.auth.common.security.core.authentication.social.service.SocialUsersConnectionService;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 钉钉用户应用信息获取
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class AppLoginServiceImpl implements AppThirdLoginAccount {

    @Autowired
    private SocialUsersConnectionService socialUsersConnectionService;
    @Autowired
    private MiniProgramFreeLogin miniProgramFreeLogin;

    @Override
    public String getUserAccount(String code, String lesseeCode, String appCode, ProviderUserInfo providerUserInfo,
                                 HySocialAuthenticationToken authenticationToken, ProviderAppAccessToken providerAppAccessToken) {
        Map<String, Object> details = (Map<String, Object>) authenticationToken.getDetails();
        String miniappDirectBind = Optional.ofNullable(details.get("miniapp_direct_bind")).map(Object::toString).orElse("");
        String miniappCode = Optional.ofNullable(details.get("miniapp_code")).map(Object::toString).orElse("");
        List<String> userAccountNames = socialUsersConnectionService.findUserAccountName(lesseeCode, appCode, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid());
        userAccountNames = userAccountNames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userAccountNames) && miniappDirectBind.equals(Boolean.TRUE.toString())) {
            // 删除空白的UserAccountName记录
            socialUsersConnectionService.deleteBlankUserAccountName(lesseeCode, appCode, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid());
            // 新建用户账号
            miniProgramFreeLogin.addUserAccount(lesseeCode, appCode, providerUserInfo.getUserDisplayName());
            // 插入第三方用户信息
            String thirdUserId = String.valueOf(socialUsersConnectionService.insertProviderUserInfo(lesseeCode, appCode, providerUserInfo));
            // 绑定第三方用户
            miniProgramFreeLogin.bingThirdAccount(lesseeCode, appCode, providerUserInfo.getProviderId(), providerUserInfo.getUserDisplayName(), providerUserInfo.getProviderUserid());
            //userAccountService.bingThirdAccount(ThirdUserBindDTO.builder().providerId(providerUserInfo.getProviderId()).thirdUserId(thirdUserId).userAccountName(providerUserInfo.getUserDisplayName()).build());
            userAccountNames.add(providerUserInfo.getUserDisplayName());
        } else if (CollectionUtils.isEmpty(userAccountNames) && !miniappDirectBind.equals(Boolean.TRUE.toString())) {
            // 5、否，提示未绑定，需要进入账号密码登录
            log.info("code:{},providerId:{},provideUserId:{},没有绑定我方用户信息。提示进行绑定", code, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid());
            socialUsersConnectionService.deleteBlankUserAccountName(lesseeCode, appCode, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid());
            long id = socialUsersConnectionService.insertProviderUserInfo(lesseeCode, appCode, providerUserInfo);
            Map<String, Object> tipMap = new HashMap<>();
            // AuthErrorCodeEnum.A0246
            tipMap.put("code", "A0246");
            tipMap.put("recordId", id + "");
            tipMap.put("recordUserId", providerUserInfo.getProviderUserid() + "");
            tipMap.put("msg", "一键登录失败，请登录绑定账号");
            throw new InternalAuthenticationServiceException(JSON.toJSONString(tipMap));
        } else if (userAccountNames.size() > 1) {
            // 6 数据出错了
            log.error("code:{},providerId:{},provideUserId:{},找到多条数据记录。userIds：{}", code, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid(), userAccountNames);
            throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("AppLoginServiceImpl.result.msg1", null) + providerUserInfo.getProviderId() + ",provideUserId:" + providerUserInfo.getProviderUserid() + LocaleUtil.getMessage("AppLoginServiceImpl.result.msg2", null) + userAccountNames.size() + LocaleUtil.getMessage("AppLoginServiceImpl.result.msg3", null));
        }
        // 微信小程序用户手机号
        String phoneNumber = null;
        if (StringUtils.isNotBlank(miniappCode) && authenticationToken.getProviderId().equals(ProviderType.MINI_PROGRAM.getCode())) {
            // 获取手机号
            phoneNumber = miniProgramFreeLogin.getPhoneNumber(providerAppAccessToken,miniappCode);
            // 修改绑定账号的账号名
            phoneNumber = miniProgramFreeLogin.updateUserAccountName(lesseeCode, appCode, phoneNumber, userAccountNames.get(0));
        }
        if (Objects.nonNull(phoneNumber)) {
            userAccountNames.set(0, phoneNumber);
        }
        // 4、是，直接放行，进入后续登录流程
        log.info("code:{},providerId:{}的用户：{},已经关联了我方应用：{}_{}的用户:{}", code, providerUserInfo.getProviderId(), providerUserInfo.getProviderUserid(), lesseeCode, appCode, userAccountNames.get(0));
        // 5. 转为我方用户名，进入后面的各种登录规则的校验
        String userAccountName = userAccountNames.get(0);
        return userAccountName;
    }
}

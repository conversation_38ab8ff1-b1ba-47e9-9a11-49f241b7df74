package cn.hy.auth.custom.user.cache.constant;

/**
 * 类描述: 缓存标识静态变量类
 *
 * <AUTHOR>
 * @date ：创建于 2020/11/27
 */
public class UserCacheKeyConst {

    private UserCacheKeyConst() {

    }

    /**
     * 报存用户完整信息，包括账户信息、机构、岗位、角色等。key格式：auth_user_all_{userid}
     */
    public static final String COMPLETE_USER_PREFIX = "auth_user_all_";
    /**
     * 保存登录名（或昵称）与用户ID之间的关系，key格式：auth_login_name_{name}
     */
    public static final String LOGIN_NAME_PREFIX = "auth_login_name_";
    /**
     * 保存token与用户ID之间的关系，key格式：auth_token_user_{tokenId}
     */
    public static final String TOKEN_USER_PREFIX = "auth_token_user_";
    /**
     * 保存用户ID与token之间的关系，key格式：auth_user_token_{userId}
     */
    public static final String USER_TOKEN_PREFIX = "auth_user_token_";

    /**
     * 保存token与用户最后登录信息之间的关系，key格式：auth_user_last_login_{tokenId}
     */
    public static final String AUTH_USER_LAST_LOGIN_PREFIX = "auth_user_last_login_";
}

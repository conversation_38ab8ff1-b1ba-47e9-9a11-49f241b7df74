package cn.hy.auth.custom.security.third.wechat.impl;

import cn.hy.auth.common.security.core.authentication.social.enums.ProviderType;
import cn.hy.auth.common.security.core.authentication.social.service.HyProviderInfoService;
import cn.hy.auth.custom.common.exceptions.AuthBusinessException;
import cn.hy.auth.custom.common.utils.LocaleUtil;
import cn.hy.auth.custom.security.third.wechat.domain.WechatchatProvider;
import cn.hy.saas.commons.SpringContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * 企业微信用户应用信息获取
 *
 * <AUTHOR>
 * @date 2022-11-08 10:34
 */
@Component
@Slf4j
public class WechatProviderInfoImpl implements HyProviderInfoService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getProviderId() {
        return ProviderType.WECHAT.getCode();
    }

    @Override
    public WechatchatProvider getProviderInfo(String lesseeCode, String appCode) {
        WechatchatProvider wechatchatProvider  = null;
        try {
            if (jdbcTemplate == null) {
                jdbcTemplate = SpringContextHolder.getApplicationContext().getBean(JdbcTemplate.class);
            }
            String querySql="SELECT config_content FROM " + lesseeCode + "_" + appCode + "_auth_config order by create_time desc limit 1 ";
            List<Map<String, Object>> configList = jdbcTemplate.queryForList(querySql);
            wechatchatProvider = new WechatchatProvider();
            String appKey;
            String appSecret;
            if (CollectionUtils.isNotEmpty(configList)) {
                Map<String, Object> configMap = configList.get(0);
                String configContent = (String) configMap.get("config_content");
                Map<String, Object> configInfo = (Map<String, Object>) JSONObject.parse(configContent);
                if (configInfo.get("appInfoMapping") != null) {
                    Map<String, Object> appInfo = (Map<String, Object>) configInfo.get("appInfoMapping");
                    if (appInfo.get("wechat") != null) {
                        Map<String, Object> dingTalk = (Map<String, Object>) appInfo.get("wechat");
                        appKey = (String) dingTalk.get("appKey");
                        appSecret = (String) dingTalk.get("appSecret");
                        wechatchatProvider.setProviderId(getProviderId());
                        wechatchatProvider.setAppKey(appKey);
                        wechatchatProvider.setAppSecret(appSecret);
                        wechatchatProvider.setOriginConfig(JSONObject.toJSONString(configInfo));
                        return wechatchatProvider;
                    }else{
                        throw new AuthBusinessException("500", LocaleUtil.getMessage("WechatProviderInfoImpl.result.msg1", null));
                    }
                }else{
                    throw new AuthBusinessException("500",LocaleUtil.getMessage("WechatProviderInfoImpl.result.msg2", null));
                }
            }else{
                throw new AuthBusinessException("500",LocaleUtil.getMessage("WechatProviderInfoImpl.result.msg3", null));
            }
        } catch (Exception e) {
            log.error("查询[auth_config]第三方应用信息失败,{},{}",e,lesseeCode,appCode);
            throw new AuthBusinessException("500",LocaleUtil.getMessage("WechatProviderInfoImpl.result.msg4", null)+e.getMessage());
        }
    }
}

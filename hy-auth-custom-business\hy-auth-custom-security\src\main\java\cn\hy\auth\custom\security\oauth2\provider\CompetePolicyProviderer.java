package cn.hy.auth.custom.security.oauth2.provider;

import cn.hy.auth.common.security.core.authentication.common.AbstractAuthenticationProvider;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthCompetitionPolicyDTO;
import cn.hy.auth.custom.common.domain.authinfo.AppAuthInfoParser;
import cn.hy.auth.custom.common.enums.LoginTypeEnum;
import cn.hy.auth.custom.user.account.service.KickOutEnum;
import cn.hy.auth.custom.user.account.service.OauthLogoutService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 登录竞争策略校验
 *
 * <AUTHOR>
 * @date 2020-11-27 11:15
 **/
@Component
@Slf4j
public class CompetePolicyProviderer extends AbstractAuthenticationProvider {
    private final OauthLogoutService oauthLogoutService;

    public CompetePolicyProviderer(OauthLogoutService oauthLogoutService) {
        this.oauthLogoutService = oauthLogoutService;
    }

    /**
     * 由子类实现逻辑.自定义校验登录认证业务，不符合可以直接抛异常，符合返回null即可。
     *
     * @param authentication .
     * @return Authentication 认证信息封装类
     */
    @Override
    protected Authentication doAuthenticate(Authentication authentication) {
        String userName = (String)authentication.getPrincipal();
        AppAuthCompetitionPolicyDTO competePolicy = AppAuthInfoParser.INSTANCE.getLoginCompetitionPolicy();
        if (!competePolicy.isEnable()) {
            log.debug("登录竞争策略未启用。");
            return null;
        }
        if (competePolicy.isSameClientCompetition() && competePolicy.isAllClientCoexist()){
            // 1、同端竞争，不同端并存,暂不实现

        } else if (competePolicy.isSameClientCompetition() && competePolicy.isAllClientCompetition()){
            // 2、同端竞争，不同端竞争
            if (competePolicy.isAllClientKickOut()){
                // 踢掉所有客户端,后登者挤出先登者
                try {
                    oauthLogoutService.kickOut(AuthContext.getContext().getLesseeCode(),userName, KickOutEnum.LOGIN_COMPETITION);
                }catch (Exception e){
                    log.info("执行挤出token功能报错了。但不影响登录功能。{}_{},userName:{}",AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),userName,e);
                }
                return null;
            } else if (competePolicy.isAllClientBlockLogin()){
               // 后登者被阻止登录
               // 查询用户上次的登录信息
                //log.info("租户号：{}，应用号：{}，用户账号：{}，已经登录了，ip:{}", AuthContext.getContext().getLesseeCode(),AuthContext.getContext().getAppCode(),userName, IpUtils.getIpAddress());
                //String msg = "用户账号："+userName+",已经登录了。";
                //throw new BlockedLoginException(msg);
            } else {
                log.info("不同竞争的策略值配置不正确，无法执行登录竞争逻辑。{}",competePolicy.getAllClient());
            }

        } else if (competePolicy.isSameClientCoexist() && competePolicy.isAllClientCompetition()){
            // 3、同端并存，不同端竞争,暂不实现

        }

        log.debug("登录竞争策略未启用。");

        return null;
    }


    /**
     * 实现多个provider执行排序
     * 序号越小，越先执行
     *
     * @return 序号
     */
    @Override
    protected int order() {
        return 25;
    }


    /**
     * 判断是否是用户密码登录模式
     *
     * @param authentication .
     * @return .
     */
    @Override
    protected boolean isSupport(Authentication authentication) {
        List<LoginTypeEnum> supports = Lists.newArrayList(LoginTypeEnum.USERNAME_PASSWORD,
                LoginTypeEnum.SMS_CODE,
                LoginTypeEnum.OAUTH2_PASSWORD);

        return supports.contains(AuthContext.getContext().loginState().getLoginType());

    }
}

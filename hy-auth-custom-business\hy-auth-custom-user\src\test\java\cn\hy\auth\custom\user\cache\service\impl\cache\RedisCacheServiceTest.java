package cn.hy.auth.custom.user.cache.service.impl.cache;

import cn.hy.auth.common.business.tool.redis.service.RedisService;
import cn.hy.auth.custom.user.cache.domain.NativeCacheDTO;
import cn.hy.auth.custom.user.cache.service.NativeDiffCacheService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 类描述: redis缓存测试类
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/3
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {RedisCacheServiceImpl.class})
public class RedisCacheServiceTest {

    @Resource(name = "redisCacheServiceImpl")
    private NativeDiffCacheService nativeDiffCacheService;

    @MockBean
    private RedisService redisService;

    @Test
    public void handle() {
        when(redisService.getValuesByPrefix(any())).thenReturn(Collections.singletonList(1L));
        NativeCacheDTO nativeCacheDTO = NativeCacheDTO.builder().rootCacheName("hy_auth").build();
        nativeDiffCacheService.handle(nativeCacheDTO);
    }
}
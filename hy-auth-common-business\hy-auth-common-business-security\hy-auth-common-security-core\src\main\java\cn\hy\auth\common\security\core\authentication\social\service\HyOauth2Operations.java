package cn.hy.auth.common.security.core.authentication.social.service;

import cn.hy.auth.common.security.core.authentication.social.bean.ProviderAppAccessToken;
import cn.hy.auth.common.security.core.authentication.social.bean.ProviderInfo;

import java.util.Map;

/**
 * 类描述：第三方平台Oauth2 服务组件
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/9 15:26
 **/
public interface HyOauth2Operations {
    /**
     * 获得在第三方平台创建应用的access_token
     * @param lesseeCode 租户号
     * @param appCode 应用号
     * @param providerInfo 第三方平台服务商信息
     * @param requestParamMap 请求入参
     * @return .
     */
    ProviderAppAccessToken getProviderAppAccessToken(String lesseeCode, String appCode, ProviderInfo providerInfo,Map<String, Object> requestParamMap);

}

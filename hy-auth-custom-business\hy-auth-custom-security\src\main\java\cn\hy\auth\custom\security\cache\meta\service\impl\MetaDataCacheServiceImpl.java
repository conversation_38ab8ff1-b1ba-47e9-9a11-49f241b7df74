package cn.hy.auth.custom.security.cache.meta.service.impl;

import cn.hy.auth.custom.security.cache.meta.service.MetaDataCacheService;
import cn.hy.metadata.engine.api.cache.DatabaseMetaDataCacheManager;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @author: ysh
 * @project: hy-authentication-center
 * @className: MetaDataCacheServiceImpl
 * @time: 2022-11-23 16:56
 * @desc: 元数据缓存实现类
 **/
@Service
@AllArgsConstructor
public class MetaDataCacheServiceImpl implements MetaDataCacheService {

    private final DatabaseMetaDataCacheManager databaseMetaDataCacheManager;

    @Override
    public void cacheClear(String lessCode, String appCode) {
        databaseMetaDataCacheManager.clearAppCache(lessCode, appCode);
    }
}

package cn.hy.auth.custom.security.oauth2.event;

import cn.hy.auth.custom.common.domain.UserLoginInfoDTO;
import lombok.Getter;

/**
 * 类描述: 登出日志
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/2
 */
@Getter
public class LogOutLogEvent extends BaseAsyncEvent {


    /**
     * 创建登出日志事件
     *
     * @param userLoginInfoDTO .
     */
    public LogOutLogEvent(UserLoginInfoDTO userLoginInfoDTO) {
        super(userLoginInfoDTO);
    }

    public UserLoginInfoDTO getUserLoginInfoDTO() {
        return (UserLoginInfoDTO) getSource();
    }
}

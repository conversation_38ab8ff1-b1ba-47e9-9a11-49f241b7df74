package cn.hy.auth.custom.multi.event;

import cn.hy.auth.custom.common.domain.authinfo.AppAuthStrategyDTO;
import org.springframework.context.ApplicationEvent;

/**
 * 应用认证规则加载事件
 * 当从数据库中加载认证规则数据时，发出该事件
 *
 * <AUTHOR>
 * @date 2020-12-07 10:01
 **/
public class AppAuthInfoLoadEvent extends ApplicationEvent {

    private static final long serialVersionUID = -8244528692234948L;

    /**
     * Create a new ApplicationEvent.
     *
     * @param appAuthInfo 应用认证规则信息
     */
    public AppAuthInfoLoadEvent(@lombok.NonNull AppAuthStrategyDTO appAuthInfo) {
        super(appAuthInfo);
    }

    public String getLesseeCode() {
        return ((AppAuthStrategyDTO) getSource()).getLesseeAccessName();
    }

    public String getAppCode() {
        return ((AppAuthStrategyDTO) getSource()).getAppAccessName();
    }
}

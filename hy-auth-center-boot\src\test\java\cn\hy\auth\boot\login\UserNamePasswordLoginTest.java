package cn.hy.auth.boot.login;

import cn.hy.auth.boot.utils.BaseJunit4Test;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.utils.AuthSpringUtil;
import cn.hy.auth.custom.user.account.mapper.DynamicUserAccountMapper;
import cn.hy.auth.custom.user.account.service.UserAccountService;
import cn.hy.auth.custom.user.utils.PasswordAesUtil;
import cn.hy.dataengine.context.DataEngineContextProxy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.Filter;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 用户名密码方式登录--集成测试
 * 由于依赖mysql语法，所以要依赖mysql数据库测试，不需要依赖表，会自动创建使用到的表和测试完后自动删除
 * 在CI/CD流程里面需要忽略，只是方便本地集成测试
 *
 * <AUTHOR>
 * @date 2020-12-04 16:04
 **/
//@Ignore
public class UserNamePasswordLoginTest extends BaseJunit4Test {
    private final AtomicBoolean isFirst = new AtomicBoolean(true);
    @Autowired
    private WebApplicationContext wac;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private List<Filter> filters;
    @Autowired
    private DynamicUserAccountMapper userAccountMapper;

    //伪造一个MVC的环境，伪造的环境不会启动tomcat
    private MockMvc mockMvc;


    @Before
    public void setup() {
        if (isFirst.getAndSet(false)) {
            Filter[] filterArray = filters.toArray(new Filter[0]);
            //在测试之前注册mockmvc
            mockMvc = MockMvcBuilders.webAppContextSetup(wac).addFilters(filterArray).build();

            MockUserData mockUserData = new MockUserData(jdbcTemplate);
            mockUserData.initUserTable();
            mockUserData.initUserData();
        }
    }

    @AfterClass
    public static void clear() {
        JdbcTemplate jdbcTemplate = AuthSpringUtil.getBean(JdbcTemplate.class);
        new MockUserData(jdbcTemplate).clear();
    }

    //region 成功登录

    /**
     * 采用用户名通过表单登录
     * 密码明文
     *
     * @throws Exception
     */
    @Test
    public void testLogin1() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", "zhangsan");
        params.add("password", "123456");
        params.add("client_id", "client_hy_web");
        params.add("client_secret", "hy123456");
        params.add("lessee_code", "hy");
        params.add("app_code", "apptest");
        params.add("pwd_encryption_type", "1");
        params.add("client_type", "4");
        params.add("client_name", "testclient");
        try {
            usernamePwdLogin(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 采用手机号码通过表单登录
     * 密码明文
     *
     * @throws Exception
     */
    @Test
    public void testLogin2() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", "13344445555");
        params.add("password", "123456");
        params.add("client_id", "client_hy_web");
        params.add("client_secret", "hy123456");
        params.add("lessee_code", "letest");
        params.add("app_code", "apptest");
        params.add("pwd_encryption_type", "1");
        params.add("client_type", "4");
        params.add("client_name", "testclient");
        try {
            usernamePwdLogin(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 采用用户名通过表单登录
     * 密码密文
     *
     * @throws Exception
     */
    @Test
    public void testLogin3() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", "lisi");
        params.add("password", PasswordAesUtil.encryptPwd("1"));
        params.add("client_id", "client_hy_web");
        params.add("client_secret", "hy123456");
        params.add("lessee_code", "letest");
        params.add("app_code", "apptest");
        params.add("pwd_encryption_type", "3");
        params.add("client_type", "4");
        params.add("client_name", "testclient");
        try {
            usernamePwdLogin(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 采用用户名通过表单登录
     * (用户+密码)密文
     *
     * @throws Exception
     */
    @Test
    public void testLogin4() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", "wangwu");
        params.add("password", PasswordAesUtil.encryptPwd("wangwu1"));
        params.add("client_id", "client_hy_web");
        params.add("client_secret", "hy123456");
        params.add("lessee_code", "letest");
        params.add("app_code", "apptest");
        params.add("pwd_encryption_type", "2");
        params.add("client_type", "2");
        params.add("client_name", "testclient");
        try {
            usernamePwdLogin(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void usernamePwdLogin(MultiValueMap<String, String> params) throws Exception {
        String resultString = usernamePwdLoginBase(params);
        JSONObject resultJson = JSON.parseObject(resultString);
        System.out.println(JSON.toJSONString(resultJson));
        Assertions.assertThat(StringUtils.isNotBlank(resultJson.getString("access_token"))).isTrue();

        checkToken(resultJson.getString("access_token"));
    }

    private String usernamePwdLoginBase(MultiValueMap<String, String> params) throws Exception {
        //发送post请求
        ResultActions result = mockMvc.perform(MockMvcRequestBuilders.post("/login")
                //参数
                .params(params)
                //设置返回类型
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andDo(MockMvcResultHandlers.print());

        MvcResult resultObj = result.andReturn();

        String resultString = resultObj.getResponse().getContentAsString();
        System.out.println(resultString);
        return resultString;
    }

    private void checkToken(String token) throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", token);
        //发送post请求
        ResultActions result = mockMvc.perform(MockMvcRequestBuilders.post("/oauth/check_token")
                //参数
                .params(params)
                //设置返回类型
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andDo(MockMvcResultHandlers.print());

        MvcResult resultObj = result.andReturn();

        String resultString = resultObj.getResponse().getContentAsString();
        JSONObject resultJson = JSON.parseObject(resultString);
        Assertions.assertThat(resultJson).isNotNull();
    }

    //endregion

    //region 登录失败

    /**
     * 采用用户名通过表单登录
     * 密码错误
     */
    @Test
    public void testLoginFailure1() {
        usernamePwdLoginMissingParameters("password", "用户信息错误。");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少username
     */
    @Test
    public void testLoginFailure2() {
        usernamePwdLoginMissingParameters("username", "用户信息错误。");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少client_id
     */
    @Test
    public void testLoginFailure3() {
        usernamePwdLoginMissingParameters("client_id", "client_id不能为空");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少client_secret
     */
    @Test
    public void testLoginFailure4() {
        usernamePwdLoginMissingParameters("client_secret", "client_secret不能为空");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少lessee_code
     */
    @Test
    public void testLoginFailure5() {
        usernamePwdLoginMissingParameters("lessee_code", "缺少租户编码参数");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少app_code
     */
    @Test
    public void testLoginFailure6() {
        usernamePwdLoginMissingParameters("app_code", "缺少应用编码参数");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少pwd_encryption_type
     */
    @Test
    public void testLoginFailure7() {
        usernamePwdLoginMissingParameters("pwd_encryption_type", "pwd_encryption_type不能为空");
    }

    /**
     * 采用用户名通过表单登录
     * 缺少client_type
     */
    @Test
    public void testLoginFailure8() {
        usernamePwdLoginMissingParameters("client_type", "client_type不能为空");
    }

    private void usernamePwdLoginMissingParameters(String missParam, String targetMsg) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", "zhangsan");
        params.add("password", "123456");
        params.add("client_id", "client_hy_web");
        params.add("client_secret", "hy123456");
        params.add("lessee_code", "letest");
        params.add("app_code", "apptest");
        params.add("pwd_encryption_type", "1");
        params.add("client_type", "4");
        params.add("client_name", "testclient");
        try {
            params.remove(missParam);
            String result = usernamePwdLoginBase(params);
            Assertions.assertThat(result.contains(targetMsg)).isTrue();
        } catch (IllegalArgumentException e) {
            Assertions.assertThat(e.getMessage().contains(targetMsg)).isTrue();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试token
     */
    @Test
    public void testLoginToken() {
        try {
            AuthContext.getContext().setLesseeCode("hy");
            AuthContext.getContext().setAppCode("8test8");
            //checkToken("hy.appmanage.4.2e3b0f53-08e4-4382-89b0-ad8a873fd0ae");
            checkToken("efc08439-9218-49e5-bbc4-36ef171553b0");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试token
     */
    @Test
    public void testRemoveBind() {
        try {
            AuthContext.getContext().setLesseeCode("hy").setAppCode("mobile");
            userAccountMapper.deleteThirdRelation("test001",null,"H03324");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //endregion
}

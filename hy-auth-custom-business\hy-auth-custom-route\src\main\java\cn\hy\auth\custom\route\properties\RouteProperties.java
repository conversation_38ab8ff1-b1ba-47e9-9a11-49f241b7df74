package cn.hy.auth.custom.route.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.style.ToStringCreator;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：
 *
 * <AUTHOR> by fuxinrong
 * @date 2021/8/24 14:09
 **/
@Component
@ConfigurationProperties(prefix = "hy.security.oauth2.route")
public class RouteProperties {
    /**
     * key:systemAreaIdentify
     * value: ip+port 多个地址使用 逗号隔开
     * https://blog.csdn.net/MTone1/article/details/91413539
     */
    private Map<String,String> serverHosts = Collections.emptyMap();

    /**
     * 链接配置信息
     */
    private HostConnectionProperties hostConnection = new HostConnectionProperties();
    /**
     * 识别请求属于哪个系统的groovy脚本. 暂时没支持登录的请求，后续可以直接在这里写逻辑进行支持
     */
    private String requestSystemAreaRecognizerScript = "if (token == null || token.toString().length()==0){return null;};if (token.toString().split(\"\\\\.\").length > 3){return \"3.X\";} else {return \"2.X\";}";
    /**
     * Setting for SendResponseServiceImpl for the initial stream buffer size.
     */
    private int initialStreamBufferSize = 8192;
    /**
     * 过滤器的拦截的url pattern
     */
    private List<String> filterPattern;
    /**
     * 是否启用外部认证
     */
    private String enableThird="2.X";

    public String getEnableThird() {
        return enableThird;
    }

    public void setEnableThird(String enableThird) {
        this.enableThird = enableThird;
    }

    private List<String> ignoreHeaders = Collections.singletonList("Connection");
    public static class HostConnectionProperties {

        /**
         * The maximum number of total connections the proxy can hold open to backends.
         */
        private int maxTotalConnections = 200;

        /**
         * The maximum number of connections that can be used by a single route.
         */
        private int maxPerRouteConnections = 20;

        /**
         * The socket timeout in millis. Defaults to 30000.
         */
        private int socketTimeoutMillis = 30000;

        /**
         * The connection timeout in millis. Defaults to 6000.
         */
        private int connectTimeoutMillis = 6000;

        /**
         * The timeout in milliseconds used when requesting a connection from the
         * connection manager. Defaults to -1, undefined use the system default.
         */
        private int connectionRequestTimeoutMillis = 10000;

        /**
         * The lifetime for the connection pool.
         */
        private long timeToLive = -1;

        /**
         * The time unit for timeToLive.
         */
        private TimeUnit timeUnit = TimeUnit.MILLISECONDS;

        public HostConnectionProperties() {
        }

        public HostConnectionProperties(int maxTotalConnections, int maxPerRouteConnections,
                                        int socketTimeoutMillis, int connectTimeoutMillis, long timeToLive,
                                        TimeUnit timeUnit) {
            this.maxTotalConnections = maxTotalConnections;
            this.maxPerRouteConnections = maxPerRouteConnections;
            this.socketTimeoutMillis = socketTimeoutMillis;
            this.connectTimeoutMillis = connectTimeoutMillis;
            this.timeToLive = timeToLive;
            this.timeUnit = timeUnit;
        }

        public int getMaxTotalConnections() {
            return maxTotalConnections;
        }

        public void setMaxTotalConnections(int maxTotalConnections) {
            this.maxTotalConnections = maxTotalConnections;
        }

        public int getMaxPerRouteConnections() {
            return maxPerRouteConnections;
        }

        public void setMaxPerRouteConnections(int maxPerRouteConnections) {
            this.maxPerRouteConnections = maxPerRouteConnections;
        }

        public int getSocketTimeoutMillis() {
            return socketTimeoutMillis;
        }

        public void setSocketTimeoutMillis(int socketTimeoutMillis) {
            this.socketTimeoutMillis = socketTimeoutMillis;
        }

        public int getConnectTimeoutMillis() {
            return connectTimeoutMillis;
        }

        public void setConnectTimeoutMillis(int connectTimeoutMillis) {
            this.connectTimeoutMillis = connectTimeoutMillis;
        }

        public int getConnectionRequestTimeoutMillis() {
            return connectionRequestTimeoutMillis;
        }

        public void setConnectionRequestTimeoutMillis(
                int connectionRequestTimeoutMillis) {
            this.connectionRequestTimeoutMillis = connectionRequestTimeoutMillis;
        }

        public long getTimeToLive() {
            return timeToLive;
        }

        public void setTimeToLive(long timeToLive) {
            this.timeToLive = timeToLive;
        }

        public TimeUnit getTimeUnit() {
            return timeUnit;
        }

        public void setTimeUnit(TimeUnit timeUnit) {
            this.timeUnit = timeUnit;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            HostConnectionProperties host = (HostConnectionProperties) o;
            return maxTotalConnections == host.maxTotalConnections
                    && maxPerRouteConnections == host.maxPerRouteConnections
                    && socketTimeoutMillis == host.socketTimeoutMillis
                    && connectTimeoutMillis == host.connectTimeoutMillis
                    && connectionRequestTimeoutMillis == host.connectionRequestTimeoutMillis
                    && timeToLive == host.timeToLive && timeUnit == host.timeUnit;
        }

        @Override
        public int hashCode() {
            return Objects.hash(maxTotalConnections, maxPerRouteConnections,
                    socketTimeoutMillis, connectTimeoutMillis,
                    connectionRequestTimeoutMillis, timeToLive, timeUnit);
        }

        @Override
        public String toString() {
            return new ToStringCreator(this)
                    .append("maxTotalConnections", maxTotalConnections)
                    .append("maxPerRouteConnections", maxPerRouteConnections)
                    .append("socketTimeoutMillis", socketTimeoutMillis)
                    .append("connectTimeoutMillis", connectTimeoutMillis)
                    .append("connectionRequestTimeoutMillis",
                            connectionRequestTimeoutMillis)
                    .append("timeToLive", timeToLive).append("timeUnit", timeUnit)
                    .toString();
        }

    }

    public Map<String, String> getServerHosts() {
        return serverHosts;
    }

    public void setServerHosts(Map<String, String> serverHosts) {
        this.serverHosts = serverHosts;
    }

    public HostConnectionProperties getHostConnection() {
        return hostConnection;
    }

    public void setHostConnection(HostConnectionProperties hostConnection) {
        this.hostConnection = hostConnection;
    }

    public String getRequestSystemAreaRecognizerScript() {
        return requestSystemAreaRecognizerScript;
    }

    public void setRequestSystemAreaRecognizerScript(String requestSystemAreaRecognizerScript) {
        this.requestSystemAreaRecognizerScript = requestSystemAreaRecognizerScript;
    }

    public int getInitialStreamBufferSize() {
        return initialStreamBufferSize;
    }

    public void setInitialStreamBufferSize(int initialStreamBufferSize) {
        this.initialStreamBufferSize = initialStreamBufferSize;
    }

    public List<String> getFilterPattern() {
        return filterPattern;
    }

    public void setFilterPattern(List<String> filterPattern) {
        this.filterPattern = filterPattern;
    }

    public List<String> getIgnoreHeaders() {
        return ignoreHeaders;
    }

    public void setIgnoreHeaders(List<String> ignoreHeaders) {
        this.ignoreHeaders = ignoreHeaders;
    }
}

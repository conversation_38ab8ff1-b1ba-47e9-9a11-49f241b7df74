package cn.hy.auth.common.security.core.authentication.social.bean;

import java.util.Map;

/**
 * 类描述：第三方平台服务商信息
 *
 * <AUTHOR> by fuxinrong
 * @date 2022/9/13 15:29
 **/
public interface ProviderInfo {
    /**
     *  服务商标识
     * @return .
     */
    String getProviderId();

    /**
     * 应用key
     * @return 。
     */
    String getAppKey();
    /**
     * 应用secret
     * @return 。
     */
    String getAppSecret();
    /**
     *  获取在第三方自建应用的access_token的url地址
     * @return .
     */
    String getAppAccessTokenUrl();

    /**
     *  获取在第三方自建应用的用户信息的url地址
     * @return .
     */
    String getAppUserInfoUrl();
    /**
     * 原始完整配置第三方平台服务商信息
     * @return .
     */
    Map<String,Object> originConfigMap();


}

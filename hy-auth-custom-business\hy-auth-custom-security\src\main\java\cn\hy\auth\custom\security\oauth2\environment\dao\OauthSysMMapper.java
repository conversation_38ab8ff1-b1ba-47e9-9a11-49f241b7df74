package cn.hy.auth.custom.security.oauth2.environment.dao;

import cn.hy.auth.custom.security.oauth2.environment.domain.OauthSysMDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 环境标识表操作mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/11
 */
@Mapper
public interface OauthSysMMapper {

    /**
     * 统计数量
     * @return 表数据量
     */
    int count();

    /**
     * 新增数据
     * @param oauthSysM 新增的数据
     */
    void insert(OauthSysMDO oauthSysM);

    /**
     * 查询第一条数据的环境标识uuid
     * @return 第一条数据的环境标识uuid
     */
    String selectFirstUuid();
}

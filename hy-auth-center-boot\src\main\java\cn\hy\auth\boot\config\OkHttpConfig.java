package cn.hy.auth.boot.config;


import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp相关配置（后面抽取到配置中心）
 * @date: 2022-04-20 19:31:59
 * <AUTHOR>
 */
@Component
@Configuration
@Slf4j
public class OkHttpConfig {


    @Bean
    public  OkHttpClient getHttpClient(){
        TrustManager[] trustManagers = buildTrustManagers();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(DefaultInitContants.minCONNECT_TIMEOUT, TimeUnit.MILLISECONDS) //连接超时
                .readTimeout(DefaultInitContants.minREAD_TIMEOUT, TimeUnit.MILLISECONDS) //读取超时
                .writeTimeout(DefaultInitContants.minWRITE_TIMEOUT, TimeUnit.MILLISECONDS) //写超时
                .callTimeout(DefaultInitContants.minCall_TIMEOUT, TimeUnit.MILLISECONDS)
                .connectionPool(DefaultInitContants.mConnectionPool)
                .sslSocketFactory(createSSLSocketFactory(trustManagers), (X509TrustManager) trustManagers[0])
                .hostnameVerifier((hostName, session) -> true)
                .retryOnConnectionFailure(true)
                .build();
        return client;
    }

    /**
     * 生成安全接字工厂，用于https请求的证书跳过
     *
     * @return
     */
    private  SSLSocketFactory createSSLSocketFactory(TrustManager[] trustAllCerts) {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ssfFactory;
    }

    private  TrustManager[] buildTrustManagers() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }
}

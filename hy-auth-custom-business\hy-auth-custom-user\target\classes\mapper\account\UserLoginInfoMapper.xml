<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.user.history.dao.UserLoginInfoMapper">
    <resultMap id="BaseResultMap" type="cn.hy.auth.custom.user.history.domain.UserLoginInfoDO">
        <id column="id" property="id" jdbcType="DECIMAL" />
        <result column="create_user_id" property="createUserId" jdbcType="DECIMAL" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
        <result column="login_type" property="loginType" jdbcType="VARCHAR" />
        <result column="data_version" property="dataVersion" jdbcType="VARCHAR" />
        <result column="ip" property="ip" jdbcType="VARCHAR" />
        <result column="client_type" property="clientType" jdbcType="INTEGER" />
        <result column="last_update_user_id" property="lastUpdateUserId" jdbcType="DECIMAL" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="client_id" property="clientId" jdbcType="VARCHAR" />
        <result column="login_count" property="loginCount" jdbcType="DECIMAL" />
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
        <result column="sequence" property="sequence" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_account_name" property="userAccountName" jdbcType="VARCHAR" />
        <result column="error_code" property="errorCode" jdbcType="VARCHAR" />
        <result column="last_update_user_name" property="lastUpdateUserName" jdbcType="VARCHAR" />
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR" />
        <result column="mac" property="mac" jdbcType="LONGVARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        id, create_user_id, create_time, create_user_name, login_type, data_version, ip,
        client_type, last_update_user_id, type, client_id, login_count, last_update_time,
        sequence, user_id, user_account_name, error_code, last_update_user_name,error_message, mac
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from `user_login_info`
        where id = #{id}
    </select>
    <select id="getLastTwoSuccessLoginInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `user_login_info`
        WHERE
        `user_id` = #{userId}
        <if test="clientId !='' and clientId != null">
            AND `client_id` = #{clientId}
        </if>
        AND `type` = 1
        ORDER BY `create_time` DESC
        LIMIT 0,2
    </select>
    <select id="getLastSuccessLoginInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `user_login_info`
        WHERE
        `user_account_name` = #{userAccountName}
        <if test="clientId !='' and clientId != null">
            AND `client_id` = #{clientId}
        </if>
        AND `type` = 1
        ORDER BY `create_time` DESC
        LIMIT 1
    </select>


    <select id="getMaxSequence" resultType="java.lang.Long">
        SELECT IFNULL(SEQUENCE,0)+1  FROM `user_login_info` ORDER BY SEQUENCE desc LIMIT 1
    </select>
    <insert id="insert" parameterType="cn.hy.auth.custom.user.history.domain.UserLoginInfoDO">
        insert into `user_login_info` (id, create_user_id, create_time,
        create_user_name, data_version, ip,
        client_type, last_update_user_id, type,
        client_id, last_update_time, login_count,
        sequence, user_id, user_account_name,
        error_code, last_update_user_name, error_message,
        mac, login_type)
        values (#{id}, #{createUserId}, #{createTime},
        #{createUserName}, #{dataVersion}, #{ip},
        #{clientType}, #{lastUpdateUserId}, #{type},
        #{clientId}, #{lastUpdateTime}, #{loginCount},
        #{sequence}, #{userId}, #{userAccountName},
        #{errorCode}, #{lastUpdateUserName}, #{errorMessage},
        #{mac}, #{loginType})
    </insert>
</mapper>
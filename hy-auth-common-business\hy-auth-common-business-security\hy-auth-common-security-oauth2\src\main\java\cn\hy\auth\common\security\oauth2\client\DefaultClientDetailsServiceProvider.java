package cn.hy.auth.common.security.oauth2.client;

import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;

import javax.sql.DataSource;

/**
 * 类描述：默认
 *
 * <AUTHOR> by fuxinrong
 * @date 2020/12/18 17:13
 **/
public class DefaultClientDetailsServiceProvider implements ClientDetailsServiceProvider {
    /**
     * 数据源
     */
    private DataSource dataSource;

    public DefaultClientDetailsServiceProvider(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public ClientDetailsService getClientDetailsService() {
        return new JdbcClientDetailsService(this.dataSource);
    }
}

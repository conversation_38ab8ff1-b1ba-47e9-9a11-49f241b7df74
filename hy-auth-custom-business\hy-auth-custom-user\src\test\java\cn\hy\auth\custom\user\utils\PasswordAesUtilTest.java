package cn.hy.auth.custom.user.utils;

import cn.hy.auth.custom.common.enums.EncryptTypeEnum;
import cn.hy.auth.custom.common.utils.AesUtil;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 类描述: 密码加密解密单元测试
 *
 * <AUTHOR>
 * @date ：创建于 2021/2/1
 */
public class PasswordAesUtilTest {

    @Test
    public void extractPwd() {
        //账号
        String account = "admin";
        //密码
        String password = "123456";
        //加密密码
        String encryptPassword = AesUtil.encrypt(password, AesUtil.DEFAULT_KEY);

        //密码明文
        String encryptPasswordResult = PasswordAesUtil.extractPwd(password, account, EncryptTypeEnum.NOT_ENCRYPTION);
        assertEquals(encryptPassword, encryptPasswordResult);

        //密码密文
        encryptPasswordResult = PasswordAesUtil.extractPwd(encryptPassword, account, EncryptTypeEnum.PASSWORD_ENCRYPTION);
        assertEquals(encryptPassword, encryptPasswordResult);


        String encryptPasswordAccount = AesUtil.encrypt(account + password, AesUtil.DEFAULT_KEY);
        //密码+账号密文
        encryptPasswordResult = PasswordAesUtil.extractPwd(encryptPasswordAccount, account, EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION);
        assertEquals(encryptPassword, encryptPasswordResult);

    }

    @Test
    public void decryptPwd() {
        //账号
        String account = "admin";
        //密码
        String password = "123456";
        //加密密码
        String encryptPassword = AesUtil.encrypt(password, AesUtil.DEFAULT_KEY);

        //密码明文
        String encryptPasswordResult = PasswordAesUtil.decryptPwd(password, account, EncryptTypeEnum.NOT_ENCRYPTION);
        assertEquals(password, encryptPasswordResult);

        //密码密文
        encryptPasswordResult = PasswordAesUtil.decryptPwd(encryptPassword, account, EncryptTypeEnum.PASSWORD_ENCRYPTION);
        assertEquals(password, encryptPasswordResult);


        String encryptPasswordAccount = AesUtil.encrypt(account + password, AesUtil.DEFAULT_KEY);
        //密码+账号密文
        encryptPasswordResult = PasswordAesUtil.decryptPwd(encryptPasswordAccount, account, EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION);
        assertEquals(password, encryptPasswordResult);
    }

    @Test
    public void matches() {
        //账号
        String account = "admin";
        //密码
        String password = "123456";
        //加密密码
        String encryptPassword = AesUtil.encrypt(password, AesUtil.DEFAULT_KEY);

        //密码明文
        Boolean result = PasswordAesUtil.matches(encryptPassword, password, account, EncryptTypeEnum.NOT_ENCRYPTION);
        assertTrue(result);

        //密码密文
        result = PasswordAesUtil.matches(encryptPassword, encryptPassword, account, EncryptTypeEnum.PASSWORD_ENCRYPTION);
        assertTrue(result);

        String encryptPasswordAccount = AesUtil.encrypt(account + password, AesUtil.DEFAULT_KEY);
        //密码+账号密文
        result = PasswordAesUtil.matches(encryptPassword, encryptPasswordAccount, account, EncryptTypeEnum.LOGIN_NAME_PASSWORD_ENCRYPTION);
        assertTrue(result);
    }

}
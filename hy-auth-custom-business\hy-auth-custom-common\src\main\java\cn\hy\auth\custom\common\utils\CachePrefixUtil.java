package cn.hy.auth.custom.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.custom.common.context.AuthContext;
import cn.hy.auth.custom.common.domain.LoginStateDTO;
import cn.hy.auth.custom.common.enums.ClientTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.StringJoiner;
import java.util.function.Supplier;

/**
 * 类描述: 获取缓存前缀
 *
 * <AUTHOR>
 * @date ：创建于 2020/12/1
 */
public class CachePrefixUtil {
    private static final ThreadLocal<Boolean> LOCAL = ThreadLocal.withInitial(() -> Boolean.TRUE);
    private CachePrefixUtil() {

    }

    public static Boolean isAutoAttachCcahePrefix(){
        return LOCAL.get();
    }
    public static void setAutoAttachCachePrefix(Boolean b){
         LOCAL.set(b);
    }
    public static void resetAutoAttachCachePrefix(){
        LOCAL.remove();
    }
    /**
     * 返回缓存前缀，格式:租户编码_应用编码_客户端标识
     *
     * @return 返回前缀
     */
    public static String get() {
        // 不需要自动添加缓存前缀
        if (!Boolean.TRUE.equals(isAutoAttachCcahePrefix())){
            return "";
        }
        //租户编码
        String lesseeCode = AuthContext.getContext().getLesseeCode();
        //应用编码
        String appCode = AuthContext.getContext().getAppCode();
        if (StringUtils.isBlank(lesseeCode) && StringUtils.isBlank(appCode)){
            return "";
        }
        LoginStateDTO loginStateDTO = AuthContext.getContext().loginState();

        StringJoiner stringJoiner = new StringJoiner("_");
        stringJoiner.add(lesseeCode).add(appCode);
        if (ObjectUtil.isNotNull(loginStateDTO)) {
            ClientTypeEnum clientType = loginStateDTO.getClientType();
            if (ObjectUtil.isNotNull(clientType)) {
                stringJoiner.add(String.valueOf(clientType.getCode()));
            }
        }

        return stringJoiner.toString() + "_";
    }

}

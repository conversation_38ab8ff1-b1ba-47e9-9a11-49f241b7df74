package cn.hy;

import cn.hy.auth.custom.common.utils.AesUtil;
import org.apache.commons.collections4.MapUtils;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-25 14:25
 **/
public class MyTest {


    @Test
    public void test() {
        System.out.println(AesUtil.encryptByDefaultKey("zhangsan1"));

    }

    @Test
    public void test2() {
        Map<String, Object> map = new HashMap<>();
        map.put("a", 1);
        map.put("b", new Date());

        System.out.println(MapUtils.getLongValue(map, "b"));
    }
}

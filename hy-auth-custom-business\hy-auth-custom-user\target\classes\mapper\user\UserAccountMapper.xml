<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.hy.auth.custom.user.account.dao.UserAccountDao">
    <update id="update">
        ${sql}
    </update>
    <update id="updatePwd">
        update ${tableName}  Set `${pwdField}` =  #{password}  Where  `${uidField}` =  #{userId};
    </update>
    <update id="updatePwdModifyTime">
        update ${tableName}  Set `${field}` =  #{time}  Where  `${uidField}` =  #{userId};
    </update>
    <update id="changeLoginInfoByUserNameList">
        update sysdev_user_online_status Set online_status = #{status}, _online_statusname = #{statusName} Where user_account_name in
        <foreach collection="userNames" item="username" separator="," open="(" close=")">
            #{username}
        </foreach>
    </update>
    <update id="changeLoginStatusNotInSet">
        update sysdev_user_online_status Set online_status = #{status}, _online_statusname = #{statusName} Where 1 = 1
        <if test="userNames != null and userNames.size() != 0">
            and user_account_name not in
            <foreach collection="userNames" item="username" separator="," open="(" close=")">
                #{username}
            </foreach>
        </if>

    </update>

    <select id="queryForList" resultType="java.util.Map">
        ${sql}
    </select>

    <select id="queryByUid" resultType="java.util.Map">
        Select * From ${tableName}  Where `${uidField}` =  #{userId}
    </select>
    <select id="queryUserInfo" resultType="java.util.Map">
        Select * From  ${tableName}  Where
        <foreach item="item" index="index" collection="list"
                 open="" separator=" OR " close="">
              `${item}` = #{val}
        </foreach>

    </select>
    <select id="queryUserInfoByJoinTable" resultType="java.util.Map">
        select
            t1.*
        from
            user_account t1
        <if test="mobileLogin and appCode != '' and appCode != 'sys' and lesseeCode != '' and lesseeCode != 'paas'">
            left join
                hr_member_account t2
            on
                t1.user_account_name = t2.account_name
            left join
                hr_member t3
            on
                t2.hr_member_code = t3.hr_code and t3.mobile = #{val}
        </if>
        where 1 = 1
        <if test="mobileLogin">
            and t3.mobile = #{val}
        </if>
        <if test="!mobileLogin">
            and t1.user_account_name = #{val}
        </if>
    </select>
    <select id="queryAllByUid" resultType="java.util.Map">
        select *
        from user_account
        where id = #{uid} limit 1
    </select>

    <select id="queryThirdById" resultType="java.util.Map">
        select *
        from third_user_info
        where id = #{id} limit 1
    </select>

    <select id="queryThirdUserById" resultType="java.util.Map">
        select *
        from third_user_info
        where provider_user_id = #{id} limit 1
    </select>

    <delete id="deleteThirdRelation">
        delete from third_user_relation where provider_id = #{providerId}
        and user_account_name = #{userAccountName}
        <if test="providerUserId!=null and providerUserId!=''" >
             and provider_user_id = #{providerUserId}
        </if>
    </delete>

    <insert id="saveThirdRelation">
        REPLACE INTO third_user_relation
        (id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name, last_update_time,
        `sequence`, provider_id, provider_user_id, provider_union_id, user_account_name)
        VALUES(#{id}, '0', 1, 'admin', #{createTime}, 1, 'admin', #{lastUpdateTime}, 1, #{providerId},  #{providerUserId}, NULL, #{userAccountName});
    </insert>
    <insert id="insertUserNameStatus">
        INSERT INTO sysdev_user_online_status
            (id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name, last_update_time, sequence, user_account_name, online_status, `_online_statusname`, login_time, last_login_time)
        VALUES
            (#{statusDTO.id}, #{statusDTO.dataVersion}, #{statusDTO.createUserId}, #{statusDTO.createUserName},
            #{statusDTO.createTime}, #{statusDTO.lastUpdateUserId}, #{statusDTO.lastUpdateUserName},
            #{statusDTO.lastUpdateTime}, #{statusDTO.sequence}, #{statusDTO.userAccountName}, #{statusDTO.onlineStatus}, #{statusDTO.onlineStatusName},
            #{statusDTO.loginTime}, #{statusDTO.lastLoginTime})
    </insert>
    <update id="updateUserNameStatus">
        update sysdev_user_online_status set
            data_version = #{statusDTO.dataVersion},
            create_user_id = #{statusDTO.createUserId},
            create_user_name = #{statusDTO.createUserName},
            create_time = #{statusDTO.createTime},
            last_update_user_id = #{statusDTO.lastUpdateUserId},
            last_update_user_name = #{statusDTO.lastUpdateUserName},
            last_update_time = #{statusDTO.lastUpdateTime},
            sequence = #{statusDTO.sequence},
            user_account_name = #{statusDTO.userAccountName},
            online_status = #{statusDTO.onlineStatus},
            _online_statusname = #{statusDTO.onlineStatusName},
            login_time = #{statusDTO.loginTime},
            last_login_time = #{statusDTO.lastLoginTime}
        where user_account_name = #{statusDTO.userAccountName}
    </update>
    <select id="findUserAccount" resultType="java.util.Map">
        select id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name, last_update_time,
        `sequence`, provider_id, CONVERT(provider_user_id,CHAR) as provider_user_id, provider_union_id, user_account_name
        from  third_user_relation
        where 1=1
        <if test="providerId!=null and providerId!=''" >
            and provider_id = #{providerId}
        </if>
        <if test="userAccountName!=null and userAccountName!=''" >
            and user_account_name != #{userAccountName}
        </if>
        <if test="providerUserId!=null and providerUserId!=''" >
            and provider_user_id = #{providerUserId}
        </if>

    </select>


    <select id="findProviderUser" resultType="java.util.Map">
        select id, data_version, create_user_id, create_user_name, create_time, last_update_user_id, last_update_user_name, last_update_time,
        `sequence`, provider_id, CONVERT(provider_user_id,CHAR) as provider_user_id, provider_union_id, user_account_name
        from  third_user_relation
        where 1=1
        <if test="providerId!=null and providerId!=''" >
            and provider_id = #{providerId}
        </if>
        <if test="userAccountName!=null and userAccountName!=''" >
            and user_account_name = #{userAccountName}
        </if>
        <if test="providerUserId!=null and providerUserId!=''" >
            and provider_user_id != #{providerUserId}
        </if>
    </select>

    <select id="selectOneByAccout" resultType="java.util.Map">
        select user_account_name,username
        from user_account
        where 1=1
        <if test="userAccountName!=null and userAccountName!=''" >
            and user_account_name = #{userAccountName}
        </if>
    </select>
    <select id="doSome" resultType="java.lang.Integer">
        select count(id) from user_account;
    </select>
    <select id="selectUserIdByUserNameOrMobile" resultType="cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO">
        select
            t1.id, t1.user_account_name, t3.mobile
        from
            user_account t1
        left join
            hr_member_account t2
        on
            t1.user_account_name = t2.account_name
        left join
            hr_member t3
        on
            t2.hr_member_code = t3.hr_code and t3.mobile = #{phoneNumber}
        where
            t3.mobile = #{phoneNumber}
        or
            t1.user_account_name = #{userName}
        limit 1
    </select>
    <select id="checkOnLineStatusTableIsExist" resultType="java.lang.Integer">
        select count(1) from information_schema.tables where table_name = #{targetTableName}
    </select>
    <select id="getUserByUserNameOrPhoneLimitOne"
            resultType="cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO">
        select
            t1.id, t1.user_account_name, t3.mobile
        from
            user_account t1
        left join
            hr_member_account t2
        on
            t1.user_account_name = t2.account_name
        left join
            hr_member t3
        on
            t2.hr_member_code = t3.hr_code
        where
            t3.mobile = #{phoneNumber}
        or
            t1.user_account_name = #{userName}
        limit 1
    </select>
    <select id="selectRecodeByUserName" resultType="cn.hy.auth.custom.user.account.domain.SysdevUserOnlineStatusDTO">
        select id, login_time, last_login_time from sysdev_user_online_status where user_account_name = #{userAccountName}
    </select>

    <select id="selectUserByEmail" resultType="cn.hy.auth.custom.user.account.domain.UserLoginAccountDTO">
        select
        t1.id, t1.user_account_name, t3.email
        from user_account t1
        left join hr_member_account t2 on t1.user_account_name = t2.account_name
        left join hr_member t3 on t2.hr_member_code = t3.hr_code and t3.email = #{email}
        where t3.email = #{email}
        limit 1
    </select>

</mapper>
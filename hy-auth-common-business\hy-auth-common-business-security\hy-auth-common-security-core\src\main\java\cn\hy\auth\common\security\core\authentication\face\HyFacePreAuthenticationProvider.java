package cn.hy.auth.common.security.core.authentication.face;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hy.auth.common.security.core.authentication.face.model.HyFaceRecognitionDTO;
import cn.hy.auth.common.security.core.authentication.face.service.OpenApiConnectionService;
import cn.hy.auth.common.security.core.authentication.social.service.HyConnectionFactory;
import cn.hy.auth.common.security.core.utils.LocaleUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 人脸登录认证提供者(预先校验处理)
 *
 * <AUTHOR>
 * @date 2023-06-07 17:34
 */
@Data
@Slf4j
@Component
public class HyFacePreAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private List<HyConnectionFactory> connectionFactories;
    @Autowired
    private OpenApiConnectionService openApiConnectionService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        HyFaceAuthenticationToken authenticationToken = (HyFaceAuthenticationToken) authentication;
        String lesseeCode = authenticationToken.getLesseeCode();
        String appCode = authenticationToken.getAppCode();
        String loginType = authenticationToken.getLoginType();
        String pic = authenticationToken.getFacePic();
        HyFaceRecognitionDTO recognitionParams = new HyFaceRecognitionDTO();
        recognitionParams.setPic(pic);
        recognitionParams.setLesseeCode(lesseeCode);
        recognitionParams.setAppCode(appCode);
        recognitionParams.setLoginTime(LocalDateTime.now());
        recognitionParams.setClientType(loginType);
        Object recognition = openApiConnectionService.getUserByFace(recognitionParams);
        try {
            if (recognition != null) {
                JSONObject result = JSONObject.parseObject(recognition.toString());
                if (result.getBoolean("pass").equals(Boolean.TRUE)) {
                    //1 获取人脸对接服务返回的人脸信息
                    JSONObject userInfo = result.getJSONObject("userInfo");
                    //2 根据人脸信息特征查询对应的用户信息
                    String hrCode = userInfo.getString("hrCode");
                    //3 根据人员查询账号信息
                    Map<String, Object> userAccountMap = openApiConnectionService.getUserAccount(hrCode);
                    if(MapUtil.isEmpty(userAccountMap)){
                        throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg1", null)+hrCode+ LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg2", null));
                    }
                    String userAccountName = (String) userAccountMap.get("account_name");
                    if (StringUtils.isEmpty(userAccountName)) {
                        throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg1", null)+hrCode+LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg3", null));
                    }
                    //3 执行人脸登录
                    authenticationToken.setPrincipal(userAccountName);
                } else {
                    if(ObjectUtil.isNotNull(result.get("msg"))){
                        throw new InternalAuthenticationServiceException(result.get("msg").toString());
                    }else{
                        throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg4", null));
                    }
                }
            } else {
                throw new InternalAuthenticationServiceException(LocaleUtil.getMessage("HyFacePreAuthenticationProvider.result.msg5", null));
            }
        } catch (Exception e) {
            throw new InternalAuthenticationServiceException(e.getMessage());
        }
        return authenticationToken;
    }


    @Override
    public boolean supports(Class<?> authentication) {
        return HyFaceAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

package cn.hy.auth.custom.user.cache.service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public interface ForgetPasswordCacheManage {

    public void put(String key, Object value);

    public void put(String key, Object value, Date expireDate);

    public void put(String key, Object value, long timeout, TimeUnit unit);

    public Object get(String key);

    public boolean invalidate(String key);

}
